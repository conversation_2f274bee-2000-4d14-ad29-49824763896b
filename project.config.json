{"miniprogramRoot": "dist/", "description": "显辉小程序", "setting": {"urlCheck": true, "es6": true, "enhance": true, "compileHotReLoad": false, "postcss": false, "minified": true, "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "coverView": false, "showShadowRootInWxmlPanel": false, "packNpmRelationList": [], "ignoreUploadUnusedFiles": true, "condition": false}, "compileType": "miniprogram", "srcMiniprogramRoot": "dist/", "condition": {}, "editorSetting": {"tabIndent": "insertSpaces", "tabSize": 2}, "appid": "wx7b32eee85988e4a1", "libVersion": "3.0.1", "packOptions": {"ignore": [], "include": []}, "projectname": "newMallApp"}