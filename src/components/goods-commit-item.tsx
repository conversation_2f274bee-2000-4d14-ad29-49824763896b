import { View, Text, Image, RichText } from "@tarojs/components";
import "./goods-commit-item.less";
import { Rate } from "@antmjs/vantui";
import { useEffect, useState } from "react";
import Taro from "@tarojs/taro";

export default ({
    headImg,
    userName,
    wxGroupNum,
    auditStatus,
    auditStatusDesc,
    scoreCode,
    evaluationContent,
    evaluationUrlList,
    evaluationTimeDesc,
    totalCountName,
    praise,
    wxGroupName,
    isEllipsisImg,
    replyResponse,
    sameWxGroup,
    specification,
}) => {
    const [show, setShow] = useState(true);
    const [isOverflow, setIsOverflow] = useState(false);

    useEffect(() => {
        const query = Taro.createSelectorQuery();
        replyResponse &&
            replyResponse.replyContent &&
            query
                .select(".good-commit-item-reply-content")
                .boundingClientRect(rect => {
                    const lineHeight = 17; // 假设行高为20px
                    const maxLines = 2; // 最大显示行数
                    const maxHeight = lineHeight * maxLines;
                    if (rect) {
                        setIsOverflow(rect.height > maxHeight);
                        setShow(rect.height < maxHeight);
                    }
                })
                .exec();
    }, []);
    console.log("specification:", specification);
    return (
        <View className="good-commit-item">
            <View className="good-commit-item-header">
                <View className="good-commit-item-userinfo">
                    <Image src={headImg} className="good-commit-item-headimg" />
                    <Text className="good-commit-item-userName">{userName}</Text>
                    {/* {wxGroupName && <View className={`good-commit-item-wxGroupNum ${sameWxGroup ? "" : "gray"}`}>{wxGroupName}</View>} */}
                    {wxGroupNum && (
                        <View className={`good-commit-item-groupNum ${sameWxGroup ? "red" : "gray"}`}>福利群</View>
                    )}
                    {wxGroupNum && (
                        <View className={`good-commit-item-wxGroupNum ${sameWxGroup ? "red-text" : "gray-text"}`}>
                            {wxGroupNum}
                        </View>
                    )}
                    {/* {totalCountName && <View className="good-commit-item-groupNum">{totalCountName}</View>} */}
                    {/* EVALUATION_WAIT(1, "待审核"),
    EVALUATION_PASS(10, "已通过"),
    EVALUATION_REJECT(20, "已驳回"), */}
                    {auditStatusDesc && (
                        <View
                            className={`good-commit-item-auditStatusName ${
                                auditStatus == "EVALUATION_PASS" ? "audit-success" : ""
                            }  ${auditStatus == "EVALUATION_REJECT" ? "audit-error" : ""} `}
                        >
                            {auditStatusDesc}
                        </View>
                    )}
                </View>
                <Text className="good-commit-item-time">{evaluationTimeDesc}</Text>
            </View>
            <View className="good-commit-item-content">
                {specification ? <View className="good-commit-item-specification">{specification}</View> : null}
                <Rate
                    value={scoreCode / 20}
                    size={24}
                    readonly
                    color="rgba(245, 196, 120, 1)"
                    style={{ margin: "0 0 10px 0" }}
                />
                <RichText className="good-commit-item-commits" nodes={evaluationContent}></RichText>
                <View className="good-commit-item-pics">
                    {(evaluationUrlList || []).map((picItems, index) => {
                        if (isEllipsisImg && index > 2) {
                            return null;
                        }
                        return (
                            <Image
                                src={picItems}
                                className="good-commit-item-pic"
                                onClick={() => {
                                    Taro.previewImage({
                                        urls: evaluationUrlList,
                                        current: picItems,
                                    });
                                }}
                            />
                        );
                    })}
                    {isEllipsisImg && (evaluationUrlList || []).length > 3 ? (
                        <View
                            className="good-commit-item-pics-ellipsis"
                            onClick={() => {
                                Taro.previewImage({
                                    urls: evaluationUrlList,
                                    current: evaluationUrlList[2],
                                });
                            }}
                        >
                            +{(evaluationUrlList || []).length - 3}
                        </View>
                    ) : null}
                </View>
                {replyResponse ? (
                    <View className="good-commit-item-reply">
                        <View className="good-commit-item-reply-title">商家回复:</View>
                        <View className={`good-commit-item-reply-content ${show ? "expand" : "collapse"}`}>
                            {replyResponse.replyContent}
                        </View>
                        {isOverflow ? (
                            <View
                                className="toggle-btn"
                                onClick={() => {
                                    setShow(!show);
                                }}
                            >
                                {show ? "收起" : "展开"}
                            </View>
                        ) : null}
                    </View>
                ) : null}
            </View>
        </View>
    );
};
