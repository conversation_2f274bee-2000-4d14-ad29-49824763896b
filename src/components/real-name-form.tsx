import api, { wxRequestParams } from "@/utils/api";
import DDYToast from "@/utils/toast";
import { Button, FormRender, Form, IFormInstanceAPI, FormItem } from "@antmjs/vantui";
import { View, Text, Input } from "@tarojs/components";
import React, { useEffect, useImperativeHandle, useRef, useState } from "react";
import { DDYObject } from "src/type/common";

type IParams = {
    // account: number
    paperName: string;
    paperNo: string;
};
interface RealNameFromProps {
    onOk: () => void;
    // infoApi?: (params: wxRequestParams) => Promise<any>;
    certInfo?: DDYObject;
    onFail: () => void;
    color?: string;
    submit?: (obj: IParams) => void;
}

export interface RealNameFromRef {
    form: IFormInstanceAPI;
}
export type RealNameFromRefProps = React.ForwardRefExoticComponent<
    React.RefAttributes<RealNameFromRef> & RealNameFromProps
>;

const RealNameFrom: RealNameFromRefProps = React.forwardRef((props, ref) => {
    const { onOk, onFail, color = "#9023d7", submit, certInfo } = props;
    const form = Form.useForm();
    const isCert = useRef(false);
    const [userCertInfo, setUserCertInfo] = useState<any>();
    const onSubmit = () => {
        const paperName = form.getFieldValue("paperName");
        const paperNo = form.getFieldValue("paperNo");
        // console.log("values:", values)
        if (paperName) {
            form.setFields({
                paperName: paperName.trim(),
            });
        }
        if (paperNo) {
            form.setFields({
                paperNo: paperNo.trim(),
            });
        }
        form.validateFields((err, res: IParams) => {
            // console.log("err:", err, res)
            if (err && err?.length > 0) {
                return;
            }
            if (submit) {
                console.log("res:", res);
                submit(res);
                return;
            }
            api.saveUserCertification({
                method: isCert.current ? "PUT" : "POST",
                data: {
                    paperBack: null,
                    paperFront: null,
                    extra: null,
                    ...userCertInfo,
                    paperName: res.paperName.trim(),
                    paperNo: res.paperNo.trim(),
                },
                filterCheck: true,
            })
                .then(res => {
                    console.log("res:", res);

                    if (res) {
                        //成功回调
                        onOk();
                    } else {
                        DDYToast.info(res.message || "操作失败");
                        onFail();
                    }
                })
                .catch(() => {});
        });
    };

    const getDetail = () => {
        // 获取实名认证信息
        const promiseFn = api.getUserCertification;
        promiseFn({
            data: {
                id: 0,
            },
            method: "GET",
            filterCheck: true,
        }).then(res => {
            if (res.message) {
                return;
            }
            setUserCertInfo(res);
            isCert.current = true;
            form.setFields(res);
        });
    };

    useEffect(() => {
        // !certInfo ? getDetail():;
        if (!certInfo) {
            getDetail();
        } else {
            setUserCertInfo(certInfo);
            isCert.current = true;
            form.setFields(certInfo);
        }
    }, [certInfo]);

    useImperativeHandle(ref, () => {
        return {
            form: form,
        };
    });
    return (
        <>
            <Form form={form}>
                <FormItem
                    label="姓名"
                    name="paperName"
                    required
                    valueFormat={e => e.detail.value}
                    trigger="onInput"
                    rules={[
                        {
                            rule: value => {
                                if (!value || value.trim() === "") {
                                    return Promise.resolve("请输入姓名");
                                }
                                return Promise.resolve("");
                            },
                        },
                    ]}
                >
                    <Input placeholder="请输入姓名" />
                </FormItem>
                <FormItem
                    label="身份证号"
                    name="paperNo"
                    required
                    valueFormat={e => e.detail.value}
                    trigger="onInput"
                    rules={[
                        {
                            rule: value => {
                                const reg =
                                    /^[1-9]\d{5}(18|19|([23]\d))\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/;
                                if (!reg.test(value)) {
                                    return Promise.resolve("请输入合法的身份证号");
                                }
                                return Promise.resolve("");
                            },
                        },
                    ]}
                >
                    <Input placeholder="请输入身份证号" />
                </FormItem>
            </Form>
            <View className="list_txt" style="height: auto;">
                <Text style={{ color: "#ccc" }}>
                    注：根据海关规定，购买跨境商品需要办理清关手续，请你配合进行实名认证，以确保你购买的商品顺利通过海关检查。本商城承诺所传身份证明只用于办理跨境商品的清关手续，不作他途使用，其他任何人均无法查看。
                </Text>
            </View>
            <Button
                style={{ width: "100%", marginTop: "20px", backgroundColor: color }}
                type="primary"
                onClick={onSubmit}
                round
            >
                提交
            </Button>
        </>
    );
});
export default RealNameFrom;
