.order-list-item {
    margin: 24rpx;
    padding: 24rpx;
    background-color: #fff;
    border-radius: 16rpx;
    .order-item-top {
        position: relative;
        .order-info {
            font-size: 26px;
            font-weight: 400;
            color: #333333;
            line-height: 40px;
            // padding-right: 200rpx;
            box-sizing: border-box;

            .order-cell {
                display: flex;
                flex-direction: row;
                align-items: center;
            }
        }
        .order-status {
            position: absolute;
            top: 0;
            right: 0;
            .order_state {
                font-size: 24px;
                font-weight: 400;
                color: #333333;
                line-height: 24px;
            }
        }
    }
    .order-item-good {
        margin-top: 20rpx;
        .van-card {
            padding-left: 0;
            // padding-right: 0;
        }
    }
    .order-total {
        width: 100%;
        text-align: right;
        padding-top: 10rpx;
        .total-text {
            font-size: 24rpx;
        }
        .total-price {
            font-size: 36rpx;
            color: #333333;
            font-weight: 500;
        }
    }
    .order-income {
        padding-left: 234rpx;
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: space-between;
        padding-top: 32rpx;
        .order-income-item {
            padding-left: 86rpx;
            padding-right: 20rpx;
        }
        .title {
            font-size: 24px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #999999;
            line-height: 28px;
            padding-bottom: 24rpx;
        }
        .value {
            font-size: 36px;
            font-family: D-DIN, D;
            font-weight: normal;
            color: #f5001d;
            line-height: 40px;
        }
    }
    .order-item-bottom {
        .btn-container {
            // padding: 20rpx 0;
            display: flex;
            flex-direction: row;
            align-items: center;
            justify-content: flex-end;
        }
        .btn {
            margin-top: 30rpx;
            height: 48rpx;
            background: #ffffff;
            border: 1rpx solid #666666;
            margin-left: 24rpx;
            font-size: 28rpx;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #333333;
            line-height: 28rpx;
            padding: 10rpx 20rpx;
            box-sizing: border-box;
            border-radius: 24rpx;
        }
        // .pay {
        //     color: #f5001d;
        //     border-color: #f5001d;
        // }
    }
    .order-item-active {
        background-color: #fdcba5;
        padding: 15px 20px;
        border-radius: 12px;
        font-size: 24px;
        margin: 15px 0;
        display: flex;
        align-items: center;
        justify-content: space-between;
        &-btn {
            line-height: 35rpx;
            text-align: center;
            background: #ff0940;
            border-radius: 15px;
            color: #fff;
            font-size: 20px;
            padding: 5px 15px;
            margin: 0;
            box-sizing: border-box;
        }
    }
    .good-name {
        .good-name-main {
            font-size: 26px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #333333;
            line-height: 38px;
            overflow: hidden;
            text-overflow: ellipsis;
            -webkit-line-clamp: 1;
            max-height: 38px;
            white-space: nowrap;
        }
        .good-type {
            margin-top: 10px;
        }
    }
}

.error-message {
    padding: 12rpx 24rpx;
    background-color: #fffbe8;
    border-radius: 8rpx;
    color: #f3a367;
}
