import { DDYNavigateTo } from "@/utils/route";
import { View, Image, Text } from "@tarojs/components";
import { ReactElement, JSXElementConstructor, ReactNode, ReactPortal } from "react";

interface GoodClassItem {
    flagList?: any[];
    promotionList?: any[];
    crossedPrice: any;
    price: number;
    title: ReactNode;
    mainPic: any;
    id: string;
    sellOutStatus: number;
}

export default (item: GoodClassItem) => {
    return (
        <View
            className="cu-list menu-avatar"
            key={item.id}
            onClick={() => {
                DDYNavigateTo({ url: "/pages/goods_detail?id=" + item.id });
            }}
        >
            <View className="cu-item-left">
                <Image
                    className="cu-avatar  lg"
                    style={{ borderRadius: "8rpx" }}
                    mode="aspectFill"
                    src={item.mainPic}
                />
                {/* <View className="cu-avatar  lg" style={{ backgroundImage: `url(${item.mainPic})` }}></View> */}
                {item.sellOutStatus == 2 && (
                    <View className="sell-out">
                        <Image
                            mode="aspectFit"
                            style={{ width: "100%", height: "100%" }}
                            src="https://dante-img.oss-cn-hangzhou.aliyuncs.com/71940503337.png"
                        />
                    </View>
                )}
                <View className="contentBox">
                    <View className="text-name" style={{ color: item.sellOutStatus == 2 ? "#999999" : "" }}>
                        {item.flagList?.map((flagItem, flagIndex) => {
                            return (
                                <Text className="label-box" key={flagIndex}>
                                    {flagItem == "promotionActivityItem" && <Text className="label-item">活动</Text>}
                                    {flagItem == "promotionLimitedItem" && (
                                        <Text className="label-item-limit">限定</Text>
                                    )}
                                </Text>
                            );
                        })}
                        {item.title}
                    </View>
                    <View className="text-gray text-sm flex">
                        <View className="text-cut" style={{ color: item.sellOutStatus == 2 ? "#999999" : "" }}>
                            <Text style={{ fontSize: "24rpx" }}> ¥ </Text>
                            {item.price / 100}
                            {!!item.crossedPrice && <Text className="original-price">¥{item.crossedPrice / 100}</Text>}
                        </View>
                        {item.promotionList?.map((couponItem, index) => {
                            return (
                                <View className="coupon-box" key={index}>
                                    {couponItem.couponType == "CASH_REBATE" && (
                                        <View className="cash-rebate">
                                            满{couponItem.conditionFee / 100}减{couponItem.reduceFee / 100}
                                        </View>
                                    )}
                                    {couponItem.couponType == "LADDER" && <View className="cash-rebate">满减</View>}
                                </View>
                            );
                        })}
                    </View>
                </View>
            </View>
        </View>
    );
};
