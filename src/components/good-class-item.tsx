import { DDYNavigateTo } from "@/utils/route";
import { View, Image, Text } from "@tarojs/components";
import { ReactElement, JSXElementConstructor, ReactNode, ReactPortal } from "react";
import DiscountPrice from "./discount-price";

interface GoodClassItem {
    maxDiscountPrice: number;
    whetherExistMultiSkus: boolean;
    discountPrice: number;
    flagList?: any[];
    promotionList?: any[];
    crossedPrice: any;
    price: number;
    title: ReactNode;
    mainPic: any;
    id: string;
    sellOutStatus: number;
}

export default (item: GoodClassItem) => {
    return (
        <View
            className="cu-list menu-avatar"
            key={item.id}
            onClick={() => {
                DDYNavigateTo({ url: "/pages/goods_detail?id=" + item.id });
            }}
        >
            <View className="cu-item-left">
                <Image
                    className="cu-avatar  lg"
                    style={{ borderRadius: "8rpx" }}
                    mode="aspectFill"
                    src={item.mainPic}
                />
                {/* <View className="cu-avatar  lg" style={{ backgroundImage: `url(${item.mainPic})` }}></View> */}
                {item.sellOutStatus == 2 && (
                    <View className="sell-out">
                        <Image
                            mode="aspectFit"
                            style={{ width: "100%", height: "100%" }}
                            src="https://dante-img.oss-cn-hangzhou.aliyuncs.com/71940503337.png"
                        />
                    </View>
                )}
                <View className="contentBox">
                    <View className="text-name" style={{ color: item.sellOutStatus == 2 ? "#999999" : "" }}>
                        {item.flagList?.map((flagItem, flagIndex) => {
                            return (
                                <Text className="label-box" key={flagIndex}>
                                    {flagItem == "promotionActivityItem" && <Text className="label-item">活动</Text>}
                                    {flagItem == "promotionLimitedItem" && (
                                        <Text className="label-item-limit">限定</Text>
                                    )}
                                </Text>
                            );
                        })}
                        {item.title}
                    </View>
                    <View className="original-price-box">
                        {item.isBonded === 0 ? (
                            <Image
                                mode="aspectFit"
                                style={{ width: "36.5px", height: "13pt" }}
                                src="https://dante-img.oss-cn-hangzhou.aliyuncs.com/19198843262.png"
                            />
                        ) : null}
                        {[1, 2].includes(item.isBonded) ? (
                            <Image
                                mode="aspectFit"
                                style={{ width: "36.5px", height: "13px" }}
                                src="https://dante-img.oss-cn-hangzhou.aliyuncs.com/19194973267.png"
                            />
                        ) : null}
                    </View>
                    <View className="text-gray text-sm flex">
                        <View className="text-cut" style={{ color: item.sellOutStatus == 2 ? "#999999" : "" }}>
                            <Text className="price">
                                <Text className="rmb">¥</Text>
                                <Text>
                                    {item.discountPrice !== item.price / 100 ? item.discountPrice : item.price / 100}
                                </Text>
                                {item.maxDiscountPrice > item.discountPrice ? <Text className="rmb">起</Text> : null}
                            </Text>
                            {/* {item.discountPrice !== item.price / 100 ? (
                                // <DiscountPrice
                                //     price={item.discountPrice}
                                //     whetherExistMultiSkus={item.whetherExistMultiSkus}
                                // />
                                <Text className="discount-price-text">折后价</Text>
                            ) : null} */}
                        </View>
                        {item.promotionList?.map((couponItem, index) => {
                            return (
                                <View className="coupon-box" key={index}>
                                    {couponItem.couponType == "CASH_REBATE" && (
                                        <View className="cash-rebate">
                                            满{couponItem.conditionFee / 100}减{couponItem.reduceFee / 100}
                                        </View>
                                    )}
                                    {couponItem.couponType == "LADDER" && <View className="cash-rebate">满减</View>}
                                </View>
                            );
                        })}
                    </View>
                </View>
            </View>
        </View>
    );
};
