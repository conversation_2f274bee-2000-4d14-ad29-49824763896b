import React, { useCallback, useState } from "react";
import { Picker, View } from "@tarojs/components";
import {
    PickerDateProps,
    PickerMultiSelectorProps,
    PickerRegionProps,
    PickerSelectorProps,
    PickerTimeProps,
} from "@tarojs/components/types/Picker";
import AreaPicker, { AreaPickerData } from "@/components/cascader/AreaPicker";
import StreetPicker, { StreetPickerData } from "@/components/cascader/StreetPicker";

type PickerOnChange = Partial<
    Pick<
        PickerMultiSelectorProps | PickerTimeProps | PickerDateProps | PickerRegionProps | PickerSelectorProps,
        "onChange"
    >
>;

type FormPickerProps = Omit<
    PickerMultiSelectorProps | PickerTimeProps | PickerDateProps | PickerRegionProps | PickerSelectorProps,
    "onChange"
> & {
    /**
     * 格式化选择后展示值
     * @param value 选择的数据
     */
    formatValueFn?: (value) => { formatValue: string; toSetValue: string };
    /**
     * 是否显示街道选择
     * 仅在 mode="region" 时有效
     */
    showStreet?: boolean;
    defaultVisible?: boolean;
} & PickerOnChange;

export const FormPicker: React.FC<FormPickerProps> = props => {
    const innerOnChange = useCallback(e => {
        if (props.onChange) {
            props.onChange(e);
        }
    }, []);
    const { value, mode, formatValueFn, showStreet, defaultVisible } = props;

    const [visible, setVisible] = useState(defaultVisible);

    const defaultFormatValue = useCallback(pickValue => {
        let buildValue = Object.create({});
        switch (mode) {
            case "selector":
                // 需要外部实现
                buildValue = formatValueFn && formatValueFn(pickValue);
                break;
            case "multiSelector":
                // 需要外部实现
                buildValue = formatValueFn && formatValueFn(pickValue);
                break;
            case "time":
                buildValue.formatValue = value;
                buildValue.toSetValue = value;
                break;
            case "date":
                buildValue.formatValue = value;
                buildValue.toSetValue = value;
                break;
            case "region":
                let defaultValue = showStreet ? "省-市-区-街道" : "省-市-区";
                buildValue.formatValue = pickValue?.value?.join("-") || defaultValue;
                buildValue.toSetValue = pickValue?.value;
                break;
        }
        return buildValue;
    }, []);
    const innerProps = Object.assign(props);

    function areaPickChange(pickData: AreaPickerData) {
        const code: number[] = [];
        const value: string[] = [];
        if (pickData.province?.areaCode) {
            code.push(pickData.province.areaCode);
            value.push(pickData.province.areaName);
        }
        if (pickData.city?.areaCode) {
            code.push(pickData.city.areaCode);
            value.push(pickData.city.areaName);
        }
        if (pickData.area?.areaCode) {
            code.push(pickData.area.areaCode);
            value.push(pickData.area.areaName);
        }
        const detail = {
            code: code,
            value: value,
        };
        innerOnChange({ detail });
    }

    function streetPickChange(pickData: StreetPickerData) {
        const code: number[] = [];
        const value: string[] = [];

        const [province, city, area, street] = pickData.selectedOptions || [];

        // 处理省级数据
        if (province) {
            code.push(province.value);
            value.push(province.text);
        }

        // 处理市级数据
        if (city) {
            code.push(city.value);
            value.push(city.text);
        }

        // 处理区县级数据
        if (area) {
            code.push(area.value);
            value.push(area.text);
        }

        // 处理街道级数据
        if (street) {
            code.push(street.value);
            value.push(street.text);
        }

        const detail = {
            code: code,
            value: value,
        };

        innerOnChange({ detail });
    }

    return (
        <>
            {mode === "region" ? (
                <View className="picker" style={{ width: "100%" }}>
                    {showStreet ? (
                        <StreetPicker
                            onSelect={(pickData: StreetPickerData) => {
                                streetPickChange(pickData);
                            }}
                            value={value || {}}
                            visible={visible}
                            onVisibleChange={val => setVisible(val)}
                        >
                            {value ? (
                                defaultFormatValue(value).formatValue
                            ) : (
                                <View className="input-placeholder"> 请选择</View>
                            )}
                        </StreetPicker>
                    ) : (
                        <AreaPicker
                            onAreaPick={(pickData: AreaPickerData) => {
                                areaPickChange(pickData);
                            }}
                        >
                            {value ? (
                                defaultFormatValue(value).formatValue
                            ) : (
                                <View className="input-placeholder"> 请选择</View>
                            )}
                        </AreaPicker>
                    )}
                </View>
            ) : (
                <Picker
                    {...innerProps}
                    onChange={innerOnChange}
                    mode={mode}
                    style={{ width: "100%" }}
                    value={defaultFormatValue(value).toSetValue}
                >
                    <View className="picker">
                        {value ? (
                            defaultFormatValue(value).formatValue
                        ) : (
                            <View className="input-placeholder"> 请选择</View>
                        )}
                    </View>
                </Picker>
            )}
        </>
    );
};
