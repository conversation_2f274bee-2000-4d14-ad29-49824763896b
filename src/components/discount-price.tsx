import { View, Text } from "@tarojs/components";
import "./discount-price.less";

export default ({ price, whetherExistMultiSkus }) => {
    return (
        <View className="discount-price-1">
            <Text className="discount-title">折后</Text>
            <Text className="discount-icon">¥</Text>
            <Text className="discount-content">{price}</Text>
            {whetherExistMultiSkus ? <Text className="discount-qi">起</Text> : null}
        </View>
    );
};
