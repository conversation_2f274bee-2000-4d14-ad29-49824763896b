import React, { useState, useEffect, useRef } from "react";
import { View } from "@tarojs/components";
import { Cascader } from "./CustomCascader/index";
import api from "@/utils/api";
import "./index.less";
import Taro from "@tarojs/taro";

interface RegionItem {
    text: string;
    value: string;
    children?: RegionItem[];
    leaf?: boolean;
}

interface StreetPickerProps {
    onSelect?: (data: StreetPickerData) => void;
    children?: React.ReactNode;
    visible?: boolean; // 控制弹窗显示状态
    onVisibleChange?: (visible: boolean) => void; // 弹窗显示状态变化回调
    value?: any;
}

export interface StreetPickerData {
    province: RegionItem;
    city: RegionItem;
    area: RegionItem;
    street: RegionItem;
}

const StreetPicker: React.FC<StreetPickerProps> = props => {
    const { visible, onVisibleChange, value } = props;

    const [show, setShow] = useState(props.visible || false); // 控制弹窗显示
    const [provinces, setProvinces] = useState<RegionItem[]>([]); // 省份数据
    const [selectedValue, setSelectedValue] = useState<string[]>([]);
    const [selectedText, setSelectedText] = useState("");
    const cascaderRef = useRef<any>(null);
    const prevTabIndex = useRef<number>(-1);

    const [options, setOptions] = useState<RegionItem[]>([]);

    // 监听外部visible变化
    useEffect(() => {
        if (visible !== undefined) {
            setShow(visible);
        }
    }, [visible]);

    // 内部状态变化时通知外部
    useEffect(() => {
        if (onVisibleChange && visible !== show) {
            onVisibleChange(show);
        }
    }, [show]);

    useEffect(() => {
        // 设置初始值
        if (value?.code?.length > 0) {
            setSelectedValue(value.code.map(item => item.toString()));
            // 懒加载回显
            initOptions();
        }
    }, [value]);

    const initOptions = async () => {
        const options = await fetchOptionsByValue(value.code);
        setOptions(options); // 这里假设 provinces 作为 options 传给 Cascader
    };

    // 加载省份数据
    const loadProvinces = async () => {
        try {
            const provinces = await api.areaListChildren({ data: { parentId: -1, isFilter: true } });
            const formattedProvinces = provinces.map(province => ({
                text: province.areaName,
                value: province.id.toString(),
                children: [],
                leaf: false, // 省级不是叶子节点
            }));
            return formattedProvinces;
        } catch (error) {
            console.error("加载省份数据失败", error);
            return [];
        }
    };

    // 懒加载子节点数据
    const lazyLoad = async (node, resolve) => {
        // node.children = [];
        if (node.root) {
            // 如果是根节点，返回省份数据
            try {
                if (provinces.length === 0) {
                    let data = await loadProvinces();
                    setProvinces(data);
                    resolve(data);
                } else {
                    resolve(provinces);
                }
            } catch (error) {
                console.error("加载省份数据失败", error);
                resolve([]);
            }
        } else {
            // 如果不是根节点，根据当前节点的值和级别加载子节点
            const { value, level } = node;
            try {
                let children: any[] = [];
                try {
                    if (level == 2) {
                        //  当是区时
                        children = await api.streetListByAreaCode({
                            data: { parentId: parseInt(value) },
                        });
                    } else {
                        children = await api.areaListChildren({
                            data: { parentId: parseInt(value), isFilter: true },
                        });
                    }
                } catch (error) {
                    console.error("加载子节点数据失败", error);
                }

                const formattedChildren = children.map(child => ({
                    text: child.areaName,
                    value: child.id.toString(),
                    leaf: level === 2, // 只有区县级(level=2)的子节点(街道级)是叶子节点
                }));
                resolve(formattedChildren);
            } catch (error) {
                console.error(`加载${level === 0 ? "城市" : level === 1 ? "区县" : "街道"}数据失败`, error);
                resolve([]);
            }
        }
    };

    // 处理选择变化
    const onChange = (value: string[], selectedOptions: RegionItem[]) => {
        console.log("onChange", value, selectedOptions);

        setSelectedValue(value);

        // 更新选中的文本
        const text = selectedOptions.map(option => option.text).join(",");
        setSelectedText(text);

        // 记录当前选项卡索引，用于检测级别切换streetPickChange
        if (cascaderRef.current) {
            prevTabIndex.current = cascaderRef.current.state.tabIndex;
        }

        // 如果选择完成（选到第四级或者当前级别是叶子节点），则调用回调
        const level = selectedOptions.length - 1;
        const currentOption = selectedOptions[level];

        if (level === 3 || currentOption.leaf) {
            const result: StreetPickerData = {
                selectedOptions,
                value,
                text,
            };
            props.onSelect && props.onSelect(result);
        }
    };

    // 递归加载每一级数据，返回完整的选项链
    const fetchOptionsByValue = async (codes: string[]) => {
        console.log("加载数据");
        let newOptions: RegionItem[] = options;
        let parentId = -1;
        let level = 0;
        let currentOptions: any;
        for (const code of codes) {
            let children = [];
            if (level === 3) {
                // 街道
                children = await api.streetListByAreaCode({ data: { parentId: parentId } });
            } else {
                children = await api.areaListChildren({ data: { parentId: parentId, isFilter: true } });
            }
            const formatted = children.map(child => ({
                text: child.areaName,
                value: child.id.toString(),
                leaf: level === 3,
            }));

            if (level == 0) {
                newOptions = formatted;
                currentOptions = newOptions;
            } else {
                for (let i = 0; i < currentOptions.length; i++) {
                    if (currentOptions[i].value == codes[level - 1]) {
                        currentOptions[i].children = formatted;
                        currentOptions = currentOptions[i].children;
                        break;
                    }
                }
            }
            parentId = parseInt(code);

            level++;
        }

        // 如果只有三级，自动加载第一个街道
        if (codes.length === 3 && currentOptions && currentOptions.length > 0) {
            // 加载街道
            let children = await api.streetListByAreaCode({ data: { parentId: parentId } });
            const formatted = children.map(child => ({
                text: child.areaName,
                value: child.id.toString(),
                leaf: true,
            }));

            if (formatted.length > 0) {
                let firstStreet = formatted[0];
                for (let i = 0; i < currentOptions.length; i++) {
                    if (currentOptions[i].value == firstStreet.value) {
                        currentOptions[i].children = formatted;
                        break;
                    }
                }
                let selectedOptions =
                    value?.code.map((item, index) => ({ value: item + "", text: value["value"][index] })) || [];
                selectedOptions.push({
                    value: firstStreet.value,
                    text: firstStreet.text,
                });
                onChange([...codes.map(item => item + ""), formatted[0].value], selectedOptions);
            }
        }

        return newOptions;
    };

    return (
        <View className="street-picker">
            <View
                onClick={() => {
                    if (props.visible === undefined) {
                        // 如果没有传入visible，则由内部状态控制
                        setShow(true);
                    } else {
                        // 如果传入了visible，则通知外部需要改变状态
                        onVisibleChange && onVisibleChange(true);
                    }
                }}
            >
                {props.children || (
                    <View className="picker">
                        {selectedText ? selectedText : <View className="input-placeholder">请选择所在地区</View>}
                    </View>
                )}
            </View>
            <Cascader
                scrollIntoView={true}
                options={options}
                ref={cascaderRef}
                childrenKey="children"
                visible={show}
                value={selectedValue}
                textKey="text"
                valueKey="value"
                title="选择地区"
                lazy
                lazyLoad={lazyLoad}
                closeable
                onClose={() => {
                    if (props.visible === undefined) {
                        // 如果没有传入visible，则由内部状态控制
                        setShow(false);
                    } else {
                        // 如果传入了visible，则通知外部需要改变状态
                        onVisibleChange && onVisibleChange(false);
                    }
                }}
                optionsNum={4}
                onChange={onChange}
                placeHolderArr={["省份/地区", "城市", "区/县", "街道/镇"]}
            />
        </View>
    );
};

export default StreetPicker;
