/* stylelint-disable selector-type-no-unknown */
// Color Palette
@black: #1a1a1a;
@white: #fff;
@gray-1: #f7f8fa;
@gray-2: #f2f3f5;
@gray-3: #ebedf0;
@gray-4: #dcdee0;
@gray-5: #c8c9cc;
@gray-6: #969799;
@gray-7: #646566;
@gray-8: #323233;
@red: #ee0a24;
@blue: #1989fa;
@orange: #ff976a;
@orange-dark: #ed6a0c;
@orange-light: #fffbe8;
@green: #07c160;
@page-back: @white;
@primary-color: @green;
@placeholder-color: #babcc0;

/* stylelint-disable-next-line selector-max-type */
:root,
page {
    --primary-color: @primary-color;
}

// z-index
@sticky-z-index: 800;
@tabbar-z-index: 805;
@navbar-z-index: 805;
@goods-action-z-index: 806;
@submit-bar-z-index: 806;
@overlay-z-index: 1000;
@dropdown-z-index: 1000;
@popup-z-index: 1010;
@popup-close-icon-z-index: 1010;
@notify-z-index: 1500;
@water-mark-z-index: 2000;

// Gradient Colors
@gradient-red: linear-gradient(to right, #ff6034, #ee0a24);
@gradient-orange: linear-gradient(to right, #ffd01e, #ff8917);

// Component Colors
@text-color: @gray-8;
@active-color: @gray-2;
@active-opacity: 0.85;
@disabled-opacity: 0.5;
@background-color: @gray-1;
@background-color-light: #fafafa;
@text-link-color: #576b95;

// Padding
@padding-base: 8px;
@padding-xs: @padding-base * 2;
@padding-sm: @padding-base * 3;
@padding-md: @padding-base * 4;
@padding-lg: @padding-base * 6;
@padding-xl: @padding-base * 8;

// Font
@font-size-xs: 20px;
@font-size-sm: 24px;
@font-size-md: 28px;
@font-size-lg: 32px;
@font-weight-bold: 500;
@line-height-xs: 28px;
@line-height-sm: 36px;
@line-height-md: 40px;
@line-height-lg: 44px;
@base-font-family: -apple-system, BlinkMacSystemFont, "Helvetica Neue", Helvetica, Segoe UI, Arial, Roboto,
    "PingFang SC", "miui", "Hiragino Sans GB", "Microsoft Yahei", sans-serif;
@price-integer-font-family: Avenir-Heavy, PingFang SC, Helvetica Neue, Arial, sans-serif;

// Animation
@animation-duration-base: 0.3s;
@animation-duration-fast: 0.2s;

// Border
@border-color: @gray-3;
@border-width-base: 2px;
@border-radius-sm: 4px;
@border-radius-md: 8px;
@border-radius-lg: 16px;
@border-radius-max: 999px;

//ActionSheet
@action-sheet-max-height: 90%;
@action-sheet-header-height: 96px;
@action-sheet-header-font-size: @font-size-lg;
@action-sheet-description-color: @gray-6;
@action-sheet-description-font-size: @font-size-md;
@action-sheet-description-line-height: 40px;
@action-sheet-item-background: @white;
@action-sheet-item-font-size: @font-size-lg;
@action-sheet-item-line-height: 44px;
@action-sheet-item-text-color: @text-color;
@action-sheet-item-disabled-text-color: @gray-5;
@action-sheet-subname-color: @gray-6;
@action-sheet-subname-font-size: @font-size-sm;
@action-sheet-subname-line-height: 40px;
@action-sheet-close-icon-size: 44px;
@action-sheet-close-icon-color: @gray-5;
@action-sheet-close-icon-padding: 0 @padding-md;
@action-sheet-cancel-text-color: @gray-7;
@action-sheet-cancel-padding-top: @padding-xs;
@action-sheet-cancel-padding-color: @background-color;

// Button
@button-line-height: 40px;

@button-normal-height: 80px;
@button-normal-font-size: @font-size-md;
@button-normal-border-radius: @border-radius-lg;

@button-mini-height: 44px;
@button-mini-font-size: @font-size-xs;
@button-mini-border-radius: @border-radius-sm;
@button-mini-min-width: 100px;

@button-small-height: 60px;
@button-small-font-size: @font-size-sm;
@button-small-border-radius: @border-radius-md;
@button-small-min-width: 120px;

@button-large-height: 100px;
@button-large-font-size: @font-size-lg;
@button-large-border-radius: @border-radius-lg;

@button-default-color: @text-color;
@button-default-background-color: @white;

@button-primary-color: @white;
@button-primary-background-color: var(--primary-color);

@button-info-color: @white;
@button-info-background-color: @blue;

@button-danger-color: @white;
@button-danger-background-color: @red;

@button-warning-color: @white;
@button-warning-background-color: @orange;

@button-border-width: 2px;
@button-round-border-radius: @border-radius-max;
@button-plain-background-color: @white;
@button-active-opacity: @active-opacity;
@button-disabled-opacity: @disabled-opacity;

// Calendar
@calendar-height: 100%;
@calendar-background-color: @white;
@calendar-popup-height: 80%;
@calendar-header-box-shadow: 0 4px 20px rgba(125, 126, 128, 0.16);
@calendar-header-title-height: 88px;
@calendar-header-title-font-size: @font-size-lg;
@calendar-header-subtitle-font-size: @font-size-md;
@calendar-weekdays-height: 60px;
@calendar-weekdays-font-size: @font-size-sm;
@calendar-month-title-font-size: @font-size-md;
@calendar-month-mark-color: fade(@gray-2, 80%);
@calendar-month-mark-font-size: 320px;
@calendar-day-height: 128px;
@calendar-day-font-size: @font-size-lg;
@calendar-range-edge-color: @white;
@calendar-range-edge-background-color: var(--primary-color);
@calendar-range-middle-color: var(--primary-color);
@calendar-range-middle-background-opacity: 0.1;
@calendar-selected-day-size: 108px;
@calendar-selected-day-color: @white;
@calendar-info-font-size: @font-size-xs;
@calendar-info-line-height: 28px;
@calendar-selected-day-background-color: var(--primary-color);
@calendar-day-disabled-color: @gray-5;
@calendar-confirm-button-height: 72px;
@calendar-confirm-button-margin: 14px 0;
@calendar-confirm-button-line-height: 68px;

// Card
@card-padding: @padding-xs @padding-md;
@card-font-size: @font-size-sm;
@card-text-color: @text-color;
@card-background-color: @background-color-light;
@card-thumb-size: 176px;
@card-title-line-height: 32px;
@card-desc-color: @gray-7;
@card-desc-line-height: 40px;
@card-price-color: @red;
@card-origin-price-color: @gray-7;
@card-origin-price-font-size: @font-size-xs;
@card-price-font-size: @font-size-sm;
@card-price-integer-font-size: @font-size-lg;
@card-price-font-family: @price-integer-font-family;

// Cell
@cell-font-size: @font-size-md;
@cell-line-height: 48px;
@cell-vertical-padding: @padding-sm;
@cell-horizontal-padding: @padding-md;
@cell-text-color: @text-color;
@cell-background-color: @white;
@cell-border-color: @border-color;
@cell-required-color: @red;
@cell-label-color: @gray-6;
@cell-label-font-size: @font-size-sm;
@cell-label-margin-top: 6px;
@cell-value-color: @gray-6;
@cell-icon-size: @font-size-lg;
@cell-right-icon-color: @gray-6;
@cell-large-vertical-padding: @padding-sm;
@cell-large-title-font-size: @font-size-lg;
@cell-large-value-font-size: @font-size-lg;
@cell-large-label-font-size: @font-size-md;

// Cell
@cell-group-background-color: @white;
@cell-group-title-color: @gray-6;
@cell-group-title-padding: @padding-md @padding-md @padding-xs;
@cell-group-title-font-size: @font-size-md;
@cell-group-title-line-height: 32px;
@cell-group-inset-padding: 0 @padding-md;
@cell-group-inset-border-radius: @border-radius-lg;
@cell-group-inset-title-padding: @padding-md @padding-md @padding-xs @padding-xl;

// Checkbox
@checkbox-size: 40px;
@checkbox-border-color: @gray-5;
@checkbox-transition-duration: 0.2s;
@checkbox-label-margin: 20px;
@checkbox-label-color: @text-color;
@checkbox-checked-icon-color: var(--primary-color);
@checkbox-disabled-icon-color: @gray-5;
@checkbox-disabled-label-color: @gray-5;
@checkbox-disabled-background-color: @border-color;

// Circle
@circle-text-color: @text-color;
@circle-font-color: 28px;

// Collapse
@collapse-item-transition-duration: 0.3s;
@collapse-item-content-padding: 30px;
@collapse-item-content-font-size: 26px;
@collapse-item-content-line-height: 1.5;
@collapse-item-content-text-color: @gray-6;
@collapse-item-content-background-color: @white;
@collapse-item-title-disabled-color: @gray-5;

// CountDown
@count-down-text-color: @text-color;
@count-down-font-size: @font-size-md;
@count-down-line-height: 40px;

// CountUp
@count-up-text-color: @text-color;
@count-up-font-size: @font-size-md;
@count-up-line-height: 40px;

// Dialog
@dialog-width: 640px;
@dialog-small-screen-width: 90%;
@dialog-font-size: @font-size-lg;
@dialog-border-radius: 32px;
@dialog-background-color: @white;
@dialog-header-font-weight: @font-weight-bold;
@dialog-header-line-height: 48px;
@dialog-header-padding-top: @padding-lg;
@dialog-header-isolated-padding: @padding-lg 0;
@dialog-message-padding: @padding-lg;
@dialog-message-font-size: @font-size-md;
@dialog-message-line-height: 40px;
@dialog-message-max-height: 60vh;
@dialog-has-title-message-text-color: @gray-7;
@dialog-has-title-message-padding-top: @padding-xs;
@dialog-cancel-button-color: @gray-7;
@dialog-confirm-button-color: @text-link-color;

// Form
@form-background-color: @white;
@form-space-horizontal: @padding-sm;
@form-space-vertical: @padding-md;
@form-border-bottom: 2px solid @gray-3;
@form-line-height: 48px;
@form-label-color: @text-color;
@form-label-width: 146px;
@form-label-font-size: @font-size-md;
@form-controll-margin-left: 40px;
@form-controll-color: @text-color;
@form-controll-font-size: @font-size-md;
@form-message-font-size: @font-size-sm;
@form-message-color: red;
@form-message-margin-top: 8px;
@form-vertical-controll-margin-top: 20px;

// Field
@field-label-color: @gray-7;
@field-input-text-color: @text-color;
@field-input-error-text-color: @red;
@field-input-disabled-text-color: @gray-5;
@field-placeholder-text-color: @gray-5;
@field-icon-size: 32px;
@field-clear-icon-size: 32px;
@field-clear-icon-color: @gray-5;
@field-icon-container-color: @gray-6;
@field-error-message-color: @red;
@field-error-message-text-font-size: @font-size-sm;
@field-text-area-min-height: 36px;
@field-word-limit-color: @gray-7;
@field-word-limit-font-size: @font-size-sm;
@field-word-limit-line-height: 32px;
@field-word-num-full-color: @red;
@field-disabled-text-color: @gray-5;
@field-text-area-min-height: 148px;
@field-font-size: @font-size-md;

// GoodsAction
@goods-action-background-color: @white;
@goods-action-height: 100px;
@goods-action-icon-width: 100px;
@goods-action-icon-height: @goods-action-height;
@goods-action-icon-color: @text-color;
@goods-action-icon-size: 36px;
@goods-action-icon-font-size: @font-size-xs;
@goods-action-icon-text-color: @gray-7;
@goods-action-button-height: 80px;
@goods-action-button-line-height: @button-line-height;
@goods-action-button-border-radius: @border-radius-max;
@goods-action-button-warning-color: @gradient-orange;
@goods-action-button-danger-color: @gradient-red;
@goods-action-button-plain-color: @white;

// Image
@image-placeholder-text-color: @gray-6;
@image-placeholder-font-size: @font-size-md;
@image-placeholder-background-color: @background-color;
@image-loading-icon-size: 64px;
@image-loading-icon-color: @gray-4;
@image-error-icon-size: 64px;
@image-error-icon-color: @gray-4;

// Info
@info-size: 32px;
@info-color: @white;
@info-padding: 0 6px;
@info-font-size: 24px;
@info-font-weight: 500;
@info-border-width: 2px;
@info-background-color: @red;
@info-dot-color: @red;
@info-dot-size: 16px;
@info-font-family: -apple-system-font, Helvetica Neue, Arial, sans-serif;

// Loading
@loading-text-color: @gray-6;
@loading-text-font-size: @font-size-md;
@loading-text-line-height: 40px;
@loading-spinner-color: @gray-5;
@loading-spinner-size: 60px;
@loading-spinner-animation-duration: 0.8s;

// NavBar
@nav-bar-height: 92px; // 对MiniNavbar无效
@nav-bar-background-color: @white;
@nav-bar-arrow-size: 32px; // 对MiniNavbar无效
@nav-bar-icon-color: var(--primary-color);
@nav-bar-text-color: var(--primary-color);
@nav-bar-title-font-size: @font-size-lg;
@nav-bar-title-text-color: @text-color;

// NoticeBar
@notice-bar-height: 80px;
@notice-bar-padding: 0 @padding-md;
@notice-bar-wrapable-padding: @padding-xs @padding-md;
@notice-bar-font-size: @font-size-md;
@notice-bar-text-color: @orange-dark;
@notice-bar-line-height: 48px;
@notice-bar-background-color: @orange-light;
@notice-bar-icon-size: 32px;
@notice-bar-icon-min-width: 44px;

// Notify
@notify-padding: 12px 30px;
@notify-font-size: 28px;
@notify-line-height: 40px;
@notify-primary-background-color: @blue;
@notify-success-background-color: @green;
@notify-danger-background-color: @red;
@notify-warning-background-color: @orange;

// Overlay
@overlay-background-color: rgba(32, 32, 32, 0.7);

// Panel
@panel-background-color: @white;
@panel-header-value-color: @red;
@panel-footer-padding: @padding-xs @padding-md;

// Picker
@picker-background-color: @white;
@picker-toolbar-height: 88px;
@picker-title-font-size: @font-size-lg;
@picker-action-padding: 0 @padding-md;
@picker-action-font-size: @font-size-md;
@picker-confirm-action-color: @text-link-color;
@picker-cancel-action-color: @gray-6;
@picker-option-font-size: @font-size-lg;
@picker-option-text-color: @black;
@picker-loading-icon-color: var(--primary-color);
@picker-loading-mask-color: rgba(255, 255, 255, 0.9);
@picker-option-disabled-opacity: 0.3;
@picker-option-selected-text-color: @text-color;

// Popup
@popup-background-color: @page-back;
@popup-round-border-radius: 32px;
@popup-close-icon-size: 36px;
@popup-close-icon-color: @gray-6;
@popup-close-icon-margin: 16px;

// Progress
@progress-height: 8px;
@progress-background-color: @gray-3;
@progress-pivot-padding: 0 10px;
@progress-color: var(--primary-color);
@progress-pivot-font-size: @font-size-xs;
@progress-pivot-line-height: 1.6;
@progress-pivot-background-color: var(--primary-color);
@progress-pivot-text-color: @white;

// Radio
@radio-size: 40px;
@radio-border-color: @gray-5;
@radio-transition-duration: 0.2s;
@radio-label-margin: 20px;
@radio-label-color: @text-color;
@radio-checked-icon-color: var(--primary-color);
@radio-disabled-icon-color: @gray-5;
@radio-disabled-label-color: @gray-5;
@radio-disabled-background-color: @border-color;

// Rate
@rate-horizontal-padding: 4px;
@rate-icon-size: 40px;
@rate-icon-void-color: @gray-5;
@rate-icon-full-color: @red;
@rate-icon-disabled-color: @gray-5;
@rate-icon-gutter: @padding-base;

// Switch
@switch-width: 124px;
@switch-height: 64px;
@switch-node-size: 64px;
@switch-node-z-index: 1;
@switch-node-background-color: @white;
@switch-node-box-shadow: 0 6px 2px 0 rgba(0, 0, 0, 0.05), 0 4px 4px 0 rgba(0, 0, 0, 0.1),
    0 6px 6px 0 rgba(0, 0, 0, 0.05);
@switch-background-color: @white;
@switch-on-background-color: var(--primary-color);
@switch-transition-duration: 0.3s;
@switch-disabled-opacity: 0.4;
@switch-border: 2px solid rgba(0, 0, 0, 0.1);

// ShareSheet
@share-sheet-header-padding: @padding-sm @padding-md @padding-base;
@share-sheet-title-color: @text-color;
@share-sheet-title-font-size: @font-size-md;
@share-sheet-title-line-height: @line-height-md;
@share-sheet-description-color: @gray-6;
@share-sheet-description-font-size: @font-size-sm;
@share-sheet-description-line-height: 32px;
@share-sheet-icon-size: 96px;
@share-sheet-option-name-color: @gray-7;
@share-sheet-option-name-font-size: @font-size-sm;
@share-sheet-option-description-color: @gray-5;
@share-sheet-option-description-font-size: @font-size-sm;
@share-sheet-cancel-button-font-size: @font-size-lg;
@share-sheet-cancel-button-height: 96px;
@share-sheet-cancel-button-background: @white;

// Search
@search-background-color: @gray-1;
@search-padding: 20px @padding-sm;
@search-input-height: 68px;
@search-label-padding: 0 10px;
@search-label-color: @text-color;
@search-label-font-size: @font-size-md;
@search-left-icon-color: @gray-6;
@search-action-padding: 0 @padding-xs;
@search-action-text-color: @text-color;
@search-action-font-size: @font-size-md;

// Sidebar
@sidebar-width: 160px;

// SidebarItem
@sidebar-font-size: @font-size-md;
@sidebar-line-height: 40px;
@sidebar-text-color: @text-color;
@sidebar-disabled-text-color: @gray-5;
@sidebar-padding: 40px @padding-sm 40px @padding-xs;
@sidebar-active-color: @active-color;
@sidebar-background-color: @background-color;
@sidebar-selected-font-weight: @font-weight-bold;
@sidebar-selected-text-color: @text-color;
@sidebar-selected-border-color: var(--primary-color);
@sidebar-selected-background-color: @white;

// Slider
@slider-active-background-color: var(--primary-color);
@slider-inactive-background-color: @gray-3;
@slider-disabled-opacity: @disabled-opacity;
@slider-bar-height: 4px;
@slider-button-width: 48px;
@slider-button-height: 48px;
@slider-button-border-radius: 50%;
@slider-button-background-color: @white;
@slider-button-box-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);

// Step
@step-text-color: @gray-6;
@step-process-text-color: @text-color;
@step-font-size: @font-size-md;
@step-line-color: @border-color;
@step-finish-line-color: var(--primary-color);
@step-finish-text-color: @text-color;
@step-icon-size: 24px;
@step-circle-size: 10px;
@step-circle-color: @gray-6;
@step-horizontal-title-font-size: @font-size-sm;

// Steps
@steps-background-color: @white;

// Stepper
@stepper-active-color: #e8e8e8;
@stepper-background-color: @active-color;
@stepper-button-icon-color: @text-color;
@stepper-button-disabled-color: @background-color;
@stepper-button-disabled-icon-color: @gray-5;
@stepper-button-round-theme-color: var(--primary-color);
@stepper-input-width: 64px;
@stepper-input-height: 56px;
@stepper-input-font-size: @font-size-md;
@stepper-input-line-height: normal;
@stepper-input-text-color: @text-color;
@stepper-input-disabled-text-color: @gray-5;
@stepper-input-disabled-background-color: @active-color;
@stepper-border-radius: @border-radius-md;

// SubmitBar
@submit-bar-height: 100px;

@submit-bar-background-color: @white;
@submit-bar-button-width: 220px;
@submit-bar-price-color: @red;
@submit-bar-price-font-size: @font-size-sm;
@submit-bar-currency-font-size: @font-size-sm;
@submit-bar-text-color: @text-color;
@submit-bar-text-font-size: 28px;
@submit-bar-tip-padding: 20px;
@submit-bar-tip-font-size: 24px;
@submit-bar-tip-line-height: 1.5;
@submit-bar-tip-color: #f56723;
@submit-bar-tip-background-color: #fff7cc;
@submit-bar-tip-icon-size: 24px;
@submit-bar-button-height: 80px;
@submit-bar-padding: 0 @padding-md;
@submit-bar-price-integer-font-size: 40px;
@submit-bar-price-font-family: @price-integer-font-family;

// Tabbar
@tabbar-height: 100px;
@tabbar-background-color: @white;

// TabbarItem
@tabbar-item-font-size: @font-size-sm;
@tabbar-item-text-color: @gray-7;
@tabbar-item-active-color: var(--primary-color);
@tabbar-item-line-height: 1;
@tabbar-item-icon-size: 44px;
@tabbar-item-margin-bottom: 8px;

// Tab
@tab-text-color: @gray-7;
@tab-active-text-color: @text-color;
@tab-disabled-text-color: @gray-5;
@tab-font-size: @font-size-md;

// Tabs
@tabs-default-color: var(--primary-color);
@tabs-line-height: 88px;
@tabs-card-height: 60px;
@tabs-nav-background-color: @white;
@tabs-bottom-bar-height: 6px;
@tabs-bottom-bar-color: @tabs-default-color;
@tabs-bottom-bar-width: 50%;
@tabs-bottom-bar-duration: 0.3s;

// Tag
@tag-padding: 0 @padding-base;
@tag-text-color: @white;
@tag-font-size: @font-size-sm;
@tag-border-radius: 4px;
@tag-line-height: 32px;
@tag-medium-padding: 4px 12px;
@tag-large-padding: @padding-base @padding-xs;
@tag-large-border-radius: @border-radius-md;
@tag-large-font-size: @font-size-md;
@tag-round-border-radius: @border-radius-max;
@tag-danger-color: @red;
@tag-primary-color: @blue;
@tag-success-color: @green;
@tag-warning-color: @orange;
@tag-default-color: @gray-6;
@tag-plain-background-color: @white;

// Toast
@toast-max-width: 70%;
@toast-font-size: 28px;
@toast-text-color: @white;
@toast-line-height: 40px;
@toast-border-radius: @border-radius-lg;
@toast-background-color: fade(@black, 70%);
@toast-icon-size: 72px;
@toast-text-min-width: 192px;
@toast-text-padding: @padding-xs @padding-sm;
@toast-default-padding: @padding-md;
@toast-default-width: 176px;
@toast-default-min-height: 176px;

// GridItem
@grid-item-content-padding: @padding-md @padding-xs;
@grid-item-content-background-color: @white;
@grid-item-content-active-color: @active-color;
@grid-item-icon-size: 52px;
@grid-item-text-color: @gray-7;
@grid-item-text-font-size: @font-size-sm;

// Divider
@divider-margin: @padding-md 0;
@divider-text-color: @gray-6;
@divider-font-size: @font-size-md;
@divider-line-height: 48px;
@divider-border-color: @border-color;
@divider-content-padding: @padding-md;
@divider-content-left-width: 10%;
@divider-content-right-width: 10%;

// Empty
@empty-padding: @padding-xl 0;
@empty-image-size: 320px;
@empty-description-margin-top: @padding-md;
@empty-description-padding: 0 120px;
@empty-description-color: @gray-6;
@empty-description-font-size: 28px;
@empty-description-line-height: 40px;
@empty-bottom-margin-top: 48px;

// TreeSelect
@tree-select-font-size: @font-size-md;
@tree-select-nav-background-color: @background-color;
@tree-select-content-background-color: @white;
@tree-select-nav-item-padding: @padding-sm @padding-xs @padding-sm @padding-sm;
@tree-select-item-height: 88px;
@tree-select-item-active-color: var(--primary-color);
@tree-select-item-disabled-color: @gray-5;

// Uploader
@uploader-size: 160px;
@uploader-icon-size: 48px;
@uploader-icon-color: @gray-4;
@uploader-text-color: @gray-6;
@uploader-text-font-size: @font-size-sm;
@uploader-upload-background-color: @gray-1;
@uploader-upload-active-color: @active-color;
@uploader-delete-color: @white;
@uploader-delete-icon-size: 28px;
@uploader-delete-background-color: rgba(0, 0, 0, 0.7);
@uploader-file-background-color: @background-color;
@uploader-file-icon-size: 40px;
@uploader-file-icon-color: @gray-7;
@uploader-file-name-padding: 0 @padding-base;
@uploader-file-name-margin-top: @padding-xs;
@uploader-file-name-font-size: @font-size-sm;
@uploader-file-name-text-color: @gray-7;
@uploader-mask-background-color: fade(@gray-8, 88%);
@uploader-mask-icon-size: 44px;
@uploader-mask-message-font-size: @font-size-sm;
@uploader-mask-message-line-height: 28px;
@uploader-loading-icon-size: 44px;
@uploader-loading-icon-color: @white;
@uploader-disabled-opacity: @disabled-opacity;

// DropdownMenu
@dropdown-menu-height: 100px;
@dropdown-menu-background-color: @white;
@dropdown-menu-title-font-size: 30px;
@dropdown-menu-title-text-color: @text-color;
@dropdown-menu-title-active-text-color: var(--primary-color);
@dropdown-menu-title-disabled-text-color: @gray-6;
@dropdown-menu-title-padding: 0 @padding-xs;
@dropdown-menu-title-line-height: 36px;
@dropdown-menu-option-active-color: var(--primary-color);
@dropdown-menu-box-shadow: 0 4px 24px fade(@gray-7, 12);

// IndexAnchor
@index-anchor-padding: 0 @padding-md;
@index-anchor-text-color: @text-color;
@index-anchor-font-weight: 500;
@index-anchor-font-size: @font-size-md;
@index-anchor-line-height: 64px;
@index-anchor-background-color: transparent;
@index-anchor-active-background-color: @white;
@index-anchor-active-text-color: var(--primary-color);

// IndexBar
@index-bar-index-font-size: @font-size-xs;
@index-bar-index-line-height: 28px;
@index-bar-index-popup-width: 70vw;
@index-bar-index-sidebar-zindex: 9999;

// skeleton
@skeleton-padding: 0 @padding-md;
@skeleton-row-height: 32px;
@skeleton-row-background-color: @gray-2;
@skeleton-row-margin-top: @padding-sm;
@skeleton-avatar-background-color: @gray-2;
@skeleton-animation-duration: 1.2s;

.theme(@property, @imp) {
    @{property}: e(replace(@imp, "(-?)@([^() ]+)", "$1@{$2}", "ig"));
    @{property}: e(replace(@imp, "(-?)@([^() ]+)", "var(--$2, $1@{$2})", "ig"));
}

// number-keyboard
@number-keyboard-sign-background-color: #eeeeee;
@number-keyboard-week-color: #999;

// table
@table-primary-color: @black;

// pagination
@pagination-color: @black;
@pagination-font-size: @font-size-md;
@pagination-item-border-color: #e4e7eb;
@pagination-active-background-color: var(--primary-color);
@pagination-disable-color: rgba(116, 116, 116, 0.31);
@pagination-disable-background-color: #f7f8fa;
@pagination-item-border-width: 1px;
@pagination-item-border-radius: 2px;
@pagination-prev-next-padding: 0 11px;

// cascader
@cascader-font-size: 28px;
@cascader-line-height: 44px;
@cascader-tabs-item-padding: 0 20px;
@cascader-title-padding: 48px 40px 34px;
@cascader-title-font-size: 36px;
@cascader-title-line-height: 40px;
@cascader-item-padding: 20px 40px;
@cascader-item-font-size: 28px;
@cascader-item-color: #1a1a1a;
@cascader-item-active-color: var(--primary-color);
@cascader-pane-height: 684px;
@cascader-pane-paddingTop: 20px;
@cascader-icon-checklist-marginLeft: 20px;

// swiper
@swiper-pagination-item-background-color: #ddd;

// ellipsis
@ellipsis-font-size: 28px;
@ellipsis-line-height: 50px;
@ellipsis-action-font-size: 30px;
@ellipsis-primary-color: var(--primary-color);

// signature
@signature-border-color: #dadada;
@signature-border-width: 1px;
@signature-height: 300px;
@signature-margin-bottom: 20px;

// infinite-scroll
@infinite-scroll-primary-color: var(--primary-color);
@infinite-scroll-color: @gray-6;
@infinite-scroll-font-size: @font-size-md;

// pull-to-refresh
@pull-to-refresh-font-size: @font-size-md;
@pull-to-refresh-animation-duration: 0.4s;

// result
@result-padding: 64px 24px;
@result-success-color: #07c160;
@result-wait-color: #4dd3b5;
@result-info-color: #1677ff;
@result-warning-color: #ff8f1f;
@result-error-color: #fc3e39;

// space
@space-gap: 8px;
@space-gap-vertical: 8px;
@space-gap-horizontal: 8px;

// badge
@badge-primary-color: #ff3141;
@badge-font-size: 24px;
@badge-color-text-light-solid: #fff;
@badge-light-borded: 2px solid #eee;

// check-lit
@check-list-font-size: 28px;
@check-list-title-font-size: 32px;
@check-list-cancel-background: #f7f7f7;
@check-list-placeholder-color: @placeholder-color;
@check-list-item-padding: 20px 0px;
@check-list-item-border: 1px solid #e9e9f1;
