import { useEffect, useState } from "react";
import { ScrollView } from "@tarojs/components";
import Taro, { nextTick } from "@tarojs/taro";

type Iprops = {
    timeout: number;
    optiosData: any[];
    tabvalue: string;
    value?: any[];
    scrollIntoView?: boolean;
    children?: React.ReactNode;
};

export default function ScrollViewTimeout(props: Iprops): JSX.Element {
    const [target, setTarget] = useState("");
    const { timeout, optiosData, tabvalue, value, scrollIntoView = true } = props;

    useEffect(
        () => {
            if (scrollIntoView && value) {
                const index = getIndexByValue(optiosData, tabvalue);
                const val = value[index];
                if (val !== undefined) {
                    nextTick(() => {
                        setTimeout(() => {
                            // 使用类名查找元素
                            const className = `vant-cascader-item${val}`;

                            // 查找具有该类名的元素
                            const query = Taro.createSelectorQuery();
                            query
                                .select(`.${className}`)
                                .boundingClientRect()
                                .exec(res => {
                                    console.log("res", res);
                                    if (res && res[0]) {
                                        // 找到了元素，获取父级滚动容器
                                        const scrollQuery = Taro.createSelectorQuery();
                                        scrollQuery
                                            .select(".van-cascader-tab")
                                            .boundingClientRect()
                                            .scrollOffset()
                                            .exec(scrollRes => {
                                                if (scrollRes && scrollRes[0] && scrollRes[1]) {
                                                    // 计算需要滚动的位置
                                                    const scrollTop =
                                                        res[0].top - scrollRes[0].top + scrollRes[1].scrollTop;

                                                    // 获取滚动容器节点并设置滚动位置
                                                    Taro.createSelectorQuery()
                                                        .select(".van-cascader-tab")
                                                        .node()
                                                        .exec(nodeRes => {
                                                            if (nodeRes && nodeRes[0] && nodeRes[0].node) {
                                                                nodeRes[0].node.scrollTop = scrollTop;
                                                            }
                                                        });
                                                }
                                            });
                                    }
                                });
                        }, timeout);
                    });
                } else {
                    // 不需要滚动
                }
            }
        },
        // eslint-disable-next-line react-hooks/exhaustive-deps
        [tabvalue, value],
    );

    return (
        <ScrollView scrollWithAnimation={false} scrollY className="van-cascader-tab" scrollIntoView={target}>
            {props.children}
        </ScrollView>
    );
}

function getIndexByValue(arr, value) {
    let res = 0;
    for (let i = 0; i < arr.length; i++) {
        const item = arr[i];
        if (item.paneKey === value) {
            res = i;
            break;
        }
    }

    return res;
}
