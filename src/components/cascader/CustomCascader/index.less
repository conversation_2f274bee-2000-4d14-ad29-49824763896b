@import "./popup.less";

.van-cascader-tab-box {
    display: flex;
    padding-left: 28px;

    .van-tabs__titles-item.active {
        position: relative;

        &::after {
            content: "";
            display: block;
            height: 6px;
            width: 70%;
            border-radius: 4px;
            position: absolute;
            bottom: -10px;

            .theme(background-color, "@cascader-item-active-color");
        }
    }
}

.van-cascadar-popup {
    height: 800px;
    overflow: hidden;

    .van-tabs__scroll {
        display: none;
    }
}

.van-cascader {
    width: 100%;

    .theme(font-size, "@cascader-font-size");

    .theme(line-height, "@cascader-line-height");

    &__title {
        position: relative;
        display: flex;
        justify-content: center;
        align-items: center;

        .theme(padding, "@cascader-title-padding");

        text-align: center;
        font-weight: bold;

        .theme(line-height, "@cascader-title-line-height");

        color: #1a1a1a;

        .theme(font-size, "@cascader-title-font-size");

        .van-cascader__ok-btn {
            position: absolute;
            right: 0;
            top: 50%;
            transform: translateY(-50%);
        }
    }

    .van-tabs__titles {
        .theme(padding, "@cascader-tabs-item-padding");

        background: #ffffff;
    }

    .van-tabs__titles-item {
        flex: initial;
        min-width: auto;
        width: auto;

        .theme(padding, "@cascader-tabs-item-padding");

        white-space: nowrap;
    }

    .van-tab {
        padding: 0;
    }

    &-tab {
        display: block;
        padding: 0;
        margin: 0;
        width: 100%;
        // padding-top: @cascader-pane-paddingTop;
        //
        .theme(height, "@cascader-pane-height");

        overflow-y: auto;
        -webkit-overflow-scrolling: touch;
    }

    &-item {
        display: flex;
        align-items: center;

        .theme(padding, "@cascader-item-padding");

        margin: 0;
        cursor: pointer;

        .theme(font-size, "@cascader-item-font-size");

        .theme(color, "@cascader-item-color");

        &.disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }

        &.active {
            &:not(.disabled) {
                .theme(color, "@cascader-item-active-color");
            }

            .van-cascader-item__icon-check {
                visibility: visible;

                .theme(color, "@cascader-item-active-color");
            }
        }
    }

    &-item__title {
        flex: 1;
    }

    .van-icon-checklist {
        margin-left: @cascader-icon-checklist-marginLeft;
        visibility: hidden;
    }
}

/* stylelint-disable-next-line no-duplicate-selectors */
.van-cascader-tab {
    overflow: auto;
    -webkit-overflow-scrolling: touch;
    height: 588px;
}
