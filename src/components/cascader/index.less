.cascade_box {
}

.address-pick-view {
    width: 100%;
    height: 400rpx;
}

.address-pick-view-column {
    line-height: 50rpx;
    text-align: center;
}

.address-pick-header {
    height: 80rpx;
    width: 100%;
    margin-bottom: 20rpx;

    .cancel,
    .confirm {
        display: block;
        position: absolute;
        width: 100rpx;
        height: 80rpx;
        line-height: 80rpx;
        /*background: #00f;*/
        text-align: center;
        color: @primary-color;
    }

    .cancel {
        color: #939393;
    }

    .confirm {
        right: 0;
        top: 0;
    }
}

.street-picker {
    .van-tabs__titles-item {
        text-align: center;
        padding: 0 !important;
        &.active::after {
            left: 50%;
            transform: translateX(-50%);
        }
    }

    .van-cascader-tab-box {
        width: 100vw;
        padding-left: 0 !important;
    }
}
