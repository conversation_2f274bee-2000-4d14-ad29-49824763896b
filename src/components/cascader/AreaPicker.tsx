import { useState, useEffect } from "react";
import { View, Text, PickerView, PickerViewColumn } from "@tarojs/components";
import "./index.less";
import api from "@/utils/api";
import { Popup, pxTransform } from "@antmjs/vantui";
import { PickerStandardProps } from "@tarojs/components/types/Picker";

interface RegionItem {
    id: number;
    areaCode: number;
    areaName: string;
}

export interface AreaPickerData {
    province: RegionItem;
    city: RegionItem;
    area: RegionItem;
}

interface AreaPickerProps extends PickerStandardProps {
    onAreaPick?: (data: AreaPickerData) => void;
}

const AreaPicker = (props: AreaPickerProps) => {
    const [provinces, setProvinces] = useState<RegionItem[]>([]); // 获取到的所有的省
    const [cities, setCities] = useState<RegionItem[]>([]); // 选择的该省的所有市
    const [areas, setAreas] = useState<RegionItem[]>([]); // 选择的该市的所有区县
    const [defaultValue, setDefaultValue] = useState([0, 0, 0]);
    const [selectedRegion, setSelectedRegion] = useState([0, 0, 0]);
    const [show, setShow] = useState(false);
    useEffect(() => {
        initAddressPicker();
    }, []);
    // 获取地址数据的函数
    const getCommonAddress = async (pid, type) => {
        const param = { parentId: pid, isFilter: true };
        const obj = await api.areaListChildren({ data: param });
        if (type === 0) {
            setProvinces(obj);
            if (obj.length > 0) {
                let firstP = obj[0].id;
                await getCommonAddress(firstP, 1); // 获取第一个省的市
            }
        } else if (type === 1) {
            setCities(obj);
            if (obj.length > 0) {
                let firstCity = obj[0].id;
                await getCommonAddress(firstCity, 2); // 获取第一个市的区县
            }
        } else {
            setAreas(obj);
        }
    };

    // 初始化 picker
    const initAddressPicker = () => {
        setDefaultValue([0, 0, 0]);
        setSelectedRegion([0, 0, 0]);
        getCommonAddress(-1, 0);
    };

    // 确认按钮的事件
    const onAddressPick = () => {
        const [provinceIndex, cityIndex, areaIndex] = selectedRegion;
        const selectedProvince = provinces[provinceIndex];
        const selectedCity = cities[cityIndex];
        const selectedArea = areas[areaIndex];
        setShow(false);
        props.onAreaPick && props.onAreaPick({ province: selectedProvince, city: selectedCity, area: selectedArea });
    };

    // 滚动选择时触发
    const handlePickerChange = e => {
        const val = e.detail.value;
        let [pid, cid] = selectedRegion;
        if (pid !== val[0]) {
            setSelectedRegion([val[0], 0, 0]);
            getCommonAddress(provinces[val[0]].id, 1); // 获取选中省份的城市
        } else if (cid !== val[1]) {
            setSelectedRegion([val[0], val[1], 0]);
            getCommonAddress(cities[val[1]].id, 2); // 获取选中城市的区县
        } else {
            setSelectedRegion(val);
        }
        setDefaultValue(val);
    };

    return (
        <View>
            <View onClick={() => setShow(true)}>{props.children}</View>
            <Popup show={show} position={"bottom"} onClose={() => setShow(false)}>
                <View className="cascade_box">
                    <View className="address-pick-header">
                        <Text className="cancel" onClick={() => setShow(false)}>
                            取消
                        </Text>
                        <Text className="confirm" onClick={onAddressPick}>
                            确认
                        </Text>
                    </View>
                    <PickerView
                        value={defaultValue}
                        indicatorStyle="height: 80rpx;font-size: 38rpx"
                        className={"address-pick-view"}
                        onChange={handlePickerChange}
                    >
                        <PickerViewColumn>
                            {provinces.map(item => (
                                <View
                                    className={"address-pick-view-column"}
                                    style={{ fontSize: item.areaName.length >= 9 ? pxTransform(24) : pxTransform(32) }}
                                    key={item.areaCode}
                                >
                                    {item.areaName}
                                </View>
                            ))}
                        </PickerViewColumn>
                        <PickerViewColumn>
                            {cities.map((item: RegionItem) => (
                                <View
                                    className={"address-pick-view-column"}
                                    style={{ fontSize: item.areaName.length >= 9 ? pxTransform(24) : pxTransform(32) }}
                                    key={item.areaCode}
                                >
                                    {item.areaName}
                                </View>
                            ))}
                        </PickerViewColumn>
                        <PickerViewColumn>
                            {areas.map(item => (
                                <View
                                    className={"address-pick-view-column"}
                                    style={{ fontSize: item.areaName.length >= 9 ? pxTransform(24) : pxTransform(32) }}
                                    key={item.areaCode}
                                >
                                    {item.areaName}
                                </View>
                            ))}
                        </PickerViewColumn>
                    </PickerView>
                </View>
            </Popup>
        </View>
    );
};

export default AreaPicker;
