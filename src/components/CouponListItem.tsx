/**
 * @description: 优惠券item(废弃)
 */
import React, { useMemo, useState } from "react";
import { View, Text } from "@tarojs/components";
import { Image } from "@antmjs/vantui";
import { icon_arrow_down_s_line, icon_arrow_up_s_line, icon_expire, icon_using } from "@/images";
import { CouponResponse } from "../pages/coupon/types";
import { timestampToTime } from "@/utils/DateTimeUtils";
import classNames from "classnames";
import "../pages/coupon/component/list-item.less";

type ListType = {
    item: CouponResponse;
    status: "unUse" | "used" | "expired";
};

const CouponListItem: React.FC<ListType> = props => {
    const [collapse, setCollapse] = useState(true);
    const { item, status = "unUse" } = props;
    const { promotion } = item;
    const storeLimit = promotion.type === 4;
    /**
     * 限定时间
     */
    const limitTime = useMemo(() => {
        if (storeLimit) {
            return `使用时间 ${promotion.extra.activityTimeStart}~${promotion.extra.activityTimeEnd}`;
        }
        return `有效期至 ${timestampToTime(promotion.endAt, "YYYY-MM-DD HH:mm:ss")}`;
    }, []);

    /**
     * 使用规则
     */
    const limitRule = useMemo(() => {
        if (storeLimit) {
            return (
                <View className={"limit-rule-content"}>
                    购买限定商品[{promotion.skuScopeParams.itemName}]{promotion.conditionParams.limitedItemMinBoughtNum}
                    件
                </View>
            );
        } else if (promotion.extra.restrictions) {
            return <View className={"limit-rule-content"}>{promotion.extra.restrictions}</View>;
        }
        return null;
    }, []);
    console.log("status", status);
    const fontGray = useMemo(() => {
        return status === "used" || status === "expired";
    }, []);
    return (
        <View className="coupon-list-item" style={{ color: "#999" }}>
            <View className="coupon-list-item-left">
                <View className={classNames("coupon-name", { gray: fontGray })}>{promotion.name}</View>
                <View className={classNames("coupon-endAt", { gray: fontGray })}>{limitTime}</View>
                {limitRule && (
                    <View>
                        <View className={"coupon-use-rule"}>
                            <View className={"coupon-use-rule-label"}>使用规则</View>
                            <View
                                className={"coupon-use-rule-collapse"}
                                onClick={() => setCollapse(!collapse)}
                                data-index="{index}"
                            >
                                <Image
                                    className={"coupon-use-rule-collapse-icon"}
                                    src={collapse ? icon_arrow_down_s_line : icon_arrow_up_s_line}
                                />
                            </View>
                        </View>
                        {!collapse && limitRule}
                    </View>
                )}
            </View>
            <View className={classNames("coupon-list-item-right", { gray: fontGray })}>
                {storeLimit ? (
                    <View className="title">门店限定</View>
                ) : (
                    <View>
                        <View className={"fee"}>
                            ￥<Text className={"amount"}>{promotion.reduceFee}</Text>
                        </View>
                        <View className={"desc"}>{promotion.conditionDesc}</View>
                    </View>
                )}
                {fontGray && (
                    <View className="use">
                        <Image className="use_img" src={status === "expired" ? icon_expire : icon_using}></Image>
                    </View>
                )}
            </View>
        </View>
    );
};

export default CouponListItem;
