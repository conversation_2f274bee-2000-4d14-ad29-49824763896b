import { Tab, Tabs } from "@antmjs/vantui";
import { View } from "@tarojs/components";
export default ({ tabList, onchange, current }) => {
    return (
        <Tabs
            onChange={e => {
                onchange(e.detail);
            }}
            active={current}
            sticky={true}
            animated={false}
            duration={0}
            titleActiveColor={"#f5001d"}
            style={{ position: "fixed", top: 0, left: 0, right: 0, zIndex: 200 }}
        >
            {tabList.map((tabItem, index) => {
                return (
                    <Tab
                        title={
                            <>
                                {tabItem.title}
                                {tabItem.dotNum > 0 && (
                                    <View
                                        style={{
                                            position: "absolute",
                                            top: "10rpx",
                                            right: "10rpx",
                                            width: "26rpx",
                                            height: "26rpx",
                                            borderRadius: "26rpx",
                                            backgroundColor: "red",
                                            color: "#fff",
                                            fontSize: "20rpx",
                                            lineHeight: "26rpx",
                                        }}
                                    >
                                        {tabItem.dotNum}
                                    </View>
                                )}
                            </>
                        }
                    ></Tab>
                );
            })}
        </Tabs>
    );
};
