import api from "@/utils/api";
import { DDYNavigateTo } from "@/utils/route";
import { ActionSheet, Button } from "@antmjs/vantui";
import { View } from "@tarojs/components";
import Taro, { useDidShow } from "@tarojs/taro";
import { useImperativeHandle, useState } from "react";
import "./agree-modal.less";
import { queryInfo, saveSignback } from "@/utils/common";
import { getStorage } from "@/utils/storage";
import { IDENTITY } from "@/utils/constant";
import React from "react";

export default React.forwardRef((props, ref) => {
    const [show, setShow] = useState(false);

    const onOK = () => {
        setShow(false);
        queryInfo({
            go: true,
            goSign: () => {
                saveSignback();
                DDYNavigateTo({
                    url: "/pages/my/sign-transfer/index",
                });
            },
        });
    };

    useDidShow(() => {
        verify();
    });

    const verify = (fn?: () => void) => {
        if (getStorage(IDENTITY) === "subStore") {
            api.querySignStatus({
                method: "POST",
                data: {},
                contentType: "application/json",
                filterCheck: true,
            }).then(res => {
                if (res.success) {
                    if (res.data !== 2) {
                        setShow(true);
                    } else {
                        fn && fn();
                    }
                }
            });
        } else {
            fn && fn();
        }
    };

    useImperativeHandle(ref, () => {
        return {
            verify,
        };
    });

    return (
        <>
            <ActionSheet show={show} title="" onClose={() => setShow(false)}>
                <View className="content">
                    <View className="title">提现需阅读并同意以下协议</View>
                    <View
                        className="tips"
                        onClick={() => {
                            DDYNavigateTo({ url: "/pages/h5/privacy-policy?type=15" });
                        }}
                    >
                        《门店推广服务协议》
                    </View>
                    <View className="btn-container">
                        <Button
                            className="btn-item"
                            onClick={() => {
                                setShow(false);
                            }}
                        >
                            暂不签约
                        </Button>
                        <Button className="btn-item red" onClick={onOK}>
                            去签约
                        </Button>
                    </View>
                </View>
            </ActionSheet>
        </>
    );
});
