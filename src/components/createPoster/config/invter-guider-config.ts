export default (postBgUrl, miniQr, subStoreName, nickName, serverAvatarUrl) => {
    return {
        width: "750rpx",
        height: "1200rpx",
        background: "#fff",
        views: [
            {
                type: "image",
                url: postBgUrl,
                css: {
                    width: "750rpx",
                    height: "920rpx",
                    borderColor: "#000000",
                    mode: "scaleToFill",
                },
            },
            {
                type: "text",
                text: "长按识别或者扫码，立即邀请",
                css: {
                    color: "#666666",
                    background: "rgba(0,0,0,0)",
                    width: "554rpx",
                    top: "1144rpx",
                    right: "30rpx",
                    fontSize: "24rpx",
                    maxLines: "1",
                    textStyle: "fill",
                    fontFamily: "",
                    textAlign: "right",
                },
            },
            {
                type: "image",
                url: miniQr,
                css: {
                    color: "#000000",
                    background: "#ffffff",
                    width: "210rpx",
                    height: "210rpx",
                    top: "924rpx",
                    left: "520rpx",
                    rotate: "0",
                },
            },
            {
                type: "text",
                text: subStoreName,
                css: {
                    color: "#333333",
                    background: "rgba(0,0,0,0)",
                    width: "354rpx",
                    top: "940rpx",
                    left: "24rpx",
                    borderColor: "#000000",
                    fontSize: "28rpx",
                    fontWeight: "bold",
                    maxLines: "2",
                    textAlign: "left",
                },
            },
            {
                type: "text",
                text: nickName,
                css: {
                    color: "#333333",
                    background: "rgba(0,0,0,0)",
                    width: "354rpx",
                    top: "1045rpx",
                    left: "160rpx",
                    borderColor: "#000000",
                    fontSize: "32rpx",
                    // fontWeight: "bold",
                    maxLines: "2",
                    textAlign: "left",
                },
            },
            {
                type: "image",
                url: serverAvatarUrl,
                css: {
                    width: "96rpx",
                    height: "96rpx",
                    top: "1010rpx",
                    left: "34rpx",
                    borderRadius: "64rpx",
                    mode: "scaleToFill",
                },
            },
        ],
    };
};
