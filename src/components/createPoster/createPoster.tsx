import React from "react";
import configFn from "./config";
import guiderConfigFn from "./config/invter-guider-config";
import QyPoster from "@/components/poster";

interface CreatePosterProps {
    data: Record<string, any>;
    onImgOK: (e: any) => void;
    onImgErr: (error: any) => void;
}

export default React.memo(
    (
        props: CreatePosterProps = {
            data: {},
            onImgOK: () => {},
            onImgErr: () => {},
        },
    ) => {
        const { data, onImgOK, onImgErr } = props;
        console.log("data", data);
        return (
            // @ts-ignore
            <QyPoster
                customStyle="position: absolute; left: -9999rpx;"
                palette={data}
                widthPixels={375}
                onImgOK={onImgOK}
                onImgErr={onImgErr}
            />
        );
    },
);

export function buildServerProviderPost({ postBgUrl = "", miniQr = "", serverShopName = "", serverAvatarUrl = "" }) {
    return configFn(postBgUrl, miniQr, serverShopName, serverAvatarUrl);
}
export function buildGuiderPost({ postBgUrl = "", miniQr = "", subStoreName = "", nickName = "", avatarUrl = "" }) {
    return guiderConfigFn(postBgUrl, miniQr, subStoreName, nickName, avatarUrl);
}
