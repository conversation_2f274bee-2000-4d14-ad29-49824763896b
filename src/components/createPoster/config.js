export default (postBgUrl, miniQr, serverShopName, serverAvatarUrl) => {
    return {
        width: "750rpx",
        height: "1200rpx",
        background: "#fff",
        views: [
            {
                type: "image",
                url: postBgUrl,
                css: {
                    width: "750rpx",
                    height: "920rpx",
                    borderColor: "#000000",
                    mode: "scaleToFill",
                },
            },
            {
                type: "text",
                text: "长按识别或者扫码，立即邀请开店",
                css: {
                    color: "#666666",
                    background: "rgba(0,0,0,0)",
                    width: "554rpx",
                    top: "1144rpx",
                    right: "30rpx",
                    fontSize: "24rpx",
                    maxLines: "1",
                    textStyle: "fill",
                    fontFamily: "",
                    textAlign: "right",
                },
            },
            {
                type: "image",
                url: miniQr,
                css: {
                    color: "#000000",
                    background: "#ffffff",
                    width: "175rpx",
                    height: "175rpx",
                    top: "954rpx",
                    left: "550rpx",
                    rotate: "0",
                },
            },
            {
                type: "text",
                text: serverShopName,
                css: {
                    color: "#333333",
                    background: "rgba(0,0,0,0)",
                    width: "354rpx",
                    top: "1035rpx",
                    left: "176rpx",
                    borderColor: "#000000",
                    fontSize: "32rpx",
                    fontWeight: "bold",
                    maxLines: "2",
                    textAlign: "left",
                },
            },
            {
                type: "image",
                url: serverAvatarUrl,
                css: {
                    width: "128rpx",
                    height: "128rpx",
                    top: "987rpx",
                    left: "24rpx",
                    borderRadius: "64rpx",
                    mode: "scaleToFill",
                },
            },
        ],
    };
};
