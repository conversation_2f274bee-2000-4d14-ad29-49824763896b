import { useMemo, useState } from "react";
import Taro from "@tarojs/taro";
import Toast from "@/utils/toast";
import CreatePoster from "@/components/createPoster/createPoster";

interface PosterResult {
    poster: JSX.Element | null;
    sharePath: string;
    handleSavePhoto: () => Promise<string>;
}

const usePoster = options => {
    const { posterInfo, onImgOK, createPalette } = options;
    const [sharePath, setSharePath] = useState("");
    const poster = useMemo(() => {
        if (posterInfo) {
            Toast.showLoading("生成海报中...");
        }
        return posterInfo ? (
            <CreatePoster
                data={createPalette()}
                onImgOK={res => {
                    onImgOK(res);
                    Toast.hideLoading();
                    setSharePath(res.path || res.detail?.path);
                }}
                onImgErr={err => {
                    console.log("onImgErr", err);
                    Toast.hideLoading();
                    Toast.info("生成分享图失败，请稍后重试");
                }}
            />
        ) : null;
    }, [posterInfo]);

    const handleSavePhoto = () => {
        Toast.showLoading("正在保存...");

        function wxAuthChcck(resolve: (e: string) => void, reject: (e: string) => void) {
            Taro.getSetting()
                .then((res: Taro.getSetting.SuccessCallbackResult) => {
                    let authSetting = res.authSetting;
                    if (!authSetting["scope.writePhotosAlbum"]) {
                        Toast.hideLoading();
                        Taro.showModal({
                            title: "提示",
                            content: "您未开启保存图片到相册的权限，请点击确定去开启权限！",
                            success(resModal: Taro.showModal.SuccessCallbackResult) {
                                if (resModal.confirm) {
                                    Taro.authorize({
                                        scope: "scope.writePhotosAlbum",
                                        success: function () {
                                            savePhoto(
                                                errMsg => resolve(errMsg),
                                                errMsg => reject(errMsg),
                                            );
                                        },
                                    });
                                }
                                if (resModal.cancel) {
                                    Toast.hideLoading();
                                    reject("cancel authorize");
                                }
                            },
                        });
                    } else {
                        savePhoto(
                            errMsg => resolve(errMsg),
                            errMsg => reject(errMsg),
                        );
                    }
                })
                .catch(e => {
                    reject(e);
                });
        }

        function alipayAuthChcck(resolve: (e: string) => void, reject: (e: string) => void) {
            Taro.getSetting()
                .then((res: Taro.getSetting.SuccessCallbackResult) => {
                    let authSetting = res.authSetting;
                    if (!authSetting["writePhotosAlbum"]) {
                        Toast.hideLoading();
                        // @ts-ignore
                        Taro.showAuthGuide({
                            authType: "PHOTO",
                            success: function () {
                                savePhoto(
                                    errMsg => resolve(errMsg),
                                    errMsg => reject(errMsg),
                                );
                            },
                            fail: function (e) {
                                console.log("faileddd", e);
                            },
                        });
                    } else {
                        savePhoto(
                            errMsg => resolve(errMsg),
                            errMsg => reject(errMsg),
                        );
                    }
                })
                .catch(e => {
                    reject(e);
                });
        }
        return new Promise((resolve: (e: string) => void, reject: (e: string) => void) => {
            if (process.env.TARO_ENV === "weapp") {
                wxAuthChcck(resolve, reject);
            } else if (process.env.TARO_ENV === "alipay") {
                alipayAuthChcck(resolve, reject);
            }
        });
    };
    const savePhoto = (success, fail) => {
        Taro.saveImageToPhotosAlbum({
            filePath: sharePath,
            success: res => {
                setTimeout(() => {
                    Toast.hideLoading();
                    success(res.errMsg);
                    Toast.success("保存成功");
                });
            },
            fail: res => {
                setTimeout(() => {
                    Toast.hideLoading();
                    fail(res.errMsg);
                }, 300);
            },
        });
    };
    return {
        poster,
        sharePath,
        handleSavePhoto,
    } as PosterResult;
};
export default usePoster;
