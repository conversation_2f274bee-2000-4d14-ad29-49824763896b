import React, { useState } from "react";
import Taro from "@tarojs/taro";
import { View } from "@tarojs/components";
import { go } from "@/utils/route";
import { hotJumpUrl } from "@/components/desgin/desginUtils";
export default ({ set = {}, commonSet = {} }) => {
    const [style, setStyle] = useState({
        marginTop: `${commonSet.marginTop}px`,
        marginBottom: `${commonSet.marginBottom}px`,
        backgroundColor: `${commonSet.bgColor || "#fff"}`,
    });

    return (
        <View className="guide" style={style}>
            <View className="list">
                {Array.isArray(set.list) &&
                    set.list.map((item, index) => {
                        return (
                            <View
                                className={`node type-${set.type} num-${set.num}`}
                                key={index}
                                onClick={() => {
                                    hotJumpUrl(item);
                                }}
                            >
                                <View
                                    className="icon"
                                    style={{ backgroundImage: `url(${item.icon}?x-oss-process=image/resize,l_90)` }}
                                ></View>
                                <View className="title">{item.title}</View>
                            </View>
                        );
                    })}
            </View>
        </View>
    );
};
