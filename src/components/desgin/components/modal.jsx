import React, { useState, useEffect, useMemo, Fragment } from "react";
import { View } from "@tarojs/components";
import { Popup, Swiper, SwiperItem, Image, Icon } from "@antmjs/vantui";
import { observer } from "mobx-react-lite";
import designStore from "@/store/desgin-store";
import Taro from "@tarojs/taro";
import "./modal.less";
import { hotJumpUrl } from "@/components/desgin/desginUtils";

const DiyModal = observer(({ set = {}, commonSet = {} }) => {
    const [show, setShow] = useState(false);
    useEffect(() => {
        setShow(designStore.showModal);
        if (modalJson.autoClose) {
            setTimeout(() => {
                setShow(false);
            }, modalJson.autoCloseTime * 1000);
        }
    }, [designStore.showModal, designStore.modalJson]);
    const handleClose = () => {
        setShow(false);
        designStore.closeModal();
    };
    const modalJson = useMemo(() => {
        return JSON.parse(designStore.modalJson);
    }, [designStore.modalJson]);

    const showPositionIsCenter = useMemo(() => {
        return modalJson.position === "center";
    }, [modalJson]);
    const screenWidth = Taro.getSystemInfoSync().windowWidth; // 获取屏幕宽度
    const imageWidth = screenWidth * 0.75; // 计算图片宽度
    const imageHeight = imageWidth * (4 / 3); // 计算图片高度

    return (
        <Popup
            show={show}
            round={!showPositionIsCenter}
            position={modalJson.position}
            closeable={showPositionIsCenter ? modalJson.showCloseBtn : false}
            onClose={handleClose}
            closeIconPosition="top-right"
        >
            <View className={showPositionIsCenter ? "modal-wrapper-center" : "modal-wrapper-bottom"}>
                {showPositionIsCenter ? (
                    <View className={`modal-content center`}>
                        {modalJson.imageUrlList.length === 1 ? (
                            <Image
                                onClick={() => {
                                    hotJumpUrl(modalJson.imageUrlList[0]);
                                }}
                                src={modalJson.imageUrlList[0].pic}
                                fit={"cover"}
                                width={`${imageWidth}px`}
                                height={`${imageHeight}px`}
                            />
                        ) : modalJson.imageUrlList.length > 1 ? (
                            <Swiper
                                height={Taro.pxTransform(modalJson.picHeight)}
                                width={`${imageWidth}px`}
                                paginationColor="#426543"
                                autoPlay="3000"
                                initPage={0}
                                paginationVisible
                            >
                                {modalJson.imageUrlList.map((item, index) => {
                                    return (
                                        <SwiperItem key={`swiper#demo1${index}`}>
                                            <Image
                                                onClick={() => {
                                                    hotJumpUrl(item);
                                                }}
                                                src={item.pic}
                                                fit={"cover"}
                                                width={`${imageWidth}px`}
                                                height={`${imageHeight}px`}
                                            />
                                        </SwiperItem>
                                    );
                                })}
                            </Swiper>
                        ) : null}
                    </View>
                ) : (
                    <Fragment>
                        <View className={"modal-header"}>
                            <View className="modal-title">{modalJson.title}</View>
                            {modalJson.showCloseBtn ? (
                                <View className={"modal-close-icon"} onClick={handleClose}>
                                    <Icon name="close" size={Taro.pxTransform(48)} color="#333" />
                                </View>
                            ) : null}
                        </View>

                        <View className="modal-line"></View>
                        <View className="modal-body">
                            {modalJson.content?.split("\n").map((line, index) => {
                                const isNumberedList = /^\d+\.\s/.test(line);
                                return (
                                    <View key={index} className={isNumberedList ? "imageUrlList-item" : ""}>
                                        {line}
                                    </View>
                                );
                            })}
                        </View>
                    </Fragment>
                )}
            </View>
        </Popup>
    );
});
export default DiyModal;
