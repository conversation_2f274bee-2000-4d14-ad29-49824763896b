.rank-list {
    .rank-list-content {
        margin-bottom: 24px;

        // 样式一 - 列表式
        .rank-list-style-one {
            padding-top: 14rpx;
            background-color: #faf0e3;
            border-radius: 32px 32px 0 0;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.05);
            width: 363px;

            .rank-header {
                display: flex;
                height: 109px;
                flex-direction: column;
                justify-content: center;
                margin-left: 18px;
                margin-right: 18px;
                background: linear-gradient(180deg, #ffffff 0%, rgba(255, 255, 255, 0.32) 100%);
                border-radius: 16px;
                opacity: 0.43;

                .rank-title {
                    text-align: center;
                    font-weight: 600;
                    font-size: 22px;
                    color: #ffffff;
                    line-height: 26px;
                    font-style: normal;
                    text-shadow: -2px -2px 0 #894724, 2px -2px 0 #894724, -2px 2px 0 #894724, 2px 2px 0 #894724;
                }

                .rank-tag {
                    color: #8b4513;
                    display: flex;
                    justify-content: center;
                    margin-top: 3px;
                    font-weight: 400;
                    font-size: 18px;
                    line-height: 25px;

                    .fire-text {
                        margin: 0 12px;
                    }

                    .fire-icon,
                    .arrow-icon {
                        font-family: "iconfont";
                        color: #8b4513;
                    }
                }
            }

            .rank-items {
                margin-top: -5px;
                padding: 18px;
                border-radius: 32px 32px 0 0;
                background: #fff;
                min-height: 317px;

                .rank-item {
                    display: flex;
                    height: 107px;
                    position: relative;

                    .rank-num {
                        left: -4px;
                        z-index: 9;
                        position: absolute;
                    }

                    .rank-img {
                        margin-top: 11px;
                        margin-left: 29px;
                    }

                    .rank-info {
                        flex: 1;
                        margin-left: 24px;
                        margin-top: 16px;

                        .rank-desc {
                            text-align: center;
                            width: 128px;
                            height: 22px;
                            background: #fff2e0;
                            border-radius: 8px;
                            font-size: 16px;
                            color: #ffa14b;
                            line-height: 22px;
                            white-space: nowrap;
                            overflow: hidden;
                            text-overflow: ellipsis;
                        }

                        .rank-name {
                            width: 182px;
                            margin-top: 18px;
                            font-weight: 400;
                            font-size: 20px;
                            color: #000000;
                            line-height: 28px;
                            white-space: nowrap;
                            overflow: hidden;
                            text-overflow: ellipsis;
                        }
                    }
                }
            }
        }

        // 样式二 - 网格式
        .rank-list-style-two {
            background-color: #4169e1;
            border-radius: 32px 32px 0 0;

            .rank-header {
                background: linear-gradient(180deg, #ffffff 0%, rgba(255, 255, 255, 0.32) 100%);
                border-radius: 16px;
                opacity: 0.43;
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 12px 24px;

                .rank-title {
                    font-weight: 600;
                    font-size: 22px;
                    color: #ffffff;
                    line-height: 26px;
                    font-style: normal;
                    text-shadow: -2px -2px 0 #894724, 2px -2px 0 #894724, -2px 2px 0 #894724, 2px 2px 0 #894724;
                }

                .rank-tag {
                    font-size: 28px;
                    color: #fff;
                    display: flex;
                    align-items: center;
                    padding: 8px 16px;
                    border-radius: 24px;
                    font-weight: 300;

                    .fire-text {
                        margin: 0 12px;
                        font-weight: 600;
                        font-size: 18px;
                        color: #894724;
                        line-height: 25px;
                    }

                    .fire-icon,
                    .arrow-icon {
                        font-family: "iconfont";
                        color: #894724;
                    }
                }
            }

            .rank-products {
                min-height: 436px;
                background-color: #ffffff;
                padding: 24px;
                border-radius: 40px 40px 0 0;

                .product-row {
                    display: flex;
                    margin-bottom: 20px;
                    flex-wrap: wrap;
                    justify-content: space-between;

                    &:last-child {
                        margin-bottom: 0;
                    }

                    .product-item {
                        width: 30%;
                        border-radius: 15px;
                        border: 1px solid #f4f4f4;
                        background: #fff;
                        position: relative;
                        margin-bottom: 8px;

                        .rank-num {
                            position: absolute;
                            top: -4px;
                            left: 10px;
                        }

                        .product-top {
                            text-align: center;

                            .product-rank {
                                position: absolute;
                                top: 0;
                                left: 0;
                                width: 48px;
                                height: 48px;
                                background: #ffb74d;
                                color: #fff;
                                border-radius: 0 0 12px 0;
                                display: flex;
                                align-items: center;
                                justify-content: center;
                                font-size: 24px;
                                font-weight: bold;
                                z-index: 1;
                            }

                            .product-img {
                                margin-top: 30px;
                            }

                            .product-info {
                                text-align: center;

                                .product-name {
                                    font-size: 20px;
                                    color: #ffcc30;
                                    margin-bottom: 4px;
                                    white-space: nowrap;
                                    overflow: hidden;
                                    text-overflow: ellipsis;
                                    // font-weight: 500;
                                    text-align: center;
                                }
                            }
                        }

                        .rank-info {
                            display: flex;
                            justify-content: center;
                            margin: 0 16px;

                            .rank-desc {
                                text-align: center;
                                width: 128px;
                                height: 22px;
                                background: #ffdb51;
                                border-radius: 8px;
                                font-weight: 500;
                                font-size: 16px;
                                color: #ffffff;
                                line-height: 22px;
                                white-space: nowrap;
                                overflow: hidden;
                                text-overflow: ellipsis;
                            }
                        }

                        .product-bottom {
                            height: 36px;
                            background: #f8f8f8;
                            border-radius: 10px;
                            padding: 0 8px;

                            .product-desc {
                                width: 100%;
                                font-weight: 500;
                                font-size: 16px;
                                color: #333;
                                line-height: 36px;
                                text-align: center;
                                white-space: nowrap;
                                overflow: hidden;
                                text-overflow: ellipsis;
                                background-color: #f3f5f4;
                                border-radius: 12px;
                            }
                        }
                    }
                }
            }
        }
    }
}
