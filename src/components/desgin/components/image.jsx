import React, { useState, useEffect } from "react";
import Taro from "@tarojs/taro";
import newAPi from "@/utils/newApi";
import { Image, View } from "@tarojs/components";
import { hotJumpUrl } from "@/components/desgin/desginUtils";
export default ({ set = {}, commonSet = {} }) => {
    const [style, setStyle] = useState({
        marginTop: `${commonSet.marginTop}px`,
        marginBottom: `${commonSet.marginBottom}px`,
        backgroundColor: `${commonSet.bgColor}`,
        height: `${commonSet.height}rpx`,
    });

    const [show, setShow] = useState(true);

    const getActicityInfo = async id => {
        if (!id) return;
        // 获取活动数据
        let params = {
            id: id,
        };
        try {
            const data = await newAPi.queryActivityById({
                data: params,
                method: "POST",
                showLoad: false,
                showError: false,
            });
            // 进行中的活动
            if (!data || data.activityStatus !== 2) {
                setShow(false);
            }
        } catch (e) {
            setShow(false);
        }
    };

    useEffect(() => {
        if (
            set &&
            set.url &&
            set.url.radioKey &&
            set.url.radioKey == "marketing_tools" &&
            set.url.radioValue &&
            set.url.radioValue.activityId
        ) {
            getActicityInfo((set.url.radioValue.activityId || [])[0]);
            Taro.eventCenter.on("indexUpdate", () => {
                getActicityInfo((set.url.radioValue.activityId || [])[0]);
            });
        }
        return () => {
            Taro.eventCenter.off("indexUpdate");
        };
    }, []);

    return (
        show && (
            <>
                <View className="image-container" style={style}>
                    <Image
                        className="image-cp"
                        src={`${set.pic}?x-oss-process=image/resize,w_750`}
                        onClick={() => {
                            hotJumpUrl(set);
                        }}
                        mode="widthFix"
                        lazy-load="true"
                    />
                </View>
            </>
        )
    );
};
