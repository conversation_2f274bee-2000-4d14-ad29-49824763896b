import { Image, View, NavigationBar } from "@tarojs/components";
import React, { useState, useEffect } from "react";
import Taro from "@tarojs/taro";
import { Icon } from "@antmjs/vantui";
import { DDYBack } from "@/utils/route";

export default ({ set = {}, commonSet = {}, children }) => {
    const [statusBarHeight, setStatusBarHeight] = useState(Taro.getSystemInfoSync().statusBarHeight);
    useEffect(() => {}, []);
    const currentPages = Taro.getCurrentPages();
    return (
        <View className="navigation">
            <View style={{ height: statusBarHeight + "px" }}></View>
            <View className="navigation-status" style={{ height: statusBarHeight + "px", top: 0 }}></View>
            <View className="navigation-content" style={{ top: statusBarHeight + "px" }}>
                <Icon
                    name={currentPages.length > 1 ? "arrow-left" : ""}
                    size="26px"
                    className={"navigation-content-left-icon"}
                    onClick={() => {
                        DDYBack();
                    }}
                />
                <View className="title" style={{ margin: currentPages.length > 1 ? "" : "0 auto" }}>
                    {set.title}
                </View>
            </View>
            <View className="fill"></View>
        </View>
    );
};
