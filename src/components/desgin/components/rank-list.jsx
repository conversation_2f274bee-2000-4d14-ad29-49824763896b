import { STORE_ID } from "@/utils/constant";
import newAPi from "@/utils/newApi";
import { getStorage } from "@/utils/storage";
import { View } from "@tarojs/components";
import { useEffect, useState } from "react";
import "./rank-list.less";
import { Icon, Image } from "@antmjs/vantui";
import Taro from "@tarojs/taro";
import { DDYNavigateTo } from "@/utils/route";

export const rankIcon = [
    "https://dante-img.oss-cn-hangzhou.aliyuncs.com/57560427103.png",
    "https://dante-img.oss-cn-hangzhou.aliyuncs.com/5755767277.png",
    "https://dante-img.oss-cn-hangzhou.aliyuncs.com/57555214425.png",
    "https://dante-img.oss-cn-hangzhou.aliyuncs.com/6014980444.png",
    "https://dante-img.oss-cn-hangzhou.aliyuncs.com/60147901748.png",
    "https://dante-img.oss-cn-hangzhou.aliyuncs.com/60145750723.png",
];
export default ({ set = {}, commonSet = {} }) => {
    const [style, setStyle] = useState({
        marginTop: `${commonSet.marginTop}px`,
        marginBottom: `${commonSet.marginBottom}px`,
    });
    const [dataList, setDataList] = useState([]);

    useEffect(() => {
        const size = set.style === "style-one" ? 3 : 6;
        if (Array.isArray(set.shopCategoryId)) {
            newAPi
                .getRankList({
                    data: {
                        shopId: getStorage(STORE_ID),
                        shopCategoryId: set.shopCategoryId[set.shopCategoryId.length - 1],
                        size,
                    },
                    method: "POST",
                    filterCheck: true,
                })
                .then(res => {
                    console.log("--------------------", res);
                    setDataList(res.data);
                });
        }
    }, [set]);
    console.log("--------------------", set);
    return (
        <View className={`rank-list`} style={style}>
            <View className="rank-list-content">
                {set.style === "style-two" ? (
                    <View
                        className="rank-list-style-two"
                        style={{ backgroundColor: set.bgColor }}
                        onClick={() => {
                            DDYNavigateTo({
                                url: `/pages/rank-list/index?shopCategoryId=${
                                    set.shopCategoryId[set.shopCategoryId.length - 1]
                                }&title=${set.title}&introduce=${set.introduce}`,
                            });
                        }}
                    >
                        <View className="rank-header">
                            <View className="rank-title">{set.title || "热卖榜"}</View>
                            <View className="rank-tag">
                                <View className="fire-icon">&#xe7a8;</View>
                                <View className="fire-text">{set.introduce}</View>
                                <View className="arrow-icon">&#xe617;</View>
                            </View>
                        </View>
                        <View className="rank-products">
                            <View className="product-row">
                                {dataList?.map((item, index) => {
                                    return (
                                        <View key={index} className="product-item">
                                            <View className="product-top">
                                                <Icon
                                                    className="rank-num"
                                                    name={rankIcon[index]}
                                                    size={Taro.pxTransform(40)}
                                                />
                                                <Image
                                                    width={Taro.pxTransform(135)}
                                                    height={Taro.pxTransform(135)}
                                                    radius={8}
                                                    className="product-img"
                                                    src={item.mainImage}
                                                />
                                            </View>
                                            <View className="product-bottom">
                                                <View className="product-desc">{item.name} </View>
                                            </View>
                                        </View>
                                    );
                                })}
                            </View>
                        </View>
                    </View>
                ) : (
                    <View
                        className="rank-list-style-one"
                        style={{ backgroundColor: set.bgColor || "#FCEAD3" }}
                        onClick={() => {
                            DDYNavigateTo({
                                url: `/pages/rank-list/index?shopCategoryId=${
                                    set.shopCategoryId[set.shopCategoryId.length - 1]
                                }&title=${set.title}&introduce=${set.introduce}`,
                            });
                        }}
                    >
                        <View className="rank-header">
                            <View className="rank-title">{set.title || "热卖榜"}</View>
                            <View className="rank-tag">
                                <View className="fire-icon">&#xe7a8;</View>
                                <View className="fire-text">{`${set.introduce}`}</View>
                                <View className="arrow-icon">&#xe617;</View>
                            </View>
                        </View>
                        <View className="rank-items">
                            {dataList?.map((item, index) => {
                                return (
                                    <View className="rank-item" key={item.id}>
                                        <Icon className="rank-num" name={rankIcon[index]} size={Taro.pxTransform(40)} />
                                        <Image
                                            width={Taro.pxTransform(84)}
                                            height={Taro.pxTransform(84)}
                                            radius={8}
                                            className="rank-img"
                                            src={item.mainImage}
                                        />
                                        <View className="rank-info">
                                            <View className="rank-name">{item.name}</View>
                                        </View>
                                    </View>
                                );
                            })}
                        </View>
                    </View>
                )}
            </View>
        </View>
    );
};
