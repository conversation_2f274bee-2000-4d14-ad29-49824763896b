.van-popup {
    .modal-wrapper-center {
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;

        .modal-content {
            position: relative;

            &.bottom {
                //border-radius: 24px 24px 0 0; // 12px × 2
                margin-top: auto;
            }

            &.center {
                //border-radius: 24px; // 12px × 2
                // margin: auto;
            }

            .modal-title {
                font-size: 32px; // 16px × 2
                font-weight: bold;
                text-align: center;
                margin-bottom: 10px; // 5px × 2
                color: #333;
            }

            .modal-line {
                width: 100%;
                height: 2px; // 1px × 2
                background-color: #eee;
                margin: 20px 0; // 10px × 2
            }

            .modal-body {
                font-size: 28px; // 14px × 2
                line-height: 1.6;
                color: #333;
                margin-bottom: 30px; // 15px × 2
                text-align: left;
                white-space: pre-line;

                .list-item {
                    margin-bottom: 16px; // 8px × 2
                    padding-left: 36px; // 18px × 2
                    position: relative;
                    text-indent: -36px; // -18px × 2

                    &:last-child {
                        margin-bottom: 0;
                    }
                }
            }

            .modal-image {
                text-align: center;
                margin: 20px 0; // 10px × 2

                img {
                    max-width: 100%;
                    border-radius: 8px; // 4px × 2
                }

                .modal-image-close {
                    position: absolute;
                    bottom: -50px; // -25px × 2
                    left: 50%;
                    transform: translateX(-50%);
                    width: 60px; // 30px × 2
                    height: 60px; // 30px × 2
                    line-height: 60px; // 30px × 2
                    text-align: center;
                    border-radius: 50%;
                    background-color: rgba(0, 0, 0, 0.5);
                    color: #fff;
                    font-size: 40px; // 20px × 2
                    cursor: pointer;
                    z-index: 10;
                }
            }

            .modal-close {
                position: absolute;
                top: 20px; // 10px × 2
                right: 20px; // 10px × 2
                width: 48px; // 24px × 2
                height: 48px; // 24px × 2
                line-height: 44px; // 22px × 2
                text-align: center;
                font-size: 40px; // 20px × 2
                color: #999;
                cursor: pointer;
                border-radius: 50%;
                font-weight: 300;

                &:hover {
                    background-color: #f5f5f5;
                }
            }
        }
    }

    .modal-slider {
        position: relative;
        width: 100%;
        overflow: hidden;

        .slider-container {
            display: flex;
            transition: transform 0.3s ease; // 时间单位保持不变
            width: 100%;

            .slider-item {
                flex: 0 0 100%;
                width: 100%;

                img {
                    width: 100%;
                    height: auto;
                    display: block;
                }
            }
        }

        .slider-dots {
            position: absolute;
            bottom: 20px; // 原 10px 乘以2
            left: 0;
            right: 0;
            display: flex;
            justify-content: center;

            .slider-dot {
                width: 16px; // 原 8px 乘以2
                height: 16px; // 原 8px 乘以2
                border-radius: 50%;
                background-color: rgba(255, 255, 255, 0.5);
                margin: 0 8px; // 原 4px 乘以2
                cursor: pointer;

                &.active {
                    background-color: #fff;
                }
            }
        }
    }

    .modal-wrapper-bottom {
        .modal-header {
            padding: 0 24px;
            position: relative;

            .modal-title {
                height: 86px;
                font-size: 32px; // 16px × 2
                font-weight: bold;
                text-align: center;
                margin-bottom: 10px; // 5px × 2
                color: #333;
                display: flex;
                justify-content: center;
                align-items: center;
            }

            .modal-close-icon {
                text-align: center;
                height: 86px;
                width: 86px;
                position: absolute;
                top: 24px;
                right: 24px;
            }
        }

        .modal-line {
            width: 100%;
            height: 2px; // 1px × 2
            background-color: #eee;
            margin: 20px 0; // 10px × 2
        }

        .modal-body {
            padding: 0 24px;
            min-height: 300px;
            font-size: 28px; // 14px × 2
            line-height: 1.6;
            color: #333;
            margin-bottom: 30px; // 15px × 2
            text-align: left;
            word-break: break-all;
            white-space: pre-line;
        }
    }
}
