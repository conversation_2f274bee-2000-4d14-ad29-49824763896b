import { Image, View } from "@tarojs/components";
import React, { useState, useEffect } from "react";
import { DDYSwitchTab, go } from "@/utils/route";
import "./search.less";
import { hotJumpUrl } from "@/components/desgin/desginUtils";
export default ({ set = {}, commonSet = {} }) => {
    const [style, setStyle] = useState({
        marginTop: `${commonSet.marginTop}px`,
        marginBottom: `${commonSet.marginBottom}px`,
        backgroundColor: `${commonSet.bgColor || "#fff"}`,
    });
    return (
        <View style={style}>
            <View className="search">
                <View className="search-content">
                    {set.style === 3 && (
                        <View className="search-icon-list">
                            {set.iconList &&
                                set.iconList.map((icon, index) => (
                                    <Image
                                        src={icon.pic}
                                        onClick={() => {
                                            hotJumpUrl(icon);
                                        }}
                                        style={{ width: "36px", height: "36px" }}
                                    />
                                ))}
                        </View>
                    )}
                    <View
                        className="search-box"
                        onClick={() => {
                            DDYSwitchTab({ url: "/pages/home/<USER>" });
                        }}
                    >
                        请输入你要搜索的宝贝
                    </View>
                    {set.style === 2 && (
                        <View className="search-icon-list">
                            {set.iconList &&
                                set.iconList.map((icon, index) => (
                                    <Image
                                        src={icon.pic}
                                        onClick={() => {
                                            hotJumpUrl(icon);
                                        }}
                                        style={{ width: "36px", height: "36px" }}
                                    />
                                ))}
                        </View>
                    )}
                </View>
            </View>
        </View>
    );
};
