import React, { useState } from "react";
import Taro from "@tarojs/taro";
import { View } from "@tarojs/components";
import "./helper.less";
import { DDYNavigateTo } from "@/utils/route";
import { observer } from "mobx-react-lite";
import infoStore from "@/store/info-store";
export default observer(({ set = {}, commonSet = {} }) => {
    const [style, setStyle] = useState({
        marginTop: `${commonSet.marginTop}px`,
        marginBottom: `${commonSet.marginBottom}px`,
        backgroundColor: `#fff`,
    });
    if (infoStore.newUserProfile.whetherGroup) {
        return null;
    }
    return (
        <View className="helper" style={style}>
            <View className="helper-content">
                <View
                    className="helper-box"
                    onClick={() => {
                        DDYNavigateTo({ url: "/pages/wool-herd/index?join=false" });
                    }}
                ></View>
            </View>
        </View>
    );
});
