.notice {
    --notice-bar-font-size: 30px;
    .notice-content {
        padding: 15rpx 16rpx;

        .notice-style-mixin(@bg-color, @text-color) {
            display: flex;
            background: @bg-color; // 浅橙色背景
            color: @text-color; // 橙色文字
            font-size: 28px;
            border-radius: 32px;
            align-items: center; // 取消注释并启用此行
            padding: 12px;

            .notice-icon {
                margin-right: 36px;
                font-size: 36px;
                font-family: "iconfont";
                color: @text-color;
            }

            .notice-wrapper {
                flex: 1;
                overflow: hidden;
            }

            .notice-close {
                font-family: "iconfont";
                font-size: 22px;
                font-weight: 700;
                color: @text-color;

                &-wrappper {
                    position: absolute;
                    right: 12px;
                    width: 86px;
                    height: 86px;
                }
            }
            .van-notice-bar {
                color: @text-color!important;
            }

            .notice-text-container {
                position: relative;
                width: 100%;
                height: 48px; // 根据实际文字大小调整
                overflow: hidden;
                margin-top: 8px; // 可选：添加上边距，进一步增加间隔
            }

            .notice-title {
                margin-right: 35px;
                font-size: 30px;
            }

            .notice-text {
                font-size: 28px;
                position: absolute;
                white-space: nowrap;
                display: flex;
                animation: scrollText 15s linear infinite;
                line-height: 48px; // 添加行高，确保文字垂直居中

                span {
                    padding-right: 50px; // 两段文字之间的间距
                }
            }

            @keyframes scrollText {
                0% {
                    transform: translateX(0);
                }
                100% {
                    transform: translateX(-50%);
                }
            }
        }

        // 使用 mixin 应用不同样式
        &.notice-low {
            .notice-style-mixin(#FFFFFF, #B3B3B7);
        }

        &.notice-mid {
            .notice-style-mixin(#FFF2E0, #FFA14B);
        }

        &.notice-high {
            .notice-style-mixin(#FFEBF1, #FF0050);
        }
    }
}

.notice-set {
    padding: 20px 30px;

    .form-group {
        margin-bottom: 30px;

        label {
            display: block;
            margin-bottom: 16px;
        }
        span {
            color: gray;
        }

        input[type="text"],
        textarea {
            width: 100%;
            padding: 16px;
            border: 2px solid #ddd;
            border-radius: 8px;
        }

        .char-count {
            float: right;
            color: #999;
            font-size: 24px;
        }
    }

    .style-options {
        display: flex;
        gap: 20px;

        .style-option {
            padding: 20px;
            border: 2px solid #ddd;
            border-radius: 8px;
            cursor: pointer;

            &.active {
                border-color: #1890ff;
                color: #1890ff;
            }
        }
    }

    .link-list {
        margin-top: 20px;

        button {
            margin-top: 20px;
            padding: 10px 20px;
            background: #1890ff;
            color: #fff;
            border: none;
            border-radius: 8px;
            cursor: pointer;
        }
    }
}

@keyframes scroll {
    from {
        transform: translateX(100%);
    }
    to {
        transform: translateX(-100%);
    }
}
