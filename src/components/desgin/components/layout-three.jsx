import { View, Image } from "@tarojs/components";
import { Row, Col } from "@antmjs/vantui";
import React, { useState, useEffect } from "react";
import video from "./video";
import guide from "./guide";
import hotArea from "./hot-area";
import image from "./image";
import itemList from "./item-list";
import search from "./search";
import slider from "./slider";
import navigation from "./navigation";
import helper from "./helper";
import notice from "./notice";
import rankList from "./rank-list";
import modal from "./modal";

const components = {
    video: video,
    search: search,
    guide: guide,
    "hot-area": hotArea,
    "item-list": itemList,
    slider: slider,
    image: image,
    navigation: navigation,
    helper: helper,
    notice: notice,
    "rank-list": rankList,
    modal: modal,
};

const LayoutThree = ({ set = {}, commonSet = {}, modules = [] }) => {
    return (
        <View>
            <Row>
                <Col span={8}>
                    {modules[0].map((item, index) => {
                        const Component = components[item.name];
                        if (!Component) return null;
                        return <Component {...item} key={index} />;
                    })}
                </Col>
                <Col span={8}>
                    {modules[1].map((item, index) => {
                        const Component = components[item.name];
                        if (!Component) return null;
                        return <Component {...item} key={index} />;
                    })}
                </Col>
                <Col span={8}>
                    {modules[2].map((item, index) => {
                        const Component = components[item.name];
                        if (!Component) return null;
                        return <Component {...item} key={index} />;
                    })}
                </Col>
            </Row>
        </View>
    );
};

export default LayoutThree;
