import React, { useState } from "react";
import Taro from "@tarojs/taro";
import { View } from "@tarojs/components";
import "./notice.less";
import { NoticeBar } from "@antmjs/vantui";
import { hotJumpUrl } from "@/components/desgin/desginUtils";
export default ({ set = {}, commonSet = {} }) => {
    const [show, setShow] = useState(true);
    const [style, setStyle] = useState({
        marginTop: `${commonSet.marginTop}px`,
        marginBottom: `${commonSet.marginBottom}px`,
    });

    return (
        <>
            {show ? (
                <View className={"notice"} style={style}>
                    <View className="notice-content">
                        <View
                            className={`notice-content ${set.style}`}
                            onClick={() => {
                                hotJumpUrl(set);
                            }}
                        >
                            <View className="notice-icon">&#xe605;</View>
                            <View className="notice-wrapper">
                                <View className="notice-title">{set.title}</View>
                                <NoticeBar
                                    text={set.content}
                                    scrollable
                                    style={{ padding: 0 }}
                                    speed={30}
                                    background="transparent"
                                />
                            </View>
                            <View
                                className={"notice-close-wrappper"}
                                onClick={e => {
                                    setShow(false);
                                    e.stopPropagation();
                                }}
                            >
                                <View className="notice-close">&#xe621;</View>
                            </View>
                        </View>
                    </View>
                </View>
            ) : (
                ""
            )}
        </>
    );
};
