.search {
    padding: 10px;
    min-height: 10px;
    .search-content {
        display: flex;
        flex: 1;
        .search-box {
            width: 100%;
            // height: 36px;
            display: flex;
            align-items: center;
            border-radius: 36px;
            margin: 0 auto;
            background: #eee;
            line-height: 60px;
            font-size: 30px;
            box-sizing: border-box;
            padding-left: 50px;
            color: #999;
            position: relative;
        }
        .search-box:before {
            font-family: "iconfont";
            content: "\e61c";
            position: absolute;
            font-size: 20px;
            top: 0;
            left: 20px;
        }
        .search-icon-list {
            display: flex;
            flex-direction: row; // 水平方向排列
            justify-content: center; // 居中对齐
            align-items: center; // 垂直居中

            img {
                display: block; // 消除图片间的空隙
                margin: 0 5px;
            }
        }
    }
}
