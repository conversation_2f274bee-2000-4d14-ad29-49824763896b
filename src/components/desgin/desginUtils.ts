import { DDYNavigateTo, DDYSwitchTab, go } from "@/utils/route";
import Taro from "@tarojs/taro";
import DDYToast from "@/utils/toast";
import newApi from "@/utils/newApi";
import designStore from "@/store/desgin-store";

export function hotJumpUrl(item) {
    console.log("item", item);
    if (typeof item.url === "string") {
        go(item.url);
    } else {
        //新版本热区
        const { radioKey, radioValue } = item.url;
        switch (radioKey) {
            case "selectPage":
                jumpSelectPage(radioValue);
                break;
            case "modal":
                getModalInfo(radioValue);
                break;
            case "video":
                const { number, id } = radioValue;
                Taro.openChannelsActivity({
                    finderUserName: number,
                    feedId: id,
                    success: function (res) {},
                    fail: function (res) {
                        DDYToast.error("打开视频号失败");
                    },
                });
                break;
            case "customer_service":
                getKfAccountUrl(radioValue);
                break;

            case "goods":
                if (Array.isArray(radioValue) && radioValue.length > 0) {
                    DDYNavigateTo({ url: "/pages/goods_detail?id=" + radioValue[0] });
                }
                break;
            // 营销活动链接
            case "marketing_tools":
                // todo 字段名,跳转url待修改
                const { toolsId, activityId } = radioValue;
                if (!activityId) return;
                let activityUrl = "";
                const toolEnum = {
                    1: "group",
                    2: "share",
                    3: "fell",
                };
                // 设置跳转路径
                switch (toolEnum[toolsId]) {
                    case "group":
                        activityUrl = `/pages/activity/group-buy/index?id=${activityId}&toolsId=${toolsId}`;
                        break;
                    case "share":
                        activityUrl = `/pages/activity/group-buy/index?id=${activityId}&toolsId=${toolsId}`;
                        break;
                    case "fell":
                        activityUrl = `/pages/activity/fell-buy/index?id=${activityId}&toolsId=${toolsId}`;
                        break;
                }
                DDYNavigateTo({ url: activityUrl });
                break;
            default:
                break;
        }
    }
}

function getModalInfo(modalId) {
    newApi
        .getPopUpById({
            data: {
                id: modalId[0],
            },
            method: "POST",
        })
        .then(res => {
            console.log("res", res.dataJson);
            designStore.setShowModal(res.dataJson);
        });
}
function getKfAccountUrl(modalId) {
    newApi
        .getKfAccountUrl({
            data: {
                openKfid: modalId,
            },
            method: "POST",
        })
        .then(res => {
            Taro.openCustomerServiceChat({
                extInfo: { url: res.url },
                corpId: res.corpId,
                success: function (_) {},
                fail: function (error) {
                    console.log("openCustomerServiceChat", error);
                    DDYToast.error("打开客服失败");
                },
            });
        });
}
export function jumpSelectPage(pageUrl) {
    if (pageUrl.pageType == "defalut") {
        switch (pageUrl.id) {
            case "home":
                DDYSwitchTab({ url: "/pages/index/index" });
                break;
            case "category":
                DDYSwitchTab({ url: "/pages/home/<USER>" });
                break;
            case "shopCart":
                DDYSwitchTab({ url: "/pages/shop_cart/index" });
                break;
            case "register":
                DDYNavigateTo({ url: "/pages/register-price/main" });
                break;
            case "login":
                DDYNavigateTo({ url: "/pages/login/home" });
                break;
            case "helper":
                DDYNavigateTo({ url: "/pages/wool-herd/index" });
                break;
            case "task":
                DDYNavigateTo({ url: "/pages/task/task-center/index" });
                break;
            default:
                break;
        }
    } else if (pageUrl.pageType == "custom") {
        DDYNavigateTo({ url: `/pages/customer-design-page/index?pageId=${pageUrl.id}` });
    }
}
export const PreviewMode = {
    PREVIEW_MODE_PUBLISH: "0",
    PREVIEW_MODE_SAVE: "1",
};
