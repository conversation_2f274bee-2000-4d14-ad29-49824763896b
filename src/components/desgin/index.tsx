import { useState } from "react";
import { View } from "@tarojs/components";
import video from "./components/video";
import guide from "./components/guide";
import hotArea from "./components/hot-area";
import image from "./components/image";
import itemList from "./components/item-list";
import search from "./components/search";
import slider from "./components/slider";
import navigation from "./components/navigation";
import helper from "./components/helper";
import notice from "./components/notice";
import rankList from "./components/rank-list";
import layoutTwo from "./components/layout-two";
import layoutThree from "./components/layout-three";
import "./index.less";
import "./icon.less";
import { getStorage } from "@/utils/storage";
import { STORE_ID } from "@/utils/constant";
import { useDidShow, useRouter } from "@tarojs/taro";
import DiyModal from "./components/modal";
import newAPi from "@/utils/newApi";
import { observer } from "mobx-react-lite";
import { PreviewMode } from "@/components/desgin/desginUtils";

const components = {
    video: video,
    search: search,
    guide: guide,
    "hot-area": hotArea,
    "item-list": itemList,
    slider: slider,
    image: image,
    navigation: navigation,
    helper: helper,
    notice: notice,
    "rank-list": rankList,
    "layout-two": layoutTwo,
    "layout-three": layoutThree,
};

const DesignPage = observer(({ pageType, pageId, onGetPageConfig }) => {
    const [modules, setModules] = useState<any[]>([]);
    const { previewMode } = useRouter().params;
    const getData = () => {
        let data = { shopId: getStorage(STORE_ID), pageType: pageType };
        if (pageId) {
            data.id = pageId;
        }
        newAPi
            .getWxappPages({
                data: data,
                method: "POST",
                filterCheck: true,
            })
            .then(res => {
                try {
                    if (Array.isArray(res.data) && res.data.length > 0) {
                        const data =
                            previewMode === PreviewMode.PREVIEW_MODE_SAVE
                                ? res.data[0].pageData
                                : res.data[0].publishData;
                        if (data) {
                            const parseData = JSON.parse(data);
                            setModules(parseData.pages[0].modules);
                            onGetPageConfig &&
                                onGetPageConfig(parseData.pages[0], previewMode === PreviewMode.PREVIEW_MODE_SAVE);
                        } else {
                            setModules([]);
                        }
                    } else {
                        setModules([]);
                    }
                } catch (error) {
                    setModules([]);
                }
            });
    };

    useDidShow(() => {
        getData();
    });

    return (
        <View className="page">
            {modules.map((item, index) => {
                const Component = components[item.name];
                if (!Component) return null;
                return <Component {...item} key={index} />;
            })}
            <DiyModal />
        </View>
    );
});
export default DesignPage;
