import { Icon } from "@antmjs/vantui";
import { View, Text, Image } from "@tarojs/components";

import "./shop-item-list.less";
export default ({ list, hideShop = false }) => {
    return (
        <View className="shop-item-list">
            {hideShop ? null : (
                <View className="store-info">
                    <Icon name="shop-o" size="32rpx"></Icon>
                    <Text className="store-name">{list?.[0].shopName}</Text>
                </View>
            )}

            <View className="goods-list">
                {list.map(goodsItem => (
                    <View className="good-item">
                        <View className="good-img">
                            <Image className="good-img" src={goodsItem.skuImage} mode="scaleToFill" />
                        </View>
                        <View className="good-content">{goodsItem.itemName}</View>
                        <View className="good-info">
                            {goodsItem.originFee ? (
                                <View className="good-price">¥{goodsItem.originFee / goodsItem.quantity / 100}</View>
                            ) : null}
                            {goodsItem.quantity ? <View className="good-num">x{goodsItem.quantity}</View> : null}
                        </View>
                    </View>
                ))}
            </View>
        </View>
    );
};
