import { Icon } from "@antmjs/vantui";
import { View, Text, Image } from "@tarojs/components";

import "./shop-item-list.less";
export default ({ list, hideShop = false }) => {
    return (
        <View className="shop-item-list">
            {hideShop ? null : (
                <View className="store-info">
                    <Icon name="shop-o" size="32rpx"></Icon>
                    <Text className="store-name">{list?.[0].shopName}</Text>
                </View>
            )}

            <View className="goods-list">
                {list.map(goodsItem => (
                    <View className="good-item">
                        <View className="good-img">
                            <Image className="good-img" src={goodsItem.skuImage} mode="scaleToFill" />
                        </View>
                        <View>
                            <View className="good-content">{goodsItem.itemName}</View>
                            <View className="good-type">
                                {goodsItem.isBonded === 0 ? (
                                    <Image
                                        mode="aspectFit"
                                        style={{ width: "36.5px", height: "13pt" }}
                                        src="https://dante-img.oss-cn-hangzhou.aliyuncs.com/19198843262.png"
                                    />
                                ) : null}
                                {[1, 2].includes(goodsItem.isBonded) ? (
                                    <Image
                                        mode="aspectFit"
                                        style={{ width: "36.5px", height: "13px" }}
                                        src="https://dante-img.oss-cn-hangzhou.aliyuncs.com/19194973267.png"
                                    />
                                ) : null}
                            </View>
                            <View
                                style={{
                                    display: "flex",
                                    flexDirection: "row",
                                    alignItems: "flex-start",
                                    justifyContent: "space-between",
                                }}
                            >
                                <View className="good-spec">{goodsItem?.specification || ""}</View>
                            </View>
                        </View>

                        <View className="good-info">
                            <View className="good-price">
                                ¥{(goodsItem.originFee / goodsItem.quantity / 100).toFixed(2)}
                            </View>
                            <View className="good-num">x{goodsItem.quantity}</View>
                        </View>
                    </View>
                ))}
            </View>
        </View>
    );
};
