import DDYToast from "@/utils/toast";
import { View, Text } from "@tarojs/components";
import Taro from "@tarojs/taro";
import { Card, Icon } from "@antmjs/vantui";
import { ORDER_STATUS_TEXT } from "../pages/order/order-detail/components/order-main-card";
import OrderBottom from "../pages/order/order-detail/components/order-bottom";
import "./order-list-item.less";
import { shopOrderObj, shopOrderOperationObj } from "src/pages/order/order-detail/hooks/useDetail";

interface OrderItemBottomProps {
    showBottom: true;
    /**
     * 底部操作按钮 买家需要订单的参数
     */
    shopOrder: shopOrderObj;
    /**
     *  底部操作按钮权限控制来源
     */
    operations: shopOrderOperationObj[];
    /**
     * 退款
     */
    refund: boolean;
    /**
     * 刷新函数
     */
    onFresh: () => void;
}

type OrderItemCommonProps = OrderItemBottomProps & {
    shopName: string;
    isFirst: boolean;
    orderNo: string;
    // orderId: number
    // packageSn: string
    // showBottom?: boolean
    createdAt: string;
    orderStatus: number;
    goodList: GoodItems[];
    clickGo: () => void;
    orderPushErrorMessage?: string;
};

export type OrderItemProps = orderItemBuyerProps | orderItemSellerProps;

interface orderItemBuyerProps extends OrderItemCommonProps {
    mode: "buyer";
    showBottom: true;
    buyerEtr: {
        isCardError: boolean;
        isReal: boolean;
        /**
         * 实际金额
         */
        actualPrice: number;
        /**
         * 实际数量
         */
        actualCount: number;
    };
}

interface orderItemSellerProps extends OrderItemCommonProps {
    mode: "seller";
    sellerEtr: {
        /**
         * （待付款，应付款，实付款）金额
         */
        actualPrice: number;
        /**
         * 服务费
         */
        commission: number;
    };
}

interface GoodItems {
    quantity: number;
    flagList?: ("promotionActivityItem" | "promotionLimitedItem")[];
    price: number;
    goodName: string;
    goodImage: string;
}

export default (props: OrderItemProps) => {
    const {
        orderStatus,
        isFirst,
        mode,
        shopName,
        orderNo,
        createdAt,
        goodList,
        clickGo,
        showBottom,
        orderPushErrorMessage,
    } = props;

    return (
        <View
            className="order-list-item"
            onClick={() => {
                // DDYNavigateTo({
                //     url: `/pages/order/order-detail/index?id=${orderId}&packageSn=${packageSn}&status=${orderStatus}`,
                // });
                clickGo();
            }}
        >
            {!!orderPushErrorMessage && <View className="error-message">{orderPushErrorMessage}</View>}
            <View className="order-item-top">
                <View className="order-info">
                    {isFirst ? <View className="isFirst">首单</View> : null}
                    {mode !== "buyer" ? <Text className="order-shop-name">店铺名称: {shopName}</Text> : null}

                    <View className="order-cell">
                        <Text style={{ fontSize: "24rpx" }}>订单号: {orderNo}</Text>
                        <Icon
                            classPrefix={"iconfont"}
                            style={{ fontSize: "30rpx" }}
                            name={" icon-file-copy-line"}
                            size={32}
                            color={"#989898"}
                            onClick={e => {
                                e.stopPropagation();
                                Taro.setClipboardData({
                                    data: orderNo,
                                    success: function (res) {
                                        DDYToast.info("复制成功");
                                    },
                                });
                            }}
                        />
                    </View>
                    <Text>下单时间: {createdAt}</Text>
                </View>
                <View className="order-status">
                    {mode === "buyer" ? (
                        <>
                            {props.buyerEtr.isCardError && <Text className="order_state">身份证错误</Text>}
                            {props.buyerEtr.isReal && <Text className="order_state">待实名审核</Text>}
                            {!props.buyerEtr.isReal && !props.buyerEtr.isCardError && (
                                <Text className="order_state">{ORDER_STATUS_TEXT[orderStatus]}</Text>
                            )}
                        </>
                    ) : (
                        <Text className="order_state">{ORDER_STATUS_TEXT[orderStatus]}</Text>
                    )}
                </View>
            </View>
            <View className="order-item-good">
                {goodList.map(goodItem => (
                    <Card
                        renderNum={<View style={{ textAlign: "right" }}>数量 x{goodItem.quantity}</View>}
                        // tag="标签"
                        renderTag={goodItem.flagList?.map((flagItem, index) => (
                            <View className="label-box" key={index}>
                                {flagItem == "promotionActivityItem" && <View className="label-item">活动</View>}
                                {flagItem == "promotionLimitedItem" && <View className="label-item-limit">限定</View>}
                            </View>
                        ))}
                        price={`${goodItem.price === null ? "" : goodItem?.price?.toFixed(2)}`}
                        title={goodItem.goodName}
                        thumb={goodItem.goodImage}
                        thumbMode="scaleToFill"
                    />
                ))}
            </View>
            {mode !== "buyer" ? (
                <View className="order-income">
                    <View className="order-income-item">
                        <View className="title">
                            {orderStatus == 0 ? "待付款" : orderStatus == -13 ? "应付款" : "实付款"}
                        </View>
                        <Text className="value">{props.sellerEtr.actualPrice}</Text>
                    </View>
                    <View className="order-income-item">
                        <View className="title">预估服务费</View>
                        <Text className="value">{props.sellerEtr.commission}</Text>
                    </View>
                </View>
            ) : (
                <View className="order-total">
                    <Text className="total-text">共{props.buyerEtr.actualCount}件商品</Text>
                    <Text className="total-text" style={{ paddingLeft: "10rpx", display: "inline-block" }}>
                        {" "}
                        {orderStatus == 0 ? "待付款" : orderStatus == -13 ? "应付款" : "实付款"}:
                    </Text>
                    <Text className="total-price">￥{props.buyerEtr.actualPrice}</Text>
                </View>
            )}
            {showBottom && (
                <View className="order-item-bottom">
                    <OrderBottom
                        shopOrder={props.shopOrder}
                        operations={props.operations}
                        refund={props.refund}
                        onFresh={props.onFresh}
                    />
                </View>
            )}
        </View>
    );
};
