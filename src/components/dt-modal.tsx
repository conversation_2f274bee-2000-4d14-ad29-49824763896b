import react, { useImperativeHandle, forwardRef } from "react";
import { Dialog, Cell, DialogProps } from "@antmjs/vantui";
import { View } from "@tarojs/components";

const Dialog_ = Dialog.createOnlyDialog();
export interface DtModalRef {
    confirm: (confirmProps: DialogProps) => Promise<"confirm" | "cancel">;
}
export default forwardRef(({}, ref) => {
    const confirm = (confirmProps: DialogProps) => {
        return Dialog_.confirm(confirmProps);
    };

    useImperativeHandle(ref, () => {
        return {
            confirm,
        };
    });

    return (
        <View>
            <Dialog_ />
        </View>
    );
});
