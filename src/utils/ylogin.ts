import api from "@/utils/api";
import { USER_INFO, USER_SPECICAL_INFO, GUIDER_INFO, IS_LOGIN } from "@/utils/constant";
import Taro from "@tarojs/taro";
import { getStorage, setStorage } from "@/utils/storage";
import { DDYObject } from "src/type/common";
import { DDYNavigateTo, DDYReLaunch, DDYSwitchTab } from "@/utils/route";
import DDYToast from "@/utils/toast";

export function loginCheck() {
    return new Promise((resolve, reject) => {
        Taro.login({
            success: function (res) {
                if (res.code) {
                    api.wechatLogin({
                        data: {
                            wxCode: res.code,
                            //登录小程序的店铺微信小程序项目id（为0默认用自己的但丁商城小程序，为-1默认用自己的微分销商城消费端）
                            wxaProjectId: getStorage("projectId"),
                            identity: "ddsc",
                        },
                        filterCheck: true,
                    })
                        .then(data => {
                            let obj: DDYObject = {};
                            console.log("wxJsCode2Session..." + JSON.stringify(data));
                            if (data) {
                                setStorage("isLogin", 1);
                                if (data) {
                                    obj.openid = data.openId;
                                    obj.sessionId = data.sessionId;
                                    //存储openid
                                    setStorage(USER_SPECICAL_INFO, obj);
                                }
                                //只有卖家用户才有shopId userInfo.shopId=data.shopId;
                                setStorage(USER_INFO, data);
                                if (data.guider) {
                                    setStorage(GUIDER_INFO, data.userId);
                                }
                                console.log("用户信息为" + JSON.stringify(data));

                                if (data && data.hasMobile == false) {
                                    setStorage("IsNewUser", 1);
                                } else {
                                    setStorage("IsNewUser", 0);
                                }
                                resolve(data);
                                //返回回调函数
                            } else {
                                setStorage("isLogin", 0);
                                reject(Error("login fail"));
                            }
                        })
                        .catch(err => {
                            reject(err);
                            setStorage("isLogin", 0);
                            console.log("微信登录失败", err);
                        });
                } else {
                    setStorage("isLogin", 0);
                    reject(res);
                    console.log("获取用户信息失败" + res.errMsg);
                }
            },
            fail: function (res) {
                console.log("wx.login登录失败" + JSON.stringify(res));
                reject(res);
                setStorage("isLogin", 0);
            },
        });
    });
}

export const reLogin = () => {
    DDYToast.normal("重新登录中", "loading");
    return loginCheck().then(() => {
        if (getStorage("IsNewUser") === 1) {
            DDYReLaunch({ url: "/pages/my/bind-mobile/index?type=register" });
        } else {
            if (getStorage("returnUrlByPUserId")) {
                // 通过扫描普通二维码带参pUserId(设过会员价的用户ID),进来直接购买，登录后跳回商品详情页
                DDYNavigateTo({
                    url: getStorage("returnUrlByPUserId"),
                });
            } else {
                // DDYSwitchTab({
                //   url: "/pages/home/<USER>",
                // });
            }
        }
    });
};
export const localLoginMask = () => {
    return getStorage(IS_LOGIN) === 1;
};
// module.exports = {
//     loginCheck: loginCheck,
//     reLogin: reLogin
// }
