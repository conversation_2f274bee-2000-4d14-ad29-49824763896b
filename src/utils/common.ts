import Taro, { clearStorageSync } from "@tarojs/taro";
import api from "./api";
import { DDYNavigateTo, DDYRedirectTo, DDYReLaunch, DDYSwitchTab } from "./route";
import { getStorage, removeStorage, setStorage } from "./storage";
import DDYToast from "./toast";
import { IS_LOGIN, IS_REGISTER, LOGIN_BACK_URL, SING_BACK_URL } from "./constant";
import infoStore from "@/store/info-store";
import { useRouter } from "@tarojs/taro";

export function shallowEqual(objA: any, objB: any): boolean {
    //From: https://github.com/facebook/fbjs/blob/c69904a511b900266935168223063dd8772dfc40/packages/fbjs/src/core/shallowEqual.js
    if (is(objA, objB)) {
        return true;
    }
    if (typeof objA !== "object" || objA === null || typeof objB !== "object" || objB === null) {
        return false;
    }
    const keysA = Object.keys(objA);
    const keysB = Object.keys(objB);
    if (keysA.length !== keysB.length) {
        return false;
    }
    for (let i = 0; i < keysA.length; i++) {
        if (!Object.hasOwnProperty.call(objB, keysA[i]) || !is(objA[keysA[i]], objB[keysA[i]])) {
            return false;
        }
    }
    return true;
}

function is(x: any, y: any): boolean {
    // From: https://github.com/facebook/fbjs/blob/c69904a511b900266935168223063dd8772dfc40/packages/fbjs/src/core/shallowEqual.js
    if (x === y) {
        return x !== 0 || 1 / x === 1 / y;
    } else {
        return x !== x && y !== y;
    }
}

export function scene_decode(e, separator = ",", paramsSeparator = ":") {
    if (e === undefined) return {};
    let scene = decodeURIComponent(e),
        params = scene.split(separator),
        data = {};
    for (let i in params) {
        var val = params[i].split(paramsSeparator);
        val.length > 0 && val[0] && (data[val[0]] = val[1] || null);
    }
    return data;
}

export const queryInfo = (props: {
    callback?: (data) => void;
    go: boolean;
    showError?: boolean;
    goSign?: () => void;
}) => {
    const fn = props.goSign || goSign;
    api.queryStepInfo({
        method: "POST",
        data: {},
        contentType: "application/json",
        filterCheck: true,
    }).then(res => {
        if (res.success) {
            const data = res.data;
            props.callback && props.callback(data);
            if (!data.stepStatus) {
                if (!props.go) {
                    return;
                }
                if (!data.accountInfo.stepBindPhone) {
                    saveSignback();
                    DDYNavigateTo({
                        url: "/pages/my/bind-bank-phone/index",
                    });
                } else if (!data.accountInfo.stepBindAuth) {
                    saveSignback();
                    DDYNavigateTo({
                        url: "/pages/my/bank-auth-real/index",
                    });
                } else if (!data.accountInfo.stepBindFirstCard) {
                    saveSignback();
                    DDYNavigateTo({
                        url: "/pages/my/add-bank/index",
                    });
                } else {
                    fn();
                }
            } else {
                if (!props.go) {
                    return;
                }
                if (!data.accountInfo.stepBindFirstCard) {
                    saveSignback();
                    DDYNavigateTo({
                        url: "/pages/my/add-bank/index",
                    });
                } else {
                    fn();
                }
            }
        } else {
            if (props.showError) {
                DDYToast.info(res.errorMsg);
            }
        }
    });
};

const goSign = () => {
    saveSignback();
    DDYNavigateTo({
        url: "/pages/my/bind-result/index?type=success",
    });
};

export const saveSignback = () => {
    if (!getStorage(SING_BACK_URL)) {
        const routes = Taro.getCurrentPages();
        const url = routes[routes.length - 1].route;
        console.log("signBackUrl:", url);
        setStorage(SING_BACK_URL, url);
    }
};

export const removeSignback = () => {
    removeStorage(SING_BACK_URL);
};

export const isAlipay = () => {
    return process.env.TARO_ENV === "alipay";
};

export const isWeixin = () => {
    return process.env.TARO_ENV === "weapp";
};

export const getChannel = () => {
    let channel = 2;
    switch (process.env.TARO_ENV) {
        case "alipay":
            channel = 4;
            break;
        case "weapp":
            channel = 2;
            break;
        case "h5":
            channel = 3;
            break;
        default:
            break;
    }
    return channel;
};

export function getUrlParams(url: string) {
    try {
        // 如果url中没有?，直接返回空对象
        if (url.indexOf("?") === -1) {
            return {};
        }

        // 获取?后面的参数字符串
        const paramsStr = url.split("?")[1];

        // 将参数字符串按&分割成数组
        return paramsStr.split("&").reduce((params, param) => {
            // 将每个参数按=分割成key和value
            const [key, value] = param.split("=");
            // 解码并添加到结果对象中
            return {
                ...params,
                [key]: decodeURIComponent(value || ""),
            };
        }, {});
    } catch (e) {
        console.error("解析URL参数失败:", e);
        return {};
    }
}

// 登录成功后的跳转逻辑
export const loginSuccessRediction = async () => {
    const url = getStorage(LOGIN_BACK_URL);
    setStorage(IS_LOGIN, 1);
    try {
        await infoStore.getNewUserProfile();
    } catch (e) {
        DDYToast.info("获取个人信息失败");
    }
    if (url) {
        // 是否清空跳转
        const params = getUrlParams(url);
        console.log("登录成功后跳转params", params);
        const reLaunchFlag = !!params.reLaunchFlag;
        if (url.indexOf("pages/wool-herd/index") !== -1 || reLaunchFlag) {
            DDYReLaunch({
                url: "/" + url,
            });
        } else {
            DDYRedirectTo({
                url: "/" + url,
            });
        }
        setStorage(LOGIN_BACK_URL, "");
    } else {
        DDYReLaunch({
            url: "/pages/index/index",
        });
    }
};

// 注册成功后的

export const registerSuccess = () => {
    setStorage(IS_REGISTER, 1);
};

export function isVideoFile(url) {
    // 提取文件扩展名（不区分大小写）
    const videoExtensions = ["mp4", "avi", "mov", "mkv", "webm", "flv", "wmv", "mpeg", "mpg", "3gp", "ogg", "ogv"];

    // 获取URL中的文件扩展名
    const extension = url.split(".").pop().toLowerCase().split(/[?#]/)[0];

    return videoExtensions.includes(extension);
}

export function useSafeRouter() {
    const router = useRouter();

    const getParam = key => {
        const val = router.params?.[key];
        return val === "undefined" || val === undefined ? undefined : val;
    };
    if (router.params) {
        for (let key in router.params) {
            router.params[key] = getParam(key);
        }
    }
    console.log("router", router.params);
    return {
        ...router,
        getParam,
    };
}

export const debounce = (fn: Function, delay = 500) => {
    let timer: NodeJS.Timeout | null = null;

    return function (...args: any[]) {
        if (timer) {
            return;
        }

        fn.apply(this, args);

        timer = setTimeout(() => {
            timer = null;
        }, delay);
    };
};
