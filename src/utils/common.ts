import Taro from "@tarojs/taro";
import api from "./api";
import { DDYNavigateTo } from "./route";
import { getStorage, removeStorage, setStorage } from "./storage";
import DDYToast from "./toast";
import { SING_BACK_URL } from "./constant";
import { PROJECT_CONFIG } from "./env";

export function shallowEqual(objA: any, objB: any): boolean {
    //From: https://github.com/facebook/fbjs/blob/c69904a511b900266935168223063dd8772dfc40/packages/fbjs/src/core/shallowEqual.js
    if (is(objA, objB)) {
        return true;
    }
    if (typeof objA !== "object" || objA === null || typeof objB !== "object" || objB === null) {
        return false;
    }
    const keysA = Object.keys(objA);
    const keysB = Object.keys(objB);
    if (keysA.length !== keysB.length) {
        return false;
    }
    for (let i = 0; i < keysA.length; i++) {
        if (!Object.hasOwnProperty.call(objB, keysA[i]) || !is(objA[keysA[i]], objB[keysA[i]])) {
            return false;
        }
    }
    return true;
}

function is(x: any, y: any): boolean {
    // From: https://github.com/facebook/fbjs/blob/c69904a511b900266935168223063dd8772dfc40/packages/fbjs/src/core/shallowEqual.js
    if (x === y) {
        return x !== 0 || 1 / x === 1 / y;
    } else {
        return x !== x && y !== y;
    }
}

export function scene_decode(e, separator = ",", paramsSeparator = ":") {
    if (e === undefined) return {};
    let scene = decodeURIComponent(e),
        params = scene.split(separator),
        data = {};
    for (let i in params) {
        var val = params[i].split(paramsSeparator);
        val.length > 0 && val[0] && (data[val[0]] = val[1] || null);
    }
    return data;
}

export const queryInfo = (props: {
    callback?: (data) => void;
    go: boolean;
    showError?: boolean;
    goSign?: () => void;
}) => {
    const fn = props.goSign || goSign;
    api.queryStepInfo({
        method: "POST",
        data: {},
        contentType: "application/json",
        filterCheck: true,
    }).then(res => {
        if (res.success) {
            const data = res.data;
            props.callback && props.callback(data);
            if (!data.stepStatus) {
                if (!props.go) {
                    return;
                }
                if (!data.accountInfo.stepBindPhone) {
                    saveSignback();
                    DDYNavigateTo({
                        url: "/pages/my/bind-bank-phone/index",
                    });
                } else if (!data.accountInfo.stepBindAuth) {
                    saveSignback();
                    DDYNavigateTo({
                        url: "/pages/my/bank-auth-real/index",
                    });
                } else if (!data.accountInfo.stepBindFirstCard) {
                    saveSignback();
                    DDYNavigateTo({
                        url: "/pages/my/add-bank/index",
                    });
                } else {
                    fn();
                }
            } else {
                if (!props.go) {
                    return;
                }
                if (!data.accountInfo.stepBindFirstCard) {
                    saveSignback();
                    DDYNavigateTo({
                        url: "/pages/my/add-bank/index",
                    });
                } else {
                    fn();
                }
            }
        } else {
            if (props.showError) {
                DDYToast.info(res.errorMsg);
            }
        }
    });
};

const goSign = () => {
    saveSignback();
    DDYNavigateTo({
        url: "/pages/my/bind-result/index?type=success",
    });
};

export const saveSignback = () => {
    if (!getStorage(SING_BACK_URL)) {
        const routes = Taro.getCurrentPages();
        const url = routes[routes.length - 1].route;
        console.log("signBackUrl:", url);
        setStorage(SING_BACK_URL, url);
    }
};

export const removeSignback = () => {
    removeStorage(SING_BACK_URL);
};

export const isAlipay = () => {
    return process.env.TARO_ENV === "alipay";
};

export const isWeixin = () => {
    return process.env.TARO_ENV === "weapp";
};

export const getChannel = () => {
    let channel = 2;
    switch (process.env.TARO_ENV) {
        case "alipay":
            channel = 4;
            break;
        case "weapp":
            channel = 2;
            break;
        case "h5":
            channel = 3;
            break;
        default:
            break;
    }
    return channel;
};
/**
 * url: 传入形如 "show/a?id=1"这样的参数
 */
export const getShareUrl = (url: string) => {
    const QR_CODE_URL = PROJECT_CONFIG.QR_CODE_URL;
    return QR_CODE_URL + url;
};
