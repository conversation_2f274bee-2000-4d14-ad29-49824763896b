import Taro from "@tarojs/taro";
import { DDYObject } from "src/type/common";
import getHeader from "./server/header";
import DDYToast from "./toast";
import { getStorage } from "@/utils/storage";
import { IS_LOGIN, STORE_ID } from "@/utils/constant";
import { jumpLogin } from "@/utils/PageUtils";
import { mall_event, mall_event_key, reportEvent } from "@/utils/track-report";

export interface requestParams {
    data: DDYObject;
    url: string;
    /**
     * showLoad
     * 是否需要展示loading，优化交互
     */
    showLoad?: boolean;
    /**
     * showError
     * 是否需要展示错误信息，默认展示
     */
    showError?: boolean;
    contentType?: string;
    /**
     * method: 请求方法
     */
    method?: keyof Method;
    /**
     * filterCheck
     * 兼容老的接口，因为后端没有对业务成功或失败的判断，而是直接返回数据
     */
    filterCheck?: boolean;
    /**
     * boolean
     * 是否默认跳转登录
     * default: 是
     */
    isDefaultGoLogin?: boolean;
}

interface Method {
    /** HTTP 请求 OPTIONS */
    OPTIONS;
    /** HTTP 请求 GET */
    GET;
    /** HTTP 请求 HEAD */
    HEAD;
    /** HTTP 请求 POST */
    POST;
    /** HTTP 请求 PUT */
    PUT;
    /** HTTP 请求 PATCH */
    PATCH;
    /** HTTP 请求 DELETE */
    DELETE;
    /** HTTP 请求 TRACE */
    TRACE;
    /** HTTP 请求 CONNECT */
    CONNECT;
}

export interface resolveData {
    code: number;
    data: any;
    msg: string;
    message?: string;
    errorMsg?: string;
}

export interface rejectData {
    error: string;
    message: string;
    path: string;
    status: number;
    timestamp: number;
    errorMsg?: string;
}

// 早期判断错误的方法，但是没啥实际用处
function isResolve(options: any): options is resolveData {
    return options && options?.code !== undefined;
}

type requestFun = (options: requestParams) => Promise<any>;
/**
 * 只要成功接收到服务器返回，无论 statusCode 是多少，都会进入 success 回调。请开发者根据业务逻辑对返回值进行判断.
 *
 * https://developers.weixin.qq.com/miniprogram/dev/framework/ability/network.html#%E5%9B%9E%E8%B0%83%E5%87%BD%E6%95%B0
 * @param status
 */
const validateStatus = function validateStatus(status) {
    return (status >= 200 && status < 300) || status === 304;
};
const loginInterceptor = function (resultData, isDefaultGoLogin) {
    if (
        resultData?.message === "账号处于非登录状态" ||
        resultData?.message === "用户未登录" ||
        resultData?.errorMsg === "用户未登录" ||
        resultData?.message === "未登录, 请登录" ||
        resultData.code === -1001
    ) {
        console.log("用户未登录-拦截中 IS_LOGIN", getStorage(IS_LOGIN));
        // if (getStorage(IS_LOGIN) === 1) {
        //     reLogin();
        // } else {
        isDefaultGoLogin && jumpLogin();
        // }
        return true;
    }
    return false;
};
const toastErrorMessage = resultData => {
    DDYToast.info(resultData.message || resultData.errorMsg || "请求异常");
};
// eslint-disable-next-line import/no-mutable-exports
let request;
const request_wx: requestFun = options => {
    return new Promise((resolve, reject) => {
        const { showError = true, method = "GET", isDefaultGoLogin = true } = options;
        const _header = getHeader(options);

        if (options.showLoad) {
            DDYToast.showLoading("加载中");
        }
        Taro.request({
            ...options,
            header: _header,
            data: {
                ...options.data,
                shopId: Number(getStorage(STORE_ID)),
            },
            method: method,
            success: (result: Taro.request.SuccessCallbackResult<resolveData | rejectData>) => {
                if (options.showLoad) {
                    DDYToast.hideLoading();
                }
                const { statusCode, data: resultData } = result;
                if (loginInterceptor(resultData, isDefaultGoLogin)) {
                    reject(resultData);
                    reportEvent(
                        mall_event.MALL_EVENT_PERF_MODULE_MONITOR,
                        new Map()
                            .set(mall_event_key.MALL_KEY_PERF_MODULE_ID, "request")
                            .set(mall_event_key.MALL_KEY_PERF_MONITOR_ID, options.url)
                            .set(mall_event_key.MALL_KEY_PERF_MONITOR_LEVEL, 0)
                            .set(mall_event_key.MALL_KEY_PERF_ERROR_CODE, statusCode)
                            .set(
                                mall_event_key.MALL_KEY_PERF_ERROR_MSG,
                                `登录拦截,异常信息：\n，${resultData?.message || resultData?.errorMsg || "请求异常"}`,
                            )
                            .set(mall_event_key.MALL_KEY_PERF_COST_TIME, 0),
                    );
                    return;
                }
                //验证请求statusCode是否成功
                if (!validateStatus(statusCode)) {
                    showError && toastErrorMessage(resultData);
                    reject(resultData);
                    reportEvent(
                        mall_event.MALL_EVENT_PERF_MODULE_MONITOR,
                        new Map()
                            .set(mall_event_key.MALL_KEY_PERF_MODULE_ID, "request")
                            .set(mall_event_key.MALL_KEY_PERF_MONITOR_ID, options.url)
                            .set(mall_event_key.MALL_KEY_PERF_MONITOR_LEVEL, 0)
                            .set(mall_event_key.MALL_KEY_PERF_ERROR_CODE, statusCode)
                            .set(
                                mall_event_key.MALL_KEY_PERF_ERROR_MSG,
                                `服务返回statusCode 不满足(status >= 200 && status < 300) || status === 304，异常信息：${
                                    resultData?.message || resultData?.errorMsg || "请求异常"
                                }`,
                            )
                            .set(mall_event_key.MALL_KEY_PERF_COST_TIME, 0),
                    );
                    return;
                }
                // 后端有些老的接口没有成功或者失败的状态判断，只有数据，以此兼容
                if (options.filterCheck) {
                    resolve(resultData);
                    return;
                }
                if (isResolve(resultData)) {
                    if (resultData.code === 0 || resultData.code === 200) {
                        resolve(resultData.data);
                    } else {
                        reportEvent(
                            mall_event.MALL_EVENT_PERF_MODULE_MONITOR,
                            new Map()
                                .set(mall_event_key.MALL_KEY_PERF_MODULE_ID, "request")
                                .set(mall_event_key.MALL_KEY_PERF_MONITOR_ID, options.url)
                                .set(mall_event_key.MALL_KEY_PERF_MONITOR_LEVEL, 0)
                                .set(mall_event_key.MALL_KEY_PERF_ERROR_CODE, -1)
                                .set(
                                    mall_event_key.MALL_KEY_PERF_ERROR_MSG,
                                    `服务返回code不等于0，${resultData?.message || resultData?.errorMsg || "请求异常"}`,
                                )
                                .set(mall_event_key.MALL_KEY_PERF_COST_TIME, 0),
                        );
                        showError && toastErrorMessage(resultData);
                        reject(result);
                    }
                } else {
                    showError && toastErrorMessage(resultData);
                    reportEvent(
                        mall_event.MALL_EVENT_PERF_MODULE_MONITOR,
                        new Map()
                            .set(mall_event_key.MALL_KEY_PERF_MODULE_ID, "request")
                            .set(mall_event_key.MALL_KEY_PERF_MONITOR_ID, options.url)
                            .set(mall_event_key.MALL_KEY_PERF_MONITOR_LEVEL, 0)
                            .set(mall_event_key.MALL_KEY_PERF_ERROR_CODE, -1)
                            .set(mall_event_key.MALL_KEY_PERF_ERROR_MSG, "请求异常：返回数据格式与处理不一致")
                            .set(mall_event_key.MALL_KEY_PERF_COST_TIME, 0),
                    );
                    console.log("请求异常", options.url, options.filterCheck);
                    reject(result);
                }
            },
            fail: err => {
                // @ts-ignore
                const { statusCode, data: resultData } = err;
                if (options.showLoad) {
                    DDYToast.hideLoading();
                }
                try {
                    let mall_key_perf_error_msg = JSON.stringify(err);
                    reportEvent(
                        mall_event.MALL_EVENT_PERF_MODULE_MONITOR,
                        new Map()
                            .set(mall_event_key.MALL_KEY_PERF_MODULE_ID, "request")
                            .set(mall_event_key.MALL_KEY_PERF_MONITOR_ID, options.url)
                            .set(mall_event_key.MALL_KEY_PERF_MONITOR_LEVEL, 0)
                            .set(mall_event_key.MALL_KEY_PERF_ERROR_CODE, -1)
                            .set(mall_event_key.MALL_KEY_PERF_ERROR_MSG, mall_key_perf_error_msg)
                            .set(mall_event_key.MALL_KEY_PERF_COST_TIME, 0),
                    );
                } catch (e) {}
                showError && DDYToast.info(err.errMsg || "请求异常");
                reject(err);
            },
        });
    });
};
const request_alipay: requestFun = options => {
    return new Promise((resolve, reject) => {
        const { showError = true, method = "GET", isDefaultGoLogin = true } = options;
        const _header = getHeader(options);
        if (options.showLoad) {
            DDYToast.showLoading("加载中");
        }
        Taro.request({
            ...options,
            header: _header,
            data: {
                ...options.data,
                shopId: Number(getStorage(STORE_ID)),
            },
            enableCookie: true,
            method: method,
            success: (result: Taro.request.SuccessCallbackResult<resolveData | rejectData>) => {
                if (options.showLoad) {
                    DDYToast.hideLoading();
                }
                const { statusCode, data: resultData } = result;
                if (loginInterceptor(resultData, isDefaultGoLogin)) {
                    reject(resultData);
                    reportEvent(
                        mall_event.MALL_EVENT_PERF_MODULE_MONITOR,
                        new Map()
                            .set(mall_event_key.MALL_KEY_PERF_MODULE_ID, "request")
                            .set(mall_event_key.MALL_KEY_PERF_MONITOR_ID, options.url)
                            .set(mall_event_key.MALL_KEY_PERF_MONITOR_LEVEL, 0)
                            .set(mall_event_key.MALL_KEY_PERF_ERROR_CODE, statusCode)
                            .set(
                                mall_event_key.MALL_KEY_PERF_ERROR_MSG,
                                `登录拦截,异常信息：\n，${resultData?.message || resultData?.errorMsg || "请求异常"}`,
                            )
                            .set(mall_event_key.MALL_KEY_PERF_COST_TIME, 0),
                    );
                    return;
                }
                //验证请求statusCode是否成功
                if (!validateStatus(statusCode)) {
                    showError && toastErrorMessage(resultData);
                    reject(resultData);
                    reportEvent(
                        mall_event.MALL_EVENT_PERF_MODULE_MONITOR,
                        new Map()
                            .set(mall_event_key.MALL_KEY_PERF_MODULE_ID, "request")
                            .set(mall_event_key.MALL_KEY_PERF_MONITOR_ID, options.url)
                            .set(mall_event_key.MALL_KEY_PERF_MONITOR_LEVEL, 0)
                            .set(mall_event_key.MALL_KEY_PERF_ERROR_CODE, statusCode)
                            .set(
                                mall_event_key.MALL_KEY_PERF_ERROR_MSG,
                                `服务返回statusCode 不满足(status >= 200 && status < 300) || status === 304，异常信息：${
                                    resultData?.message || resultData?.errorMsg || "请求异常"
                                }`,
                            )
                            .set(mall_event_key.MALL_KEY_PERF_COST_TIME, 0),
                    );
                    return;
                }
                // 后端有些老的接口没有成功或者失败的状态判断，只有数据，以此兼容
                if (options.filterCheck) {
                    resolve(resultData);
                    return;
                }
                if (isResolve(resultData)) {
                    if (resultData.code === 0 || resultData.code === 200) {
                        resolve(resultData.data);
                    } else {
                        reportEvent(
                            mall_event.MALL_EVENT_PERF_MODULE_MONITOR,
                            new Map()
                                .set(mall_event_key.MALL_KEY_PERF_MODULE_ID, "request")
                                .set(mall_event_key.MALL_KEY_PERF_MONITOR_ID, options.url)
                                .set(mall_event_key.MALL_KEY_PERF_MONITOR_LEVEL, 0)
                                .set(mall_event_key.MALL_KEY_PERF_ERROR_CODE, -1)
                                .set(
                                    mall_event_key.MALL_KEY_PERF_ERROR_MSG,
                                    `服务返回code不等于0，${resultData?.message || resultData?.errorMsg || "请求异常"}`,
                                )
                                .set(mall_event_key.MALL_KEY_PERF_COST_TIME, 0),
                        );
                        showError && toastErrorMessage(resultData);
                        reject(result);
                    }
                } else {
                    showError && toastErrorMessage(resultData);
                    reportEvent(
                        mall_event.MALL_EVENT_PERF_MODULE_MONITOR,
                        new Map()
                            .set(mall_event_key.MALL_KEY_PERF_MODULE_ID, "request")
                            .set(mall_event_key.MALL_KEY_PERF_MONITOR_ID, options.url)
                            .set(mall_event_key.MALL_KEY_PERF_MONITOR_LEVEL, 0)
                            .set(mall_event_key.MALL_KEY_PERF_ERROR_CODE, -1)
                            .set(mall_event_key.MALL_KEY_PERF_ERROR_MSG, "请求异常：返回数据格式与处理不一致")
                            .set(mall_event_key.MALL_KEY_PERF_COST_TIME, 0),
                    );
                    console.log("请求异常", options.url, options.filterCheck);
                    reject(result);
                }
            },
            fail: err => {
                // @ts-ignore
                const { statusCode, data: resultData } = err;
                if (options.showLoad) {
                    DDYToast.hideLoading();
                }
                if (loginInterceptor(resultData, isDefaultGoLogin)) {
                    reject(resultData);
                    reportEvent(
                        mall_event.MALL_EVENT_PERF_MODULE_MONITOR,
                        new Map()
                            .set(mall_event_key.MALL_KEY_PERF_MODULE_ID, "request")
                            .set(mall_event_key.MALL_KEY_PERF_MONITOR_ID, options.url)
                            .set(mall_event_key.MALL_KEY_PERF_MONITOR_LEVEL, 0)
                            .set(mall_event_key.MALL_KEY_PERF_ERROR_CODE, statusCode)
                            .set(
                                mall_event_key.MALL_KEY_PERF_ERROR_MSG,
                                `登录拦截,异常信息：\n，${resultData?.message || resultData?.errorMsg || "请求异常"}`,
                            )
                            .set(mall_event_key.MALL_KEY_PERF_COST_TIME, 0),
                    );
                    return;
                }
                if (!validateStatus(statusCode)) {
                    showError && toastErrorMessage(resultData);
                    reject(resultData);
                    reportEvent(
                        mall_event.MALL_EVENT_PERF_MODULE_MONITOR,
                        new Map()
                            .set(mall_event_key.MALL_KEY_PERF_MODULE_ID, "request")
                            .set(mall_event_key.MALL_KEY_PERF_MONITOR_ID, options.url)
                            .set(mall_event_key.MALL_KEY_PERF_MONITOR_LEVEL, 0)
                            .set(mall_event_key.MALL_KEY_PERF_ERROR_CODE, statusCode)
                            .set(
                                mall_event_key.MALL_KEY_PERF_ERROR_MSG,
                                `服务返回statusCode 不满足(status >= 200 && status < 300) || status === 304，异常信息：${
                                    resultData?.message || resultData?.errorMsg || "请求异常"
                                }`,
                            )
                            .set(mall_event_key.MALL_KEY_PERF_COST_TIME, 0),
                    );
                    return;
                }
                try {
                    let mall_key_perf_error_msg = JSON.stringify(err);
                    reportEvent(
                        mall_event.MALL_EVENT_PERF_MODULE_MONITOR,
                        new Map()
                            .set(mall_event_key.MALL_KEY_PERF_MODULE_ID, "request")
                            .set(mall_event_key.MALL_KEY_PERF_MONITOR_ID, options.url)
                            .set(mall_event_key.MALL_KEY_PERF_MONITOR_LEVEL, 0)
                            .set(mall_event_key.MALL_KEY_PERF_ERROR_CODE, -1)
                            .set(mall_event_key.MALL_KEY_PERF_ERROR_MSG, mall_key_perf_error_msg)
                            .set(mall_event_key.MALL_KEY_PERF_COST_TIME, 0),
                    );
                } catch (e) {}
                showError && DDYToast.info(err.errMsg || "请求异常");
                reject(err);
            },
        });
    });
};
if (process.env.TARO_ENV === "alipay") {
    request = request_alipay;
} else {
    request = request_wx;
}
export default request;
