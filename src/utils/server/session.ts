import { getStorage, setStorage } from "../storage";
import { USER_SPECICAL_INFO } from "../constant";

/**
 * 获取发现好商品接口
 * @param  {[type]} params [description]
 * @return {[type]}        [description]
 */

export const setSession = (param: any) => {
    setStorage(USER_SPECICAL_INFO, param);
};

export const getSession = () => {
    const user = getStorage(USER_SPECICAL_INFO);
    return user;
};

export const getSessionId = () => {
    const user = getStorage(USER_SPECICAL_INFO);
    return user.sessionId;
};
