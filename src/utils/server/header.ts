import { getSessionId } from "./session";
import { getStorage } from "@/utils/storage";
import { STORE_ID } from "@/utils/constant";
import { PROJECT_CONFIG } from "@/utils/env";

const getHeader = params => {
    let sessionId = getSessionId();
    // console.log("wxRequest sessionId:", sessionId);
    let header = {};
    if (sessionId) {
        sessionId = "msid=" + sessionId;
        header = {
            "Content-Type": params.contentType || "application/json",
            Cookie: sessionId,
            FromSource: "wechatMini",
            "x-app-type": process.env.TARO_ENV === "weapp" ? 0 : 1,
        };
    } else {
        header = {
            "Content-Type": params.contentType || "application/json",
            FromSource: "wechatMini",
            "x-app-type": process.env.TARO_ENV === "weapp" ? 0 : 1,
        };
    }
    header = {
        ...header,
        "x-shop-id": getStorage(STORE_ID),
        "x-app-id": PROJECT_CONFIG.APP_ID,
        "x-project-id": PROJECT_CONFIG.PROJECT_ID,
    };
    return header;
};
export default getHeader;
