import { getSessionId } from "./session";

const getHeader = params => {
    let sessionId = getSessionId();
    // console.log("wxRequest sessionId:", sessionId);
    let header = {};
    if (sessionId) {
        sessionId = "msid=" + sessionId;
        header = {
            "Content-Type": params.contentType || "application/json",
            Cookie: sessionId,
            FromSource: "wechatMini",
            "x-app-type": process.env.TARO_ENV === "weapp" ? 0 : 1,
        };
    } else {
        header = {
            "Content-Type": params.contentType || "application/json",
            FromSource: "wechatMini",
            "x-app-type": process.env.TARO_ENV === "weapp" ? 0 : 1,
        };
    }
    return header;
};
export default getHeader;
