import request, { requestParams } from "../utils/request";
import { PROJECT_CONFIG } from "./env";
const host = PROJECT_CONFIG.API_MALL; //正式https://m.mall.yang800.com/backend,测试https://mtest.mall.yang800.cn/backend
// const hostMock = "http://yapi.yang800.cn/mock/116"
//测试http://m.mall.c0d5363253401480d8190afc3e7977570.cn-hangzhou.alicontainer.com/backend
//测试环境，生产环境一定要加/backend,const host = 'https://m.mall.yang800.com/backend'

type wxRequestParams = Omit<requestParams, "url">;

const wxRequest = (params: wxRequestParams, url: string) => {
    return request({ ...params, url: url });
};

const getHomeAd = (params: wxRequestParams) => {
    return wxRequest(params, host + "/api/adv/img/wxaShopImg");
};

const getShopHomeData = (params: wxRequestParams) => {
    return wxRequest(params, host + "/xhr/html/home/<USER>");
};
const getAllCatogry = (params: wxRequestParams) => {
    return wxRequest(params, host + "/api/seller/shopCategories/fullTree");
}; //获取商品的类目

const getClassifyData = (params: wxRequestParams) => {
    console.log("get classify data ====");
    //  return wxRequest(params, host + '/xhr/goods/listPage.json')
    return wxRequest(params, host + "/api/search-in-shop");
}; //获取店主精选的商品list

//
const getClassifyAd = (params: wxRequestParams) => {
    return wxRequest(params, host + "/xhr/adv/img/storeChoiceness");
}; //获取精选的广告图列表

const updateUserInfo = (params: wxRequestParams) => wxRequest(params, host + "/xhr/user/updateInfo.json");
const getCartNumber = (params: wxRequestParams) => wxRequest(params, host + "/api/wxa/carts/count");
const getProfitStatistics = (params: wxRequestParams) => wxRequest(params, host + "/api/subStore/getProfitStatistics");
const getBalance = (params: wxRequestParams) => wxRequest(params, host + "/api/accountStatement/findRemainAmount");
const getBankCard = (params: wxRequestParams) =>
    wxRequest(params, host + "/api/withdrawProfitApply/findDefaultWithdrawAccount");

const getOrderCount = (params: wxRequestParams) => wxRequest(params, host + "/xhr/user/getUserOrderCount.json");

const getDiscoverList = (params: wxRequestParams) =>
    wxRequest(params, host + "/goods/list?cateidOne=1&cateidTwo=0&price=0&sales=2");

//微信的jscode换取sessionKey
const wxJsCode2Session = (params: wxRequestParams) => wxRequest(params, host + "/api/wechat/jscode2session");
const user2session = (params: wxRequestParams) => wxRequest(params, host + "/api/wechat/user2session?jsoncallback=?");

//商品接口---begin
//首页发现商品接口
const hostGoodsList = (params: wxRequestParams) => wxRequest(params, host + "/api/home/<USER>");
const getHomeDisvocerList = (params: wxRequestParams) => wxRequest(params, host + "/api/mall/discoverList");
//查询商品列表
const getGoodsList = (params: wxRequestParams) => wxRequest(params, host + "/api/mall/searchGoodsList");

//查询商品详情信息
const goodsDetail = (params: wxRequestParams) => wxRequest(params, host + "/api/item/" + params.data.ids + "/for-view");
const goodsDetailByPid = (params: wxRequestParams) =>
    wxRequest(params, host + "/api/item/" + params.data.ids + "/membershipByPid/for-view");
//商品详细页-商品详情
const goodsDetailInfo = (params: wxRequestParams) =>
    wxRequest(params, host + "/api/item/" + params.data.itemId + "/detail-info");
//const goodsDetailInfo = (params: wxRequestParams) => wxRequest(params, host + '/api/item/5017/detail-info');
//商品加入购物车
const addCart = (params: wxRequestParams) => wxRequest(params, host + "/api/carts");
//用户的购物车商品列表
const cartList = (params: wxRequestParams) => wxRequest(params, host + "/api/wxa/user/cart");
//购物车的商品选中状态
const cartCheck = (params: wxRequestParams) => wxRequest(params, host + "/api/mall/goodsCart/check");
//购物车的商品删除
const cartDel = (params: wxRequestParams) => wxRequest(params, host + "/api/carts/batchDelete");
//购物车的商品数量更新
const cartUpdateNum = (params: wxRequestParams) => wxRequest(params, host + "/api/carts");
//直接购买商品
const preOrder = (params: wxRequestParams) => wxRequest(params, host + "/api/mall/goodsOrder/commitData");

//支付前生成订单
const saveByCart = (params: wxRequestParams) => wxRequest(params, host + "/api/mall/goodsOrder/saveByCart");
// 修改个人信息
const saveUserInfo = (params: wxRequestParams) => wxRequest(params, host + "/api/user/my-profile");

//支付统一下单
const toPay = (params: wxRequestParams) => wxRequest(params, host + "/wepay/toPay");

//商品收藏
const goodsFavorite = (params: wxRequestParams) => wxRequest(params, host + "/api/mall/goodsFavorite/add");

//商品收藏删除
const goodsUnFavorite = (params: wxRequestParams) => wxRequest(params, host + "/api/mall/goodsFavorite/delete");

//商品是否已收藏
const goodsIsFavorite = (params: wxRequestParams) =>
    wxRequest(params, host + "/api/mall/goodsFavorite/goodsIsFavorite");

//商品接口---end

//用户相关信息--begin
//用户的当天签到信息
const userSginInfo = (params: wxRequestParams) => wxRequest(params, host + "/api/userSign/signInfo");

// 确认订单获取数据
const preCheck = (params: wxRequestParams) => wxRequest(params, host + "/api/order/preview/check");
const initOrder = (params: wxRequestParams) => wxRequest(params, host + "/api/order/preview");
const initOrderByPid = (params: wxRequestParams) => wxRequest(params, host + "/api/membershipByPid/order/preview");

const submitOrder = (params: wxRequestParams) => wxRequest(params, host + "/api/order");
//目前消息
const getNoticebar = (params: wxRequestParams) => wxRequest(params, host + "/api/msg/current");
//消息类别
const getNoticebarList = (params: wxRequestParams) => wxRequest(params, host + "/api/msg/list");
//判断是否有消息
const getUnread = (params: wxRequestParams) => wxRequest(params, host + "/api/msg/unread-main");
const submitOrderByPid = (params: wxRequestParams) =>
    wxRequest(params, host + "/api/membershipByPid/order?pid=" + params.data.pid);

//const payOrder=(params) => wxRequest(params, host + '/xhr/order/pay.json');
const payOrder = (params: wxRequestParams) => wxRequest(params, host + "/api/order/pay");

const doSign = (params: wxRequestParams) => wxRequest(params, host + "/api/userSign/doSign");
//获取最近七天签到情况
const getSignDate = (params: wxRequestParams) => wxRequest(params, host + "/api/userSign/getSignDate");

//用户积分信息
const pointInfo = (params: wxRequestParams) => wxRequest(params, host + "/api/userPoint/pointInfo");

//用户足迹信息
const browseInfo = (params: wxRequestParams) => wxRequest(params, host + "/api/userBrowse/browseInfo");
//添加用户足迹
const addBrowser = (params: wxRequestParams) => wxRequest(params, host + "/api/userBrowse/add");
//添加用户足迹
const delUserBrowser = (params: wxRequestParams) => wxRequest(params, host + "/api/userBrowse/delete");

// 调用后端登录
// const wechatLogin = (params: wxRequestParams) => wxRequest(params, host + '/xhr/login/wxSmallProgramLogin.json');
// 调用node登录/gateway/wxaProjectLogin
const wechatLogin = (params: wxRequestParams) =>
    wxRequest(params, host.replace("/backend", "") + "/backend/api/user/wxaProjectLogin");
// 调用node getWxOpenId
const getWxOpenId = (params: wxRequestParams) =>
    wxRequest(params, host.replace("/backend", "") + "/gateway/getWxOpenIdByProjectId");
//用户收藏的商品
const favoriteInfo = (params: wxRequestParams) => wxRequest(params, host + "/api/goodsFavorite/favoriteInfo");

//用户消息
const messageInfo = (params: wxRequestParams) => wxRequest(params, host + "/api/systemMessage/messageInfo");

//用户手机绑定
const registerUser = (params: wxRequestParams) => wxRequest(params, host + "/api/user/change-mobile");
// 手机号一键注册,调node端服务
const registerUserByMobile = (params: wxRequestParams) =>
    wxRequest(params, host.replace("/backend", "") + "/gateway/registerByMobile");
//发送短信
const sendRandCode = (params: wxRequestParams) => wxRequest(params, host + "/api/user/wxa/change-mobile/send-sms");
// 手机号登录/认证发送短信
const sendSmsLogin = (params: wxRequestParams) => wxRequest(params, host + "/api/user/login-by-mobile/send-sms");
// 手机号注册发送短信
const sendSmsRegister = (params: wxRequestParams) =>
    wxRequest(params, host + "/api/user/register-by-mobile/without-captcha/send-sms");
// 调用node端手机号绑定服务
const bindPhone = (params: wxRequestParams) =>
    wxRequest(params, host.replace("/backend", "") + "/gateway/verifyMobile");
// 手机号登录
const mobileLogin = (params: wxRequestParams) => wxRequest(params, host.replace("/backend", "") + "/gateway/login");
//用户是否绑定手机号
const getUserInfo = (params: wxRequestParams) => wxRequest(params, host + "/api/user/my-profile");
// 省市区获取-接口
const getCommonAddress = (params: wxRequestParams) =>
    wxRequest(params, host + "/api/address/" + params.data.parentId + "/children");

//用户收货地址
const getUserAddress = (params: wxRequestParams) => wxRequest(params, host + "/api/buyer/receiver-infos");
const getDefaultAddress = (params: wxRequestParams) => wxRequest(params, host + "/api/buyer/receiver-infos/default");
//保存用户收货地址
const saveAddress = (params: wxRequestParams) => wxRequest(params, host + "/api/buyer/receiver-infos");
// 设置默认收货地址
const setDefault = (params: wxRequestParams) =>
    wxRequest(params, host + "/api/buyer/receiver-infos/" + params.data.id + "/set-default");

// 获取地址库
const areaList = (params: wxRequestParams) => wxRequest(params, host + "/api/baseAddress/all/list");

const areaListChildren = (params: wxRequestParams) => wxRequest(params, host + "/api/baseAddress/children");

// 根据地区编码获取街道列表
const streetListByAreaCode = (params: wxRequestParams) => wxRequest(params, host + "/api/baseAddress/street");

// 编辑收货地址
const editAddress = (params: wxRequestParams) =>
    wxRequest(params, host + "/api/buyer/receiver-infos/" + params.data.id);

const updateDefaultFlag = (params: wxRequestParams) => wxRequest(params, host + "/xhr/address/updateDefaultFlag.json");

//用户收货地址根据id查询
const receiverInfoById = (params: wxRequestParams) => wxRequest(params, host + "/api/receiverInfo/receiverInfoById");

//根据Id删除收货地址
const delUserAddress = (params: wxRequestParams) =>
    wxRequest(params, host + "/api/buyer/receiver-infos/" + params.data.id);

//查询关键字保存
const addSearchKeyword = (params: wxRequestParams) => wxRequest(params, host + "/api/searchkeyword/add");
//查询关键字列表
const searchKeywordList = (params: wxRequestParams) => wxRequest(params, host + "/api/searchkeyword/list");
//查询关键字清除
const clearSearchKeyword = (params: wxRequestParams) => wxRequest(params, host + "/api/searchkeyword/clear");

// const loginHold = (params: wxRequestParams) => wxRequest(params, host + '/xhr/login/loginHold.json');
const loginHold = (params: wxRequestParams) => wxRequest(params, host + "/api/user/current");
//查询我的订单
const getMyOrderList = (params: wxRequestParams) => wxRequest(params, host + "/api/order/paging");
//查询我的订单 不区分角色
const getOrderList = (params: wxRequestParams) => wxRequest(params, host + "/api/order/substore/paging");
//我的订单-无效订单（退款订单）
const getRefundOrderList = (params: wxRequestParams) => wxRequest(params, host + "/api/refundOrder/substore/paging");
const getOrderListCount = (params: wxRequestParams) => wxRequest(params, host + "/api/order/substore/count-by-status");
//退款单列表查询
const getRefundList = (params: wxRequestParams) => wxRequest(params, host + "/api/order/substore/findRefundList");
//查询我的订单数量
const getMyOrderSize = (params: wxRequestParams) => wxRequest(params, host + "/api/mall/goodsOrder/getMyOrderSize");

const delOrder = (params: wxRequestParams) =>
    wxRequest(params, host + "/api/order/delete?orderId=" + params.data.orderId);
const cancelOrder = (params: wxRequestParams) => wxRequest(params, host + "/api/buyer/order/cancel");
const confirmOrder = (params: wxRequestParams) => wxRequest(params, host + "/api/buyer/confirm");
const refundApply = (params: wxRequestParams) => wxRequest(params, host + "/api/buyer/order/refund"); // 申请退款
const transInfo = (params: wxRequestParams) => wxRequest(params, host + "/xhr/order/getExpressDetail.json");

//根据订单号查询详情
const getOrderInfo = (params: wxRequestParams) => wxRequest(params, host + "/api/order/" + params.data.id + "/detail");
const getFindRefundDetail = (params: wxRequestParams) =>
    wxRequest(params, host + "/api/order/substore/findRefundDetail");

const getStoreInfo = (params: wxRequestParams) => wxRequest(params, host + "/xhr/order/storeInfo.json");

//根据订单号查询详情
const getPayOrderDetail = (params: wxRequestParams) =>
    wxRequest(params, host + "/api/order/" + params.data.orderNo + "/detail");

//根据订单号查询详情
const editOrderInfo = (params: wxRequestParams) => wxRequest(params, host + "/api/mall/goodsOrder/opt");

//根据订单号查询物流
const orderExpressInfo = (params: wxRequestParams) => wxRequest(params, host + "/api/orderExpress/orderExpressInfo");

//查询用户的已订购产品
const goodsUserOrderList = (params: wxRequestParams) =>
    wxRequest(params, host + "/api/mall/goodsOrder/goodsUserOrderList");

// 查询京东云库存状态
const queryJDYSkuStatus = (params: wxRequestParams) => wxRequest(params, host + "/api/thirdPartySku/check/stockInfo");

//退货操作
// const refundApply = (params: wxRequestParams) => wxRequest(params, host + '/api/mall/refund/saveRefundApply');

//用户相关信息--end

//商品分类--begin
//一级分类
const rootCtegoryList = (params: wxRequestParams) => wxRequest(params, host + "/api/mall/rootCtegoryList");
//二级三级分类
const childGoodsCatetoryList = (params: wxRequestParams) =>
    wxRequest(params, host + "/api/mall/childGoodsCatetoryList");
//商品分类--end

//查询广告列表
const getAdList = (params: wxRequestParams) => wxRequest(params, host + "/api/adverts/list");

//获取商品二维码
const getGoodEwm = (params: wxRequestParams) => wxRequest(params, host + "/api/wxa/share/url");
//const getGoodEwm = (params: wxRequestParams) => wxRequest(params, host+'/xhr/wxa/share/goods/url');

//获取店铺二维码
const getShopEwm = (params: wxRequestParams) => wxRequest(params, host + "/api/wxa/share/url");

//获取未使用优惠券
const getUnuse = (params: wxRequestParams) => wxRequest(params, host + "/xhr/coupon/unuseList");

//获取已过期优惠券
const getExpire = (params: wxRequestParams) => wxRequest(params, host + "/xhr/coupon/expireList");

//获取用户优惠券数量
const getCountInfo = (params: wxRequestParams) => wxRequest(params, host + "/xhr/coupon/countInfo");

//获取优惠券11
const getCoupon = (params: wxRequestParams) => wxRequest(params, host + "/api/buyer/promotion/paging");

//商品详情页到订单详情的获取数据
const getGoods = (params: wxRequestParams) => wxRequest(params, host + "/xhr/order/goodsNumMaxCountVerify.json");

//领取优惠劵
const receive = (params: wxRequestParams) => wxRequest(params, host + "/xhr/coupon/receive.json");
const getLogisticsDetail = (params: wxRequestParams) => wxRequest(params, host + "/api/order/express");
// 获取规格
const getSpec = (params: wxRequestParams) => wxRequest(params, host + "/api/item/" + params.data.itemId + "/for-view");
const getSpecByPid = (params: wxRequestParams) =>
    wxRequest(params, host + "/api/item/" + params.data.itemId + "/membershipByPid/for-view");
//const getSpec = (params: wxRequestParams) => wxRequest(params, host+'/api/item/5017/for-view');

// 修改身份证
const certifylayer = (params: wxRequestParams) =>
    wxRequest(params, host + "/api/buyer/receiver-infos/" + params.data.id + "/set-paper-no");
// 获取用户未付款数量
const getUnPayCount = (params: wxRequestParams) => wxRequest(params, host + "/api/order/countNotPaid");
// 获取店铺信息
const getShopInfo = (params: wxRequestParams) => {
    return wxRequest(params, host + "/api/shop/" + params.data.shopId);
};
// 根据id获取收货地址信息
const getAddress = (params: wxRequestParams) => {
    return wxRequest(params, host + "/api/buyer/receiver-infos/" + params.data.id);
};
// 根据id获取用户实名认证信息
const getUserCertification = (params: wxRequestParams) => {
    return wxRequest(params, host + "/api/userCertification/" + params.data.id);
};
// 当前用户是否已同意下单相关协议
const getHasUserAgreeTerms = (params: wxRequestParams) => {
    return wxRequest(params, host + "/api/user/hasUserAgreeTerms");
};
// 用户同意下单相关协议
const getAgreeConsumerTerms = (params: wxRequestParams) => {
    return wxRequest(params, host + "/api/user/agreeConsumerTerms");
};

// 仓库名称列表
const getQuery = (params: wxRequestParams) => {
    return wxRequest(params, host + "/api/seller/items/query");
};
// 创建更新实名认证信息
const saveUserCertification = (params: wxRequestParams) => {
    return wxRequest(params, host + "/api/userCertification");
};
// 删除用户实名认证信息
const delUserCertification = (params: wxRequestParams) => {
    return wxRequest(params, host + "/api/userCertification/" + params.data.id);
};

// 订单身份证信息错误 修改实名认证
const identityChang = (params: wxRequestParams) => {
    return wxRequest(
        params,
        host +
            "/api/order/" +
            params.data.shopOrderId +
            "/identity/change?identityName=" +
            params.data.identityName +
            "&identityCode=" +
            params.data.identityCode +
            "&formId=" +
            params.data.formId,
    );
};
// 订单预览获取运费
const getFee = (params: wxRequestParams) => {
    return wxRequest(params, host + "/api/delivery-fee-charge/order-preview");
};

// 获取商家邀请码
const getShopInviteCode = (params: wxRequestParams) => {
    return wxRequest(params, host + "/api/shop/inviteCodeWithShopId");
};

// 仓库名称列表
const getdata = (params: wxRequestParams) => {
    return wxRequest(params, host + "/api/seller/items/data");
};
// 根据status获取订单数量
const getCountByStatus = (params: wxRequestParams) => wxRequest(params, host + "/api/order/count-by-status");
// 根据纬度经度获取地址信息
const getAddressByJwd = (params: wxRequestParams) => wxRequest(params, "https://apis.map.qq.com/ws/geocoder/v1/");
// 获取或设置门店信息
const getOrSetStoreInfo = (params: wxRequestParams) => {
    return wxRequest(params, host + "/api/subStore/info");
};
// 删除门店分销广告banner图/api/subStore/adv/{id}
const deleteStoreBanner = (params: wxRequestParams) =>
    wxRequest(params, host + "/api/subStore/adv/" + params.data.id + "?status=" + params.data.status);
// 门店分销注册
const storeDistributionRegister = (params: wxRequestParams) => {
    return wxRequest(params, host + "/api/subStore/register");
};
// 创建门店分销广告 /api/adv
const createStoreBanner = (params: wxRequestParams) => {
    return wxRequest(params, host + "/api/subStore/adv/add");
};
// 添加导购员
const addGuider = (params: wxRequestParams) => {
    return wxRequest(params, host + "/api/subStore/guider-add");
};
// 添加门店
const addStore = (params: wxRequestParams) => {
    return wxRequest(params, host + "/api/subStore/service-provider/add-substore");
};
// 服务商添加门店
const setviceAddStore = (params: wxRequestParams) => {
    return wxRequest(params, host + "/api/v2/subStore/add-substore");
};

// 获取导购员列表
const getGuiderList = (params: wxRequestParams) => {
    return wxRequest(params, host + "/api/subStore/guider-list");
};
// 获取门店列表
const getStoreList = (params: wxRequestParams) => {
    return wxRequest(params, host + "/api/subStore/service-provider/view-substore");
};
// 获取门店信息
const getSubStoreInfo = (params: wxRequestParams) => {
    return wxRequest(params, host + "/api/subStore/info");
};
// 收益统计
const getSubStoreProfitStatistics = (params: wxRequestParams) => {
    return wxRequest(params, host + "/api/subStore/profit-total");
};
// 收益明细
const getStoreProfitDetail = (params: wxRequestParams) => {
    return wxRequest(params, host + "/api/subStore/profit-list");
};
// 收入明细
const getIncomeDetail = (params: wxRequestParams) => {
    return wxRequest(params, host + "/api/subStore/profit-page");
};
// 对账单明细
const getQueryOrder = (params: wxRequestParams) => {
    return wxRequest(params, host + "/api/subStoreWithdraw/queryByOrder");
};
// 查询历史所有的提现申请单
const getWithdrawProfitApply = (params: wxRequestParams) => {
    return wxRequest(params, host + "/api/withdrawProfitApply/findAll");
};
// 查询历史所有的结算账单
const getAccountStatement = (params: wxRequestParams) => {
    return wxRequest(params, host + "/api/accountStatement/findAll");
};
// 查询指定账单的推广订单详情
const getFindPromoteDetail = (params: wxRequestParams) => {
    return wxRequest(params, host + "/api/accountStatement/findPromoteDetail");
};
// 判断当前“提现”操作是否可用
const getIsWithdraw = (params: wxRequestParams) => {
    return wxRequest(params, host + "/api/withdrawProfitApply/isWithdrawAvailable");
};
// 判断当前“提现”时间
const getWithdrawDate = (params: wxRequestParams) => {
    return wxRequest(params, host + "/api/withdrawProfitApply/findWithdrawPeriod");
};
// 生成小程序指定页面二维码(邀请导购)
const getQrCode = (params: wxRequestParams) => {
    return wxRequest(params, host + "/api/open/v1/user/createQrCode");
};
// 扫码加入门店成为导购
const getJoinSubStore = (params: wxRequestParams) => {
    return wxRequest(params, host + "/api/subStore/joinSubStore");
};
// 发起提现申请
const getWithdraw = (params: wxRequestParams) => {
    return wxRequest(params, host + "/api/withdrawProfitApply/withdrawAll");
};
// 总收益（可提福卡额）
const getStoreProfitSum = (params: wxRequestParams) => {
    return wxRequest(params, host + "/api/subStore/profit");
};
// 获取门店商品列表
const getSubStoreGoodsList = (params: wxRequestParams) => {
    //原来的不走搜索引擎return wxRequest(params, host + '/api/subStore/item/paging')
    return wxRequest(params, host + "/api/news/search-in-shop");
};
// 提交提现申请,原来/api/subStore/profit-withdraw，
const withdrawal = (params: wxRequestParams) => {
    return wxRequest(params, host + "/api/subStoreWithdraw/withdrawByProfitId");
};
// 微分销商分页查找提现明细
const getWithdrawalList = (params: wxRequestParams) => {
    return wxRequest(params, host + "/api/balance/with-draw-page");
};
// 获取店铺腾讯视频vid
const getShopVideoUrl = (params: wxRequestParams) => {
    return wxRequest(params, host + "/api/storeVideo/find-storeVideo");
};
// 获取当前登录用户身份（角色）
const getIdentity = (params: wxRequestParams) => {
    return wxRequest(params, host + "/api/identity/getIdentity");
};
// 获取隐私条款配置
const getPrivacyPolicy = (params: wxRequestParams) => {
    return wxRequest(params, host + "/api/shop/get-shop-fwb");
};
// 获取快递物流详情信息
const getExpressDetail = (params: wxRequestParams) => wxRequest(params, host + "/api/order/express/findWmsId");

// 发送用于设置提现密码的验证码短信
const sendSmsSet = (params: wxRequestParams) => wxRequest(params, host + "/api/balance/set-withdraw-password-sms");
// 用于验证设置提现密码的验证码
const validateSet = (params: wxRequestParams) => wxRequest(params, host + "/api/weShopWallet/set-password-code-check");
// 设置提现密码
const setPassword = (params: wxRequestParams) => wxRequest(params, host + "/api/balance/set-withdraw-password");
// 用旧提现密码设置新的提现密码,修改提现密码
const changePassword = (params: wxRequestParams) => wxRequest(params, host + "/api/balance/change-withdraw-password");
// 给当前用户手机号发送用于修改手机号的短信验证码
const sendSmsChange = (params: wxRequestParams) => wxRequest(params, host + "/api/user/change-mobile-by-old/send-sms");
// 用于验证通过旧手机号修改手机号的验证码（仅验证）
const validateChange = (params: wxRequestParams) =>
    wxRequest(params, host + "/api/user/change-mobile-by-old-code-check");
// 用于修改手机号码的提现密码验证
const validatePassword = (params: wxRequestParams) =>
    wxRequest(params, host + "/api/user/change-mobile-wallet-password-check");
// 用于验证过后修改手机号
const validateMobile = (params: wxRequestParams) =>
    wxRequest(params, host + "/api/user/change-mobile-by-old-after-check");
// 获取提现密码有没有设置过
const existCashPassword = (params: wxRequestParams) => wxRequest(params, host + "/api/balance/profit-balance");
// 忘记提现密码设置新提现密码
const forgetWithdrawPassword = (params: wxRequestParams) =>
    wxRequest(params, host + "/api/balance/forget-withdraw-password");
// 批量获取订单状态数量，减少请求
const countByStatusAll = (params: wxRequestParams) => wxRequest(params, host + "/api/order/count-by-status-all");
// 添加提现账号
const addCashAccount = (params: wxRequestParams) => wxRequest(params, host + "/api/balance/account/add");

// 获取diy装修数据
const getShopDiyData = (params: wxRequestParams) => {
    return wxRequest(params, host + `/api/${params.data.shopId}/design/page/getPage/v2`);
};

// 获取门店审核信息
const getSubStoreId = (params: wxRequestParams) => wxRequest(params, host + "/api/v2/subStore/current");
// 获取用户可用积分
const getUserIntegralNum = (params: wxRequestParams) => wxRequest(params, host + "/api/integral/gift/get-useGrade");
// 扫积分码加积分到积分账户/api/integral/genAccept
const addIntegralToAccount = (params: wxRequestParams) => wxRequest(params, host + "/api/integral/genAccept");
// 积分商品查询，原/api/integral/item/paging
const getIntegralGoodsList = (params: wxRequestParams) => wxRequest(params, host + "/api/search-in-shop-gift");
// 查询积分记录含使用记录和获取记录 以状态区分,原/api/integral/get-recordGrade
const getRecordGrade = (params: wxRequestParams) => wxRequest(params, host + "/api/integral/gift/get-recordGrade");
// 提交用户上下级关系 /api/shop/reach?shopId=&refererId=&subStoreId=
const commitReach = (params: wxRequestParams) => wxRequest(params, host + "/api/shop/reach");

//关于门店是否是审核的 /api/v2/subStore/auth/current
const authcurrent = (params: wxRequestParams) => wxRequest(params, host + "/api/v2/subStore/auth/current");
// 获取通用自定义页面的数据
const getCustomPageData = (params: wxRequestParams) => {
    return wxRequest(
        params,
        "http://web.myazgo.com.cn/index.php?s=/api/page/custom&page_id=10006&wxapp_id=10001&token=fc1748667546dc06de4a2479879ca1a1",
    );
};

//分类类目
const getcategory = (params: wxRequestParams) => wxRequest(params, host + "/api/v2/search/category");

//服务商审核门店详情
const serviceGetStore = (params: wxRequestParams) => wxRequest(params, host + "/api/v2/subStore/view");
//服务商审核门店-同意
const serviceAuthStore = (params: wxRequestParams) =>
    wxRequest(params, host + "/api/v2/subStore/" + params.data.subStoreId + "/auth");
//服务商审核门店-拒绝
const serviceRejectStore = (params: wxRequestParams) =>
    wxRequest(params, host + "/api/v2/subStore/" + params.data.subStoreId + "/reject");
//服务商分享二维码
const serviceInviteImg = (params: wxRequestParams) => wxRequest(params, host + "/api/v2/serviceProvider/invite-img");
//扫描服务商分享二维码
//const serviceInviteImg=(params) => wxRequest(params, host + '/api/v2/serviceProvider/invite-img');

// 申请门店
const seviceRegsiterStore = (params: wxRequestParams) => {
    return wxRequest(params, host + "/api/v2/subStore/register");
};
const getInviterInfo = (params: wxRequestParams) => {
    return wxRequest(params, host + "/api/v2/view/inviterInfo");
};

//获取优惠券列表
const getMyCoupons = (params: wxRequestParams) => wxRequest(params, host + "/api/buyer/promotion/paging");
//获取可用优惠券
const getCoupons = (params: wxRequestParams) => wxRequest(params, host + "/api/order/preview");

//通商云对接相关接口
/** 发送验证码 */
const sendPhoneCode = (params: wxRequestParams) => wxRequest(params, host + "/allinPay/sendPhoneCode");

/** 绑定手机号 */
const bindingPhone = (params: wxRequestParams) => wxRequest(params, host + "/allinPay/bindingPhone");

/** 实名认证 */
const authentication = (params: wxRequestParams) => wxRequest(params, host + "/allinPay/authentication");

/** 设置企业信息 */
const setCompanyInfo = (params: wxRequestParams) => wxRequest(params, host + "/allinPay/setCompanyInfo");

/** 静默绑定银行卡 */
const applyNewBindBankCard = (params: wxRequestParams) => wxRequest(params, host + "/allinPay/applyBindBankCard");

/** 查询卡bin */
const cardBin = (params: wxRequestParams) => wxRequest(params, host + "/allinPay/cardBin");

/** 获取会员电子协议签约地址 */
const getSignUrl = (params: wxRequestParams) => wxRequest(params, host + "/allinPay/signContract");

/** 查询本地会员电子协议签约状态 */
const querySignStatus = (params: wxRequestParams) => wxRequest(params, host + "/allinPay/querySignContract");

/** 通联门店身份注册查询 */
const queryStepInfo = (params: wxRequestParams) => wxRequest(params, host + "/allinPay/bindStepInfo");

/** 银行卡解除绑定 */
const unBindBankCard = (params: wxRequestParams) => wxRequest(params, host + "/allinPay/unBindBankCard");

/** 通联静默初始化会员（对老用户） */
const initTongLianMember = (params: wxRequestParams) => wxRequest(params, host + "/allinPay/initAllinPayUser");

/** 临时绑定 */
const bindOpenId = (params: wxRequestParams) => wxRequest(params, host + "/allinPay/bindOpenId");

/** 获取授权书列表 */
const getAuthLetter = (params: wxRequestParams) => wxRequest(params, host + "/api/shop/authorization/pages");

const getMariaInfo = (params: wxRequestParams) => wxRequest(params, "https://maria.yang800.com/api/data/v2/790/604");

const submitClock = (params: wxRequestParams) => wxRequest(params, host + "/api/storeMonthlyPunch");

const getLastClockTime = (params: wxRequestParams) => wxRequest(params, host + "/api/storeMonthlyPunch/latest/record");

const jdApplyReturn = (params: wxRequestParams) => wxRequest(params, host + "/api/app/refund/apply");

const getJdReasonList = (params: wxRequestParams) =>
    wxRequest(params, host + "/api/app/refund/reason/type/drop/down/box");

const getCompanyList = (params: wxRequestParams) =>
    wxRequest(params, host + "/api/app/refund/express/company/drop/down/box");

const submitExpressInfo = (params: wxRequestParams) => wxRequest(params, host + "/api/app/refund/upload/express/info");

export default {
    getDefaultAddress,
    preCheck,
    initTongLianMember,
    bindOpenId,
    unBindBankCard,
    queryStepInfo,
    sendPhoneCode,
    bindingPhone,
    authentication,
    setCompanyInfo,
    applyNewBindBankCard,
    cardBin,
    getSignUrl,
    querySignStatus,
    getSubStoreId,
    getInviterInfo,
    seviceRegsiterStore,
    getcategory,
    authcurrent,
    getCompanyList,
    serviceInviteImg,
    serviceGetStore,
    serviceAuthStore,
    serviceRejectStore,
    hostGoodsList,
    getDiscoverList,
    getHomeDisvocerList,
    getGoodsList,
    goodsDetail,
    goodsDetailInfo,
    wxJsCode2Session,
    user2session,
    userSginInfo,
    doSign,
    addCart,
    cartList,
    cartCheck,
    cartDel,
    cartUpdateNum,
    preOrder,
    refundApply,
    pointInfo,
    browseInfo,
    addBrowser,
    delUserBrowser,
    favoriteInfo,
    messageInfo,
    registerUser,
    sendRandCode,
    getUserInfo,
    saveAddress,
    receiverInfoById,
    bindPhone,
    getCommonAddress,
    getUserAddress,
    getQuery,
    addSearchKeyword,
    searchKeywordList,
    clearSearchKeyword,
    delOrder,
    cancelOrder,
    getMyOrderList,
    getOrderList,
    getOrderListCount,
    getRefundOrderList,
    getRefundList,
    saveByCart,
    toPay,
    rootCtegoryList,
    childGoodsCatetoryList,
    getStoreInfo,
    getOrderInfo,
    getFindRefundDetail,
    saveUserInfo,
    editOrderInfo,
    goodsUserOrderList,
    orderExpressInfo,
    delUserAddress,
    goodsFavorite,
    goodsUnFavorite,
    goodsIsFavorite,
    initOrder,
    updateDefaultFlag,
    getMyOrderSize,
    getPayOrderDetail,
    getOrderCount,
    transInfo,
    getAdList,
    loginHold,
    updateUserInfo,
    getNoticebar,
    getNoticebarList,
    getUnread,
    submitOrder,
    payOrder,
    getSignDate,
    getClassifyData,
    wechatLogin,
    getCartNumber,
    getProfitStatistics,
    getBalance,
    getBankCard,
    getShopHomeData,
    getGoodEwm,
    getShopEwm,
    getHomeAd,
    getAllCatogry,
    getUnuse,
    getExpire,
    getCountInfo,
    getCoupon,
    getGoods,
    getClassifyAd,
    receive,
    getLogisticsDetail,
    getSpec,
    setDefault,
    editAddress,
    certifylayer,
    getUnPayCount,
    confirmOrder,
    getShopInfo,
    getAddress,
    getUserCertification,
    getHasUserAgreeTerms,
    getAgreeConsumerTerms,
    saveUserCertification,
    delUserCertification,
    sendSmsLogin,
    mobileLogin,
    registerUserByMobile,
    sendSmsRegister,
    identityChang,
    getWxOpenId,
    goodsDetailByPid,
    getSpecByPid,
    initOrderByPid,
    submitOrderByPid,
    getFee,
    getShopInviteCode,
    getdata,
    getShopDiyData,
    getCustomPageData,
    getStoreList,
    getQueryOrder,
    getWithdrawProfitApply,
    getAccountStatement,
    getFindPromoteDetail,
    getIsWithdraw,
    getWithdrawDate,
    getQrCode,
    getJoinSubStore,
    getWithdraw,
    addStore,
    getCountByStatus,
    getAddressByJwd,
    getOrSetStoreInfo,
    deleteStoreBanner,
    storeDistributionRegister,
    createStoreBanner,
    addGuider,
    getGuiderList,
    getSubStoreInfo,
    getSubStoreProfitStatistics,
    getStoreProfitDetail,
    getIncomeDetail,
    getStoreProfitSum,
    getSubStoreGoodsList,
    withdrawal,
    getWithdrawalList,
    getShopVideoUrl,
    getPrivacyPolicy,
    getIdentity,
    getExpressDetail,
    sendSmsSet,
    validateSet,
    setPassword,
    changePassword,
    sendSmsChange,
    validateChange,
    validatePassword,
    validateMobile,
    existCashPassword,
    forgetWithdrawPassword,
    countByStatusAll,
    addCashAccount,
    getUserIntegralNum,
    addIntegralToAccount,
    getIntegralGoodsList,
    getRecordGrade,
    commitReach,
    setviceAddStore,
    getMyCoupons,
    getCoupons,
    getMariaInfo,
    getAuthLetter,
    submitClock,
    getLastClockTime,
    areaList,
    areaListChildren,
    streetListByAreaCode,
    jdApplyReturn,
    getJdReasonList,
    queryJDYSkuStatus,
    submitExpressInfo,
};
