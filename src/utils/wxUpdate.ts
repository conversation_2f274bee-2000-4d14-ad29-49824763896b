import Taro, { showModal } from "@tarojs/taro";
import { mall_event, mall_event_key, reportEvent } from "@/utils/track-report";
import { IS_TEST } from "@/utils/env";
const checkUpdateVersion = () => {
    if (Taro.canIUse("getUpdateManager")) {
        //创建 UpdateManager 实例
        const updateManager = Taro.getUpdateManager();
        if (IS_TEST) {
            reportEvent(
                mall_event.MALL_EVENT_APP_UPDATE,
                new Map().set(mall_event_key.MALL_KEY_APP_UPDATE, "beforeOnCheckForUpdate"),
            );
        }
        //检测版本更新
        updateManager.onCheckForUpdate(function (res) {
            // 请求完新版本信息的回调
            if (res.hasUpdate) {
                //监听小程序有版本更新事件
            }
            reportEvent(
                mall_event.MALL_EVENT_APP_UPDATE,
                new Map().set(mall_event_key.MALL_KEY_APP_UPDATE, res.hasUpdate ? "hasUpdate" : "notHasUpdate"),
            );
        });
        updateManager.onUpdateReady(function () {
            reportEvent(
                mall_event.MALL_EVENT_APP_UPDATE,
                new Map().set(mall_event_key.MALL_KEY_APP_UPDATE, "onUpdateReady"),
            );
            showModal({
                title: "新版本更新提示",
                content: "新版本已准备好,需要重启应用",
                showCancel: false,
                success: () => {
                    updateManager.applyUpdate();
                },
            });
        });
        updateManager.onUpdateFailed(function () {
            reportEvent(
                mall_event.MALL_EVENT_APP_UPDATE,
                new Map().set(mall_event_key.MALL_KEY_APP_UPDATE, "onUpdateFailed"),
            );
            // 新版本下载失败
            showModal({
                title: "新版本更新失败",
                content: "请您稍后重启小程序。或者删除当前小程序，到微信 “发现-小程序” 页，重新打开哦~",
            });
        });
    } else {
        reportEvent(
            mall_event.MALL_EVENT_APP_UPDATE,
            new Map().set(
                mall_event_key.MALL_KEY_APP_UPDATE,
                "当前微信版本过低，无法使用该功能，请升级到最新微信版本后重试。",
            ),
        );
        showModal({
            title: "溫馨提示",
            content: "当前微信版本过低，无法使用该功能，请升级到最新微信版本后重试。",
        });
    }
};

export default checkUpdateVersion;
