import Taro from "@tarojs/taro";
import { getStorage } from "@/utils/storage";
import { IDENTITY, USER_INFO } from "@/utils/constant";
import { isWeixin } from "./common";

export function reportEvent(eventId: string, data: Map<string, string | number>) {
    const accountInfo = Taro.getAccountInfoSync();
    const userInfo = getStorage(USER_INFO);
    const identity = getStorage(IDENTITY);
    if (!(data instanceof Map)) {
        console.error(`reportEvent ${eventId} 传产data 不是Map类型请检查 `);
    } else {
        if (userInfo) {
            const { id, openId } = userInfo;
            data.set(mall_event_key.MALL_KEY_USERID, id).set(mall_event_key.MALL_KEY_OPENID, openId);
        }
        if (identity) {
            data.set(mall_event_key.MALL_KEY_IDENTITY, identity);
        }
        data.set(mall_event_key.MALL_KEY_ENV_VERSION, accountInfo.miniProgram.envVersion);
        if (isWeixin()) {
            Taro.reportEvent(eventId, Object.fromEntries(data));
        }
    }
}

export const mall_event = {
    MALL_EVENT_GOODS_SHARE: "mall_event_goods_share",
    MALL_EVENT_APP_UPDATE: "mall_event_app_update",
    MALL_EVENT_SCAN_SERVICE_PROVIDER_MINI_QR: "mall_event_scan_service_provider_mini_qr",
    MALL_EVENT_SCAN_SERVICE_PROVIDER_MINI_QR_SUBMIT: "mall_event_scan_service_provider_mini_qr_submit",
    MALL_EVENT_SCAN_GUIDER_MINI_QR: "mall_event_scan_guider_mini_qr",
    MALL_EVENT_SCAN_GUIDER_MINI_QR_SUBMIT: "mall_event_scan_guider_mini_qr_submit",
    MALL_EVENT_SHARE_INVITE_GUIDER_MINI_QR: "mall_event_share_invite_guider_mini_qr", //分享邀请加入导购小程码
    MALL_EVENT_SHARE_INVITE_STORE_MINI_QR: "mall_event_share_invite_store_mini_qr", //分享邀请加入码门店小程码
    MALL_EVENT_LOOK_GOOD: "mall_event_look_good", // 查看商品详情
    MALL_EVENT_ADD_CART: "mall_event_add_cart", // 商品加入购物车
    MALL_EVENT_GOOD_IMMEDIATELY_BUY: "mall_event_good_immediately_bug", // 商品点击立即购买
    MALL_EVENT_GOOD_IMMEDIATELY_BUY_FAIL: "mall_event_good_immediately_buy_fail", // 商品点击立即购买失败
    MALL_EVENT_CART_SETTLEMENT: "mall_event_cart_settlement", // 购物车点击结算按钮
    MALL_EVENT_SUBMIT_ORDER: "mall_event_submit_order", // 提交订单
    MALL_EVENT_PAY_SUCCESS: "mall_event_pay_success", // 支付成功
    MALL_EVENT_PAY_FAIL: "mall_event_pay_fail", // 支付失败
    MALL_EVENT_ORDER_APPLY_REFUND: "mall_event_order_apply_refund", // 申请退单
    MALL_EVENT_ORDER_CANCEL_ORDER: "mall_event_order_cancel_order", // 取消订单
    MALL_EVENT_ORDER_APPLY_PAY: "mall_event_order_apply_pay", // 申请支付
    MALL_EVENT_ORDER_CONFIRM_RECEIPT: "mall_event_order_confirm_receipt", // 确认收货
    MALL_EVENT_ORDER_CONFIRM_RESULT: "mall_event_order_confirm_result", // 确认收货结果
    MALL_EVENT_GLOBAL_CATCH_ERROR: "mall_event_global_catch_error",
    MALL_EVENT_PERF_MODULE_MONITOR: "mall_event_perf_module_monitor", //功能/模块监控
    MALL_EVENT_ORDER_PAGING_START: "mall_event_order_paging_start", // 订单分页请求发起
    MALL_EVENT_ORDER_PAGING_END: "mall_event_order_paging_end", // 订单分页请求结束
};
export const mall_event_key = {
    MALL_KEY_USERID: "mall_key_userid",
    MALL_KEY_OPENID: "mall_key_openid",
    MALL_KEY_IDENTITY: "mall_key_identity",
    MALL_KEY_ENV_VERSION: "mall_key_env_version",
    MALL_KEY_APP_UPDATE: "mall_key_app_update_result",
    MALL_KEY_INVITE_CODE: "mall_key_invite_code",
    MALL_KEY_LOGIN_CALL_BACK: "mall_key_login_call_back",
    MALL_KEY_SUBMIT: "mall_key_submit",
    MALL_KEY_SUBMIT_INFO: "mall_key_submit_info",
    MALL_KEY_GOOD_ID: "mall_key_good_id", // 商品id
    MALL_KEY_GOODS_ID: "mall_key_goods_id", // 商品id
    MALL_KEY_GOOD_NUM: "mall_key_good_num", // 商品数量
    MALL_KEY_GOODS_NUM: "mall_key_goods_num", // 商品数量
    MALL_KEY_GOOD_SPEC_ID: "mall_key_good_spec_id", // 商品specid
    MALL_KEY_BUY_FAIL_REASON: "mall_key_buy_fail_reason", //立即购买失败
    MALL_KEY_ORDER_ID: "mall_key_order_id", // 订单id
    MALL_KEY_ORDER_IDS: "mall_key_order_ids", // 订单id
    MALL_KEY_ORDER_PAY_CHANNEL: "mall_key_order_pay_channel", // 订单支付方式 微信，积分
    MALL_KEY_ORDER_PAY_ERROR_MESSAGE: "mall_key_order_pay_error_message", // 订单支付错误信息

    MALL_KEY_ORDER_CONFIRM_RESPONSE: "mall_key_order_confirm_response", // 确认收货结果

    MALL_KEY_ORDER_REFUND_REASON: "mall_key_order_refund_reason", // 申请退款理由
    MALL_KEY_ORDER_REFUND_INTRODUCE: "mall_key_order_refund_introduce", // 申请退款说明
    MALL_KEY_ITEM_NAME: "mall_key_item_name", //商品名
    MALL_KEY_ITEM_ID: "mall_key_item_id", //商品id
    MALL_KEY_SOURCE_FROM: "mall_key_source_from", //触发分享的地方，按钮或小程序顶部分享
    MALL_KEY_PATH: "mall_key_path", //分享商品路径
    MALL_KEY_INVITE_ADD_GUIDER_FROM: "mall_key_invite_add_from", //接受或分享时上报动作来源

    MALL_KEY_INVITE_SUBSTORE_ID: "mall_key_invite_substore_id", //邀请加入导购的门店id
    MALL_KEY_INVITE_SCENE: "mall_key_invite_scene", //邀请加入导购的门店id

    MALL_KEY_INVITE_ADD_GUIDER_NAME: "mall_key_invite_add_guider_name", //接受邀请的导购名称
    MALL_KEY_INVITE_ADD_GUIDER_MOBILE: "mall_key_invite_add_guider_mobile", //接受邀请的导购手机号
    MALL_KEY_INVITE_ADD_GUIDER_RESULT: "mall_key_invite_add_guider_result", //分享导购码请求结果
    MALL_KEY_INVITE_ADD_STORE_SERVICE_PROVIDER_NAME: "mall_key_invite_add_store_service_provider_name", //分享加入门店 服务商名称
    MALL_KEY_INVITE_ADD_STORE_SERVICE_PROVIDER_AVATAR: "mall_key_invite_add_store_service_provider_avatar", //分享加入门店 服务商名称
    MALL_KEY_INVITE_ADD_STORE_SERVICE_PROVIDER_QR: "mall_key_invite_add_store_service_provider_name", //分享加入门店 服务商名称
    MALL_KEY_GLOBAL_CATCH_ERROR_PAGE: "mall_key_global_catch_error_page", //发生错误的页面
    MALL_KEY_GLOBAL_CATCH_ERROR_INFO: "mall_key_global_catch_error_info", //错误详情
    MALL_KEY_PERF_MODULE_ID: "mall_key_perf_module_id", //模块ID，例如搜索模块的内部ID编码
    MALL_KEY_PERF_MONITOR_ID: "mall_key_perf_monitor_id", //接口ID，即服务调用接口ID或者接口英文名称
    MALL_KEY_PERF_MONITOR_LEVEL: "mall_key_perf_monitor_level", //重要等级，0为普通，非0为重要，数字越大越重要
    MALL_KEY_PERF_ERROR_CODE: "mall_key_perf_error_code", //接口调用情况，0代表正常，其他均为失败
    MALL_KEY_PERF_ERROR_MSG: "mall_key_perf_error_msg", //调用信息，用来辅助判断接口调用失败的内容
    MALL_KEY_PERF_COST_TIME: "mall_key_perf_cost_time", //接口调用耗时，单位为毫秒

    MALL_KEY_ORDER_PAGING_PAGE: "mall_key_order_paging_page", // 订单分页第几页
    MALL_KEY_ORDER_PAGING_STATUSSTR: "mall_key_order_paging_statusStr", // 订单状态 ,
    MALL_KEY_ORDER_PAGING_COUNT: "mall_key_order_paging_count", // 当前已加载的订单总数量,
    MALL_KEY_ORDER_PAGING_TOTAL: "mall_key_order_paging_total", // 当前状态的订单总数量,
    MALL_KEY_ORDER_PAGING_RESULT: "mall_key_order_paging_result", // 当前状态加载的订单返回结果
    MALL_KEY_ORDER_PAGING_ERROR: "mall_key_order_paging_error", // 当前状态加载的订单错误信息
    MALL_KEY_ORDER_PAGING_REFUND_STATUS: "mall_key_order_paging_refund_status", // 分页退款订单状态
};
