import request, { requestParams } from "../utils/request";
import { PROJECT_CONFIG } from "./env";

const host = PROJECT_CONFIG.API_MALL; //正式https://m.mall.yang800.com/backend,测试https://mtest.mall.yang800.cn/backend
// const hostMock = "http://yapi.yang800.cn/mock/116"
//测试http://m.mall.c0d5363253401480d8190afc3e7977570.cn-hangzhou.alicontainer.com/backend
//测试环境，生产环境一定要加/backend,const host = 'https://m.mall.yang800.com/backend'

type wxRequestParams = Omit<requestParams, "url">;

const wxRequest = (params: wxRequestParams, url: string) => {
    return request({ ...params, url: url });
};

// 阻力领券
const assistCoupon = (params: wxRequestParams) => {
    return wxRequest(params, host + "/mall-app/api/couponActivity/clickFissionActivity");
};

// 助力详情
const getfissionDetail = (params: wxRequestParams) => {
    return wxRequest(params, host + "/mall-app/api/couponActivity/queryFissionDetail");
};
/**
 * 获取活动列表
 * @param params
 */
const activityList = (params: wxRequestParams) => wxRequest(params, host + "/mall-app/api/app-promotion/activity/list");

/**
 * 获取活动详情
 * @param params
 */
const activityDetail = (params: wxRequestParams) =>
    wxRequest(params, host + "/mall-app/api/app-promotion/activity/detail");
/**
 * 获取用户报名活动详情
 * @param params
 */
const activityUserDetail = (params: wxRequestParams) =>
    wxRequest(params, host + "/mall-app/api/app-activity/user/detail");

/**
 * 参与用户
 * @param params
 */
const activityUserList = (params: wxRequestParams) => wxRequest(params, host + "/mall-app/api/app-activity/user/list");
/**
 * 任务活动 点击报名
 * @param params
 */
const activitySignUp = (params: wxRequestParams) => wxRequest(params, host + "/mall-app/api/app-activity/user/signUp");

/**
 * 任务活动 提交资料
 * @param params
 */
const activitySubmitMaterial = (params: wxRequestParams) =>
    wxRequest(params, host + "/mall-app/api/app-activity/user/material");
/**
 * 获取渠道下拉
 * @param params
 */
const activityChannel = (params: wxRequestParams) =>
    wxRequest(params, host + "/mall-app/api/app-promotion/activity/channel");

// 邀请码注册
const inviteRejister = (params: wxRequestParams) => {
    return wxRequest(params, host + "/mall-app/api/family/registerByInviteCode");
};

// 发送短信（登录）
const sendMsg = (params: wxRequestParams) => {
    return wxRequest(params, host + "/mall-app/api/family/login/sms/send");
};
// 发送短信（注册）
const sendegisterMsg = (params: wxRequestParams) => {
    return wxRequest(params, host + "/mall-app/api/family/register/sms/send");
};

// 发送短信（修改密码）
const sendUpdateMsg = (params: wxRequestParams) => {
    return wxRequest(params, host + "/mall-app/api/family/password/sms/send");
};

// 注册来源
const getRegisterSource = (params: wxRequestParams) => {
    return wxRequest(params, host + "/mall-app/api/family/register/source");
};

// 静默注册
const staticLogin = (params: wxRequestParams) => {
    return wxRequest(params, host + "/mall-app/api/family/loginByUnionId");
};

// 手机号码快捷登录
const loginPhone = (params: wxRequestParams) => {
    return wxRequest(params, host + "/mall-app/api/family/loginByUnionIdAndPhone");
};

// 密码登录/验证码登录
const loginPswOrVerify = (params: wxRequestParams) => {
    return wxRequest(params, host + "/mall-app/api/family/login");
};

// queryCoupon
const queryWaitGetCoupons = (params: wxRequestParams) => {
    return wxRequest(params, host + "/mall-app/api/user/coupon/queryWaitGetCoupons");
};

// 修改秘密
const updatePassword = (params: wxRequestParams) => {
    return wxRequest(params, host + "/mall-app/api/family/user/updatePassword");
};

// 用户余额详情
const getUserAmountDetail = (params: wxRequestParams) => {
    return wxRequest(params, host + "/mall-app/api/userAmount/detail");
};

// 福豆券列表
const getUserAmountSendList = (params: wxRequestParams) => {
    return wxRequest(params, host + "/mall-app/api/userAmountSend/list");
};

// 福豆规则
const getBalanceRemark = (params: wxRequestParams) => {
    return wxRequest(params, host + "/mall-app/api/rule/balance/getDefaultRemark");
};

// 余额变更记录
const getUserAmountFlowList = (params: wxRequestParams) => {
    return wxRequest(params, host + "/mall-app/api/userAmountFlow/list");
};

// 获取未查看的余额券
const getUnReadAmountSend = (params: wxRequestParams) => {
    return wxRequest(params, host + "/mall-app/api/userAmountSend/getUnReadAmountSend");
};

// 获取小程序页面
const getWxappPages = (params: wxRequestParams) => {
    return wxRequest(params, host + "/mall-app/api/wxappPages/getPage");
};

// 获取排行榜
const getRankList = (params: wxRequestParams) => {
    return wxRequest(params, host + "/mall-app/api/items/hotSaleRankList");
};
// 获取可领取优惠券列表并自动领取
const getAutoGetCoupons = (params: wxRequestParams) => {
    return wxRequest(params, host + "/mall-app/api/user/coupon/autoGetCoupons");
};

// 获取注册金额
const getRegisterPrice = (params: wxRequestParams) => {
    return wxRequest(params, host + "/mall-app/api/family/register/info");
};

// 支付注册
const registerByPrice = (params: wxRequestParams) => {
    return wxRequest(params, host + "/mall-app/api/family/registerByPay");
};

// 获取支付注册的金额
const getPriceOfRegister = (params: wxRequestParams) => {
    return wxRequest(params, host + "/mall-app/api/family/register/info");
};

// 支付注册结果查询
const queryRegisterByPay = (params: wxRequestParams) => {
    return wxRequest(params, host + "/mall-app/api/family/registerByPay/check");
};

/**
 * 购物车相关接口
 */
// 用户的购物车商品列表
const getCarts = (params: wxRequestParams) => {
    return wxRequest(params, host + "/mall-app/api/cart/item/user/list");
};

// 修改购物车商品数量
const updateCarts = (params: wxRequestParams) => {
    return wxRequest(params, host + "/mall-app/api/wxa/carts/update");
};

// 购物车商品数量
const getCartsCount = (params: wxRequestParams) => {
    return wxRequest(params, host + "/mall-app/api/cart/item/count");
};

const getCartGoods = (params: wxRequestParams) => {
    return wxRequest(params, host + "/mall-app/api/news/search-in-shop");
};

// 批量删除购物车商品
const batchDeleteCart = (params: wxRequestParams) => {
    return wxRequest(params, host + "/mall-app/api/wxa/carts/batchDelete");
};

// 获取个人信息
const getUserInfo = (params: wxRequestParams) => {
    return wxRequest(params, host + "/mall-app/api/family/user/info");
};

// 保存信息
const saveUserInfo = (params: wxRequestParams) => {
    return wxRequest(params, host + "/mall-app/api/family/user/syncWeChatUserInfo");
};

// 查询商品的可用优惠券
const queryCoupons = (params: wxRequestParams) => {
    return wxRequest(params, host + "/mall-app/api/user/coupon/queryCoupons");
};

// 自动领取所有的优惠券
const autoCoupons = (params: wxRequestParams) => {
    return wxRequest(params, host + "/mall-app/api/user/coupon/autoGetCoupons");
};

// 查询商品组的可用优惠券
const getGoodsCoupon = (params: wxRequestParams) => {
    return wxRequest(params, host + "/mall-app/api/user/coupon/optimizationCoupons");
};

const orderPreview = (params: wxRequestParams) => {
    return wxRequest(params, host + "/mall-app/api/shop/order/preview");
};

const submit = (params: wxRequestParams) => {
    return wxRequest(params, host + "/mall-app/api/shop/order/submit");
};

// 用户主动领取优惠券
const userGetCoupons = (params: wxRequestParams) => {
    return wxRequest(params, host + "/mall-app/api/user/coupon/getCoupon");
};

// 创建活动
const createFission = (params: wxRequestParams) => {
    return wxRequest(params, host + "/mall-app/api/couponActivity/createFission");
};

// 更改选择的优惠券
const planChangeCheck = (params: wxRequestParams) => {
    return wxRequest(params, host + "/mall-app/api/user/coupon/planChangeCheck");
};

// const get
// /mall-app/mall-app/api/userAmount/detail
const getUserAmount = (params: wxRequestParams) => {
    return wxRequest(params, host + "/mall-app/api/userAmount/detail");
};

const getBullets = (params: wxRequestParams) => {
    return wxRequest(params, host + "/mall-app/api/family/redirectToPage");
};

// 查询当前店铺进行中的裂变券活动信息
// /mall-app/api/couponActivity/queryShopFissionActivity
const queryShopFissionActivity = (params: wxRequestParams) => {
    return wxRequest(params, host + "/mall-app/api/couponActivity/queryShopFissionActivity");
};

// 获取商品详情
const getGoodsDetail = (params: wxRequestParams) => {
    return wxRequest(params, host + `/mall-app/api/item/${params.data.itemId}/detail-info`);
};

// 根据sku获取对应的实时数据
const getSkuInfo = (params: wxRequestParams) => {
    return wxRequest(params, host + `/mall-app/api/skus/stock/discountPrice/purchaseLimit`);
};

// 修改购物车商品数量
const changeCartNum = (params: wxRequestParams) => {
    return wxRequest(params, host + "/mall-app/api/cart/item/update");
};
// 获取购物车数量
const getCartCount = (params: wxRequestParams) => {
    return wxRequest(params, host + "/mall-app/api/cart/item/count");
};
// 获取/api/shop/order/recentlyPurchased/count
const getPurchaseCount = (params: wxRequestParams) => {
    return wxRequest(params, host + "/mall-app/api/shop/order/recentlyPurchased/count");
};
// 获取最近购买数据
const getPurchaseLists = (params: wxRequestParams) => {
    return wxRequest(params, host + "/mall-app/api/shop/order/recentlyPurchased");
};

const getSkuDiscount = (params: wxRequestParams) => {
    return wxRequest(params, host + "/mall-app/api/skus/discount/price");
};
// 获取sku信息
const getSkus = (params: wxRequestParams) => {
    return wxRequest(params, host + "/mall-app/api/items/sku/info");
};

const updateCartsSku = (params: wxRequestParams) => {
    return wxRequest(params, host + "/mall-app/api/cart/item/update/sku/info");
};

const updateSinglePrice = (params: wxRequestParams) => {
    return wxRequest(params, host + "/mall-app/api/item/discount/info");
};

const updateCheckedCarts = (params: wxRequestParams) => {
    return wxRequest(params, host + "/mall-app/api/cart/item/check");
};

const deleteCarts = (params: wxRequestParams) => {
    return wxRequest(params, host + "/mall-app/api/cart/item/batchDelete");
};
/**
 *
 * @param params
 */
const getPopUpById = (params: wxRequestParams) => {
    return wxRequest(params, host + "/mall-app/api/wxappPages/getPopUpById");
};
/**
 * 获取客服账号链接
 *
 * @param reqVO
 * @result
 */
const getKfAccountUrl = (params: wxRequestParams) => {
    return wxRequest(params, host + "/mall-app/api/weCom/KfAccount/getUrl");
};

// 获取门店商品列表
const getSubStoreGoodsList = (params: wxRequestParams) => {
    return wxRequest(params, host + "/mall-app/api/news/search-in-shop");
};

const getPersonalCenterActivity = (params: wxRequestParams) => {
    return wxRequest(params, host + "/mall-app/api/personal/center/activity/content");
};
const getPersonalCenterInfo = (params: wxRequestParams) => {
    return wxRequest(params, host + "/mall-app/api/personal/center/user/info");
};

const syncWeChatUserInfo = (params: wxRequestParams) =>
    wxRequest(params, host + "/mall-app/api/family/user/syncWeChatUserInfo");

// 获取收藏列表
const getCollectList = (params: wxRequestParams) => wxRequest(params, host + "/mall-app/api/item/collect/list");

// 批量取消收藏
const getCancelCollectBatch = (params: wxRequestParams) =>
    wxRequest(params, host + "/mall-app/api/item/collect/cancelBatch");
// /api/gb_group/getGroupDetail
const findActivityByOrderId = (params: wxRequestParams) =>
    wxRequest(params, host + "/mall-app/api/shop/order/findActivityByOrderId");
// 单个取消收藏
const getCancelCollect = (params: wxRequestParams) => wxRequest(params, host + "/mall-app/api/item/collect/submit");

const loginCheck = (params: wxRequestParams) => wxRequest(params, host + "/mall-app/api/family/login/check");

// c端评价相关
// 商品详情展示的评价信息
const getDetailCommentInfo = (params: wxRequestParams) =>
    wxRequest(params, host + "/mall-app/api/item/evaluation/detailList");
// 评价列表数据
const getDetailComments = (params: wxRequestParams) => wxRequest(params, host + "/mall-app/api/item/evaluation/list");
// 评价列表汇总
const getDetailCommentAllData = (params: wxRequestParams) =>
    wxRequest(params, host + "/mall-app/api/item/evaluation/count");

const getskusInfo = (params: wxRequestParams) => wxRequest(params, host + "/mall-app/api/shop/order/skuList");
// 获取拼团购活动信息
const queryGroupBuyActivity = (params: wxRequestParams) =>
    wxRequest(params, host + "/mall-app/api/gb_group/getGroupDetail");

// 用户参团
const memberJoinActivity = (params: wxRequestParams) => wxRequest(params, host + "/mall-app/api/gb_group/member_join");
// 用户开团
const memberCreateActivity = (params: wxRequestParams) =>
    wxRequest(params, host + "/mall-app/api/gb_group/before_pay_create");

// 查询我的团的信息
const queryMyGroupInfo = (params: wxRequestParams) => wxRequest(params, host + "/mall-app/api/gb_group/myGroup");

// 通过id查询活动状态
const queryActivityById = (params: wxRequestParams) => wxRequest(params, host + "/mall-app/api/gb_activity/info");

// 查询活动日志信息
const queryGroupRecordInfo = (params: wxRequestParams) => wxRequest(params, host + "/mall-app/api/gb_group/page");

const submitComment = (params: wxRequestParams) => wxRequest(params, host + "/mall-app/api/item/evaluation/submit");
const activityCancelBatch = (params: wxRequestParams) =>
    wxRequest(params, host + "/mall-app/api/app-activity/user/cancelSignUp");
const newAPi = {
    assistCoupon,
    getfissionDetail,
    inviteRejister,
    sendMsg,
    sendegisterMsg,
    sendUpdateMsg,
    updatePassword,
    getRegisterSource,
    staticLogin,
    loginPhone,
    loginPswOrVerify,
    activityList,
    activityDetail,
    activitySignUp,
    activitySubmitMaterial,
    activityChannel,
    activityUserList,
    getUserAmountDetail,
    getUserAmountSendList,
    getBalanceRemark,
    getUserAmountFlowList,
    getUnReadAmountSend,
    activityUserDetail,
    getWxappPages,
    getRankList,
    getAutoGetCoupons,
    getRegisterPrice,
    registerByPrice,
    getCarts,
    updateCarts,
    getCartsCount,
    batchDeleteCart,
    queryCoupons,
    autoCoupons,
    getGoodsCoupon,
    orderPreview,
    userGetCoupons,
    createFission,
    queryShopFissionActivity,
    getGoodsDetail,
    changeCartNum,
    getCartCount,
    planChangeCheck,
    getUserAmount,
    queryWaitGetCoupons,
    getUserInfo,
    saveUserInfo,
    updateSinglePrice,
    updateCheckedCarts,
    submit,
    queryRegisterByPay,
    getPriceOfRegister,
    getPopUpById,
    getSkuInfo,
    getskusInfo,
    getSubStoreGoodsList,
    getPersonalCenterActivity,
    getPersonalCenterInfo,
    syncWeChatUserInfo,
    getCartGoods,
    deleteCarts,
    getBullets,
    loginCheck,
    getDetailCommentInfo,
    getDetailCommentAllData,
    getDetailComments,
    submitComment,
    getKfAccountUrl,
    getCollectList,
    findActivityByOrderId,
    getSkus,
    getPurchaseCount,
    getPurchaseLists,
    getSkuDiscount,
    memberJoinActivity,
    memberCreateActivity,
    getCancelCollectBatch,
    getCancelCollect,
    updateCartsSku,
    queryGroupBuyActivity,
    queryMyGroupInfo,
    queryGroupRecordInfo,
    activityCancelBatch,
    queryActivityById,
};

export default newAPi;
