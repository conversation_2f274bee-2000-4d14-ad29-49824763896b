/**
 * 用户code 换取 session_key
 * @type {String}
 */
export const USER_SPECICAL_INFO = "userSpecialInfo";

export const default_avatar = "https://dante-img.oss-cn-hangzhou.aliyuncs.com/online/wx/default_avatar.png";
export const default_banner_home = "https://dante-img.oss-cn-hangzhou.aliyuncs.com/online/wx/default_bg.png";
export const default_banner_type = "https://dante-img.oss-cn-hangzhou.aliyuncs.com/online/wx/banner2.png";
export const buy_notice = "https://dante-img.oss-cn-hangzhou.aliyuncs.com/online/wx/buy_notice.jpg";

/**
 * 用户信息
 * @type {String}
 */
export const USER_INFO = "userInfo";
/**
 * 用户getUserInfo获取微信用户信息
 * @type {String}
 */
export const USER_WX_INFO = "userInfo_wx";
/**
 * 导购id
 * @type {String}
 */
export const GUIDER_INFO = "guiderId";
/**
 * 门店id
 * @type {String}
 */
export const STORE_ID = "storeId";
/**
 * 分享商品id
 * @type {Number}
 */
export const STORE_GOODS_ID = "storeGoodsId";

/**
 * 系统信息
 * @type {String}
 */
export const SYSTEM_INFO = "systemInfo";

/**
 * 默认地址信息
 * @type {number}
 */
export const ADDRESS_ID = "addressId";

export const PROJECT_ID = "projectId";

export const APP_ID = "APP_ID";

export const CAT_INDEX = "catIndex";
/**
 * 默认门店id
 */
export const SUB_STORE_ID = "subStoreId";

/**
 * 是否登录
 * 1: 登录 0: 未登录
 */
export const IS_LOGIN = "isLogin";

/**
 * IDENTITYINFO
 * 身份信息
 */
export const IDENTITYINFO = "IdentityInfo";

export const IDENTITY = "identity";

export const SUB_STORE_INFO = "subStoreInfo";

export const SHOP_INFO = "ShopInfo";

export const MEMBER_SHIP_LEVEL = "membershipLevel";

export const POSTER_NAME = "posterName";

export const SCAN_PAGE = "scanPage";

/**
 * signSuccessUrl
 * (临时标记)确认订单页检测未签约时缓存需要调整的订单详情地址，待签约成功后
 */
export const SIGN_SCCESS_URL = "signSuccessUrl";

/**
 * isSignSuccess
 * (临时标记)是否签约成功，只在通商云页面生成，每次进入小程序需要销毁
 */
export const IS_SIGN_SUCCESS = "isSignSuccess";

/**
 * signBackUrl
 * 签约需要返回的页面地址（交互优化）
 */
export const SING_BACK_URL = "signBackUrl";
