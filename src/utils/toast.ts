import Taro from "@tarojs/taro";

const normal = (msg: string, icon: "success" | "error" | "loading" | "none", duration?: number) => {
    Taro.showToast({
        title: filterEnText(msg),
        mask: true,
        icon: icon,
        duration: duration || 1500,
        // image: require('../assets/icons/warn.png')
    });
};
const success = (msg: string, duration?: number) => {
    Taro.showToast({
        title: filterEnText(msg),
        mask: true,
        icon: "success",
        duration: duration || 1500,
    });
};

// 失败
const error = (msg: string, duration?: number) => {
    Taro.showToast({
        title: filterEnText(msg),
        mask: true,
        icon: "error",
        duration: duration || 1500,
        // image: require('../assets/icons/error.png')
    });
};
// 加载中
const showLoading = (msg: string) => {
    Taro.showLoading({
        title: filterEnText(msg || ""),
        mask: true,
    });
};
// 关闭加载
const hideLoading = () => {
    try {
        Taro.hideLoading({
            fail: error => {
                console.log("hideLoading, fail ->: " + error);
            },
            success: res => {
                console.log("hideLoading, success ->: " + res);
            },
        });
    } catch (error) {}
};

const info = (msg: string, duration?: number) => {
    if (msg.length > 22) {
        Taro.showModal({
            title: msg,
            success: hideLoading,
            fail: hideLoading,
        });
        return;
    }
    Taro.showToast({
        title: filterEnText(msg),
        mask: true,
        icon: "none",
        duration: duration || 1500,
        // image: require('../assets/icons/warn.png')
    });
};

// 确认弹框
const showModal = params => {
    Taro.showModal(params);
};

// filter
const filterEnText = (msg: string) => {
    if (msg.indexOf(":") > -1 || msg.indexOf("：") > -1) {
        //@ts-ignore
        return msg
            .replace(/[a-zA-Z.]+:/g, "")
            .replace(/[a-zA-Z.]+：/g, "")
            .replaceAll("*", "");
    }
    //@ts-ignore
    return msg.replaceAll("*", "");
};

const DDYToast = {
    normal,
    info,
    showLoading,
    success,
    hideLoading,
    error,
    showModal,
};
export default DDYToast;
