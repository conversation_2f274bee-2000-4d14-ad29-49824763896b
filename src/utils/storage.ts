import { setStorageSync, getStorageSync, removeStorageSync } from "@tarojs/taro";

const configLis = [
    "subStoreId",
    "guiderId",
    "ShopInfo",
    "projectId",
    "APP_ID",
    "catIndex",
    "tbbarList",
    "identityInfo",
    "api_root",
    "siteInfo",
    "scene",
    "c",
];

const setStorage = setStorageSync;

const getStorage = getStorageSync;

const removeStorage = removeStorageSync;

export { setStorage, getStorage, removeStorage };
