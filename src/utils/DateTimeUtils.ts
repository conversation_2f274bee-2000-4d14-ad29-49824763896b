import dayjs from "dayjs";

/**
 * 时间戳->时间格式
 * @param timestamp 时间戳
 * @param formats  格式化  如：YYYY-MM-DD HH:mm:ss
 */
export function timestampToTime(timestamp, formats) {
    return dayjs(timestamp).format(formats);
}
export function formatEndTime(endTime) {
    const now = new Date();
    const end = new Date(endTime);
    const diffTime = Math.ceil((end - now) / (1000 * 60 * 60 * 24)); // 计算剩余天数
    return `还剩 ${diffTime} 天到期`;
}
