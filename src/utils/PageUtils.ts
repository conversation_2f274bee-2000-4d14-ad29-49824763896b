/**
 * 取当前页带参数的url
 */
import Taro from "@tarojs/taro";
import { getStorage, setStorage, removeStorage } from "./storage";
import { GUIDER_INFO, IDENTITY, SCAN_PAGE, STORE_GOODS_ID, STORE_ID, SUB_STORE_ID } from "./constant";
import { DDYNavigateTo } from "@/utils/route";

export function getCurrentPageUrlWithArgs() {
    const pages = Taro.getCurrentPages(); //小程序API，可以直接调用
    const currentPage = pages[pages.length - 1];
    const { route, options } = currentPage;
    let urlWithArgs = `/${route}?`;
    for (let key in options) {
        const value = options[key];
        urlWithArgs += `${key}=${value}&`;
    }

    return urlWithArgs.substring(0, urlWithArgs.length - 1);
}

export function getCurrentPageUrl() {
    const pages = Taro.getCurrentPages(); //获取加载的页面
    const currentPage = pages[pages.length - 1]; //获取当前页面的对象
    const url = currentPage.route;
    //当前页面url
    return url;
}

export function handleGoodsLink(option) {
    if (option.storeId) {
        //wepy.setStorageSync("storeId", option.storeId);
    }
    const { subStoreId, shareStoreId, id, guiderId, shareGuiderId } = option;
    console.log(
        "接收分享参数",
        `subStoreId=${subStoreId},shareStoreId=${shareStoreId},id=${id},guiderId=${guiderId},shareGuiderId=${shareGuiderId}`,
    );
    //判断是否有门店id
    let hasStoreId = shareStoreId || subStoreId;
    //判断是否有导购id
    let hasGuiderId = shareGuiderId || guiderId;
    if (subStoreId) {
        setStorage(SUB_STORE_ID, subStoreId);
    }
    if (shareStoreId) {
        setStorage(SUB_STORE_ID, shareStoreId);
    }
    //消费者先扫导购，再次扫门店，要清除导购id，
    if (getStorage(IDENTITY) === "buyer" && hasStoreId && !hasGuiderId) {
        removeStorage(GUIDER_INFO);
    }

    if (shareGuiderId) {
        setStorage(GUIDER_INFO, shareGuiderId);
    }
    if (guiderId) {
        setStorage(GUIDER_INFO, guiderId);
    }

    let goodsId = option.id;
    if (option.q || option.qrCode) {
        //表示扫描普通二维码进来的参数
        let optionQString = decodeURIComponent(option.q || option.qrCode);
        let strArr = optionQString.split("?")[1];
        let storeId = "";
        let storeSkuSn = "0";
        let atArr = strArr.split("&");
        for (let i = 0; i < atArr.length; i++) {
            let innerStr = atArr[i];
            let innerArr = innerStr.split("=");
            console.log(innerArr, "innerArr");
            if (innerStr.indexOf("storeId") != -1) {
                storeId = innerArr[1];
            } else if (innerStr.indexOf("skuSn") != -1) {
                storeSkuSn = innerArr[1];
            }
        }
        // that.goodsId = storeSkuSn;
        if (storeId != "0") {
            setStorage(STORE_ID, storeId);
        }

        //通过分享二维码进来时， 重新获取门店信息，避免购买时获取不到门店信息
        // await that.getStoreInfo();
        console.log("goods_detail.onload.substroeInfo为" + JSON.stringify(getStorage("subStoreInfo")));
        setStorage(SCAN_PAGE, "goods_detail?id=" + storeSkuSn);
        goodsId = storeSkuSn;
    }
    if (process.env.TARO_ENV == "alipay") {
        if (!goodsId) {
            goodsId = getStorage(STORE_GOODS_ID);
        }
    }
    return {
        id: goodsId,
    };
}

export function jumpLogin() {
    DDYNavigateTo({ url: "/pages/switch-login/index" });
}
