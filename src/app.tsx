import { Component, PropsWithChildren } from "react";
import "./app.less";
import { getStorage, removeStorage, setStorage } from "@/utils/storage";
import {
    APP_ID,
    CAT_INDEX,
    GUIDER_INFO,
    IS_SIGN_SUCCESS,
    PROJECT_ID,
    SIGN_SCCESS_URL,
    STORE_ID,
    SUB_STORE_ID,
    USER_INFO,
    STORE_GOODS_ID,
} from "./utils/constant";
import { getSystemInfoSync } from "@tarojs/taro";
import api from "@/utils/api";
import checkUpdateVersion from "./utils/wxUpdate";
import { localLoginMask, reLogin } from "@/utils/ylogin";
import { ConfigProvider } from "@antmjs/vantui";
import { themeVars } from "./app/theme";
import infoStore from "./store/info-store";
import identityStore from "./store/identity-store";
import { PROJECT_CONFIG } from "./utils/env";
import { removeSignback } from "./utils/common";
import { mall_event, mall_event_key, reportEvent } from "@/utils/track-report";
import { getCurrentPageUrl } from "@/utils/PageUtils";
import newApi from "@/utils/newApi";

const statusBarHeight = getSystemInfoSync().statusBarHeight;
const globalData = {
    userInfo: null,
    appid: PROJECT_CONFIG.APP_ID,
    storeId: PROJECT_CONFIG.STORE_ID, //  37 正式 177
    navigateToMiniProgramAppIdList: ["wx0fb538f6dc093892"],
    weDistributeSellerAppId: "wx0fb538f6dc093892",
    ShowSellerRecruit: false, // 是否显示我那里的店主招募
    // api地址
    api_root: "",
    // siteInfo: siteinfo,
    //@ts-ignore
    miniAppName: PROJECT_CONFIG.MINI_APP_NAME,
    //@ts-ignore
    subStoreId: PROJECT_CONFIG.SUB_STORE_ID, // 默认门店 177
    //@ts-ignore
    projectId: PROJECT_CONFIG.PROJECT_ID, // 项目id,用来获取小程序信息 21正式 25 测试环境
    //@ts-ignore
    statusBarHeight: statusBarHeight, // 小程序装修
    bottomBarHeight: 0,
    consoleSwitch: true,
};
class App extends Component<PropsWithChildren> {
    timer: NodeJS.Timer;
    async componentDidMount() {
        console.log("首页加载");
        // removeSignback();
        //
        // api.getMariaInfo({ data: {} }).then(res => {
        //     const arr = res;
        //     const result = arr.filter(item => item.id === "substoreId");
        //     if (result.length > 0) {
        //         result[0].name && setStorage("subStoreId", result[0].name);
        //     }
        // });
        //@ts-ignore
        this.globalData = globalData;
        //@ts-ignore

        let his = getStorage(STORE_ID);
        if (!his) {
            setStorage(STORE_ID, globalData.storeId);
        }
        setStorage(PROJECT_ID, globalData.projectId);
        setStorage(APP_ID, globalData.appid);
        setStorage(CAT_INDEX, 0);
    }

    componentDidShow(CallbackResult) {
        console.log("微信小程序入口", CallbackResult);
        this.initApp(CallbackResult);
        this.checkLogin();
    }

    async checkLogin() {
        let hasLogin = localLoginMask();
        let hold = await newApi.loginCheck({ data: {}, showError: true, filterCheck: true, method: "POST" });
        //如果信息过期,且以前登录过，则自动登录，以保持登录
        try {
            //如果信息过期,且以前登录过，则自动登录，以保持登录
            if (!hold && hasLogin) {
                try {
                    await reLogin();
                } catch (e) {
                    hasLogin = false;
                }
            }
            if (hasLogin) {
                this.defaultInitTongLian();
                await Promise.all([
                    infoStore.getNewUserProfile(false),
                    identityStore.getIdentityInfo(false),
                    infoStore.getStoreInfo(false),
                ]);
            }
        } catch (e) {}
    }

    initApp(option) {
        checkUpdateVersion();
        // 上次缓存的storeId
        let his = getStorage(STORE_ID);

        if (option.query && option.query.storeId) {
            globalData.storeId = option.query.storeId;
            if (option && option.query && option.query.subStoreId) {
                // console.log("SUB_STORE_ID:",)
                //globalData.subStoreId = option.query.subStoreId;
                setStorage(SUB_STORE_ID, option.query.subStoreId);
            }
            if (option && option.query && option.query.guiderId) {
                setStorage(GUIDER_INFO, option.query.guiderId);
            }
        } else if (option.query?.h === undefined) {
            if (his !== undefined && his !== null && his !== "") {
                // 表示上次存储的有值，使用之前的storeId
                globalData.storeId = his;
            }
        }
        // 分享的二维码有参数时
        if (option && option.query) {
            const { q, qrCode } = option.query;
            if (qrCode || q) {
                //表示扫描普通二维码进来的参数
                let optionQString = decodeURIComponent(q || qrCode);
                let strArr = optionQString.split("?")[1];
                let storeId = 0;
                let subStoreId = 0;
                let guiderId = 0;
                let storeSkuSn = 0;
                let atArr = strArr.split("&");
                for (let i = 0; i < atArr.length; i++) {
                    let innerStr = atArr[i];
                    let innerArr = innerStr.split("=");
                    if (innerStr.indexOf("storeId") != -1) {
                        storeId = +innerArr[1];
                    }
                    if (innerStr.indexOf("subStoreId") != -1) {
                        subStoreId = +innerArr[1];
                    }
                    if (innerStr.indexOf("guiderId") != -1) {
                        guiderId = +innerArr[1];
                    } else {
                        // 没有导购信息时说明扫的是门店的分享码，要清除以前扫的导购码
                        removeStorage("guiderId");
                    }
                    if (innerStr.indexOf("skuSn") != -1) {
                        storeSkuSn = +innerArr[1];
                    }
                }
                // 当扫描参数不为0，则才修改默认商家shopId,要不变是成0了
                if (storeId != 0) {
                    //@ts-ignore
                    globalData.storeId = storeId;
                } else {
                    globalData.storeId = his;
                }
                if (storeSkuSn != 0) {
                    setStorage(STORE_GOODS_ID, storeSkuSn);
                }
                //globalData.subStoreId=subStoreId
                if (subStoreId != 0) {
                    setStorage("subStoreId", subStoreId);
                }
                if (guiderId != 0) {
                    setStorage("guiderId", guiderId);
                }
            }
        }
        // 更新缓存
        setStorage(STORE_ID, globalData.storeId);
        setStorage(PROJECT_ID, globalData.projectId);
        // 点立即购买没绑门店再取默认门店
        if (!getStorage(SUB_STORE_ID)) {
            setStorage(SUB_STORE_ID, globalData.subStoreId);
        }
        setStorage(APP_ID, globalData.appid);

        this.commitCurrentReach();
    }

    commitCurrentReach() {
        api.commitReach({
            data: getStorage(GUIDER_INFO)
                ? {
                      shopId: getStorage(STORE_ID),
                      refererId: getStorage(GUIDER_INFO),
                      subStoreId: getStorage(SUB_STORE_ID),
                  }
                : {
                      shopId: getStorage(STORE_ID),
                      subStoreId: getStorage(SUB_STORE_ID),
                  },
            showError: false,
            showLoad: false,
            isDefaultGoLogin: false,
            filterCheck: true,
        });
    }

    /** 对静默用户做初始化 */
    defaultInitTongLian() {
        const userInfo = getStorage(USER_INFO);
        api.initTongLianMember({
            method: "POST",
            data: {
                appId: PROJECT_CONFIG.APP_ID,
                openId: userInfo.openId,
            },
            filterCheck: true,
            showLoad: false,
            showError: false,
        });
    }

    componentDidHide() {}

    onError(error) {
        let map = new Map();
        map.set(mall_event_key.MALL_KEY_GLOBAL_CATCH_ERROR_PAGE, getCurrentPageUrl());
        map.set(mall_event_key.MALL_KEY_GLOBAL_CATCH_ERROR_INFO, error.toString());
        reportEvent(mall_event.MALL_EVENT_GLOBAL_CATCH_ERROR, map);
    }

    // this.props.children 就是要渲染的页面
    render() {
        return <ConfigProvider themeVars={themeVars}>{this.props.children}</ConfigProvider>;
    }
}

export default App;
