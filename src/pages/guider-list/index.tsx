import React, { useMemo, useRef, useState } from "react";
import { Input, Textarea, View } from "@tarojs/components";
import {
    Empty,
    InfiniteScroll,
    InfiniteScrollProps,
    InfiniteScrollInstance,
    IPullToRefreshProps,
    PullToRefresh,
    Popup,
    Image,
    Button,
} from "@antmjs/vantui";
import api from "@/utils/api";
import { getStorage } from "@/utils/storage";
import usePoster from "@/components/createPoster/usePoster";
import "./index.less";
import { buildGuiderPost } from "@/components/createPoster/createPoster";
import { useDidShow } from "@tarojs/taro";
import { PROJECT_CONFIG } from "@/utils/env";
import { STORE_ID, SUB_STORE_ID, SUB_STORE_INFO, USER_INFO } from "@/utils/constant";
import AddGuider from "./add";
import { mall_event, mall_event_key, reportEvent } from "@/utils/track-report";

const GuiderList: React.FC = () => {
    const pageSize = 10;
    const [record, setRecord] = useState<any[]>();
    const [miniQr, setMiniQr] = useState();
    const [show, setShow] = useState(false);
    const [showAdd, setShowAdd] = useState(false);
    const pageNo = useRef(0);
    const infiniteScrollInstance = useRef<InfiniteScrollInstance>();
    useDidShow(() => {
        getInvitationRecord(true).then(data => {
            setRecord([...data]);
            infiniteScrollInstance.current?.reset();
        });
    });
    const { poster, sharePath, handleSavePhoto } = usePoster({
        posterInfo: miniQr,
        createPalette: () => {
            const { userName, avatarUrl } = getStorage(USER_INFO);
            const { name } = getStorage(SUB_STORE_INFO);
            return buildGuiderPost({
                postBgUrl: PROJECT_CONFIG.INVITE_BG,
                miniQr: miniQr,
                subStoreName: name,
                nickName: userName,
                avatarUrl: avatarUrl,
            });
        },
        onImgOK: () => {
            setShow(true);
        },
    });

    const onRefresh: IPullToRefreshProps["onRefresh"] = () => {
        return new Promise(resolve => {
            getInvitationRecord(true)
                .then(data => {
                    setRecord([...data]);
                    infiniteScrollInstance.current?.reset();
                    resolve(undefined);
                })
                .catch(e => {
                    resolve(undefined);
                });
        });
    };
    const loadMore: InfiniteScrollProps["loadMore"] = () => {
        return new Promise(resolve => {
            resolve("complete");
            // getInvitationRecord(false)
            //   .then(data => {
            //     setRecord(prevState => {
            //       let newRecord = [...(prevState || []), ...data];
            //       resolve(data.length < pageSize ? "complete" : "loading");
            //       return newRecord;
            //     });
            //   })
            //   .catch(e => {
            //     resolve("error");
            //   });
        });
    };

    const getInvitationRecord = isRefresh => {
        if (isRefresh) {
            pageNo.current = 1;
        } else {
            pageNo.current += 1;
        }
        return api.getGuiderList({
            data: {
                shopId: getStorage(STORE_ID),
                size: pageSize,
                page: pageNo.current,
            },
            filterCheck: true,
        });
    };
    const addGuider = () => {
        setShowAdd(true);
    };
    const inviteClick = () => {
        let subStoreId = getStorage(SUB_STORE_ID);
        reportEvent(
            mall_event.MALL_EVENT_SHARE_INVITE_GUIDER_MINI_QR,
            new Map()
                .set(mall_event_key.MALL_KEY_INVITE_SUBSTORE_ID, subStoreId)
                .set(mall_event_key.MALL_KEY_INVITE_ADD_GUIDER_FROM, "beforeGet"),
        );
        api.getQrCode({
            method: "POST",
            data: {
                shopId: getStorage(STORE_ID),
                projectId: PROJECT_CONFIG.PROJECT_ID,
                page: "pages/info",
                scene: `inviteSubStoreId=${subStoreId}`,
            },
            showLoad: true,
        })
            .then(res => {
                if (miniQr === res.qrCodeImageUrl) {
                    setShow(true);
                } else {
                    setMiniQr(res.qrCodeImageUrl);
                }
                reportEvent(
                    mall_event.MALL_EVENT_SHARE_INVITE_GUIDER_MINI_QR,
                    new Map()
                        .set(mall_event_key.MALL_KEY_INVITE_SUBSTORE_ID, subStoreId)
                        .set(mall_event_key.MALL_KEY_INVITE_ADD_GUIDER_RESULT, res?.qrCodeImageUrl ?? "")
                        .set(mall_event_key.MALL_KEY_INVITE_ADD_GUIDER_FROM, "getSuccess"),
                );
            })
            .then(e => {
                reportEvent(
                    mall_event.MALL_EVENT_SHARE_INVITE_GUIDER_MINI_QR,
                    new Map()
                        .set(mall_event_key.MALL_KEY_INVITE_SUBSTORE_ID, subStoreId)
                        .set(mall_event_key.MALL_KEY_INVITE_ADD_GUIDER_RESULT, "")
                        .set(mall_event_key.MALL_KEY_INVITE_ADD_GUIDER_FROM, "getFail"),
                );
            });
    };

    return (
        <PullToRefresh onRefresh={onRefresh}>
            <View className="guider-list">
                <View className="top">
                    <View className="top-item" onClick={addGuider}>
                        <View className="dt-mini  icon-mini-add-box-fill icon" />
                        <View className="text">添加导购</View>
                    </View>
                    <View className="top-item" onClick={inviteClick}>
                        <View className="dt-mini icon-mini-reply-fill icon" />
                        <View className="text">邀请导购</View>
                    </View>
                </View>
                <View className="list">
                    <View className="title">导购列表（{record?.length || 0}个导购员）</View>
                    {record?.length === 0 && (
                        <Empty
                            description="暂无数据！"
                            image="https://dante-img.oss-cn-hangzhou.aliyuncs.com/55439458101.png"
                        />
                    )}
                    <View className="items">
                        {record?.map((item, index) => {
                            return (
                                <View className="item" key={item.id}>
                                    <View className="item-num">序号：{item.id}</View>
                                    <View className="menbox">
                                        <View className="name">{item.name}</View>
                                    </View>
                                    <View className="mobile">{item.mobile}</View>
                                </View>
                            );
                        })}
                    </View>
                    <InfiniteScroll loadMore={loadMore} ref={infiniteScrollInstance} />
                </View>
                {poster}
                <Popup show={show} style={{ background: "#00000000" }}>
                    <View className="close" onClick={() => setShow(false)}>
                        <View className="iconfont iconfont_2n icon-close-circle-line icon"></View>
                    </View>
                    <View className="share-container">
                        <Image fit="widthFix" src={sharePath} className="share-image" />
                        <Button
                            className="save-btn"
                            onClick={() => {
                                handleSavePhoto().then(() => {
                                    setShow(false);
                                });
                            }}
                        >
                            保存图片
                        </Button>
                    </View>
                </Popup>
                <AddGuider
                    show={showAdd}
                    onClose={(isSuccess: boolean) => {
                        if (isSuccess) {
                            getInvitationRecord(true).then(data => {
                                setRecord([...data]);
                                infiniteScrollInstance.current?.reset();
                            });
                        }
                        setShowAdd(false);
                    }}
                />
            </View>
        </PullToRefresh>
    );
};
export default GuiderList;
