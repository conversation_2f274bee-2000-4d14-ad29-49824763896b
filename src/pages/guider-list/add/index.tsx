import React, { useEffect } from "react";
import { Input, View } from "@tarojs/components";
import { Button, Dialog, Form, FormItem, Popup } from "@antmjs/vantui";
import Toast from "@/utils/toast";
import "./index.less";
import api from "@/utils/api";
import { getStorage } from "@/utils/storage";
import { STORE_ID, SUB_STORE_INFO } from "@/utils/constant";
import { isString } from "@tarojs/shared";

interface Props {
    show: boolean;
    onClose: (isSuccess: boolean) => void;
}

const AddGuider: React.FC<Props> = ({ show, onClose }) => {
    const formIt = Form.useForm();

    const add = (value: Record<string, any>) => {
        api.addGuider({
            method: "POST",
            data: {
                ...value,
                shopId: getStorage(STORE_ID),
            },
            filterCheck: true,
        }).then(res => {
            onClose(true);
            Toast.success("保存成功");
        });
    };
    const valueTrim = name => {
        let fieldValue = formIt.getFieldValue(name);
        if (isString(fieldValue)) {
            formIt.setFieldsValue(name, fieldValue.trim());
        }
    };
    return (
        <Popup className={"add-guider"} show={show} title={"添加导购员"} round>
            <View>
                <View className="guiderForm_title">添加导购员</View>
                <Form
                    form={formIt}
                    initialValues={{ shopName: getStorage(SUB_STORE_INFO)?.name }}
                    onFinish={(errs, res) => add(res)}
                >
                    <FormItem label="当前门店" name="shopName" required borderBottom>
                        <Input disabled />
                    </FormItem>
                    <FormItem
                        label="导购姓名"
                        name="name"
                        required
                        borderBottom
                        trigger="onInput"
                        valueFormat={e => e.detail.value}
                    >
                        <Input placeholder="请输入导购姓名" onBlur={() => valueTrim("name")} maxlength={12} />
                    </FormItem>
                    <FormItem
                        label="手机号码"
                        name="mobile"
                        required
                        borderBottom
                        trigger="onInput"
                        valueFormat={e => e.detail.value}
                    >
                        <Input placeholder="请输入手机号码" maxlength={11} type="number" />
                    </FormItem>
                    <view className="btn_box">
                        <Button className="from_btn close_btn" onClick={() => onClose(false)}>
                            取消
                        </Button>
                        <Button
                            className="from_btn submit_btn"
                            onClick={() => {
                                formIt.submit();
                            }}
                        >
                            确定
                        </Button>
                    </view>
                </Form>
            </View>
        </Popup>
    );
};
export default AddGuider;
