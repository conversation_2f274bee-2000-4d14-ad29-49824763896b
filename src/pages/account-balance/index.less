.margin-small-top {
    margin-top: 8px;
}

.margin-base-top {
    margin-top: 24px;
}

.margin-base-left {
    margin-left: 24px;
}

.balance {
    padding: 24px;
    background-color: #f2f2f2;
    min-height: 100%;

    // 一级标题
    .title {
        font-size: 32px;
        color: #333;
        font-weight: 500;
    }

    // 二级标题
    .sub-title {
        font-size: 28px;
        color: #333;
        font-weight: 400;
    }

    // 三级标题
    .third-title {
        font-size: 26px;
        color: #b5b5b5;
    }

    // 四级标题
    .fourth-title {
        font-size: 18px;
        color: #b5b5b5;
    }

    &-header {
        background-color: #fff;
        padding: 32px;
        border-radius: 16px;
        margin-bottom: 40px;
        position: relative;
        overflow: hidden;

        .icon {
            width: 28px;
            height: 28px;
            margin-right: 12px;
        }

        &-content {
            display: flex;
            align-items: baseline;
        }

        .total-amount {
            color: #e64340;
            font-size: 72px;
            font-weight: bold;
            margin-bottom: 20px;
        }

        .sub-balance {
            display: flex;
            align-items: center;
            justify-content: space-between;
            background-color: #fff;

            &-item {
                flex: 1;
                display: flex;
                align-items: baseline;
            }

            &-content {
                display: flex;
                flex-direction: column;
                justify-content: center;
            }

            .divider {
                width: 2px;
                height: 100px;
                background-color: #ddd;
            }

            .hidden {
                visibility: hidden; // 隐藏但占位
            }

            .item-amount {
                color: #e64340;
                font-size: 40px;
                font-weight: bold;
                margin-top: 16px;
                .unit {
                    font-size: 32px;
                }
            }
        }

        .coins-image {
            position: absolute;
            top: 40px;
            right: 40px;
            width: 200px;
            height: 200px;
            opacity: 0.2;
        }
    }

    &-records {
        background-color: #fff;
        padding: 32px;
        border-radius: 16px;

        &-title {
            font-size: 32px;
            font-weight: bold;
        }

        .record-item {
            display: flex;
            align-items: center;
            border-bottom: 1px solid #f1f1f1;
            padding: 24px 0;

            .icon {
                margin-right: 24px;
                display: flex;
                align-items: center;
                justify-content: center;

                image {
                    width: 72px;
                    height: 72px;
                }
            }

            .content {
                flex: 1;

                .title {
                    font-size: 28px;
                    margin-bottom: 10px;
                }

                .amount {
                    font-size: 24px;
                    color: #888888;
                    margin-bottom: 10px;
                }

                .date {
                    font-size: 24px;
                    color: #888888;
                }
            }

            .amount-value {
                font-size: 32px;
                font-weight: 400;
                margin-left: auto;

                &.income {
                    color: #e64340;
                }

                &.expense {
                    color: #6dd400;
                }

                &.gary {
                    color: #d7d7d7;
                }
            }
        }
    }

    .rule-button {
        display: flex;
        flex-direction: column;
        align-items: center;
        width: 61px;
        text-align: center;
        position: fixed;
        top: 230px;
        z-index: 10;
        right: 0;
        height: 195px;
        background: rgba(255, 255, 255, 0.9);
        box-shadow: 0 2px 4px 0 rgba(91, 91, 91, 0.22);
        border-top-left-radius: 14px;
        border-bottom-left-radius: 14px;
        .text {
            margin-top: 21px;
            writing-mode: vertical-rl;
            /* 竖直排列，从右到左 */
            text-align: center;
            white-space: nowrap;
            height: 132px;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            font-size: 24px;
            letter-spacing: 6px;
            color: #5e5e5e;
        }
    }
}
