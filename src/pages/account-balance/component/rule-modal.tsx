/**
 * @description: 使用规则展示
 * @author: wangchunting
 */
import newAPi from "@/utils/newApi";
import { ActionSheet } from "@antmjs/vantui";
import { View } from "@tarojs/components";
import { useDidShow } from "@tarojs/taro";
import { useState } from "react";

interface RuleModalProps {
    open: boolean;
    onClose: () => void;
}
export default function RuleModal({ open, onClose }: RuleModalProps) {
    const [detail, setDetail] = useState(<></>);

    useDidShow(() => {
        getDeatil();
    });
    const getDeatil = () => {
        newAPi
            .getBalanceRemark({
                method: "POST",
                data: {},
            })
            .then(res => {
                setDetail(res);
            });
    };

    const createMarkup = htmlContent => {
        return { __html: htmlContent };
    };

    return (
        <ActionSheet show={open} title="使用规则" onClose={onClose}>
            <View dangerouslySetInnerHTML={createMarkup(detail)}></View>
        </ActionSheet>
    );
}
