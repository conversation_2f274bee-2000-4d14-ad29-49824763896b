/**
 * @description: 福豆/福卡明细
 * @author: wangchunting
 */
import newAPi from "@/utils/newApi";
import { Image, View } from "@tarojs/components";
import { useDidShow } from "@tarojs/taro";
import { useState } from "react";
import { FLAG_ENUM, TYPE_ENUM } from "../enum";
import { formatAmountUtil } from "../fn/formatAmountUtil";
import "../index.less";

interface BalanceRecordsProps {
    /**
     * 只获取福卡或者福豆的数据，如果不传就获取全部
     */
    isAmountType?: boolean;
    title?: string;
}
function BalanceRecords({ isAmountType, title }: BalanceRecordsProps) {
    const [records, setRecords] = useState<any>([]);
    useDidShow(() => {
        getSendList();
    });

    // 余额变更记录
    const getSendList = () => {
        let values: { amountType?: number } = {};
        if (isAmountType) {
            values.amountType = TYPE_ENUM.CASH;
        }
        newAPi
            .getUserAmountFlowList({
                method: "POST",
                data: values,
            })
            .then(data => {
                setRecords(data);
            });
    };

    // 标识展示
    let flagDetails = [
        {
            flag: FLAG_ENUM.GET,
            color: "income",
            icon: "https://dante-img.oss-cn-hangzhou.aliyuncs.com/14138733118.png",
            sign: "+",
        },
        {
            flag: FLAG_ENUM.USE,
            color: "expense",
            icon: "https://dante-img.oss-cn-hangzhou.aliyuncs.com/89617770537.png",
            sign: "-",
        },
        {
            flag: FLAG_ENUM.RETURN,
            color: "income",
            icon: "https://dante-img.oss-cn-hangzhou.aliyuncs.com/14138733118.png",
            sign: "+",
        },
        {
            flag: FLAG_ENUM.EXPIRE,
            color: "gary",
            icon: "https://dante-img.oss-cn-hangzhou.aliyuncs.com/93861543295.svg",
            sign: "-",
        },
    ];

    return (
        <View className="balance-records">
            <View className="balance-records-title">{title}</View>
            {records.map(record => {
                const recordFlag = flagDetails.find(item => item.flag === record.flag);
                return (
                    <View className="record-item" key={record.id}>
                        <View className="icon">
                            <Image src={recordFlag?.icon ?? ""} />
                        </View>
                        <View className="content">
                            <View className="title">{record?.activityName}</View>
                            {}
                            <View className="amount">
                                变更后{record?.amountTypeDesc}余额:
                                {/* {record?.amountType === TYPE_ENUM.CASH  && "￥"}
                                {record?.amount}
                                {record?.amountType === TYPE_ENUM.COUPON  && "个"} */}
                                {formatAmountUtil(record?.amountType, record?.amount)}
                            </View>
                            <View className="date">
                                {record?.alterTimeDesc} {record?.flagDesc}
                            </View>
                        </View>
                        <View className={`amount-value ${recordFlag?.color}`}>
                            {recordFlag?.sign}
                            {/* ￥{record?.alterAmount} */}
                            {formatAmountUtil(record?.amountType, record?.alterAmount)}
                        </View>
                    </View>
                );
            })}
        </View>
    );
}

export default BalanceRecords;
