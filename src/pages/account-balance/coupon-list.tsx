/**
 * @description: 福豆列表
 */
import newAPi from "@/utils/newApi";
import { Image, Text, View } from "@tarojs/components";
import { useDidShow } from "@tarojs/taro";
import { useState } from "react";
import "./coupon-list.less";
import { STATUS_ENUM } from "./enum";

export default function CouponList() {
    const [availableCoupons, setAvailableCoupons] = useState([]);
    const [expiredCoupons, setExpiredCoupons] = useState([]);

    useDidShow(() => {
        getSendList(STATUS_ENUM.ENABLE);
        getSendList(STATUS_ENUM.EXPIRE);
    });

    // 福豆券列表
    const getSendList = status => {
        newAPi
            .getUserAmountSendList({
                method: "POST",
                data: {
                    status: status,
                },
            })
            .then(data => {
                if (status === STATUS_ENUM.ENABLE) {
                    setAvailableCoupons(data);
                }
                if (status === STATUS_ENUM.EXPIRE) {
                    setExpiredCoupons(data);
                }
            });
    };

    return (
        <View className="coupon">
            <View className="coupon-list">
                <View className="title">
                    <Image className="icon" src="https://dante-img.oss-cn-hangzhou.aliyuncs.com/55826433895.png" />
                    可用福豆
                </View>
                <View className="line"></View>
                {availableCoupons.map((coupon: any) => (
                    <View className="coupon-item" key={coupon?.id}>
                        <View className="left">
                            <Image
                                className="icon"
                                src="https://dante-img.oss-cn-hangzhou.aliyuncs.com/14138733118.png"
                            />
                            <View className="content">
                                <Text className="name">{coupon?.activityName}</Text>
                                <View className="date">{coupon?.activityEndTimeDesc} 过期</View>
                            </View>
                        </View>
                        <View className="right">{coupon?.amount}个</View>
                    </View>
                ))}
            </View>

            {expiredCoupons.length > 0 && (
                <View className="coupon-list" style={{ marginTop: "20px" }}>
                    <View className="expired-title">
                        <Image className="icon" src="https://dante-img.oss-cn-hangzhou.aliyuncs.com/55989634402.png" />
                        已过期
                    </View>
                    <View className="line"></View>
                    {expiredCoupons.map((coupon: any) => (
                        <View className="coupon-item expired" key={coupon?.id}>
                            <View className="left">
                                <Image
                                    className="icon"
                                    src="https://dante-img.oss-cn-hangzhou.aliyuncs.com/29595410858.svg"
                                />
                                <View className="content">
                                    <Text className="name">{coupon?.activityName}</Text>
                                    <View className="date">{coupon?.activityEndTimeDesc} 过期</View>
                                </View>
                            </View>
                            <View className="right">{coupon?.amount}个</View>
                        </View>
                    ))}
                </View>
            )}
        </View>
    );
}
