.product-card-warp {
    width: 100%;
    min-height: 100%;
    background-size: cover;
    background-position: center;
    // height: 100vh;
    background-image: url("https://dante-img.oss-cn-hangzhou.aliyuncs.com/97972241682.png");

    .nav-bar {
        background-color: transparent;

        .van-icon-arrow-left:before {
            content: "\e668";
            color: #000;
            font-size: 48px;
        }
    }

    .product-card-content {
        background-color: rgba(255, 9, 64, 0.09);
        min-height: 100vh;
        z-index: 100;
        padding-bottom: 32px;

        .product-card-header {
            width: 100%;
            display: flex;
            align-items: center;
            justify-content: center;

            .product-card-title {
                background: url(https://dante-img.oss-cn-hangzhou.aliyuncs.com/9978316470.png) 100% no-repeat;
                background-size: 100% 100%;
                width: 564px;
                height: 161px;
                position: relative;
                display: flex;
                align-items: center;
                justify-content: center;
                flex-direction: column;

                .title {
                    font-size: 45px;
                    color: #ffffff;
                    line-height: 53px;
                    -webkit-text-stroke: 2px #5c5c77;
                }

                .sub-title {
                    font-weight: 300;
                    font-size: 22px;
                    color: #727272;
                    margin-top: 8px;
                }
            }
        }

        .product-card-item {
            background-color: #fff;
            border-radius: 16px;
            position: relative;
            box-sizing: border-box;
            margin: 32px;
            display: flex;
            align-items: center;
            padding: 24px;

            .top-label {
                position: absolute;
                top: -18px;
                left: 10px;

                .top-image {
                    width: 80px;
                    height: 80px;
                }
            }

            .product-left {
                margin-left: 88px;
                width: 30%;
            }

            .product-right {
                width: 70%;
            }

            /* 商品图片样式 */
            .product-image {
                width: 140px;
                height: 140px;
                border-radius: 5px;
                margin-right: 20px;
            }

            /* 商品信息样式 */
            .product-info {
                overflow: hidden;
                /* 防止文字环绕 */
                margin-bottom: 10px;

                .product-title {
                    font-weight: 400;
                    font-size: 26px;
                    color: #000000;
                    margin-bottom: 5px;
                }

                .product-features {
                    display: flex;
                    align-items: center;
                    font-size: 12px;
                    color: #757575;

                    .guarantee {
                        display: flex;
                        align-items: center;
                        margin-right: 10px;

                        .dot {
                            color: #ffca28;
                            margin-right: 3px;
                        }
                    }
                }

                .guarantee {
                    font-weight: 300;
                    font-size: 18px;
                    color: #999999;
                }
            }

            /* 价格和购买按钮样式 */
            .price-and-button {
                display: flex;
                align-items: center;
                justify-content: space-between;
                margin-top: 24px;

                .price {
                    font-weight: 500;
                    font-size: 26px;
                    color: #e9b140;
                }

                .buy-button {
                    font-weight: 400;
                    font-size: 22px;
                    background: #ffdb51;
                    border-radius: 34px;
                    color: #fff;
                    cursor: pointer;
                    padding: 12px 24px;
                }
            }
        }
    }
}
