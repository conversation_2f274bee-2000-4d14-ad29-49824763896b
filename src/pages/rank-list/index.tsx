/**
 * @description 排行榜
 */
import { isAlipay } from "@/utils/common";
import newAPi from "@/utils/newApi";
import { DDYNavigateTo, DDYBack } from "@/utils/route";
import { NavBar } from "@antmjs/vantui";
import { Image, Text, View } from "@tarojs/components";
import Taro, { useRouter } from "@tarojs/taro";
import { useEffect, useState } from "react";
import "./index.less";

const ProductCard = () => {
    // title  标题  introduce  介绍  shopCategoryId 分类id
    const { title, introduce, shopCategoryId } = useRouter().params;
    const [dataList, setDataList] = useState<any>([]);
    const statusBarHeight = Taro?.getSystemInfoSync().statusBarHeight;
    const rankIcon = [
        "https://dante-img.oss-cn-hangzhou.aliyuncs.com/57560427103.png",
        "https://dante-img.oss-cn-hangzhou.aliyuncs.com/5755767277.png",
        "https://dante-img.oss-cn-hangzhou.aliyuncs.com/57555214425.png",
    ];

    useEffect(() => {
        newAPi
            .getRankList({
                data: {
                    shopCategoryId: shopCategoryId,
                    size: 10,
                },
                method: "POST",
                filterCheck: true,
            })
            .then(res => {
                setDataList(res.data);
            });
    }, []);
    return (
        <>
            <View className="product-card-warp">
                <NavBar
                    fixed
                    className="nav-bar"
                    border={false}
                    leftArrow={!isAlipay()}
                    leftText=""
                    onClickLeft={() => {
                        DDYBack();
                    }}
                />
                <View className="product-card-content" style={{ paddingTop: `${(statusBarHeight || 20) + 40}px` }}>
                    <View className="product-card-header">
                        <View className="product-card-title">
                            <View className="title">{title}</View>
                            <View className="sub-title">{introduce}</View>
                        </View>
                    </View>
                    {dataList?.map((item, index) => {
                        return (
                            <View key={index} className="product-card-item">
                                <View className="product-left">
                                    {/* 前三条有标签 */}
                                    {index < 3 && (
                                        <View className="top-label">
                                            <Image className="top-image" src={rankIcon[index]} />
                                        </View>
                                    )}
                                    <Image className="product-image" src={item?.mainImage} />
                                </View>

                                <View className="product-right">
                                    <View className="product-info">
                                        <Text className="product-title">{item?.name}</Text>
                                    </View>

                                    <View className="price-and-button">
                                        <Text className="price">¥ {item.lowPrice / 100}</Text>
                                        <View
                                            className="buy-button"
                                            onClick={() => {
                                                DDYNavigateTo({ url: `/pages/goods_detail?id=${item.id}` });
                                            }}
                                        >
                                            立即抢购
                                        </View>
                                    </View>
                                </View>
                            </View>
                        );
                    })}
                </View>
            </View>
        </>
    );
};

export default ProductCard;
