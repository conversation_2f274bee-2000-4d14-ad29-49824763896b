import "./index.less";

import { Image, View } from "@tarojs/components";
import { useEffect, useState } from "react";

import { DDYSwitchTab } from "@/utils/route";
import Taro from "@tarojs/taro";
import newAPi from "@/utils/newApi";

export default () => {
    const [splashImage, setSplashImage] = useState("");
    const [countdown, setCountdown] = useState(3);
    const [timer, setTimer] = useState<NodeJS.Timeout>();
    const [statusBarHeight, setStatusBarHeight] = useState(0);

    // 跳转到首页
    const navigateToHome = () => {
        // 清除定时器
        if (timer) {
            clearInterval(timer);
            setTimer(undefined);
        }
        DDYSwitchTab({
            url: "/pages/index/index",
        });
    };

    useEffect(() => {
        // 获取状态栏高度
        const systemInfo = Taro.getSystemInfoSync();
        setStatusBarHeight(systemInfo.statusBarHeight || 0);

        // 获取启动页配置
        newAPi
            .getWxappPages({
                data: { pageType: 100 },
                method: "POST",
                filterCheck: true,
            })
            .then(res => {
                if (Array.isArray(res.data) && res.data.length > 0) {
                    const data = res.data[0].publishData;
                    if (data) {
                        const parseData = JSON.parse(data).pages[0];

                        if (parseData.pageSet && parseData.pageSet.imageUrl) {
                            setSplashImage(parseData.pageSet.imageUrl);
                            // 开始倒计时
                            const intervalTimer = setInterval(() => {
                                setCountdown(prev => {
                                    if (prev <= 1) {
                                        clearInterval(intervalTimer);
                                        navigateToHome();
                                        return 0;
                                    }
                                    return prev - 1;
                                });
                            }, 1000);
                            setTimer(intervalTimer);
                        } else {
                            navigateToHome();
                        }
                    } else {
                        navigateToHome();
                    }
                } else {
                    navigateToHome();
                }
            });

        // 组件卸载时清除定时器
        return () => {
            if (timer) {
                clearInterval(timer);
            }
        };
    }, []);

    return (
        <View className="splash-page">
            {splashImage && (
                <>
                    <Image className="splash-image" src={splashImage} mode="aspectFill" />
                    <View
                        className="skip-button"
                        onClick={navigateToHome}
                        style={{
                            top: `${statusBarHeight + 20}px`,
                        }}
                    >
                        跳过 {countdown}s
                    </View>
                </>
            )}
        </View>
    );
};
