import GoodsDetail from "./goods_detail/index";
definePageConfig({
    usingComponents: {
        // painter: "../../components/painter/painter",
    },
    enableShareAppMessage: true,
    // navigationStyle: "custom",
    navigationBarTitleText: "商品详情",
    transparentTitle: "always",
    titlePenetrate: "YES", // 允许点击穿透后，才能触发导航栏上的 onTap 事件
    // navigationBarTextStyle: "white"
});
export default () => {
    return <GoodsDetail />;
};
