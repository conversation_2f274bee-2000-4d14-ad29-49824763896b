import { View } from "@tarojs/components";
import "./index.less";
import { useDidShow, useRouter } from "@tarojs/taro";
import { isAlipay } from "@/utils/common";
import DesginPage from "@/components/desgin";

export default () => {
    const { pageId } = useRouter().params;
    useDidShow(() => {
        if (isAlipay()) {
            //@ts-ignore
            my.hideBackHome();
        }
    });

    return (
        <View className="home-page">
            <DesginPage pageType={20} pageId={pageId} />
        </View>
    );
};
