/**
 * @description 付费页面
 */
import newAPi from "@/utils/newApi";
import DDYToast from "@/utils/toast";
import { ActionSheet, Checkbox, Field, Icon, Image } from "@antmjs/vantui";
import { Button, View } from "@tarojs/components";
import Taro, { useRouter } from "@tarojs/taro";
import { useEffect, useRef, useState } from "react";
import "../register/index.less";
import "./index.less";
import DesignPage from "@/components/desgin";
import { getStorage, setStorage } from "@/utils/storage";
import { LOGIN_BACK_URL, USER_INFO } from "@/utils/constant";
import { loginSuccessRediction, useSafeRouter } from "@/utils/common";
import { DDYNavigateTo } from "@/utils/route";
import { DDYObject } from "src/type/common";
import useCheckedAgreement from "../login/useCheckedAgreement";

function App() {
    const { activityId, marketingToolId, previewMode, groupId } = useSafeRouter().params;
    const [phone, setPhone] = useState("");
    const [code, setCode] = useState("");
    const [counting, setCounting] = useState(false);
    const [showPassword, setShowPassword] = useState(false);
    const [password, setPassword] = useState("");
    const [count, setCount] = useState(60);
    const timerRef = useRef<NodeJS.Timeout>();
    const [show, setShow] = useState(false);
    const [value, setValue] = useState("");
    const [sources, setSources] = useState([]);
    const [pageSet, setPageSet] = useState({});
    const { isChecked, node } = useCheckedAgreement();

    const getCode = () => {
        if (!/^1[3-9]\d{9}$/.test(phone)) {
            DDYToast.info("请输入正确的手机号");
            return;
        }
        newAPi
            .sendegisterMsg({
                method: "POST",
                data: {
                    phone,
                },
            })
            .then(res => {
                console.log("res:", res);
                DDYToast.info("发送成功");
                setCounting(true);
                timerRef.current = setInterval(() => {
                    setCount(prevCount => {
                        if (prevCount <= 1) {
                            clearInterval(timerRef.current);
                            setCounting(false);
                            return 60;
                        }
                        return prevCount - 1;
                    });
                }, 1000);
            });
    };

    const getSource = () => {
        newAPi
            .getRegisterSource({
                method: "POST",
                data: {},
            })
            .then(res => {
                setSources(res);
            });
    };

    const handleSubmit = () => {
        if (!isChecked) {
            DDYToast.info("请阅读用户注册协议和隐私保护");
            return;
        }
        if (!phone || phone.length !== 11) {
            DDYToast.info("请输入正确的手机号");
            return;
        }
        if (!code || code.length !== 6) {
            DDYToast.info("请输入正确的验证码");
            return;
        }
        if (!value?.id) {
            DDYToast.info("请选择来源渠道");
            return;
        }
        Taro.login().then(res => {
            if (res.code) {
                newAPi
                    .registerByPrice({
                        method: "POST",
                        data: {
                            wxCode: res.code,
                            phone: phone,
                            password: password,
                            smsCode: code,
                            channelType: value?.id,
                            activityId: activityId || "",
                            marketingToolId: marketingToolId || "",
                            groupId: groupId || "",
                            // payChannel: '1'
                        },
                    })
                    .then(res => {
                        // cons
                        // 微信支付
                        const payObj: DDYObject = JSON.parse(res.redirectInfo);
                        DDYToast.showLoading("支付查询中");
                        Taro.requestPayment({
                            //@ts-ignore
                            appId: payObj.appId,
                            timeStamp: payObj.timeStamp,
                            nonceStr: payObj.nonceStr,
                            package: payObj.package,
                            signType: payObj.signType,
                            paySign: payObj.paySign,
                            success(successRes) {
                                // DDYToast.hideLoading();
                                // const
                                // queryRegister();
                                let count = 1;
                                let timer = setInterval(() => {
                                    console.log("sss");
                                    if (count > 5) {
                                        DDYToast.hideLoading();
                                        clearInterval(timer);
                                        DDYToast.info("注册失败");
                                        return;
                                    }
                                    queryRegister(() => {
                                        DDYToast.hideLoading();
                                        clearInterval(timer);
                                    });
                                    count++;
                                }, 1000);
                            },
                            fail(failRes) {
                                DDYToast.hideLoading();
                                /**
                                 * 本因在调用支付之前判断的实名认证失败不知为何是在支付失败后提示
                                 * */
                                if (payObj.code === "ID-ERROR") {
                                    DDYToast.info("支付失败：实名认证失败");
                                } else {
                                    DDYToast.info("支付失败");
                                }
                            },
                        });
                    });
            }
        });
    };

    const queryRegister = fn => {
        Taro.login()
            .then(res => {
                if (res.code) {
                    newAPi
                        .queryRegisterByPay({
                            method: "POST",
                            data: { wxCode: res.code },
                            showError: false,
                        })
                        .then(data => {
                            console.log(data);
                            fn && fn();
                            DDYToast.success("注册成功");
                            const url = getStorage(LOGIN_BACK_URL);
                            if (!url && !data.inGroup && data.isJumpAllowed) {
                                setStorage(LOGIN_BACK_URL, `pages/wool-herd/index?join=false`);
                            }
                            setStorage(USER_INFO, data);
                            setTimeout(() => {
                                loginSuccessRediction();
                            }, 1000);
                        });
                } else {
                    console.log("res.code:", res);
                }
            })
            .catch(err => {
                console.log("err:", err);
            });
    };

    const togglePassword = () => {
        setShowPassword(!showPassword);
    };
    useEffect(() => {
        getSource();
    }, []);
    useEffect(() => {
        // if(useRouter())
        // { id: "FRIEND", name: "朋友推荐" }
        if (groupId) {
            setValue({ id: "FRIEND", name: "朋友推荐" });
        }
    }, []);
    return (
        <View className="register-price login-warp">
            <View className="login-content">
                <View className="price-container">
                    <Image width={"100%"} height={Taro.pxTransform(240)} src={pageSet.topPic} />
                </View>

                <View className="form-container">
                    <View className="form-item">
                        <View className="label">手机号</View>
                        <Field
                            maxlength={11}
                            placeholder="请输入手机号"
                            value={phone}
                            onChange={e => setPhone(e.detail)}
                            className="input"
                        />
                    </View>

                    <View className="form-item">
                        <View className="label">验证码</View>
                        <Field
                            maxlength={6}
                            placeholder="请输入验证码"
                            value={code}
                            onChange={e => setCode(e.detail)}
                            className="input"
                            renderButton={
                                <Button onClick={getCode} disabled={counting} className="verify-button">
                                    {counting ? `${count}s后重试` : "获取验证码"}
                                </Button>
                            }
                        />
                    </View>

                    {/* <View className="form-item">
                        <View className="label">密码</View>
                        <Field
                            placeholder="请输入密码"
                            value={password}
                            onChange={e => setPassword(e.detail)}
                            className="input"
                            type={showPassword ? "text" : "password"}
                            renderButton={
                                <View onClick={togglePassword} className="password-icon">
                                    <Icon name={showPassword ? "eye-o" : "closed-eye"} size={42} />
                                </View>
                            }
                        />
                    </View> */}

                    <View className="form-item">
                        <View className="label">来源</View>
                        <View
                            className="input"
                            style={{ lineHeight: "24px", flex: 1, height: "24px" }}
                            onClick={() => {
                                if (groupId) {
                                    return;
                                }
                                setShow(true);
                            }}
                        >
                            {value?.name}
                        </View>
                        <ActionSheet
                            show={show}
                            actions={sources}
                            onClose={() => setShow(false)}
                            onSelect={e => {
                                console.log(e.detail);
                                setValue(e.detail);
                                setShow(false);
                            }}
                        />
                    </View>
                    {/* 用户协议 */}
                    {node}

                    <Button onClick={handleSubmit} className="submit-button">
                        {pageSet?.registerPrice} 立即注册
                    </Button>
                    <DesignPage
                        pageType={30}
                        previewMode={previewMode}
                        onGetPageConfig={page => {
                            setPageSet(page.pageSet);
                            Taro.setNavigationBarTitle({ title: page?.pageSet?.title || "注册" });
                        }}
                    />
                </View>
            </View>
        </View>
    );
}

export default App;
