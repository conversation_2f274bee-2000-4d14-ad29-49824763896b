import { Steps } from "@antmjs/vantui";
import { View, Text } from "@tarojs/components";
import "./index.less";
import { useEffect, useState } from "react";
import api from "@/utils/api";
import { useRouter } from "@tarojs/taro";
import Taro from "@tarojs/taro";
import DDYToast from "@/utils/toast";

definePageConfig({
    // navigationStyle: "custom",
    navigationBarTitleText: "物流详情",
    // navigationBarTextStyle: "white"
});

export default () => {
    const [detail, setDetail] = useState({ expNo: "", comName: "" });
    const [list, setList] = useState([]);
    const route = useRouter();
    const getDetail = (orderId, orderType) => {
        api.getExpressDetail({
            data: {
                orderId,
                orderType,
            },
            filterCheck: true,
        })
            .then(res => {
                console.log("res:", res);
                setDetail(res.data);
                const result = res.data.dataList.map(item => {
                    return {
                        text: item.info,
                        desc: item.time,
                    };
                });
                setList(result);
            })
            .catch(err => {
                console.log("err:", err);
            });
    };

    useEffect(() => {
        getDetail(route.params.orderId, route.params.orderType || 1);
    }, []);
    return (
        <View className="logistics">
            <View className="logistics-head">
                <View
                    className="logistics-head-item"
                    onClick={() => {
                        Taro.setClipboardData({
                            data: detail.expNo,
                            success: function (res) {
                                DDYToast.info("复制成功");
                            },
                        });
                    }}
                    style={{ marginBottom: "24rpx" }}
                >
                    物流单号：{detail.expNo} <Text className="copy">复制</Text>
                </View>
                <View className="logistics-head-item">物流公司：{detail.comName}</View>
            </View>
            <View className="list">
                <View className="list-title">订单跟踪</View>
                <View className="list_content">
                    <Steps steps={list} active={0} direction="vertical" activeColor="#ee0a24" />
                </View>
            </View>
        </View>
    );
};
