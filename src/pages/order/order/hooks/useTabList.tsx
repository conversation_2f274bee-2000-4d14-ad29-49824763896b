import api from "@/utils/api";
import { IDENTITY, STORE_ID, USER_INFO } from "@/utils/constant";
import { getStorage } from "@/utils/storage";
import { useEffect, useState } from "react";

export default () => {
    const [tabList, setTabList] = useState([
        { title: "全部", dotNum: 0 },
        { title: "待付款", dotNum: 0 },
        { title: "待发货", dotNum: 0 },
        { title: "待收货", dotNum: 0 },

        { title: "退款/售后", dotNum: 0 },
        { title: "已完成", dotNum: 0 },
    ]);

    const getTabsData = () => {
        const identity = getStorage(IDENTITY);
        let userInfo = getStorage(USER_INFO) || {};
        let way = 0; // 默认为0,0为消费者端,1为供应商端,2为门店,3为导购员
        switch (identity) {
            case "guider":
                way = 3;
                break;
            case "seller":
                way = 2;
                break;
            case "sellerAudit":
            case "buyer":
                way = 0;
                break;
        }
        let queryData = {
            buyerId: userInfo.userId,
            way,
            outFrom: "communityOperation",
            shopId: getStorage(STORE_ID),
            type: 1, //1是正常订单 3积分订单
        };
        api.countByStatusAll({
            data: queryData,
            filterCheck: true,
            showError: false,
        }).then(res => {
            tabList[1].dotNum = res.tobeshipped;
            tabList[2].dotNum = res.shipped;
            tabList[3].dotNum = res.toBeReceived;
            setTabList([...tabList]);
        });
    };

    useEffect(() => {
        getTabsData();
    }, []);
    return [tabList, getTabsData];
};
