import { View } from "@tarojs/components";
import { useState } from "react";
import NewTabs from "../../../components/new-tabs";
import OrderList from "./componens/order-list";
import "./index.less";
import { useDidShow, useRouter } from "@tarojs/taro";
import useTabList from "./hooks/useTabList";
import { getStorage } from "@/utils/storage";
import { IS_LOGIN } from "@/utils/constant";
import { jumpLogin } from "@/utils/PageUtils";
import { Button, Empty } from "@antmjs/vantui";
definePageConfig({
    navigationBarTitleText: "我的订单",
});
export default () => {
    const route = useRouter();
    const [tabList, getTabsData] = useTabList();
    const [current, setCurrent] = useState(route.params.tab);

    // 为小程序审核添加的逻辑
    const [isLogin, setIsLogin] = useState(!!getStorage(IS_LOGIN));

    useDidShow(() => {
        setIsLogin(!!getStorage(IS_LOGIN));
    });

    return (
        <View className="order">
            <NewTabs
                tabList={tabList}
                onchange={e => {
                    setCurrent(e.index);
                }}
                current={current ? Number(current) : 0}
            />
            {isLogin ? (
                <OrderList current={current} sourceBuy={true} updateTabsData={getTabsData} />
            ) : (
                <View
                    className="flex_row v_center"
                    style={{ width: "100%", alignItems: "center", justifyContent: "center" }}
                >
                    <Empty
                        description="请先登录再查看数据"
                        image="https://dante-img.oss-cn-hangzhou.aliyuncs.com/55439458101.png"
                    >
                        <Button className="to-login" onClick={() => jumpLogin()}>
                            立即登录
                        </Button>
                    </Empty>
                </View>
            )}
        </View>
    );
};
