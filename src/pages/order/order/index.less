.order {
    padding-top: 100px;
    .isFirst {
        width: 52rpx;
        height: 28rpx;
        background: linear-gradient(90deg, #f50050 0%, #f5001d 100%);
        border-radius: 4rpx;
        font-size: 20rpx;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #ffffff;
        line-height: 28rpx;
        text-align: center;
    }
    .to-login {
        font-size: 40px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: #333333;
        line-height: 56px;
    }
    .van-tabs__wrap--scrollable .van-tab:nth-child(5) {
        width: 200px !important;
        flex-basis: unset !important;
    }
    .order-list {
        .order-list-item {
            .order-item-bottom {
                .pay {
                    // color: #f5001d;
                    // border-color: #f5001d;
                }
            }
        }
    }
    .van-tabs__line {
        display: none;
    }
}
