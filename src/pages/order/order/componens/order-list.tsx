import api from "@/utils/api";
import { STORE_ID, USER_INFO } from "@/utils/constant";
import { getStorage } from "@/utils/storage";
import { Empty, InfiniteScroll, PullToRefresh, VirtualList } from "@antmjs/vantui";
import { View, Image, Text, ScrollView } from "@tarojs/components";
import { useRouter } from "@tarojs/taro";
import { memo, useEffect, useRef, useState } from "react";
import OrderItem from "@/components/order-item";
import { DDYNavigateTo } from "@/utils/route";
import { OrderDetailTYPE, shopOrderObj, shopOrderOperationObj } from "../../order-detail/hooks/useDetail";
import { timestampToTime } from "@/utils/DateTimeUtils";
import { DDYObject } from "src/type/common";
import "../index.less";
import { mall_event, mall_event_key, reportEvent } from "@/utils/track-report";
// 根据tabindex 映射 对应的orderType
const ORDER_STATUS = {
    0: "",
    1: "0",
    2: 1,
    3: 2,
    4: -30,
    5: 3,
};

interface BuyerOrderItem {
    orderPushErrorMessage?: string;
    buyerAvatar: string;
    declaredNo: string;
    guiderRealProfit: boolean;
    orderProfitViewList: any[];
    paymentOutId: string;
    refund: boolean;
    serviceProviderName: string;
    serviceProviderProfit: number;
    serviceProviderRealProfit: boolean;
    shopOrder: shopOrderObj;
    shopOrderOperations: shopOrderOperationObj[];
    shopTax: number;
    skuOrderAndOperations: {
        flagList: ("promotionActivityItem" | "promotionLimitedItem")[];
        sku: DDYObject;
        skuOrder: DDYObject;
        skuOrderOperations: shopOrderOperationObj[];
    }[];
    subStoreName: string;
    subStoreRealProfit: boolean;
    waitingSellerAuth: boolean;
    waitingSellerAuthDesc: string;
    packageSn: string;
}
export default ({ current, sourceBuy, updateTabsData }) => {
    const route = useRouter();
    // const endStatus = useRef(false);
    const [data, setData] = useState<BuyerOrderItem[]>([]);
    const infiniteScrollRef = useRef<any>();
    const pageSize = 20;
    const pageNo = useRef(1);
    /**
     * 订单类型 : 1为正常订单，3为积分订单
     */
    const orderType = route.params.type || 1;

    const getOrderDatas = () => {
        let userInfo = getStorage(USER_INFO) || {};
        let orderStatus = ORDER_STATUS[current];

        return api.getMyOrderList({
            data: {
                //openId: openId,
                buyerId: userInfo.userId,
                statusStr: orderStatus || "",
                pageNo: pageNo.current || 1,
                size: pageSize,
                outFrom: "subStore",
                shopId: getStorage(STORE_ID),
                type: orderType, // 订单类型，1为正常订单，3为积分订单
            },
            showError: false,
            showLoad: false,
            filterCheck: true,
        });
    };

    const onRefresh = () => {
        pageNo.current = 1;
        let map = new Map();
        map.set(mall_event_key.MALL_KEY_ORDER_PAGING_PAGE, pageNo.current);
        map.set(mall_event_key.MALL_KEY_ORDER_PAGING_STATUSSTR, ORDER_STATUS[current]);
        reportEvent(mall_event.MALL_EVENT_ORDER_PAGING_START, map);
        return new Promise(async (resolve: (value: undefined) => void) => {
            infiniteScrollRef.current?.reset();
            updateTabsData && updateTabsData();
            try {
                const result = await getOrderDatas();
                let map = new Map();
                map.set(mall_event_key.MALL_KEY_ORDER_PAGING_PAGE, pageNo.current);
                map.set(mall_event_key.MALL_KEY_ORDER_PAGING_STATUSSTR, ORDER_STATUS[current]);
                map.set(mall_event_key.MALL_KEY_ORDER_PAGING_COUNT, result.data.length);
                map.set(mall_event_key.MALL_KEY_ORDER_PAGING_TOTAL, result.total);
                map.set(mall_event_key.MALL_KEY_ORDER_PAGING_RESULT, "成功");
                reportEvent(mall_event.MALL_EVENT_ORDER_PAGING_END, map);
                pageNo.current++;
                setData(result.data);
                console.log("newata:", result.data);
            } catch (error) {
                console.log("error:", error);
                let map = new Map();
                map.set(mall_event_key.MALL_KEY_ORDER_PAGING_PAGE, pageNo.current);
                map.set(mall_event_key.MALL_KEY_ORDER_PAGING_STATUSSTR, ORDER_STATUS[current]);
                map.set(mall_event_key.MALL_KEY_ORDER_PAGING_ERROR, error.toString());
                map.set(mall_event_key.MALL_KEY_ORDER_PAGING_RESULT, "错误");
                reportEvent(mall_event.MALL_EVENT_ORDER_PAGING_END, map);
            }
            infiniteScrollRef.current?.reset(true);
            resolve(undefined);
        });
    };
    const loadMore = () => {
        let map = new Map();
        map.set(mall_event_key.MALL_KEY_ORDER_PAGING_PAGE, pageNo.current);
        map.set(mall_event_key.MALL_KEY_ORDER_PAGING_STATUSSTR, ORDER_STATUS[current]);
        reportEvent(mall_event.MALL_EVENT_ORDER_PAGING_START, map);
        return new Promise(async (resolve: (value: "loading" | "error" | "complete") => void) => {
            try {
                const result = await getOrderDatas();
                let newData: any[] = [];
                if (pageNo.current === 1) {
                    updateTabsData && updateTabsData();
                    newData = result.data;
                } else {
                    newData = data.concat(result.data);
                }
                let map = new Map();
                map.set(mall_event_key.MALL_KEY_ORDER_PAGING_PAGE, pageNo.current);
                map.set(mall_event_key.MALL_KEY_ORDER_PAGING_STATUSSTR, ORDER_STATUS[current]);
                map.set(mall_event_key.MALL_KEY_ORDER_PAGING_COUNT, newData.length);
                map.set(mall_event_key.MALL_KEY_ORDER_PAGING_TOTAL, result.total);
                map.set(mall_event_key.MALL_KEY_ORDER_PAGING_RESULT, "成功");
                reportEvent(mall_event.MALL_EVENT_ORDER_PAGING_END, map);
                pageNo.current++;
                setData(newData);
                //  endStatus.current = newData.length >= result.total;
                resolve(newData.length >= result.total ? "complete" : "loading");
            } catch (error) {
                let map = new Map();
                map.set(mall_event_key.MALL_KEY_ORDER_PAGING_PAGE, pageNo.current);
                map.set(mall_event_key.MALL_KEY_ORDER_PAGING_STATUSSTR, ORDER_STATUS[current]);
                map.set(mall_event_key.MALL_KEY_ORDER_PAGING_ERROR, error.toString());
                map.set(mall_event_key.MALL_KEY_ORDER_PAGING_RESULT, "错误");
                reportEvent(mall_event.MALL_EVENT_ORDER_PAGING_END, map);
                resolve("error");
            }
        });
    };

    useEffect(() => {
        pageNo.current = 1;
        infiniteScrollRef.current?.reset(true);
    }, [current]);

    return (
        <PullToRefresh onRefresh={onRefresh}>
            <View className="order-list">
                {data.map(orderItem => {
                    const goodList = orderItem.skuOrderAndOperations.map(skuItem => {
                        return {
                            quantity: skuItem.skuOrder.quantity,
                            flagList: skuItem.flagList,
                            price: skuItem.skuOrder.originFee / skuItem.skuOrder.quantity / 100,
                            goodName: skuItem.skuOrder.itemName,
                            goodImage: skuItem.skuOrder.skuImage,
                        };
                    });
                    return (
                        <OrderItem
                            mode="buyer"
                            showBottom={true}
                            orderStatus={orderItem.shopOrder.status}
                            orderNo={orderItem.shopOrder.declaredId}
                            shopOrder={orderItem.shopOrder}
                            operations={orderItem.shopOrderOperations}
                            refund={orderItem.refund}
                            orderPushErrorMessage={orderItem.orderPushErrorMessage}
                            onFresh={() => {
                                pageNo.current = 1;
                                infiniteScrollRef.current?.reset(true);
                            }}
                            buyerEtr={{
                                // shopOrder: orderItem.shopOrder,
                                isCardError: orderItem.shopOrder.extra.identityError == "true",
                                isReal: orderItem.shopOrder.extra.identityError == "wait",
                                actualPrice: orderItem.shopOrder.fee / 100,
                                actualCount: orderItem.skuOrderAndOperations.length,
                            }}
                            goodList={goodList}
                            createdAt={timestampToTime(orderItem.shopOrder.createdAt, "YYYY-MM-DD HH:mm:ss")}
                            shopName={orderItem.subStoreName}
                            isFirst={false}
                            clickGo={() => {
                                DDYNavigateTo({
                                    url: `/pages/order/order-detail/index?id=${orderItem.shopOrder.id}&packageSn=${orderItem.packageSn}&status=${orderItem.shopOrder.status}`,
                                });
                            }}
                        />
                    );
                })}
            </View>
            <InfiniteScroll
                completeText={
                    <>
                        {data.length == 0 ? (
                            <Empty
                                description="暂无数据！"
                                image="https://dante-img.oss-cn-hangzhou.aliyuncs.com/55439458101.png"
                            />
                        ) : (
                            "没有更多了"
                        )}
                    </>
                }
                loadMore={loadMore}
                ref={infiniteScrollRef}
            />
        </PullToRefresh>
    );
};
