.coupon {
    width: 702rpx;
    height: 32rpx;
    font-size: 32rpx;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: bolder;
    color: #333333;
    line-height: 32rpx;
    margin: 24rpx auto;
    //   border: 1px red solid;
    .cou {
        font-size: 24rpx;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #999999;
        line-height: 24rpx;
    }
}
.loadMore {
    width: 702rpx;
    //height: 210rpx;
    //border: 1px red solid;
    font-family: PingFangSC-Medium, PingFang SC;
    position: relative;
    margin: 20rpx auto;
    .front_img {
        position: absolute;
        z-index: -999;
        width: 702rpx;
        height: 100% !important;
    }
    .con {
        display: flex;
        .left {
            width: 496rpx;
            //height: 210rpx;
            margin-left: 32rpx;
            .icon {
                width: 24rpx;
                height: 24rpx;
                border-radius: 50%;
                background-color: #dddddd;
                position: relative;
                right: 32rpx;
                float: right;
                .downline {
                    width: 24rpx;
                    height: 24rpx;
                    position: relative;
                    top: -3rpx;
                }
            }
        }
        .right {
            font-family: HelveticaNeue-Medium, HelveticaNeue;
            text-align: center;
            width: 266rpx;
            //height: 210rpx;
            color: #f5001e;
            //   border: 1px red solid;
            display: flex;
            .right_l {
                width: 206rpx;
                box-sizing: border-box;
                padding: 0 20px;
                display: flex;
                justify-content: center;
                align-items: center;
                flex-direction: column;
            }
            .right_r {
                width: 60rpx;
                line-height: 210rpx;
                .shop_img {
                    width: 36rpx;
                    height: 36rpx;
                    //   text-align: center;
                }
            }
        }
    }
}
.bottom-box {
    display: flex;
    justify-content: space-between;
    width: 100%;
    height: 120rpx;
    position: fixed;
    bottom: 0;
    padding-bottom: env(safe-area-inset-bottom);
    left: 0;
    border-bottom: 1px solid #eee;
    border-top: 1px solid #eee;
    background-color: #fff;
    z-index: 9999;
    .left-price {
        display: flex;
        width: 600rpx;
        // justify-content: space-between;
        // padding: 0 18rpx 0 26rpx;
        padding-left: 230rpx;
        font-size: 24rpx;
        box-sizing: border-box;
        align-items: center;
        .total {
            color: #f51214;
            font-size: 38rpx;
        }
    }
    .cu-btn {
        color: #ffffff;
        margin: 20rpx 24rpx;
        width: 204rpx;
        height: 80rpx;
        box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.1);
        border-radius: 100px;
    }
}
