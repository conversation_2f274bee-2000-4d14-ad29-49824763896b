.coupon {
    width: 702rpx;
    height: 32rpx;
    font-size: 32rpx;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: bolder;
    color: #333333;
    line-height: 32rpx;
    margin: 24rpx auto;
    //   border: 1px red solid;
    .cou {
        font-size: 24rpx;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #999999;
        line-height: 24rpx;
    }
}
.coupon-rect {
    padding-bottom: calc(150px + env(safe-area-inset-bottom));
    .coupon-title {
        font-size: 36px;
        line-height: 40px;
        color: #333;
        margin: 30px 0 24px 24px;
        .coupon-title-info {
            font-size: 24px;
            line-height: 40px;
            color: #999999;
            margin-left: 10px;
        }
    }
    .coupon-list {
        .coupon-item-2 {
            opacity: 0.5;
            .coupon-left,
            .coupon-right {
                .coupon-text1,
                .coupon-text2,
                .coupon-text3,
                .coupon-text5,
                .coupon-text6 {
                    color: #333333;
                }
            }
        }
        .coupon-item-1,
        .coupon-item-2 {
            background-image: url(https://dante-img.oss-cn-hangzhou.aliyuncs.com/21692716456.png);
            background-repeat: no-repeat;
            background-size: 692px 203px;
            background-position: center;
            width: 692px;
            height: 203px;
            display: flex;
            flex-direction: row;
            align-items: center;
            margin-top: 49px;
            margin-left: 30px;
            .coupon-left {
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                width: 172px;
                .coupon-text1 {
                    font-size: 32px;
                    line-height: 72px;
                    color: #e50a1a;
                }
                .coupon-text2 {
                    font-weight: 600;
                    font-size: 60px;
                    line-height: 72px;
                    color: #e50a1a;
                }
                .coupon-text6 {
                    font-weight: 600;
                    font-size: 20px;
                    line-height: 72px;
                    color: #e50a1a;
                }
                .coupon-text3 {
                    font-weight: 200;
                    font-size: 18px;
                    color: #e50a1a;
                    line-height: 25px;
                    text-align: center;
                    font-style: normal;
                }
            }
            .coupon-right {
                width: 493px;
                display: flex;
                flex-direction: row;
                align-items: center;
                justify-content: center;
                box-sizing: border-box;
                padding-left: 30px;
                position: relative;
                .coupon-info {
                    width: 393px;
                    height: 203px;
                    box-sizing: border-box;
                    padding-top: 20px;
                    padding-bottom: 20px;
                    padding-left: 24px;
                    display: flex;
                    align-items: flex-start;
                    justify-content: center;
                    flex-direction: column;
                }
                .coupon-text4 {
                    font-weight: 500;
                    font-size: 28px;
                    color: #e50a1a;
                    line-height: 45px;
                    text-align: center;
                    font-style: normal;
                    .coupon-use-text {
                        margin-left: 10px;
                        &:after {
                            position: absolute;
                            // right: 0px;
                            content: "\e678";
                            font-family: "iconfont-fx";
                        }
                    }
                }
                .coupon-text5 {
                    font-weight: 400;
                    font-size: 20px;
                    color: #ff0940;
                    line-height: 28px;
                    text-align: left;
                    font-style: normal;
                }
                .coupon-text6 {
                    color: #333;
                    font-size: 24px;
                    line-height: 45px;
                }
                .coupon-btn1 {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    width: 100px;
                    height: 100%;
                }
                .coupon-btn2 {
                    width: 133px;
                    height: 58px;
                    border-radius: 32px;
                    font-weight: 400;
                    font-size: 24px;
                    color: #ff7c99;
                    line-height: 58px;
                    text-align: center;
                    font-style: normal;
                    margin-left: 44px;
                }
                .coupon-availa {
                    font-size: 24px;
                    line-height: 36px;
                    transform: rotate(45deg); /* 旋转4度 */
                    color: #c84749;
                    position: absolute;
                    right: -16px;
                    top: 36px;
                }
            }
        }
    }
}

.loadMore {
    width: 702rpx;
    //height: 210rpx;
    //border: 1px red solid;
    font-family: PingFangSC-Medium, PingFang SC;
    position: relative;
    margin: 20rpx auto;
    .front_img {
        position: absolute;
        z-index: -999;
        width: 702rpx;
        height: 100% !important;
    }
    .con {
        display: flex;
        .left {
            width: 496rpx;
            //height: 210rpx;
            margin-left: 32rpx;
            .icon {
                width: 24rpx;
                height: 24rpx;
                border-radius: 50%;
                background-color: #dddddd;
                position: relative;
                right: 32rpx;
                float: right;
                .downline {
                    width: 24rpx;
                    height: 24rpx;
                    position: relative;
                    top: -3rpx;
                }
            }
        }
        .right {
            font-family: HelveticaNeue-Medium, HelveticaNeue;
            text-align: center;
            width: 266rpx;
            //height: 210rpx;
            color: #f5001e;
            //   border: 1px red solid;
            display: flex;
            .right_l {
                width: 206rpx;
                box-sizing: border-box;
                padding: 0 20px;
                display: flex;
                justify-content: center;
                align-items: center;
                flex-direction: column;
            }
            .right_r {
                width: 60rpx;
                line-height: 210rpx;
                .shop_img {
                    width: 36rpx;
                    height: 36rpx;
                    //   text-align: center;
                }
            }
        }
    }
}
.bottom-box {
    display: flex;
    justify-content: space-between;
    width: 100%;
    height: 120rpx;
    position: fixed;
    bottom: 0;
    padding-bottom: env(safe-area-inset-bottom);
    left: 0;
    border-bottom: 1px solid #eee;
    border-top: 1px solid #eee;
    background-color: #fff;
    z-index: 9999;
    .push-info {
        padding: 7px 20px;
        // padding: "3px 10px",
        color: #f50e0c;
        font-size: 24px;
        line-height: 32px;
        border-radius: 20px;
        background-color: #f5edec;
        text-align: center;
        position: absolute;
        // width: 180px;
        text-align: center;
        top: 44rpx;
        left: 24px;
    }
    .left-price {
        display: flex;
        width: 600px;
        // justify-content: space-between;
        // padding: 0 18rpx 0 26rpx;
        padding-left: 230rpx;
        font-size: 24rpx;
        box-sizing: border-box;
        align-items: center;
        .total {
            color: #f51214;
            font-size: 38rpx;
        }
    }
    .cu-btn {
        color: #ffffff;
        margin: 20rpx 24rpx;
        width: 204rpx;
        height: 80rpx;
        box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.1);
        border-radius: 100px;
    }
}
