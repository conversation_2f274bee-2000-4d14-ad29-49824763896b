import { View, Image, Text, Button } from "@tarojs/components";
import { useDidShow, useRouter } from "@tarojs/taro";
import { useState } from "react";
import { DDYObject } from "src/type/common";
import "./index.less";
import { DDYBack } from "@/utils/route";
import {
    icon_coupon_bg,
    icon_arrow_down_s_line,
    icon_arrow_up_s_line,
    icon_coupon_select,
    icon_coupon_select_no,
} from "@/images";
import { timestampToTime } from "@/utils/DateTimeUtils";
import Taro from "@tarojs/taro";

export default () => {
    const route = useRouter();
    const [selectCoupon, setSelectCoupon] = useState<DDYObject>({});
    const [couponList, setCouponList] = useState<DDYObject>({});

    useDidShow(() => {
        try {
            const coupons = route.params.coupons && JSON.parse(route.params.coupons);
            const promotionId = route.params.promotionId;
            const newSkuPromotions = coupons.skuPromotions.map(item => {
                const isSelected = item.id === promotionId;
                const iconClick = true;
                setSelectCoupon(isSelected ? item : null);
                return {
                    ...item,
                    endAt: timestampToTime(item.endAt, "YYYY-MM-DD HH:mm:ss"),
                    isSelected,
                    iconClick,
                };
            });
            couponList.skuPromotions = newSkuPromotions;
        } catch (error) {}
    });

    const tapIcon = index => {
        const iconClick = !!couponList.skuPromotions[index].iconClick;
        couponList.skuPromotions[index].iconClick = !iconClick;
        couponList.skuPromotions = [...couponList.skuPromotions];
        setCouponList({ ...couponList });
    };

    const selectAll = index => {
        const newSkuPromotions = couponList.skuPromotions.map((i, eg) => {
            if (index === eg) {
                return { ...i, isSelected: !i.isSelected };
            }
            return { ...i, isSelected: false };
        });
        setCouponList({
            ...couponList,
            skuPromotions: newSkuPromotions,
        });
        if (newSkuPromotions[index].isSelected === true) {
            setSelectCoupon(newSkuPromotions[index]);
        } else {
            setSelectCoupon({});
        }
    };

    const toPayOrder = () => {
        if (selectCoupon && selectCoupon.id) {
            const pages = Taro.getCurrentPages();
            const current = pages[pages.length - 1];
            const eventChannel = current.getOpenerEventChannel();
            eventChannel.emit("handleCouponSelected", { id: selectCoupon.id });
        }
        DDYBack();
    };
    return (
        <View>
            <View className="coupon">可用优惠券</View>
            <View className="coupon-list">
                {couponList.skuPromotions?.map((item, index) => (
                    <View className="loadMore">
                        <Image className="front_img" src={icon_coupon_bg} mode="widthFix"></Image>
                        <View className="con">
                            <View className="left">
                                <View style="font-size: 26rpx;font-weight:bolder; padding-top: 40rpx;">
                                    {item.name}
                                </View>
                                {item.type === 4 ? (
                                    <View style="font-size: 20rpx; margin-top: 12rpx;">
                                        使用时间 {item.extra.activityTimeStart}~{item.extra.activityTimeEnd}
                                    </View>
                                ) : (
                                    <View style="font-size: 20rpx; margin-top: 12rpx;">有效期至 {item.endAt}</View>
                                )}
                                {item.extra.restrictions && (
                                    <View>
                                        <View style="font-size: 20rpx; color: #999999;margin-top: 5rpx;">
                                            使用规则
                                            <View className="icon" onClick={() => tapIcon(index)}>
                                                <Image
                                                    className="downline"
                                                    src={item.iconClick ? icon_arrow_down_s_line : icon_arrow_up_s_line}
                                                    mode="widthFix"
                                                ></Image>
                                            </View>
                                        </View>
                                        {!item.iconClick && (
                                            <View style="font-size: 20rpx; margin-top: 5rpx;color: #999999;">
                                                {item.extra.restrictions}
                                            </View>
                                        )}
                                    </View>
                                )}
                            </View>
                            <View className="right">
                                {item.type === 4 ? (
                                    <View className="right_l">
                                        <View>门店限定</View>
                                    </View>
                                ) : (
                                    <View className="right_l">
                                        <View style="font-size: 36rpx;font-weight:bolder;margin-top:12rpx;">
                                            ￥<Text style="font-size: 60rpx;line-height: 60rpx;">{item.reduceFee}</Text>
                                        </View>
                                        <View style="font-size: 20rpx;">{item.conditionDesc}</View>
                                    </View>
                                )}

                                <View
                                    className="right_r"
                                    onClick={() => {
                                        selectAll(index);
                                    }}
                                >
                                    <Image
                                        className="shop_img"
                                        src={item.isSelected ? icon_coupon_select : icon_coupon_select_no}
                                        mode="widthFix"
                                    ></Image>
                                </View>
                            </View>
                        </View>
                    </View>
                ))}
            </View>
            <View className="bottom-box">
                <View className="left-price">
                    <View>
                        已选{selectCoupon?.reduceFee ? 1 : 0}张，可减：
                        <Text className="total">¥{selectCoupon?.reduceFee ? selectCoupon?.reduceFee : 0}</Text>
                    </View>
                </View>
                <Button style="background-color:#F50050;line-height: 80rpx;" className="cu-btn" onClick={toPayOrder}>
                    确定
                </Button>
            </View>
        </View>
    );
};
