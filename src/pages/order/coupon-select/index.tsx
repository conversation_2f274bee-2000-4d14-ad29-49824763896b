import { View, Image, Text, Button } from "@tarojs/components";
import { useDidShow, useRouter } from "@tarojs/taro";
import { useEffect, useRef, useState } from "react";
import { DDYObject } from "src/type/common";
import "./index.less";
import { DDYBack, DDYNavigateTo, DDYSwitchTab } from "@/utils/route";
import { Tabs, Tab, Checkbox } from "@antmjs/vantui";
import Taro from "@tarojs/taro";
import newAPi from "@/utils/newApi";
import DDYToast from "@/utils/toast";

const couponMethod = {
    RE_PURCHASE: "复购券",
    NEW_CUSTOMER: "新客券",
    SINGLE_PRODUCT: "单品券",
    ASSISTANCE: "助力裂变券",
};

const couponType = {
    PRODUCT: "商品券",
    SHOP: "店铺券",
};

export default () => {
    const route = useRouter();
    const [selectCoupon, setSelectCoupon] = useState<DDYObject>([]);
    const [couponList, setCouponList] = useState<DDYObject>({});
    const [shopCoupons, setShopCoupons] = useState([]);
    const [goodsCoupons, setGoodsCoupons] = useState([]);
    const [unAvailableCoupons, setUnAvailableCoupons] = useState([]);
    const [totalDiscount, settotalDiscount] = useState(0);
    const discount = useRef(0);
    const [count, setCount] = useState(0);
    const goods = useRef();
    useEffect(() => {
        try {
            const coupons = JSON.parse(route.params.coupons);
            const { itemCouponIdList = [], shopCouponIdList = [] } = coupons;
            console.log(itemCouponIdList, shopCouponIdList, coupons);
            goods.current = JSON.parse(route.params.goods);
            console.log("sss", goods.current);
            getData(
                (shopCouponIdList || []).map(item => {
                    return { couponId: item };
                }),
                (itemCouponIdList || []).map(item => {
                    return { couponId: item };
                }),
            );
        } catch (error) {
            console.log("error", error);
        }
    }, []);

    const getData = (selectShopCouponList, selectGoodsCouponList) => {
        newAPi
            .planChangeCheck({
                method: "POST",
                data: {
                    goodsList: goods.current.map(item => {
                        return {
                            goodsCode: item.skuId,
                            lineNo: item.skuId,
                            count: item.quantity,
                        };
                    }),
                    selectShopCouponList,
                    selectGoodsCouponList,
                    tradeType: 1,
                    activityInfo: route.params.marketingToolId
                        ? {
                              marketingToolId: route.params.marketingToolId,
                              groupId: route.params.groupId,
                              activityId: route.params.activityId,
                          }
                        : null,
                },
            })
            .then(res => {
                setGoodsCoupons(res.goodsCoupons);
                setShopCoupons(res.shopCoupons);
                setUnAvailableCoupons(res.unAvailableCoupons);
                settotalDiscount(res.totalDiscount);
                if (res.totalDiscount > discount.current) {
                    console.log(res.totalDiscount, discount.current);
                    discount.current = res.totalDiscount;
                }
                let num = 0;
                res.goodsCoupons.map(item => {
                    // item.selected
                    if (item.selected) {
                        num++;
                    }
                });
                res.shopCoupons.map(item => {
                    // item.selected
                    if (item.selected) {
                        num++;
                    }
                });
                setCount(num);
            });
    };

    const changeSelect = (shopCoupons, goodsCoupons, lastSelectedCouponId) => {
        // getData(shopCoupons, goodsCoupons);
        const arr1 = shopCoupons
            .filter(item => item.selected)
            .map(item => {
                return { couponId: item.couponId };
            });
        const arr2 = goodsCoupons
            .filter(item => item.selected)
            .map(item => {
                return { couponId: item.couponId };
            });

        newAPi
            .planChangeCheck({
                method: "POST",
                data: {
                    goodsList: goods.current.map(item => {
                        return {
                            goodsCode: item.skuId,
                            lineNo: item.skuId,
                            count: item.quantity,
                            // singlePrice: 100,
                        };
                    }),
                    selectShopCouponList: arr1,
                    selectGoodsCouponList: arr2,
                    tradeType: 1,
                    lastSelectedCouponId,
                    activityInfo: route.params.marketingToolId
                        ? {
                              marketingToolId: route.params.marketingToolId,
                              groupId: route.params.groupId,
                              activityId: route.params.activityId,
                          }
                        : null,
                },
            })
            .then(res => {
                setGoodsCoupons(res.goodsCoupons);
                setShopCoupons(res.shopCoupons);
                setUnAvailableCoupons(res.unAvailableCoupons);
                settotalDiscount(res.totalDiscount);
                if (res.totalDiscount > discount.current) {
                    console.log(res.totalDiscount, discount.current);
                    discount.current = res.totalDiscount;
                }
                let num = 0;
                res.goodsCoupons.map(item => {
                    // item.selected
                    if (item.selected) {
                        num++;
                    }
                });
                res.shopCoupons.map(item => {
                    // item.selected
                    if (item.selected) {
                        num++;
                    }
                });
                setCount(num);
            });
    };

    const toPayOrder = () => {
        // if (selectCoupon && selectCoupon.id) {
        const pages = Taro.getCurrentPages();
        const current = pages[pages.length - 1];
        const eventChannel = current.getOpenerEventChannel();
        const ids1 = [];
        const ids2 = [];
        // const arr1 = shopCoupons.filter(item => item.selected).map((item) => { return { couponId: item.couponId } })
        // const arr2 = goodsCoupons.filter(item => item.selected).map((item) => { return { couponId: item.couponId } })
        shopCoupons.map(item => {
            if (item.selected) ids1.push(item.couponId);
        });
        goodsCoupons.map(item => {
            if (item.selected) ids2.push(item.couponId);
        });
        // console.log("ids", ids)
        eventChannel.emit("handleCouponSelected", {
            ids: {
                shopCouponIdList: ids1,
                itemCouponIdList: ids2,
            },
        });
        DDYBack({});
    };
    return (
        <View>
            <Tabs sticky>
                <Tab key={"unUse"} title={"可用优惠券"}>
                    <View className="coupon-rect">
                        {goodsCoupons.length ? (
                            <View className="coupon-title">
                                商品优惠券
                                <Text className="coupon-title-info">每个商品项可使用一张优惠券</Text>
                            </View>
                        ) : null}

                        <View className="coupon-list">
                            {goodsCoupons.map((item, index) => {
                                return (
                                    <View className={"coupon-item-1"}>
                                        <View className="coupon-left">
                                            {item.method === "DIRECT_REDUCTION" ? (
                                                <Text className="coupon-text1">
                                                    <Text className="coupon-text2">{item.deductionAmount}</Text>
                                                    <Text className="coupon-text6">元</Text>
                                                </Text>
                                            ) : null}
                                            {item.method === "FULL_REDUCTION" ? (
                                                <Text className="coupon-text1">
                                                    <Text className="coupon-text2">
                                                        {item.thresholdDeductionAmount}
                                                    </Text>
                                                    <Text className="coupon-text6">元</Text>
                                                </Text>
                                            ) : null}
                                            {item.method === "DISCOUNT" ? (
                                                <Text className="coupon-text1">
                                                    <Text className="coupon-text2">{item.discount}</Text>
                                                    <Text className="coupon-text6">折</Text>
                                                </Text>
                                            ) : null}

                                            <View className="coupon-text3">
                                                {!item.thresholdAmount ? "无门槛使用" : `满${item.thresholdAmount}可用`}
                                            </View>
                                        </View>
                                        <View className="coupon-right">
                                            <View className="coupon-info">
                                                <View
                                                    className="coupon-text4"
                                                    onClick={() => {
                                                        if (item.scopeType === "PRODUCT") {
                                                            DDYNavigateTo({
                                                                url: "/pages/goods_detail?id=" + item.scopeValue,
                                                            });
                                                        } else {
                                                            DDYSwitchTab({ url: "/pages/home/<USER>" });
                                                        }
                                                    }}
                                                >
                                                    {couponType[item.scopeType]}-{couponMethod[item.activityType]}
                                                    <Text className="coupon-use-text">可用品</Text>
                                                </View>
                                                <View />
                                                <Text className="coupon-text5">有效期至{item.endTime}</Text>
                                                {!item.selected && !item.availableUse ? (
                                                    <View className="coupon-text6">此券暂不可与已勾选的券叠加使用</View>
                                                ) : null}
                                            </View>
                                            <View
                                                className="coupon-btn1"
                                                onClick={() => {
                                                    // if (!item.availableUse && !goodsCoupons[index].selected) {
                                                    //     return DDYToast.info("此券暂不可与已勾选的券叠加使用");
                                                    // }
                                                    // userGetCoupons(item.couponActivityId, item.couponScopeId);
                                                    const bol = !goodsCoupons[index].selected;
                                                    goodsCoupons[index].selected = bol;
                                                    setGoodsCoupons([...goodsCoupons]);
                                                    changeSelect(
                                                        shopCoupons,
                                                        [...goodsCoupons],
                                                        bol ? goodsCoupons[index].couponId : null,
                                                    );
                                                }}
                                            >
                                                <Checkbox
                                                    value={item.selected}
                                                    onChange={e => {
                                                        console.log(e.detail);
                                                    }}
                                                />
                                            </View>
                                            {item.availableUse ? <View className="coupon-availa">可叠加</View> : null}
                                        </View>
                                    </View>
                                );
                            })}
                        </View>
                        <View className="coupon-title">
                            店铺优惠券
                            <Text className="coupon-title-info">每个订单只可使用一张优惠券</Text>
                        </View>
                        <View className="coupon-list">
                            {shopCoupons.map((item, index) => {
                                return (
                                    <View className={"coupon-item-1"}>
                                        <View className="coupon-left">
                                            {item.method === "DIRECT_REDUCTION" ? (
                                                <Text className="coupon-text1">
                                                    <Text className="coupon-text2">{item.deductionAmount}</Text>
                                                    <Text className="coupon-text6">元</Text>
                                                </Text>
                                            ) : null}
                                            {item.method === "FULL_REDUCTION" ? (
                                                <Text className="coupon-text1">
                                                    <Text className="coupon-text2">
                                                        {item.thresholdDeductionAmount}
                                                    </Text>
                                                    <Text className="coupon-text6">元</Text>
                                                </Text>
                                            ) : null}
                                            {item.method === "DISCOUNT" ? (
                                                <Text className="coupon-text1">
                                                    <Text className="coupon-text2">{item.discount}</Text>
                                                    <Text className="coupon-text6">折</Text>
                                                </Text>
                                            ) : null}

                                            <View className="coupon-text3">
                                                {!item.thresholdAmount ? "无门槛使用" : `满${item.thresholdAmount}可用`}
                                            </View>
                                        </View>
                                        <View className="coupon-right">
                                            <View className="coupon-info">
                                                <Text className="coupon-text4">
                                                    {couponType[item.scopeType]}-{couponMethod[item.activityType]}
                                                </Text>
                                                <View />
                                                <Text className="coupon-text5">有效期至{item.endTime}</Text>
                                                {!item.selected && !item.availableUse ? (
                                                    <View className="coupon-text6">此券暂不可与已勾选的券叠加使用</View>
                                                ) : null}
                                            </View>
                                            <View
                                                className="coupon-btn1"
                                                onClick={() => {
                                                    // if (!item.availableUse && !shopCoupons[index].selected) {
                                                    //     return DDYToast.info("此券暂不可与已勾选的券叠加使用");
                                                    // }
                                                    // userGetCoupons(item.couponActivityId, item.couponScopeId);
                                                    // shopCoupons[index].selected = !shopCoupons[index].selected;
                                                    // setShopCoupons([...shopCoupons])
                                                    // changeSelect([...shopCoupons], goodsCoupons);
                                                    const bol = !shopCoupons[index].selected;
                                                    shopCoupons[index].selected = bol;
                                                    setShopCoupons([...shopCoupons]);
                                                    changeSelect(
                                                        [...shopCoupons],
                                                        goodsCoupons,
                                                        bol ? shopCoupons[index].couponId : null,
                                                    );
                                                }}
                                            >
                                                <Checkbox
                                                    value={item.selected}
                                                    onChange={e => {
                                                        console.log(e.detail);
                                                    }}
                                                />
                                            </View>
                                            {item.availableUse ? <View className="coupon-availa">可叠加</View> : null}
                                        </View>
                                    </View>
                                );
                            })}
                        </View>
                    </View>
                </Tab>
                <Tab key={"used"} title={"不可用优惠券"}>
                    <View className="coupon-rect">
                        <View className="coupon-list">
                            {unAvailableCoupons.map(item => {
                                return (
                                    <View className={"coupon-item-2"}>
                                        <View className="coupon-left">
                                            {item.method === "DIRECT_REDUCTION" ? (
                                                <Text className="coupon-text1">
                                                    <Text className="coupon-text2">{item.deductionAmount}</Text>
                                                    <Text className="coupon-text6">元</Text>
                                                </Text>
                                            ) : null}
                                            {item.method === "FULL_REDUCTION" ? (
                                                <Text className="coupon-text1">
                                                    <Text className="coupon-text2">
                                                        {item.thresholdDeductionAmount}
                                                    </Text>
                                                    <Text className="coupon-text6">元</Text>
                                                </Text>
                                            ) : null}
                                            {item.method === "DISCOUNT" ? (
                                                <Text className="coupon-text1">
                                                    <Text className="coupon-text2">{item.discount}</Text>
                                                    <Text className="coupon-text6">折</Text>
                                                </Text>
                                            ) : null}

                                            <View className="coupon-text3">
                                                {!item.thresholdAmount ? "无门槛使用" : `满${item.thresholdAmount}可用`}
                                            </View>
                                            {item.reason && <View className="coupon-text3">{item.reason}</View>}
                                        </View>
                                        <View className="coupon-right">
                                            <View className="coupon-info">
                                                <Text className="coupon-text4">
                                                    {couponType[item.scopeType]}-{couponMethod[item.activityType]}
                                                    {/* <Text>去使用</Text> */}
                                                </Text>
                                                <View />
                                                <Text className="coupon-text5">有效期至{item.endTime}</Text>
                                            </View>
                                        </View>
                                    </View>
                                );
                            })}
                        </View>
                    </View>
                </Tab>
            </Tabs>

            <View className="bottom-box">
                {discount.current === totalDiscount ? <View className="push-info">已选推荐优惠</View> : null}
                <View className="left-price">
                    <View>
                        已选{count}张，可减：
                        <Text className="total">¥{totalDiscount}</Text>
                    </View>
                </View>
                <Button style="background-color:#F50050;line-height: 80rpx;" className="cu-btn" onClick={toPayOrder}>
                    确定
                </Button>
            </View>
        </View>
    );
};
