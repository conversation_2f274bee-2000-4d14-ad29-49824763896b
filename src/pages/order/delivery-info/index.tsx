// import { View } from "@tarojs/components"

import React, { useEffect, useState } from "react";
import { <PERSON>, <PERSON><PERSON>, Picker, Text } from "@tarojs/components";
import { Field } from "@antmjs/vantui";
import "./index.less";
import api from "@/utils/api";
import { useRouter } from "@tarojs/taro";
import DDYToast from "@/utils/toast";
import { DDYBack } from "@/utils/route";
import { VIEW } from "@tarojs/runtime";
definePageConfig({
    navigationBarTitleText: "上传快递信息",
});
interface OrderDetailProps {}

const DeliveryfastInfo: React.FC<OrderDetailProps> = () => {
    const { params } = useRouter();
    const [expressCompany, setExpressCompany] = useState<number>();
    const [expressCode, setExpressCode] = useState(-1);
    const [companyList, setCompanyList] = useState(["111", "222", "333"]);
    const [trackingNumber, setTrackingNumber] = useState<string>("");
    const getCompanyList = () => {
        api.getCompanyList({
            data: {},
        }).then(res => {
            setCompanyList(res);
        });
    };

    const submitForm = () => {
        if (expressCode < 0) {
            return DDYToast.info("请选择快递公司");
        }
        if (!trackingNumber) {
            return DDYToast.info("请属于快递单号");
        }
        if (!/^[a-zA-Z0-9]{1,20}$/.test(trackingNumber)) {
            return DDYToast.info("请输入20位由英文数字组成的快递单号");
        }
        // 提交表单逻辑
        api.submitExpressInfo({
            data: {
                refundId: params.id,
                expressNo: trackingNumber,
                expressCompany: expressCompany,
                expressCode: companyList[expressCode]?.id,
            },
            method: "POST",
        }).then(res => {
            DDYToast.success("提交成功");
            const timer = setTimeout(() => {
                DDYBack();
                clearTimeout(timer);
            }, 1000);
        });
    };

    useEffect(() => {
        getCompanyList();
    }, []);

    return (
        <View className="order-detail">
            <View className="order-card">
                <Text className="head">请正确填写快递单号,以免影响退款进度</Text>
                <Picker
                    mode={"selector"}
                    range={companyList}
                    rangeKey={"name"}
                    // label={}
                    value={expressCode}
                    onChange={e => {
                        console.log("e", e.detail.value);
                        setExpressCode(e.detail.value);
                        setExpressCompany(companyList[e.detail.value]?.name);
                    }}
                >
                    <View className="van-cell van-field">
                        <View
                            className="van-cell__title title-class"
                            style={{ minWidth: "86px", maxWidth: "86px", marginRight: "10px" }}
                        >
                            快递公司
                        </View>
                        <View
                            className="van-cell__value value-class"
                            style={{ textAlign: "left", paddingLeft: "10px" }}
                        >
                            {companyList[expressCode]?.name || "请选择快递公司"}
                        </View>
                    </View>
                </Picker>

                <Field
                    type="text"
                    value={trackingNumber}
                    label={"快递单号"}
                    // onChange={handleTrackingNumberChange}
                    placeholder="请输入运单号"
                    onChange={e => {
                        setTrackingNumber(e.target.value);
                    }}
                />
            </View>

            <View onClick={submitForm} className="btn">
                确认提交
            </View>
        </View>
    );
};
export default DeliveryfastInfo;
