.order-detail {
    .picker {
        width: 100%;
        padding: 20px;
        border: 1px solid #ccc;
        margin-bottom: 10px;
    }
    input {
        width: 100%;
        padding: 10px;
        margin-bottom: 10px;
        // border: 1px solid #ccc;
    }
    button {
        width: 100%;
        padding: 10px;
    }
    // .value-class{
    //     padding-left: 20px;
    // }
    .btn {
        height: 88px;
        background: #ffffff;
        border-radius: 32rpx;
        border: 2rpx solid #cccccc;
        margin: 40px 40px;
        font-size: 28rpx;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #333333;
        line-height: 62px;
        padding: 14rpx 32rpx;
        box-sizing: border-box;
        border-radius: 32rpx;
        color: #fff;
        background-color: #f5001d;
        text-align: center;
        position: absolute;
        left: 0;
        right: 0;
        bottom: calc(env(safe-area-inset-bottom) + 100px);
    }
    .order-card {
        margin: 20rpx 24rpx 20rpx 24rpx;
        padding: 32rpx 32rpx;
        background-color: #fff;
        min-height: 200px;
        border-radius: 16rpx;
        .head {
            padding: 20px 20px 30px 30px;
        }
    }
}
