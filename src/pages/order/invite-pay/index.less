.order-notice {
    padding: 24px;
    font-size: 24px;
    .notice-head {
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: center;
        .notice-img {
            width: 88px;
            height: 88px;
        }
        .speech-bubble {
            position: relative;
            background: #f8a0a0; /* 对话框背景颜色 */
            border-radius: 40px; /* 圆角 */
            padding: 20px 40px; /* 内边距 */
            // max-width: 300px; /* 最大宽度 */
            margin: 40px; /* 外边距 */
            .speech-text {
                /* 这里可以添加文本样式 */
                font-size: 32px;
                font-weight: bold;
                color: #fff;
                text-align: center;
            }
        }

        /* 创建对话框的小尾巴 */
        .speech-bubble::after {
            content: "";
            position: absolute;
            bottom: -38px; /* 尾巴距离对话框底部的距离 */
            left: 31px; /* 尾巴距离对话框左边的距离 */
            width: 0;
            height: 0;
            border: 20px solid transparent; /* 透明边框 */
            border-top-color: #f8a0a0; /* 尾巴颜色 */
            border-top-width: 30x; /* 尾巴宽度 */
            // background: #f8a0a0; /* 对话框背景颜色 */
            border-left-width: 0;
        }
    }

    .code-rect {
        display: flex;
        align-items: center;
        justify-content: center;
        margin-top: 40px;
        flex-direction: column;
    }

    .notice {
        background-color: #fff;
        border-radius: 8px;
        padding: 30px 24px;
        margin-top: 40px;
        .title {
            font-size: 32px;
            font-weight: bold;
            margin-bottom: 30px;
            color: #333;
        }

        .item {
            display: block;
            margin-bottom: 10px;
            color: #666;
            font-size: 28px;
        }
    }
}
