import Taro, { useRouter } from "@tarojs/taro";
import { View, Text, Image } from "@tarojs/components";
import "./index.less";
import { QRCode } from "taro-code";
import { getStorage } from "@/utils/storage";
import { STORE_ID } from "@/utils/constant";
import { getShareUrl, isAlipay, isWeixin } from "@/utils/common";
definePageConfig({
    // navigationStyle: "custom",
    navigationBarTitleText: "邀请代付",
    // navigationBarTextStyle: "white"
});
const OrderNotice = () => {
    const router = useRouter();
    const { id } = router.params;
    // console.log("router:", router)
    const arCodeText = getShareUrl(`store-distribution/help-pay?storeId=${getStorage(STORE_ID)}&shareQr=${id}`);
    console.log("arCodeText:", arCodeText);
    return (
        <View className="order-notice">
            {/* 委托购买提示 */}
            <View className="notice-head">
                <Image className="notice-img" src="https://dante-img.oss-cn-hangzhou.aliyuncs.com/30840666772.png" />
                <View className="speech-bubble">
                    <View className="speech-text">
                        <Text className="title">您的亲友正委托您进行购买</Text>
                    </View>
                </View>
            </View>

            <View className="code-rect">
                <QRCode
                    text={arCodeText}
                    size={140}
                    scale={4}
                    errorCorrectLevel="M"
                    typeNumber={2}
                    lazyLoad={true}
                    showMenuByLongpress={true}
                />
                {isWeixin() ? (
                    <Text style={{ textAlign: "center", color: "#ccc", fontSize: "16px", marginTop: "5px" }}>
                        长按保存
                    </Text>
                ) : null}
            </View>

            {/* 订购须知 */}
            <View className="notice">
                <View className="title">订购须知</View>
                <Text className="item">1. 亲友完成支付后，将计入您亲友的2.6万年度跨境购买限额</Text>
                <Text className="item">2. 每笔订单仅支持委托1位亲友订购</Text>
                <Text className="item">
                    3. 若订单已被亲友A订购但未完成支付，若需替换成亲友B，请取消订单后重新分享给亲友B
                </Text>
                <Text className="item">
                    4. 委托订购链接，仅可打开一次，页面关闭后失效，如需付款，请发起方重新生成链接
                </Text>
            </View>
        </View>
    );
};

export default OrderNotice;
