import { Card, Cell, NavBar } from "@antmjs/vantui";
import "./index.less";
import { View } from "@tarojs/components";
import Taro from "@tarojs/taro";

import useDetail from "./hooks/useDetail";
import { DDYBack } from "@/utils/route";
import { isAlipay } from "@/utils/common";

export default () => {
    const [detail, getDetail] = useDetail();

    const statusBarHeight = Taro?.getSystemInfoSync().statusBarHeight;
    return (
        <>
            <NavBar
                title="退款详情"
                leftText=""
                className="page-title"
                fixed
                leftArrow={!isAlipay()}
                border={false}
                onClickLeft={() => {
                    DDYBack();
                }}
            />
            <View className="order-bg-top" style={{ height: `${(statusBarHeight || 20) + 184}px` }}></View>
            <View className="order-content" style={{ paddingTop: `${(statusBarHeight || 20) + 70}px` }}>
                {/** 推送异常信息 */}
                {!!detail?.orderPushErrorMessage && (
                    <View className="error-message">{detail.orderPushErrorMessage}</View>
                )}

                <View className="order-main-card">
                    <View className="card-title">{detail?.statusString}</View>
                </View>
                <View className="order-card order-price">
                    <Cell title={"退款金额"} value={`¥${detail?.fee || 0}`} border={false} />
                </View>
                <View className="order-card" style={{ paddingBottom: "20rpx" }}>
                    <Cell title={"退款信息"} value={""} border={false} />
                    {detail?.itemList?.map(item => (
                        <Card
                            title={item.itemName}
                            thumb={item.skuImage}
                            num={item.quantity.toString()}
                            price={item.originUnitFee.toString()}
                        />
                    ))}
                    <Cell title={"退款原因"} value={detail?.reason} border={false} className="mini-card" />
                    <Cell title={"退款金额"} value={detail?.fee} border={false} className="mini-card" />
                    <Cell title={"申请时间"} value={detail?.applyTime} border={false} className="mini-card" />
                    <Cell title={"退款单号"} value={detail?.refundId} border={false} className="mini-card" />
                    <Cell title={"订单号"} value={detail?.declareId} border={false} className="mini-card small-size" />
                </View>
            </View>
        </>
    );
};
