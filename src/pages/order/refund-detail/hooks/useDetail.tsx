import api from "@/utils/api";
import { useRouter } from "@tarojs/taro";
import { useEffect, useState } from "react";

interface RefundDetailType {
    applyTime: string;
    buyerNote: string;
    declareId: string;
    fee: number;
    itemList: {
        itemName: string;
        originUnitFee: number;
        quantity: number;
        skuImage: string;
    }[];
    reason: string;
    refundId: string;
    statusString: string;
    orderPushErrorMessage?: string;
}

const useDetail: () => [RefundDetailType | null, () => void] = () => {
    const [detail, setDetail] = useState<RefundDetailType | null>(null);
    const route = useRouter();
    const getDetail = () => {
        api.getFindRefundDetail({
            data: {
                refundId: route.params?.refundId,
            },
            filterCheck: true,
        })
            .then(res => {
                setDetail(res);
            })
            .catch(err => {});
    };
    useEffect(() => {
        getDetail();
    }, []);
    return [detail, getDetail];
};
export default useDetail;
