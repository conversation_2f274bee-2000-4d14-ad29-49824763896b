.order-bg-top {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 140rpx;
    border-bottom-left-radius: 30rpx;
    border-bottom-right-radius: 30rpx;
    background-color: #f50050;
    z-index: 1;
}

.error-message {
    margin: 20rpx 24rpx 20rpx 24rpx;
    padding: 12rpx 24rpx;
    background-color: #fffbe8;
    border-radius: 8rpx;
    color: #f3a367;
}

.page-title {
    color: #fff;
    z-index: 999;
    background-color: #f50050;

    .van-nav-bar__content {
        height: 88rpx;
    }

    .van-icon-arrow-left:before {
        content: "\e668";
        color: #fff;
    }

    .van-nav-bar__title {
        color: #fff;
    }
}

page {
    position: relative;
    height: 100vh;
    box-sizing: border-box;

    .order-content {
        z-index: 10;
        position: relative;
        padding-bottom: calc(110px + env(safe-area-inset-bottom));

        .order-main-card {
            margin: 20rpx 24rpx 20rpx 24rpx;
            padding: 32rpx 32rpx;
            background-color: #fff;
            border-radius: 16rpx;

            .card-title {
                font-size: 36rpx;
                font-family: PingFangSC-Medium, PingFang SC;
                color: #333333;
                line-height: 80rpx;
                font-weight: 800;
            }
        }

        .order-price {
            .van-cell__value {
                color: #f50050;
            }
        }

        .order-card {
            margin: 20rpx 24rpx 20rpx 24rpx;
            // padding: 32rpx 32rpx;
            background-color: #fff;
            border-radius: 16rpx;

            .info-item {
                padding: 20rpx 0;
                line-height: 30rpx;
            }

            .mini-card {
                padding-top: 12rpx;
                padding-bottom: 12rpx;
            }
            .small-size {
                .van-cell__value {
                    font-size: 24rpx;
                }
            }
            .title-class {
                max-width: 200px;
            }
        }
    }
}
