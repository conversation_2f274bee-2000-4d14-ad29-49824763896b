import { Icon } from "@antmjs/vantui";
import { View } from "@tarojs/components";

const passColor = "green";
const unPassColor = "grey";
export default ({ orderItem }) => {
    const { returnType } = orderItem;
    const progresss =
        returnType === 3
            ? [
                  { id: "-3,-7,-8,-6", name: "商家处理" },
                  { id: "-6,-8", name: "买家寄回" },
                  { id: "-6", name: "退款完成" },
              ]
            : [
                  { id: "-3,-7,-6", name: "商家处理" },
                  { id: "-6", name: "退款完成" },
              ];
    return (
        <View style={{ display: "flex", flexDirection: "row", alignItems: "center" }}>
            {progresss.map(item => {
                return <ProgessItem selected={item.id.indexOf("-8") > -1} title={item.name} />;
            })}
        </View>
    );
};

const ProgessItem = ({ selected, title }) => {
    return (
        <View style={{ flex: 1 }}>
            <View style={{ fontSize: "14px", color: selected ? passColor : unPassColor }}>{title}</View>
            <View style={{ display: "flex", flexDirection: "row", alignItems: "center" }}>
                <Icon
                    name="passed"
                    size="20px"
                    style={{
                        color: selected ? passColor : unPassColor,
                        padding: "6px 10px",
                    }}
                />
                <View style={{ flex: 1, height: "2px", backgroundColor: selected ? passColor : unPassColor }} />
            </View>
        </View>
    );
};
