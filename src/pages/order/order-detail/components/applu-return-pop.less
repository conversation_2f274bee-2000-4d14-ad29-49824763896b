.action-pop-return {
    background-color: #f3f3f3;
    padding-top: 10px;
    .pop-item {
        margin: 24px;
        background-color: #fff;
        border-radius: 8px;
        padding: 20px;
        .return-types {
            display: flex;
            align-items: center;
            // justify-content:;
            padding: 20px 0;
            .select-item {
                border: 2px solid #333;
                font-size: 26px;
                padding: 4px 10px;
                border-radius: 6px;
                margin-right: 18px;
            }
            .selected {
                border-color: #f50050;
                color: #f50050;
            }
        }
        .return-info {
            font-size: 24px;
            color: #f50050;
        }
        .pop-item-title {
            padding: 0 0 10px 0;
        }
        .pop-item-reason {
            .select-reason-item {
                border: 2px solid #333;
                font-size: 26px;
                padding: 10px 10px;
                border-radius: 50px;
                text-align: center;
                // margin-right: 18px;
                margin: 0 auto 20px;
                width: 80%;
            }
            .selected {
                border-color: #f50050;
                color: #f50050;
            }
        }
    }
    .bottom-rect {
        margin-top: 120px;
        padding: 20px;
        .bottom-rect-btn {
            width: 100%;
            background-color: #f50050;
            color: #fff;
        }
    }
}
