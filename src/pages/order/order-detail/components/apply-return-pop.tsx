import react, { useEffect, useState } from "react";
import { View } from "@tarojs/components";
import { ActionSheet, Button, Divider, Field, Radio, RadioGroup, Uploader } from "@antmjs/vantui";
import ShopItemList from "@/components/shop-item-list";
import "./applu-return-pop.less";
import Taro from "@tarojs/taro";
import { PROJECT_CONFIG } from "@/utils/env";
import DDYToast from "@/utils/toast";
import api from "@/utils/api";
const infos = {
    1: "选择【仅退款】,无需寄回商品",
    3: "选择【退货退款】,需要将包裹寄回后才能退款，若未收到货请选择【仅退款】",
};
export default function ApplyReturnPop({ skuOrders, orderId, onload }) {
    const [show, setShow] = react.useState(false);
    const [step, setStep] = useState(0);
    const [returnType, setReturnType] = useState(0); //1: 退款；3: 退货退款
    const [receiptStatus, setReceiptStatus] = useState(false);
    const [reasonType, setReasonType] = useState("");
    const [value, setValue] = useState("");
    const [reasonList, setReasonList] = useState([
        { id: 1, name: "质量问题" },
        { id: 2, name: "其他" },
        { id: 3, name: "描述与实际不符" },
    ]);

    const [fileList, setFileList] = useState([]);
    const onClose = () => {
        setShow(false);
        setStep(0);
        setReturnType(1);
    };
    const afterRead = event => {
        const { file } = event.detail;
        // 可在此处新增云上传图片操作
        Taro.uploadFile({
            url: `${PROJECT_CONFIG.API_MALL}/api/file/upload`, //仅为示例，非真实的接口地址
            name: "file",
            filePath: file.url,
            success(res) {
                DDYToast.hideLoading();
                file.url = JSON.parse(res.data).data;
                // @ts-ignore
                setFileList(fileList.concat(file));
            },
        });
        ///@ts-ignore
        // setFileList(value.concat(file))
    };

    const deleteAction = event => {
        const { index } = event.detail;
        const valueNew = JSON.parse(JSON.stringify(value));
        valueNew.splice(index, 1);
        setFileList(valueNew);
    };

    const submit = () => {
        if (!reasonType) {
            return DDYToast.info("请选择退款原因");
        }
        api.jdApplyReturn({
            data: {
                orderId,
                orderType: 1,
                refundType: returnType,
                buyerRemark: value,
                reasonType: reasonType,
                imagesJson: JSON.stringify(fileList.map(item => item.url)),
                buyerReceivedStatus: receiptStatus,
            },
            method: "POST",
        }).then(res => {
            // console.log("res:", res);
            DDYToast.success("申请成功");
            setShow(false);
            setReturnType(0);
            setReceiptStatus(false);
            setReasonType("");
            setValue("");
            onload && onload();
        });
    };

    useEffect(() => {
        if (show) {
            api.getJdReasonList({
                data: {},
                // method: 'POST'
            }).then(res => {
                // console.log("res:", res);
                setReasonList(res);
            });
        }
    }, [show]);

    return (
        <>
            <ActionSheet
                show={show}
                title={step === 0 ? "选择售后类型" : `选择${returnType === 1 ? "退款" : "退货退款"}原因`}
                onClose={() => {
                    onClose();
                    setReturnType(0);
                    setReceiptStatus(false);
                    setReasonType("");
                    setValue("");
                }}
            >
                {step === 0 ? (
                    <View className="action-pop-return">
                        <View className="pop-item">
                            <View>售后类型</View>
                            <View className="return-types">
                                <View
                                    className={`select-item ${returnType === 1 ? "selected" : ""}`}
                                    onClick={() => {
                                        setReturnType(1);
                                    }}
                                >
                                    仅退款
                                </View>
                                <View
                                    className={`select-item ${returnType === 3 ? "selected" : ""}`}
                                    onClick={() => {
                                        setReturnType(3);
                                    }}
                                >
                                    退货退款
                                </View>
                            </View>
                            <View className="return-info">{infos[returnType]}</View>
                        </View>
                        <View className="pop-item">
                            <ShopItemList list={skuOrders || []} hideShop={true} />
                        </View>
                        <View className="bottom-rect">
                            <Button
                                className="bottom-rect-btn"
                                onClick={() => {
                                    if (returnType === 0) {
                                        return DDYToast.info("请先选择售后类型");
                                    }
                                    setStep(2);
                                }}
                            >
                                下一步
                            </Button>
                        </View>
                    </View>
                ) : (
                    <View className="action-pop-return">
                        {returnType === 1 ? (
                            <View className="pop-item">
                                <View className="pop-item-title">是否收到货</View>
                                <RadioGroup
                                    direction="horizontal"
                                    value={receiptStatus}
                                    onChange={e => setReceiptStatus(e.detail)}
                                >
                                    <Radio name="false">未收到货</Radio>
                                    <Radio name="true">已收到货</Radio>
                                </RadioGroup>
                            </View>
                        ) : null}
                        <View className="pop-item">
                            <View className="pop-item-title">{`${returnType === 1 ? "退款" : "退货退款"}原因`}</View>
                            <View className="pop-item-reason">
                                {reasonList.map((reansonItem: any, index) => {
                                    return (
                                        <View
                                            className={`select-reason-item ${
                                                reasonType === reansonItem.id ? "selected" : ""
                                            }`}
                                            key={index}
                                            onClick={() => {
                                                setReasonType(reansonItem.id);
                                            }}
                                        >
                                            {reansonItem.name}
                                        </View>
                                    );
                                })}
                            </View>
                            <Divider />
                            <View className="pop-item-title">{`${returnType === 1 ? "退款" : "退货退款"}说明`}</View>
                            <Field
                                // label="留言"
                                value={value}
                                type="textarea"
                                placeholder="请输入退款说明,最多一百个字"
                                autosize={{ minHeight: "30px" }}
                                border={false}
                                onChange={e => setValue(e.detail)}
                                maxlength={100}
                            />
                            {returnType === 3 ? (
                                <Uploader
                                    fileList={fileList}
                                    onAfterRead={afterRead}
                                    onDelete={deleteAction}
                                    deletable
                                    maxCount={1}
                                />
                            ) : null}
                        </View>
                        <View className="bottom-rect">
                            <Button
                                className="bottom-rect-btn"
                                onClick={() => {
                                    submit();
                                }}
                            >
                                提交申请
                            </Button>
                        </View>
                    </View>
                )}
            </ActionSheet>
            <Button type="primary" className="btn" onClick={() => setShow(true)}>
                申请售后
            </Button>
        </>
    );
}
