import DDYToast from "@/utils/toast";
import { Icon } from "@antmjs/vantui";
import { Button, View } from "@tarojs/components";
import Taro from "@tarojs/taro";

export default ({ sellerAddress }) => {
    return (
        <>
            <View className="order-card">
                <View>请您自行联系快递公司退回，需填写快递发单号; 请不要邮寄到付</View>
                <View className="order-address-block">
                    <Icon name="location-o" size="32rpx" />
                    <View className="order-address-content">
                        <View>{sellerAddress}</View>
                        {/* <View>浙江省 金华市 义乌市 xxxx街道xx公司xx</View> */}
                    </View>
                    {/* <Button type="default">复制</Button> */}
                    <View
                        className="order-address-btn"
                        onClick={() => {
                            Taro.setClipboardData({
                                data: sellerAddress,
                                success: function (res) {
                                    DDYToast.info("复制成功");
                                },
                            });
                        }}
                    >
                        复制
                    </View>
                </View>
            </View>
            <View className="order-card" style={{ minHeight: "40px" }}>
                {/* <View>退货须知</View> */}
                <View className="card-title">退货须知</View>
                <View>请确保商品不影响二次销售（质量问题除外！）</View>
            </View>
        </>
    );
};
