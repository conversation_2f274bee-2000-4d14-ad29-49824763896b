import { Cell } from "@antmjs/vantui";
import { View } from "@tarojs/components";
import { payInfoObj } from "../hooks/useDetail";
interface OrderPaymentCardProps {
    payInfo: payInfoObj;
}
export default (props: OrderPaymentCardProps) => {
    const { payInfo } = props;
    return (
        <View className="order-payment-card">
            <View className="payment-title">支付人信息</View>
            <Cell className="info-item" title="支付人" border={false} value={`${payInfo.payName}`} />
            <Cell className="info-item" title="身份证号" border={false} value={`${payInfo.payIdCard}`} />
            <Cell className="info-item" title="微信昵称" border={false} value={`${payInfo.phone}`} />
        </View>
    );
};
