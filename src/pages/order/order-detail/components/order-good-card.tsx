import { Text, View } from "@tarojs/components";
import { shopOrderObj, skuOrderObj } from "../hooks/useDetail";
import ShopItemList from "@/components/shop-item-list";
import { Cell } from "@antmjs/vantui";

interface OrderGoodCardProps {
    skuOrders: skuOrderObj[];
    shopOrder: shopOrderObj;
    discount: number;
    totalTax: number;
}
export default (props: OrderGoodCardProps) => {
    const { skuOrders, shopOrder, totalTax, discount } = props;
    const totalFee = ((shopOrder.fee - shopOrder.shipFee - totalTax + discount) / 100).toFixed(2);
    return (
        <View className="order-good-card">
            <View className="goods-block">
                <ShopItemList list={skuOrders} />
            </View>
            <View className="info-block">
                <Cell className="info-item" title="商品总额" border={false} value={`¥${totalFee}`} />
                <Cell className="info-item" title="税费" border={false} value={`¥${(totalTax / 100).toFixed(2)}`} />
                <Cell
                    className="info-item"
                    title="运费"
                    border={false}
                    value={`¥${(shopOrder.shipFee / 100).toFixed(2)}`}
                />
                <Cell className="info-item" title="优惠" border={false} value={`-¥${(discount / 100).toFixed(2)}`} />
            </View>
            <View className="goods-total">
                <Text className="price-title">
                    需付款：
                    <Text className="price-value">￥{(shopOrder.fee / 100).toFixed(2)}</Text>
                </Text>
            </View>
        </View>
    );
};
