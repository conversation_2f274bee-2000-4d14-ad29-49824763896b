import { View, Text, Image } from "@tarojs/components";
import { CountDown, ICountDownRef, Icon } from "@antmjs/vantui";
import { orderReceiverInfoObj, paymentObj, shopOrderObj } from "../hooks/useDetail";
import { useEffect, useRef, useState } from "react";

export const ORDER_STATUS_TEXT = {
    "0": "未付款",
    "1": "已付款",
    "2": "已发货",
    "3": "已确认收货",
    "4": "已结算",
    "-1": "买家关闭订单",
    "-2": "商家关闭订单",
    "-3": "申请退款",
    "-4": "同意退款",
    "-5": "拒绝退款",
    "-6": "已退款",
    "-7": "申请退货退款",
    "-8": "待买家退货",
    "-9": "拒绝退货退款",
    "-10": "买家已退货",
    "-11": "商家确认退货",
    "-12": "退货后商家拒绝",
    "-13": "超时关闭订单",
    "-14": "订单删除",
    "-15": "退款处理中",
    "-16": "订单已关闭, 等待自动退款",
    "-17": "订单已关闭且已退款",
    "-18": "取消退款请求",
    "-19": "退款失败",
    "-100": "订单流转错误",
    "500": "异常订单",
};

interface OrderMainCardProps {
    shopOrder: shopOrderObj;
    orderReceiverInfos: orderReceiverInfoObj[];
    payment: paymentObj;
    refund?: boolean;
}
export default (props: OrderMainCardProps) => {
    const { shopOrder, orderReceiverInfos, payment } = props;
    const addressInfo = orderReceiverInfos?.[0]?.receiverInfo;
    const [remainTime, setRemainTime] = useState(0);
    const timeRef = useRef<ICountDownRef>();

    useEffect(() => {
        if (payment) {
            let timestamp = new Date().getTime() - payment.createdAt;
            let timeSurplus = 15 * 60 * 1000 - Math.abs(timestamp);
            setRemainTime(timeSurplus);
        }
    }, [payment]);

    console.log("addressInfo:", addressInfo);
    return (
        <View className="order-main-card">
            <View className="order-status">
                {shopOrder.extra.identityError == "true" && <Text className="order-status-text">身份证错误</Text>}
                {shopOrder.extra.identityError == "wait" && <Text className="order-status-text">待实名审核</Text>}
                {shopOrder.extra.identityError !== "true" && shopOrder.extra.identityError !== "wait" && (
                    <>
                        <Text className="order-status-text">{ORDER_STATUS_TEXT[shopOrder.status]}</Text>
                        {shopOrder.status === 0 && remainTime > 0 && (
                            <CountDown
                                time={remainTime}
                                ref={timeRef}
                                format="剩 mm 分 ss 秒自动关闭"
                                className="order-time-text"
                                onChange={e => {
                                    console.log(e.detail);
                                    if ((e.detail as unknown) === 0) {
                                        timeRef.current?.pause();
                                    }
                                }}
                            />
                        )}
                    </>
                )}
            </View>

            {/* {!props.refund && ( */}
            <View className="address-block">
                <View className="receive-info">
                    <Icon name="location-o" size="32rpx" />
                    <Text className="name">{addressInfo?.receiveUserName}</Text>
                    <Text className="phone">{addressInfo?.mobile}</Text>
                </View>
                <View className="address-detail">
                    {addressInfo?.province} {addressInfo?.city} {addressInfo?.region} {addressInfo?.street}{" "}
                    {addressInfo?.detail}
                </View>
            </View>
            {/* )} */}
        </View>
    );
};
