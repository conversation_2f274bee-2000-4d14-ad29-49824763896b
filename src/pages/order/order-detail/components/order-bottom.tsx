import { IDENTITY } from "@/utils/constant";
import { getStorage, setStorage } from "@/utils/storage";
import { Button, View } from "@tarojs/components";
import { shopOrderObj, shopOrderOperationObj } from "../hooks/useDetail";
import api from "@/utils/api";
import { DDYNavigateTo } from "@/utils/route";
import Taro, { useDidShow } from "@tarojs/taro";
import DDYToast from "@/utils/toast";
import { mall_event, mall_event_key, reportEvent } from "@/utils/track-report";
import { isAlipay, queryInfo, saveSignback } from "@/utils/common";
import OrderUpdateRealAuth from "./order-update-real-auth";
import { useRef, useState } from "react";
import ApplyReturnPop from "./apply-return-pop";
import orderItem from "@/components/order-item";

interface OrderBottomProps {
    shopOrder: shopOrderObj;
    operations: shopOrderOperationObj[];
    refund: boolean;
    refundId?: number;
    onFresh?: () => void;
    skuOrders?: any[];
    showDetailBtn?: boolean;
    showCommit?: boolean;
}

export default (props: OrderBottomProps) => {
    const { shopOrder, operations, refund, onFresh, skuOrders, showDetailBtn, showCommit } = props;
    const [show, setShow] = useState(false);
    const identity = getStorage(IDENTITY);
    const orderInfo = useRef();
    const changeCard = (id, extra) => {
        setShow(true);
    };

    const delOrder = (id, status) => {
        DDYToast.showModal({
            title: "提示",
            content: "确认删除订单吗？",
            success(res) {
                if (res.confirm) {
                    // console.log('用户点击确定')
                    api.delOrder({
                        data: {
                            orderId: id,
                        },
                        method: "DELETE",
                        contentType: "application/x-www-form-urlencoded",
                        filterCheck: true,
                    })
                        .then(res => {
                            if (res) {
                                setStorage("dealOrder", "true");
                                onFresh && onFresh();
                                // DDYBack({
                                //     delta: 1,
                                // });
                            } else {
                                DDYToast.info(res.message || "删除订单异常");
                            }
                        })
                        .catch();
                } else if (res.cancel) {
                    // console.log('用户点击取消')
                }
            },
        });
    };

    const cancelOrder = (id, status) => {
        DDYToast.showModal({
            title: "提示",
            content: "确认取消订单吗？",
            success(res) {
                if (res.confirm) {
                    let map = new Map();
                    map.set(mall_event_key.MALL_KEY_ORDER_ID, id);
                    // map.set(mall_event_key.MALL_KEY_GOOD_NUM, orderNum);
                    // map.set(mall_event_key.MALL_KEY_GOOD_SPEC_ID, specId)
                    reportEvent(mall_event.MALL_EVENT_ORDER_APPLY_PAY, map);
                    api.cancelOrder({
                        data: {
                            orderId: id,
                            orderType: 1,
                        },
                        method: "POST",
                        contentType: "application/x-www-form-urlencoded",
                        filterCheck: true,
                    })
                        .then(res => {
                            if (res === true) {
                                onFresh && onFresh();
                                // setStorage("dealOrder", "true");
                                // DDYBack({
                                //     delta: 1,
                                // });
                            } else {
                                DDYToast.info(res.errorMsg || "取消订单异常");
                            }
                        })
                        .catch();
                }
            },
        });
    };
    useDidShow(() => {
        let options = Taro.getEnterOptionsSync();
        if (options.scene === 1038 && options.referrerInfo.appId == "wxef277996acc166c3") {
            // 代表从收银台小程序返回
            let extraData = options.referrerInfo.extraData;
            DDYToast.hideLoading();
            if (!extraData) {
                // "当前通过物理按键返回，未接收到返参，建议自行查询交易结果";
                console.log("当前通过物理按键返回，未接收到返参，建议自行查询交易结果");
                if (shopOrder.status == 1 && orderInfo.current) {
                    DDYNavigateTo({
                        url: "/pages/order/pay_success/index?success=0&orderNo=" + orderInfo.current,
                    });
                    orderInfo.current = undefined;
                }
            } else {
                DDYToast.hideLoading();
                if (extraData.code == "success") {
                    // "支付成功";
                    console.log("支付成功");
                    if (orderInfo.current) {
                        DDYNavigateTo({
                            url: "/pages/order/pay_success/index?success=0&orderNo=" + orderInfo.current,
                        });
                        orderInfo.current = undefined;
                    }
                } else if (extraData.code == "cancel") {
                    // "支付已取消";
                    console.log("支付已取消");
                    // gotoDetail(orderInfo.current)
                    orderInfo.current && onFresh && onFresh();
                    orderInfo.current = undefined;
                } else {
                    // "支付失败：" + extraData.errmsg;
                    console.log("支付失败");
                    // gotoDetail(orderInfo.current)
                    orderInfo.current && onFresh && onFresh();
                    orderInfo.current = undefined;
                }
            }
        }
    });
    const payMoney = (id, status) => {
        // 查询是否同意协议

        let map = new Map();
        map.set(mall_event_key.MALL_KEY_ORDER_ID, id);
        // map.set(mall_event_key.MALL_KEY_GOOD_NUM, orderNum);
        // map.set(mall_event_key.MALL_KEY_GOOD_SPEC_ID, specId)
        reportEvent(mall_event.MALL_EVENT_ORDER_APPLY_PAY, map);
        // 支付请求
        api.payOrder({
            data: {
                orderIds: [id],
                // channel: shopOrder.type == 3 ? "Integral-pay" : "wechatpay-jsapi", // 增加积分支付判断
                usageChannel: 2,
            },
            method: "POST",
            filterCheck: true,
        }).then(res => {
            payBandle(res, id);
        });
    };

    const payBandle = (res, id) => {
        if (!res.success) {
            if (res.error === "需完善通商云账户信息") {
                DDYToast.showModal({
                    title: "实名制注册，请您先【填写实名信息】、【绑定银行卡】并【签署协议】",
                    success: res => {
                        if (res.confirm) {
                            queryInfo({
                                go: true,
                                goSign: () => {
                                    saveSignback();
                                    DDYNavigateTo({
                                        url: "/pages/my/bank-list/index",
                                    });
                                },
                            });
                        } else {
                        }
                    },
                });
                DDYToast.hideLoading();
                return;
            }
            DDYToast.info(res.error);
            return;
        }
        if (res.result.invokeMiniProgram) {
            DDYToast.hideLoading();
            Taro.openEmbeddedMiniProgram({
                appId: "wxef277996acc166c3",
                extraData: res.result.redirectInfo,
            });
            orderInfo.current = res[0];
            return;
        }
        if (res.result.invokeH5) {
            if (res.result.requestOriginalUrl) {
                try {
                    const requestOriginalUrl = JSON.parse(res.result.requestOriginalUrl);
                    DDYToast.showModal({
                        title: "是否使用微信h5支付",
                        content: requestOriginalUrl.payInfo,
                        success: res => {
                            if (res.confirm) {
                                Taro.setClipboardData({
                                    data: requestOriginalUrl.payInfo,
                                    success(res) {
                                        DDYToast.info("复制成功");
                                    },
                                });
                            }
                        },
                    });
                } catch (error) {}
                DDYToast.hideLoading();
                return;
            }
            return;
        }
        if (isAlipay() && res.result.channel === "allinpay-yst" && res.result.redirectInfo) {
            Taro.tradePay({
                tradeNO: res.result.redirectInfo,
                success(data) {
                    console.log("allinpay-yst:", res);
                    DDYToast.hideLoading();
                    //@ts-ignore
                    if (data.resultCode === "9000") {
                        goSuccess(id);
                    } else {
                        //@ts-ignore
                        DDYToast.info(data.memo);
                    }
                },
                fail(res) {
                    DDYToast.info("支付失败");
                    DDYToast.hideLoading();
                },
            });
            return;
        }
        if (res.result.redirectInfo) {
            try {
                const payObj = JSON.parse(res.result.redirectInfo);
                if (payObj) {
                    Taro.requestPayment({
                        //@ts-ignore
                        appId: payObj.appId,
                        timeStamp: payObj.timeStamp,
                        nonceStr: payObj.nonceStr,
                        package: payObj.package,
                        signType: payObj.signType,
                        paySign: payObj.paySign,
                        success: function (res) {
                            DDYToast.success("支付成功", 1000);
                            goSuccess(id);
                            reportEvent(
                                mall_event.MALL_EVENT_PAY_SUCCESS,
                                new Map()
                                    .set(mall_event_key.MALL_KEY_ORDER_ID, id)
                                    .set(mall_event_key.MALL_KEY_ORDER_PAY_CHANNEL, "微信"),
                            );
                        },
                        fail: function (res) {
                            DDYToast.info("支付失败");
                            reportEvent(
                                mall_event.MALL_EVENT_PAY_FAIL,
                                new Map()
                                    .set(mall_event_key.MALL_KEY_ORDER_ID, id)
                                    .set(mall_event_key.MALL_KEY_ORDER_PAY_CHANNEL, "微信")
                                    .set(mall_event_key.MALL_KEY_ORDER_PAY_ERROR_MESSAGE, JSON.stringify(res)),
                            );
                        },
                    });
                } else {
                    DDYToast.success("积分抵扣成功", 1000);
                    reportEvent(
                        mall_event.MALL_EVENT_PAY_SUCCESS,
                        new Map()
                            .set(mall_event_key.MALL_KEY_ORDER_ID, id)
                            .set(mall_event_key.MALL_KEY_ORDER_PAY_CHANNEL, "积分"),
                    );
                    goSuccess(id);
                }
            } catch (error) {
                console.log("支付信息解析异常");
                reportEvent(
                    mall_event.MALL_EVENT_PAY_FAIL,
                    new Map()
                        .set(mall_event_key.MALL_KEY_ORDER_ID, id)
                        .set(mall_event_key.MALL_KEY_ORDER_PAY_CHANNEL, "微信")
                        .set(mall_event_key.MALL_KEY_ORDER_PAY_ERROR_MESSAGE, JSON.stringify(error)),
                );
            }
            return;
        }

        DDYToast.success("积分抵扣成功", 1000);
        reportEvent(
            mall_event.MALL_EVENT_PAY_SUCCESS,
            new Map().set(mall_event_key.MALL_KEY_ORDER_ID, id).set(mall_event_key.MALL_KEY_ORDER_PAY_CHANNEL, "积分"),
        );
        goSuccess(id);
    };

    const goSuccess = id => {
        setTimeout(() => {
            console.log("onFresh:", onFresh);
            onFresh && onFresh();
            DDYNavigateTo({
                url: "/pages/order/pay_success?success=0&orderNo=" + id,
            });
        }, 1000);
    };

    const confirmOrder = (id: number, status: number) => {
        DDYToast.showModal({
            title: "提示",
            content: "是否确认收货",
            success(res) {
                if (res.confirm) {
                    reportEvent(
                        mall_event.MALL_EVENT_ORDER_CONFIRM_RECEIPT,
                        new Map().set(mall_event_key.MALL_KEY_ORDER_ID, id),
                    );
                    api.confirmOrder({
                        data: {
                            orderId: id,
                            orderType: 1,
                        },
                        method: "POST",
                        contentType: "application/x-www-form-urlencoded",
                        filterCheck: true,
                    }).then(data => {
                        reportEvent(
                            mall_event.MALL_EVENT_ORDER_CONFIRM_RESULT,
                            new Map()
                                .set(mall_event_key.MALL_KEY_ORDER_ID, id)
                                .set(mall_event_key.MALL_KEY_ORDER_CONFIRM_RESPONSE, JSON.stringify(data)),
                        );
                        if (data.data === true) {
                            DDYToast.success("确认成功");
                            // this.$emit("refreshOrderList");
                            onFresh && onFresh();
                        } else {
                            DDYToast.info(data.errorMsg || data.message || "确认收货异常");
                        }
                        console.log("确认收货成功");
                    });
                }
            },
        });
    };

    const refundApply = (id, status) => {
        let map = new Map();
        map.set(mall_event_key.MALL_KEY_ORDER_ID, id);
        // map.set(mall_event_key.MALL_KEY_ORDER_PAY_CHANNEL, '积分')
        reportEvent(mall_event.MALL_EVENT_ORDER_APPLY_REFUND, map);
        DDYNavigateTo({
            url:
                "/pages/order/pre-return/index?orderId=" +
                id +
                "&orderType=" +
                1 +
                "&status=" +
                status +
                1 +
                "&refundId=" +
                props.refundId,
        });
    };

    const updateRealAuth = data => {
        api.identityChang({
            method: "POST",
            data: {
                shopOrderId: shopOrder.id,
                identityName: data.paperName,
                identityCode: data.paperNo,
            },
            filterCheck: true,
        }).then(res => {
            DDYToast.success("修改成功");
            setShow(false);
            onFresh && onFresh();
        });
    };
    return identity !== "serviceProvider" ? (
        <View className="order-bottom">
            <View className="btn-container">
                {shopOrder.extra.identityError == "true" && (
                    <View
                        className="btn pay"
                        onClick={e => {
                            e.stopPropagation();
                            changeCard(shopOrder.id, shopOrder.extra);
                        }}
                    >
                        修改身份证
                    </View>
                )}
                {(shopOrder.status == -1 || shopOrder.status == -13) && (
                    <View
                        className="btn pay"
                        onClick={e => {
                            e.stopPropagation();
                            delOrder(shopOrder.id, shopOrder.status);
                        }}
                    >
                        删除订单
                    </View>
                )}

                {operations.map(itemOpt => {
                    if (itemOpt.text == "buyerCancel")
                        return (
                            <View
                                className="btn"
                                onClick={e => {
                                    e.stopPropagation();
                                    cancelOrder(shopOrder.id, shopOrder.status);
                                }}
                            >
                                取消订单
                            </View>
                        );
                    if (itemOpt.text == "pay")
                        return (
                            <View
                                className="btn pay"
                                onClick={e => {
                                    e.stopPropagation();
                                    payMoney(shopOrder.id, shopOrder.status);
                                }}
                            >
                                立即支付
                            </View>
                        );
                    if (itemOpt.text == "confirm")
                        return (
                            <View
                                className="btn pay"
                                onClick={e => {
                                    e.stopPropagation();
                                    confirmOrder(shopOrder.id, shopOrder.status);
                                }}
                            >
                                确认收货
                            </View>
                        );
                    if (itemOpt.text == "refundApply" && refund && shopOrder.type == 1) {
                        return (
                            <View
                                className="btn pay"
                                onClick={e => {
                                    e.stopPropagation();
                                    refundApply(shopOrder.id, shopOrder.status);
                                }}
                            >
                                申请退款
                            </View>
                        );
                    }
                    if (itemOpt.text === "return" && showDetailBtn) {
                        return (
                            <View
                                className="btn pay"
                                onClick={() => {
                                    // 填写快递信息
                                    DDYNavigateTo({
                                        url: `/pages/order/delivery-info/index?id=${props.refundId}`,
                                    });
                                }}
                            >
                                我已退回，填写快递单号
                            </View>
                        );
                    }
                    if (itemOpt.text === "returnApply" && showDetailBtn) {
                        return <ApplyReturnPop skuOrders={skuOrders} orderId={shopOrder.id} onload={props.onFresh} />;
                    }
                    if (itemOpt.text === "comment") {
                        return (
                            <View
                                className="btn pay"
                                onClick={e => {
                                    e.stopPropagation();
                                    // refundApply(shopOrder.id, shopOrder.status);
                                    DDYNavigateTo({
                                        url: `/pages/submit-commit/index?id=${shopOrder.id}`,
                                    });
                                }}
                            >
                                发表评价
                            </View>
                        );
                    }
                    return null;
                })}
            </View>

            <OrderUpdateRealAuth
                show={show}
                onOk={data => {
                    updateRealAuth(data);
                }}
                closeFn={() => {
                    setShow(false);
                }}
            />
        </View>
    ) : null;
};
