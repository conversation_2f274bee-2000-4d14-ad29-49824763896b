import { icon_info_arrow } from "@/images";
import { DDYNavigateTo } from "@/utils/route";
import { View, Text, Image } from "@tarojs/components";
import { useRouter } from "@tarojs/taro";
import { useState } from "react";

export default ({ logisticsDetail }) => {
    const route = useRouter();
    // const logisticsDetail = [];
    // const nowPackge = 1;
    const [nowPackge, setNowPackge] = useState(0);

    const queryLogistic = () => {
        DDYNavigateTo({
            url: "/pages/order/logistics/index?orderType=1&orderId=" + route.params.id,
        });
    };
    return (
        <>
            <View className="order-logistics-card">
                <View className="card-title">物流信息</View>
                <View className="info_block">
                    {logisticsDetail.length > 0 ? (
                        <View className="logistic_title">
                            {logisticsDetail.map((item, index) => (
                                <View
                                    className={
                                        nowPackge === index ? "logistic_title_list active" : "logistic_title_list"
                                    }
                                    onClick={() => {
                                        setNowPackge(index);
                                    }}
                                >
                                    {" "}
                                    包裹{index + 1}
                                </View>
                            ))}
                        </View>
                    ) : null}
                    {logisticsDetail.map((item, index) => {
                        if (nowPackge !== index) return null;
                        return (
                            <View onClick={() => queryLogistic()}>
                                <View className="item">
                                    <Text className="title">物流信息:</Text>
                                    <Image className="arrow" src={icon_info_arrow} mode="widthFix"></Image>
                                </View>
                                <View className="item">
                                    <Text className="title">配送方式:</Text>
                                    <Text className="cost">普通快递</Text>
                                </View>
                                <View className="item">
                                    <Text className="title">承运公司:</Text>
                                    <Text className="cost">{item.shipmentCorpName}</Text>
                                </View>
                                <View className="item">
                                    <Text className="title">运单号:</Text>
                                    <Text className="cost">{item.shipmentSerialNo}</Text>
                                </View>
                            </View>
                        );
                    })}
                </View>
            </View>
        </>
    );
};
