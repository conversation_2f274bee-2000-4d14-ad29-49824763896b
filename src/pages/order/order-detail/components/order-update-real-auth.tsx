import RealNameForm from "@/components/real-name-form";
import { ActionSheet, Icon, NoticeBar } from "@antmjs/vantui";
import { View, Text } from "@tarojs/components";

export default ({ show, closeFn, onOk }) => {
    return (
        <>
            <ActionSheet
                show={show}
                title="实名认证"
                onClose={() => {
                    // e.stopPropagation();
                    closeFn();
                }}
                onClick={e => {
                    e.stopPropagation();
                }}
            >
                <View style={{ padding: "20rpx 20rpx 0 20px" }}>
                    <RealNameForm
                        onOk={() => {}}
                        onFail={() => {}}
                        submit={obj => {
                            onOk(obj);
                        }}
                    />
                </View>
            </ActionSheet>
        </>
    );
};
