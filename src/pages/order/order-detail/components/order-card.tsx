import { Cell, Icon } from "@antmjs/vantui";
import { View } from "@tarojs/components";
import { payInfoObj, paymentObj, shopOrderObj } from "../hooks/useDetail";
import Taro from "@tarojs/taro";
import DDYToast from "@/utils/toast";
import { timestampToTime } from "@/utils/DateTimeUtils";
interface OrderCardProps {
    shopOrder: shopOrderObj;
    payInfo: payInfoObj;
    payment: paymentObj;
}
export default (props: OrderCardProps) => {
    const { shopOrder, payInfo, payment } = props;
    return (
        <View className="order-card">
            <View className="order-title">订单信息</View>
            <Cell
                className="info-item"
                title="订单编号:"
                border={false}
                value={`${shopOrder.declaredId}`}
                renderRightIcon={
                    <Icon
                        classPrefix={"iconfont"}
                        style={{ fontSize: "30rpx" }}
                        name={" icon-file-copy-line"}
                        size={40}
                        color={"#999"}
                        onClick={() => {
                            Taro.setClipboardData({
                                data: shopOrder.declaredId,
                                success: function (res) {
                                    DDYToast.info("复制成功");
                                },
                            });
                        }}
                    />
                }
            />
            <Cell
                className="info-item"
                title="下单时间:"
                border={false}
                value={`${timestampToTime(shopOrder.createdAt, "YYYY-MM-DD HH:mm:ss")}`}
            />
            <Cell className="info-item" title="备注:" border={false} value={`${shopOrder.buyerNote || "无备注"}`} />
            <Cell className="info-item" title="支付人:" border={false} value={`${payInfo.payName}`} />
            <Cell className="info-item" title="身份证号:" border={false} value={`${payInfo.payIdCard}`} />
            {/* <Cell
                className="info-item"
                title="支付方式:"
                border={false}
                value={`${shopOrder ? (shopOrder.channel == 2 ? "微信支付" : "积分支付") : ""}`}
            /> */}
        </View>
    );
};
