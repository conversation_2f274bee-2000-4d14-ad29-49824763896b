import { Button, NavBar, Progress } from "@antmjs/vantui";
import "./index.less";
import { ScrollView, View } from "@tarojs/components";
import Taro, { pxTransform } from "@tarojs/taro";

import useDetail from "./hooks/useDetail";
import OrderMainCard from "./components/order-main-card";
import OrderGoodCard from "./components/order-good-card";
import OrderCard from "./components/order-card";
import OrderBottom from "./components/order-bottom";
import { DDYBack } from "@/utils/route";
import useLogistics from "./hooks/useLogistics";
import OrderLogisticsCard from "./components/order-logistics-card";
import { isAlipay } from "@/utils/common";
import OrderReturnCard from "./components/order-return-card";
import OrderReturnProgress from "./components/order-return-progress";

export default () => {
    const [detail, getDetail] = useDetail();
    const [logistics, getLogistics] = useLogistics(detail?.shopOrder?.status);
    const statusBarHeight = Taro?.getSystemInfoSync().statusBarHeight;
    return (
        <>
            <NavBar
                title="订单详情"
                leftText=""
                className="page-title"
                fixed
                leftArrow={!isAlipay()}
                border={false}
                onClickLeft={() => {
                    if (detail && detail.discount > 0) {
                        Taro.showModal({
                            // title: '提示',
                            cancelText: "残忍离开",
                            confirmText: "继续支付",
                            content: `已为您优惠了${detail.discount / 100}元，确定要放弃支付吗`,
                            success(res) {
                                if (res.confirm) {
                                } else if (res.cancel) {
                                    DDYBack();
                                }
                            },
                        });
                    } else {
                        DDYBack();
                    }
                }}
            />

            {/* <ScrollView className="page" scrollY> */}
            <View className="order-bg-top" style={{ height: `${(statusBarHeight || 20) + 184}px` }}></View>

            {!!detail && (
                <View className="order-content" style={{ paddingTop: `${(statusBarHeight || 20) + 70}px` }}>
                    {/** 推送异常信息 */}
                    {!!detail?.orderPushErrorMessage && (
                        <View className="error-message">{detail.orderPushErrorMessage}</View>
                    )}
                    {/** 订单状态 收货地址 */}
                    <OrderMainCard
                        shopOrder={detail?.shopOrder}
                        orderReceiverInfos={detail?.orderReceiverInfos}
                        payment={detail?.payment}
                        refund={detail?.refund}
                    />
                    {/* {
                        <View className="order-card">
                            <OrderReturnProgress orderItem={detail} />
                        </View>
                    } */}
                    {detail?.shopOrder.status === -8 && <OrderReturnCard sellerAddress={detail?.sellerAddress} />}
                    {/* <OrderReturnCard sellerAddress={detail?.sellerAddress} /> */}
                    {logistics.length > 0 && <OrderLogisticsCard logisticsDetail={logistics || []} />}
                    {/** 店铺 商品 价格信息 */}
                    <OrderGoodCard
                        skuOrders={detail?.skuOrders}
                        shopOrder={detail?.shopOrder}
                        totalTax={detail.totalTax}
                        discount={detail.discount}
                    />
                    {/** 支付信息 */}
                    {/* <OrderPaymentCard
                        payInfo={detail.payInfo}
                    /> */}
                    {/** 订单信息 */}
                    <OrderCard
                        shopOrder={detail.shopOrder}
                        payInfo={detail.payInfo}
                        payment={detail.payment}
                        detail={detail}
                    />

                    {/* <View style={{ height: pxTransform(120) }} />    */}
                </View>
            )}
            {/* </ScrollView> */}
            {/** 底部操作栏 */}
            {detail && (
                <OrderBottom
                    shopOrder={detail.shopOrder}
                    operations={detail.shopOrderOperations}
                    refund={detail.refund}
                    skuOrders={detail?.skuOrders || []}
                    showDetailBtn={true}
                    refundId={detail?.refundId}
                    onFresh={() => {
                        getDetail();
                        getLogistics();
                    }}
                />
            )}
        </>
    );
};
