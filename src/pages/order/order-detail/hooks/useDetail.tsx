import api from "@/utils/api";
import { useDidShow, useRouter } from "@tarojs/taro";
import { useEffect, useState } from "react";

export interface OrderDetailTYPE {
    sellerAddress: any;
    refundId: number | undefined;
    autoCancelTime: number;
    discount: number;
    invoices: invoicesObj[];
    orderReceiverInfos: orderReceiverInfoObj[];
    payInfo: payInfoObj;
    payment: paymentObj;
    refund: boolean;
    shipType: number;
    shopOrder: shopOrderObj;
    shopOrderOperations: shopOrderOperationObj[];
    skuOrders: skuOrderObj[];
    totalDiscountOfSkuOrders: number; // 0
    totalTax: number; // 910
    orderPushErrorMessage?: string;
}
export interface invoicesObj {
    createdAt: number;
    detail: {
        bankAccount: string;
        companyName: string;
        registerAddress: string;
        registerBank: string;
        registerPhone: string;
        taxRegisterNo: string;
    };
    id: number;
    isDefault: boolean;
    status: number;
    title: string;
    updatedAt: number;
    userId: number;
}
export interface orderReceiverInfoObj {
    createdAt: number;
    id: number;
    orderId: number;
    orderType: number;
    updatedAt: number;
    receiverInfo: {
        city: string;
        cityId: number;
        createdAt: number;
        detail: string;
        id: number;
        isDefault: true;
        mobile: string;
        paperNo: string;
        phone: string;
        province: string;
        provinceId: number;
        receiveUserName: string;
        region: string;
        regionId: number;
        street?: string;
        streetId?: number;
        status: number;
        updatedAt: number;
        userId: number;
    };
}

export interface payInfoObj {
    payIdCard: string; // "******************"
    payName: string; // "周青松"
    phone: string; // "***********"
}
export interface paymentObj {
    channel: "wechatpay-jsapi" | "Integral-pay" | 1 | 2; // "wechatpay-jsapi"
    createdAt: number;
    fee: number;
    id: number;
    originFee: number;
    outId: string;
    payAccountNo: string;
    payInfoMd5: string;
    pushStatus: number;
    stage: number;
    status: number;
    updatedAt: number;
}

export interface shopOrderObj {
    paymentWay: any;
    buyerId: number; //106493
    buyerName: string; // "微信用户423"
    buyerName_: string; // "5b6u5L+h55So5oi3NDIz"
    buyerNote: string; //
    channel: number;
    commissionRate: number;
    createdAt: number; //*************
    declaredId: string; // "DTCP202309261437540001086099"
    depotCustomName: string; // "HANGZHOU"
    distributionRate: number; // 0
    extra: {
        identityError: string;
        outShopId: string; //"2697"
    };
    extraId: number; // 249
    fee: number; // 11010
    feeId: number; // 365
    flag: number; // 0
    id: number; //1086099
    originFee: number; // 10910
    originShipFee: number; // 100
    outFrom: number; // "subStore"
    outFromType: number; // 2
    outShopId: string; // "2697"
    payType: number; // 1
    shipFee: number; // 100
    shopId: number; // 179
    shopName: string; // "1188服务商 - 曹新方测试门店"
    /**
     * 订单状态
     */
    status: number;
    type: number; //1
    updatedAt: number; // *************
}

export interface shopOrderOperationObj {
    text: string; // "sellerCancel"|"pay"|"buyerCancel"
    value: number; // -1,1,-2
    operator: string[]; // "buyer" | "seller" | "admin"
}

export interface skuOrderObj {
    afterDiscountFee: number; // 10000
    buyerId: number; //  106493
    buyerName: string; // "微信用户423"
    buyerName_: string; //"5b6u5L+h55So5oi3NDIz"
    categoryNameSnapshot: string; // "类目3"
    channel: number; // 2
    createdAt: number; //  *************
    depotId: number; // 1
    diffFee: number; //  0
    distributionRate: number; //  0
    extra: {
        activityEndTime: string; //"2024-11-11 11:11:11",
        activitySalesPrice: string; //"5566"
        activityStartTime: string; //"2023-11-11 11:11:11"
        orderQuantityLimit: string; //"985"
        skuOrderSplitLine: string; // "589"
        unitQuantity: string; //"1"
    };
    extraId: number; //  229
    fee: number; // 10910
    feeId: number; // 168
    flag: number; // 0
    hasRefund: false;
    id: number; //  1086255
    invoiced: false;
    isBonded: number; //  2
    isThirdPartyItem: number; //  0
    itemId: number; //  5981
    itemName: string; // "保税奶粉"
    itemSnapshotId: number; //  27324
    orderId: number; //  1086099
    originFee: number; // 10000
    outFrom: string; // "subStore"
    outFromType: number; //  2
    outShopId: string; // "2697"
    outerSkuId: string; // "test0005"
    payType: number; //  1
    pushStatus: number; //  0
    quantity: number; // 1
    shipmentType: number; // 1
    shopId: number; // 179
    shopName: string; // "1188服务商 - 曹新方测试门店"
    skuId: number; //  68637
    skuImage: string; // "http://dante-img.oss-cn-hangzhou.aliyuncs.com/test/3b2ebc3c45f06423a590b8a58a8845c3.jpg"
    skuImageId: number; //  9
    status: number; //  0
    tags: {};
    tax: number; // 910
    updatedAt: number; // *************
}

type UseDetailType = () => [OrderDetailTYPE | undefined, () => void];

const useDetail: UseDetailType = () => {
    const [detail, setDetail] = useState<OrderDetailTYPE>();
    const route = useRouter();
    const getDetail = () => {
        api.getOrderInfo({
            data: {
                id: route.params?.id,
            },
            filterCheck: true,
        })
            .then(res => {
                setDetail(res);
            })
            .catch(err => {});
    };
    useDidShow(() => {
        getDetail();
    });
    return [detail, getDetail];
};
export default useDetail;
