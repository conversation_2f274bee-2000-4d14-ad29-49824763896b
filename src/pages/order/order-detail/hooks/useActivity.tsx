import React, { useMemo } from "react";

const useActivity = orderActivityInfo => {
    // 拼团购和分享购配置
    return useMemo((): any => {
        let res: any = {
            text: "",
            showShare: false,
        };
        const { groupStatus, memberRole, activityNum, groupMemberNum, memberStatus } = orderActivityInfo;
        if (groupStatus == 4) {
            res = {
                text: "拼团失败~",
            };
        } else {
            switch (
                memberStatus // 团员状态 1 待支付 2 已支付  3 已退团  4 开团成功 5 已发货 6 已签收 10 待下单
            ) {
                case 1:
                    if (memberRole == 1) {
                        // 团角色：1团长 2团员
                        res = {
                            text: "支付完成即可开团~",
                        };
                    } else {
                        res = {
                            text: "支付完成即可参团~",
                        };
                    }
                    break;
                case 2:
                    if (activityNum - groupMemberNum > 0) {
                        res = {
                            text: `拼团中，还差${activityNum - groupMemberNum}个人`,
                            showShare: true,
                        };
                    }
                    break;
                case 3:
                    res = {
                        text: "拼团失败~",
                    };
                    break;
                case 4:
                case 5:
                case 6:
                    res = {
                        text: "拼团已成功~",
                    };
                    break;
            }
        }

        return res;
    }, [orderActivityInfo]);
};
export default useActivity;
