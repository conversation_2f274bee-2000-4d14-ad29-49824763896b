import api from "@/utils/api";
import { useRouter } from "@tarojs/taro";
import { useEffect, useState } from "react";

type LogisticsDetailTYPE = any[];

type UseLogisticsType = (status?: number) => [LogisticsDetailTYPE, () => void];

const useLogistics: UseLogisticsType = status => {
    const [logistics, setDetail] = useState<LogisticsDetailTYPE>([]);
    const route = useRouter();
    const getLogistics = () => {
        api.getLogisticsDetail({
            data: {
                orderId: route.params.id,
                orderType: 1,
            },
            filterCheck: true,
        })
            .then(res => {
                setDetail(res);
            })
            .catch(err => {});
    };
    useEffect(() => {
        if (status && status >= 2) {
            getLogistics();
        }
    }, [status]);
    return [logistics, getLogistics];
};
export default useLogistics;
