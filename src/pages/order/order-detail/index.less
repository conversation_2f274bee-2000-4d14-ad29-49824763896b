.order-bg-top {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 140rpx;
    border-bottom-left-radius: 30rpx;
    border-bottom-right-radius: 30rpx;
    background-color: #f50050;
    z-index: 1;
}
.page-title {
    color: #fff;
    z-index: 999;
    background-color: #f50050;
    .van-nav-bar__content {
        height: 88rpx;
    }
    .van-icon-arrow-left:before {
        content: "\e668";
        color: #fff;
    }
    .van-nav-bar__title {
        color: #fff;
    }
}

page {
    position: relative;
    height: 100vh;
    box-sizing: border-box;

    .order-content {
        z-index: 10;
        position: relative;
        padding-bottom: calc(110px + env(safe-area-inset-bottom));
        .error-message {
            margin: 20rpx 24rpx 20rpx 24rpx;
            padding: 12rpx 24rpx;
            background-color: #fffbe8;
            border-radius: 8rpx;
            color: #f3a367;
        }
        .order-main-card {
            margin: 20rpx 24rpx 20rpx 24rpx;
            padding: 0 32rpx;
            background-color: #fff;
            min-height: 100px;
            border-radius: 16rpx;
            .order-status {
                padding: 40rpx 0;
                border-bottom: 1rpx solid #f5f5f5;
                display: flex;
                flex-direction: row;
                align-items: center;
                justify-content: space-between;
                .order-status-text {
                    font-size: 36rpx;
                    font-family: PingFangSC-Medium, PingFang SC;
                    font-weight: 500;
                    color: #333333;
                    line-height: 50rpx;
                }
                .order-time-text {
                    font-size: 26rpx;
                    font-family: PingFangSC-Regular, PingFang SC;
                    font-weight: 400;
                    color: #999999;
                    line-height: 30rpx;
                }
            }
            .address-block {
                .receive-info {
                    padding-top: 20rpx;
                    padding-bottom: 12rpx;
                    .name {
                        margin-left: 24rpx;
                    }
                    .phone {
                        margin-left: 24rpx;
                    }
                }
                .address-detail {
                    font-size: 26rpx;
                    color: #999999;
                    font-weight: 400;
                    margin-left: 50rpx;
                    padding-bottom: 24rpx;
                }
            }
        }
        .order-logistics-card {
            margin: 20rpx 24rpx 20rpx 24rpx;
            padding: 24px;
            background-color: #fff;
            min-height: 200px;
            border-radius: 16rpx;
            .card-title {
                font-size: 32rpx;
                font-weight: 500;
                padding-bottom: 24rpx;
            }
            .logistic_title {
                display: flex;
                justify-content: flex-start;
                align-items: center;
                // margin: 15rpx 0;
                .logistic_title_list {
                    font-size: 24rpx;
                    // margin-right: 20rpx;
                    color: #3a3a3a;
                    border: 1px solid #cccccc;
                    padding: 5rpx;
                    border-radius: 5rpx;
                }
                .active {
                    color: #f51214;
                    border: 1px solid #f51214;
                }
            }
            .info_block {
                // margin-top: 20rpx;
                background: #fff;
                // padding: 0 36rpx 36rpx 36rpx;
                .item {
                    height: 58rpx;
                    line-height: 58rpx;
                    // border-bottom: 1rpx solid #cccccc;
                    display: flex;
                    justify-content: space-between;
                    font-size: 24rpx;
                    .title {
                        color: #333333;
                        font-size: 24rpx;
                        font-weight: 400;
                    }
                    .total_price {
                        color: #333333;
                    }
                    .cost {
                        color: #333333;
                    }
                    .tatal {
                        font-weight: 400;
                        color: #333333;
                        font-size: 24rpx;
                    }
                    .arrow {
                        width: 20rpx;
                        height: 20rpx;
                    }
                }
            }
        }
        .order-good-card {
            margin: 20rpx 24rpx 20rpx 24rpx;
            padding: 0 32rpx;
            background-color: #fff;
            min-height: 200px;
            border-radius: 16rpx;
            .info-block {
                .info-item {
                    padding: 20rpx 0;
                    line-height: 30rpx;
                }
            }
            .goods-total {
                display: flex;
                align-items: center;
                justify-content: flex-end;
                padding-bottom: 32rpx;
                .price-title {
                    text-align: right;
                    font-size: 24rpx;
                    font-weight: 400;
                    color: #333333;
                    line-height: 24rpx;
                }
                .price-value {
                    font-size: 32rpx;
                    font-family: PingFangSC-Medium, PingFang SC;
                    font-weight: 500;
                    color: #f5001d;
                    line-height: 32rpx;
                }
            }
        }
        .order-payment-card {
            margin: 20rpx 24rpx 20rpx 24rpx;
            padding: 32rpx 32rpx;
            background-color: #fff;
            min-height: 200px;
            border-radius: 16rpx;
            .payment-title {
                font-size: 32rpx;
                font-family: PingFangSC-Medium, PingFang SC;
                font-weight: 500;
                color: #333333;
                line-height: 32rpx;
                padding-bottom: 20rpx;
            }
            .info-item {
                padding: 20rpx 0;
                line-height: 30rpx;
            }
        }
        .order-card {
            margin: 20rpx 24rpx 20rpx 24rpx;
            padding: 32rpx 32rpx;
            background-color: #fff;
            min-height: 200px;
            border-radius: 16rpx;
            .order-title {
                font-size: 32rpx;
                font-family: PingFangSC-Medium, PingFang SC;
                font-weight: 500;
                color: #333333;
                line-height: 32rpx;
                padding-bottom: 20rpx;
            }
            .info-item {
                padding: 20rpx 0;
                line-height: 30rpx;
            }
            .order-address-block {
                border-radius: 8px;
                background-color: #f3f3f3;
                display: flex;
                align-items: center;
                // justify-content: ;
                flex-direction: row;
                padding: 24px 10px;
                margin-top: 10px;
                .van-icon {
                    // padding: 10px;
                    margin-right: 5px;
                }
                .order-address-content {
                    flex: 1;
                }
                .order-address-btn {
                    width: 100px;
                    text-align: center;
                    color: rgb(90, 90, 228);
                }
            }
        }
        .van-cell__value {
            flex: 4;
        }
    }
}
.order-bottom {
    position: fixed;
    padding-bottom: calc(5px + env(safe-area-inset-bottom));
    left: 0;
    right: 0;
    bottom: 0;
    height: 104rpx;
    z-index: 20;
    background: #ffffff;
    .btn-container {
        padding: 20rpx 0;
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: flex-end;
    }
    .btn {
        height: 64rpx;
        background: #ffffff;
        border-radius: 32rpx;
        border: 2rpx solid #cccccc;
        margin: 0 24px 0 0;
        font-size: 28rpx;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #333333;
        line-height: 36rpx;
        padding: 14rpx 32rpx;
        box-sizing: border-box;
        border-radius: 32rpx;
    }
    .pay {
        color: #f5001d;
        border-color: #f5001d;
    }
}
