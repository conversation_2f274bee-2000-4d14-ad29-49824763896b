import api from "@/utils/api";
import { IDENTITY, IDENTITYINFO, STORE_ID, USER_INFO } from "@/utils/constant";
import { getStorage } from "@/utils/storage";
import { Empty, InfiniteScroll, PullToRefresh, VirtualList } from "@antmjs/vantui";
import { View, Image, Text, ScrollView } from "@tarojs/components";
import { memo, useEffect, useRef, useState, useImperativeHandle, forwardRef } from "react";
import RefundStatus from "./refund-status";
import { DDYObject } from "src/type/common";
import OrderItem, { OrderItemProps } from "@/components/order-item";
import { timestampToTime } from "@/utils/DateTimeUtils";
import { DDYNavigateTo } from "@/utils/route";
import { mall_event, mall_event_key, reportEvent } from "@/utils/track-report";
// 根据tabindex 映射 对应的orderType
const ORDER_STATUS = {
    0: "",
    1: "0",
    2: 1,
    3: 2,
    4: -30,
    5: 3,
};
const REFUND_STATUS = {
    0: "REFUND_IN_PROGRESS",
    1: "REFUND_SUCCESS",
    2: "REFUND_FAIL",
};
export type OrderListProps = React.ForwardRefExoticComponent<
    React.RefAttributes<OrderListRef> & { current: number; updateTabsData: () => void }
>;

export interface OrderListRef {
    changTab: (index: number) => void;
}

const OrderList: OrderListProps = forwardRef((props, ref) => {
    const { current, updateTabsData } = props;
    const [data, setData] = useState<any[]>([]);
    const refundIndex = useRef<number>(0);
    const infiniteScrollRef = useRef<any>();
    const identity = getStorage(IDENTITY);
    const pageSize = 20;
    const pageNo = useRef(1);
    const getOrderDatas = () => {
        let userInfo = getStorage(USER_INFO) || {};
        let orderStatus = ORDER_STATUS[current];

        let data: DDYObject = {
            buyerId: userInfo.userId,
            statusStr: orderStatus,
            pageNo: pageNo.current || 1,
            size: pageSize,
            refererId: "",
            startAt: "",
            endAt: "",
            outFrom: "subStore",
            shopId: getStorage("storeId"),
        };

        if (identity == "seller" || identity == "subStore") {
            data = {
                statusStr: orderStatus,
                pageNo: pageNo.current || 1,
                size: pageSize,
                outShopId: getStorage("subStoreId"),
                outFrom: "subStore",
                startAt: "",
                endAt: "",
                shopId: getStorage("storeId"),
            };
        }
        if (identity == "guider") {
            data = {
                statusStr: orderStatus,
                pageNo: pageNo.current || 1,
                size: pageSize,
                refererId: getStorage("guiderId"),
                outFrom: "subStore",
                startAt: "",
                endAt: "",
                shopId: getStorage(STORE_ID),
            };
        }
        if (identity == "serviceProvider") {
            data = {
                statusStr: orderStatus,
                pageNo: pageNo.current || 1,
                size: pageSize,
                startAt: "",
                endAt: "",
                shopId: getStorage(STORE_ID),
            };
        }
        console.log("pageNo.current :", pageNo.current);
        return api.getOrderList({
            data: data,
            showError: false,
            showLoad: false,
            filterCheck: true,
        });
    };

    const getRefundOrder = () => {
        const data = {
            status: REFUND_STATUS[refundIndex.current],
            pageNo: pageNo.current,
            pageSize: pageSize,
            shopId: getStorage(STORE_ID),
        };
        return api.getRefundOrderList({
            data,
            showError: false,
            showLoad: false,
            filterCheck: true,
        });
    };

    const onRefresh = () => {
        console.log("onRefresh:");
        pageNo.current = 1;
        let map = new Map();
        map.set(mall_event_key.MALL_KEY_ORDER_PAGING_PAGE, pageNo.current);
        map.set(mall_event_key.MALL_KEY_ORDER_PAGING_STATUSSTR, ORDER_STATUS[current]);
        map.set(mall_event_key.MALL_KEY_ORDER_PAGING_REFUND_STATUS, REFUND_STATUS[refundIndex.current]);
        reportEvent(mall_event.MALL_EVENT_ORDER_PAGING_START, map);
        return new Promise(async (resolve: (value) => void) => {
            infiniteScrollRef.current?.reset();
            updateTabsData && updateTabsData();
            try {
                // const result = current === 5 ? await getRefundOrder() : await getOrderDatas();
                const result = await getOrderDatas();
                pageNo.current++;
                let newData: any[] = [];
                console.log(newData.length >= result.total);
                setData(result.data);
                infiniteScrollRef.current?.reset(true);
                // resolve(result.data.length >= result.total ? "complete" : "loading");
                let map = new Map();
                map.set(mall_event_key.MALL_KEY_ORDER_PAGING_PAGE, pageNo.current);
                map.set(mall_event_key.MALL_KEY_ORDER_PAGING_STATUSSTR, ORDER_STATUS[current]);
                map.set(mall_event_key.MALL_KEY_ORDER_PAGING_REFUND_STATUS, REFUND_STATUS[refundIndex.current]);
                map.set(mall_event_key.MALL_KEY_ORDER_PAGING_COUNT, newData.length);
                map.set(mall_event_key.MALL_KEY_ORDER_PAGING_TOTAL, result.total);
                map.set(mall_event_key.MALL_KEY_ORDER_PAGING_RESULT, "成功");
                reportEvent(mall_event.MALL_EVENT_ORDER_PAGING_END, map);
                resolve(undefined);
            } catch (error) {
                infiniteScrollRef.current?.reset(true);
                let map = new Map();
                map.set(mall_event_key.MALL_KEY_ORDER_PAGING_PAGE, pageNo.current);
                map.set(mall_event_key.MALL_KEY_ORDER_PAGING_STATUSSTR, ORDER_STATUS[current]);
                map.set(mall_event_key.MALL_KEY_ORDER_PAGING_REFUND_STATUS, REFUND_STATUS[refundIndex.current]);
                map.set(mall_event_key.MALL_KEY_ORDER_PAGING_ERROR, error.toString());
                map.set(mall_event_key.MALL_KEY_ORDER_PAGING_RESULT, "错误");
                console.log("error:", error);
            }

            resolve(undefined);
        });
    };
    const loadMore = () => {
        console.log("loadMore:");
        let map = new Map();
        map.set(mall_event_key.MALL_KEY_ORDER_PAGING_PAGE, pageNo.current);
        map.set(mall_event_key.MALL_KEY_ORDER_PAGING_STATUSSTR, ORDER_STATUS[current]);
        map.set(mall_event_key.MALL_KEY_ORDER_PAGING_REFUND_STATUS, REFUND_STATUS[refundIndex.current]);
        reportEvent(mall_event.MALL_EVENT_ORDER_PAGING_START, map);
        return new Promise(async (resolve: (value: "loading" | "error" | "complete") => void) => {
            try {
                // const result = current === 5 ? await getRefundOrder() : await getOrderDatas();
                const result = await getOrderDatas();
                console.log("result:", result, data, pageNo.current);
                let newData: any[] = [];
                if (pageNo.current === 1) {
                    updateTabsData && updateTabsData();
                    newData = result.data;
                } else {
                    newData = data.concat(result.data);
                }
                let map = new Map();
                map.set(mall_event_key.MALL_KEY_ORDER_PAGING_PAGE, pageNo.current);
                map.set(mall_event_key.MALL_KEY_ORDER_PAGING_STATUSSTR, ORDER_STATUS[current]);
                map.set(mall_event_key.MALL_KEY_ORDER_PAGING_REFUND_STATUS, REFUND_STATUS[refundIndex.current]);
                map.set(mall_event_key.MALL_KEY_ORDER_PAGING_COUNT, newData.length);
                map.set(mall_event_key.MALL_KEY_ORDER_PAGING_TOTAL, result.total);
                map.set(mall_event_key.MALL_KEY_ORDER_PAGING_RESULT, "成功");
                reportEvent(mall_event.MALL_EVENT_ORDER_PAGING_END, map);
                pageNo.current++;
                setData(newData);
                //  endStatus.current = newData.length >= result.total;
                resolve(newData.length >= result.total ? "complete" : "loading");
            } catch (error) {
                console.log("error:", error);
                let map = new Map();
                map.set(mall_event_key.MALL_KEY_ORDER_PAGING_PAGE, pageNo.current);
                map.set(mall_event_key.MALL_KEY_ORDER_PAGING_STATUSSTR, ORDER_STATUS[current]);
                map.set(mall_event_key.MALL_KEY_ORDER_PAGING_REFUND_STATUS, REFUND_STATUS[refundIndex.current]);
                map.set(mall_event_key.MALL_KEY_ORDER_PAGING_ERROR, error.toString());
                map.set(mall_event_key.MALL_KEY_ORDER_PAGING_RESULT, "错误");
                reportEvent(mall_event.MALL_EVENT_ORDER_PAGING_END, map);
                resolve("error");
            }
        });
    };

    useImperativeHandle(ref, () => {
        return {
            changTab: index => {
                // if()
                setData([]);
                setTimeout(() => {
                    pageNo.current = 1;
                    infiniteScrollRef.current?.reset(true);
                }, 10);
            },
        };
    });

    return (
        <>
            {/* {current === 5 ? (
                <RefundStatus
                    onChange={(index: number) => {
                        refundIndex.current = index;
                        pageNo.current = 1;
                        infiniteScrollRef.current?.reset(true);
                    }}
                />
            ) : null} */}
            <PullToRefresh onRefresh={onRefresh}>
                <View className="order-list">
                    {data.map(orderItem => {
                        let orderItemProps = {};
                        // if (current === 5) {
                        //     const goodList =
                        //         orderItem.itemList?.map(skuItem => {
                        //             return {
                        //                 quantity: skuItem.itemNum,
                        //                 flagList: [],
                        //                 price: null,
                        //                 goodName: skuItem.itemName,
                        //                 goodImage: skuItem.itemImageUrl,
                        //             };
                        //         }) || [];
                        //     orderItemProps = {
                        //         mode: "seller",
                        //         orderStatus: orderItem.refundStatus,
                        //         orderNo: orderItem.declaredId,
                        //         goodList: goodList,
                        //         orderPushErrorMessage: orderItem.orderPushErrorMessage,
                        //         sellerEtr: {
                        //             actualPrice: orderItem.fee / 100,
                        //             commission: 0,
                        //         },
                        //         createdAt: timestampToTime(orderItem.orderCreateAt, "YYYY-MM-DD HH:mm:ss"),
                        //         shopName: orderItem.subStoreName,
                        //         isFirst: false,
                        //         clickGo: () => {
                        //             DDYNavigateTo({
                        //                 url: `/pages/order/refund-detail/index?refundId=${orderItem.refundId}`,
                        //             });
                        //         },
                        //     };
                        // } else {
                        const goodList =
                            orderItem.skuOrderAndOperations?.map(skuItem => {
                                return {
                                    quantity: skuItem.skuOrder.quantity,
                                    flagList: skuItem.flagList,
                                    price: skuItem.skuOrder.originFee / skuItem.skuOrder.quantity / 100,
                                    goodName: skuItem.skuOrder.itemName,
                                    goodImage: skuItem.skuOrder.skuImage,
                                };
                            }) || [];

                        const show = identity === "buyer" || identity == "guider" || identity == "subStore";

                        orderItemProps = {
                            mode: "seller",
                            orderStatus: orderItem.shopOrder.status,
                            orderNo: orderItem.shopOrder.declaredId,
                            orderPushErrorMessage: orderItem.orderPushErrorMessage,
                            goodList: goodList,
                            ishide: show,
                            sellerEtr: {
                                actualPrice: orderItem.shopOrder.fee / 100 || orderItem.fee / 100,
                                commission:
                                    orderItem.shopOrder.status > 0
                                        ? identity == "subStore"
                                            ? orderItem.subStoreProfit / 100 || 0
                                            : identity == "serviceProvider"
                                            ? orderItem.serviceProviderProfit / 100
                                            : orderItem.guiderProfit / 100
                                        : 0,
                            },
                            showBottom: show,
                            shopOrder: orderItem.shopOrder,
                            operations: orderItem.shopOrderOperations,
                            refund: orderItem.refund,
                            onFresh: () => {
                                pageNo.current = 1;
                                infiniteScrollRef.current?.reset(true);
                            },
                            createdAt: timestampToTime(orderItem.shopOrder.createdAt, "YYYY-MM-DD HH:mm:ss"),
                            shopName: orderItem.subStoreName,
                            isFirst: false,
                            clickGo: () => {
                                DDYNavigateTo({
                                    url: `/pages/order/order-detail/index?id=${orderItem.shopOrder.id}&packageSn=${orderItem.packageSn}&status=${orderItem.shopOrder.status}`,
                                });
                            },
                        };
                        // }

                        return <OrderItem {...(orderItemProps as OrderItemProps)} />;
                    })}
                </View>
                <InfiniteScroll
                    completeText={
                        <>
                            {data.length == 0 ? (
                                <Empty
                                    description="暂无数据！"
                                    image="https://dante-img.oss-cn-hangzhou.aliyuncs.com/55439458101.png"
                                />
                            ) : (
                                "没有更多了"
                            )}
                        </>
                    }
                    loadMore={loadMore}
                    ref={infiniteScrollRef}
                />
            </PullToRefresh>
        </>
    );
});

export default OrderList;
