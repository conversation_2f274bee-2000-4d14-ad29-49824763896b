import { View } from "@tarojs/components";
import { useState } from "react";

export default ({ onChange }) => {
    const [cur, setCur] = useState(0);
    const lists = ["退款中", "已退款", "退款失败"];
    return (
        <View className="refund-select">
            {lists.map((item, index) => (
                <View
                    className={cur === index ? "status-item selected" : "status-item"}
                    onClick={() => {
                        setCur(index);
                        onChange(index);
                    }}
                >
                    {item}
                </View>
            ))}
        </View>
    );
};
