import { View } from "@tarojs/components";
import { useRef, useState } from "react";
import "./index.less";
import { useRouter } from "@tarojs/taro";
import NewTabs from "../../../components/new-tabs";
import OrderList, { OrderListRef } from "./compoents/order-list";
import useTabList from "../order/hooks/useTabList";
definePageConfig({
    navigationBarTitleText: "我的订单",
});
export default () => {
    const route = useRouter();
    const orderListRef = useRef<OrderListRef>({} as OrderListRef);
    // const [tabList, setTabList] = useState([
    //     { title: "全部", dotNum: 0 },
    //     { title: "待付款", dotNum: 0 },
    //     { title: "待发货", dotNum: 0 },
    //     { title: "待收货", dotNum: 0 },
    //     { title: "已完成", dotNum: 0 },
    //     { title: "无效", dotNum: 0 },
    // ]);
    const [tabList, getTabsData] = useTabList();
    const [current, setCurrent] = useState(Number(route.params.tab));

    return (
        <View className="order">
            <NewTabs
                tabList={tabList}
                onchange={e => {
                    setCurrent(e.index);
                    orderListRef.current.changTab(e.index);
                }}
                current={current ? Number(current) : 0}
            />
            <OrderList
                current={current as number}
                ref={orderListRef}
                //@ts-ignore
                updateTabsData={getTabsData}
            />
        </View>
    );
};
