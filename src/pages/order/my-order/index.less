.order {
    padding-top: 100px;
    .isFirst {
        width: 52rpx;
        height: 28rpx;
        background: linear-gradient(90deg, #f50050 0%, #f5001d 100%);
        border-radius: 4rpx;
        font-size: 20rpx;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #ffffff;
        line-height: 28rpx;
        text-align: center;
    }

    .order-list {
        // padding-top: 24rpx;
        //    margin-top: 100px;
    }
    .van-tabs__wrap--scrollable .van-tab:nth-child(5) {
        width: 200px !important;
        flex-basis: unset !important;
    }
    .refund-select {
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: space-around;
        padding: 30rpx 0 0 0;
        .status-item {
            width: 152px;
            height: 50px;
            background: #d8d8d8;
            border-radius: 30px;
            font-size: 28px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #333333;
            line-height: 40px;
            text-align: center;
            padding-top: 5rpx;
            box-sizing: border-box;
        }
        .selected {
            color: #f5001d;
            border: 1px solid #f5001d;
            background-color: rgba(245, 0, 29, 0.15);
        }
    }
    .van-tabs__line {
        display: none;
    }
}
