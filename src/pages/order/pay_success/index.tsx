import { View, Text, Button } from "@tarojs/components";
import Taro, { useRouter, useShareAppMessage } from "@tarojs/taro";
import { useEffect, useState } from "react";

import "./index.less";
import { DDYNavigateTo, DDYSwitchTab, DDYBack, DDYReLaunch } from "@/utils/route";
import { setStorage } from "@/utils/storage";
import { Icon, NavBar } from "@antmjs/vantui";
import api from "@/utils/api";
import newAPi from "@/utils/newApi";
import infoStore from "@/store/info-store";
import { isAlipay } from "@/utils/common";

definePageConfig({
    navigationBarTitleText: "支付结果",
    navigationBarBackgroundColor: "#fff",
    navigationStyle: "custom",
});

export default () => {
    const route = useRouter();
    const success = route.params.success || "0";
    const orderNo = route.params.orderNo || "";
    const showPrice = route.params.orderNo && route.params.orderNo?.indexOf(",") > -1 ? false : true;
    const [payData, setPayData] = useState<any>({});

    const goOrderDetail = () => {
        // 如果有拆单，orderNo会有多个，此时就跳到我tab页，否则跳订单详情
        // const orderNo = route.params.orderNo
        if (orderNo?.indexOf(",") > -1) {
            DDYSwitchTab({ url: "/pages/info" });
        } else {
            setStorage("paySuccessViewOrder", "1");
            DDYNavigateTo({
                url:
                    "/pages/order/order-detail/index?from=paySucees&id=" +
                    orderNo +
                    "&status=" +
                    (payData?.shopOrder?.status || ""),
            });
        }
    };

    const goBack = () => {
        let pages = Taro.getCurrentPages();
        if (pages.length > 1) {
            let last = pages[pages.length - 2];
            if (last.route == "pages/activity/group-buy/index") {
                const inviteCode = infoStore.newUserProfile.inviteCode;
                const { activityId, groupId, activityPics, marketingToolId } = payData;
                DDYReLaunch({
                    url: `/pages/activity/group-buy/index?id=${activityId}&groupId=${groupId}&inviteCode=${inviteCode}&toolsId=${marketingToolId}`,
                });
            } else {
                DDYBack();
            }
        } else {
            DDYSwitchTab({ url: "/pages/index/index" });
        }
    };

    const goIndex = () => {
        DDYSwitchTab({
            url: "/pages/home/<USER>",
        });
    };

    const getPayOrderDetail = () => {
        newAPi
            .findActivityByOrderId({
                data: {
                    orderId: orderNo.indexOf(",") > -1 ? orderNo.substring(0, orderNo.indexOf(",")) : orderNo,
                },
                method: "GET",
                // filterCheck: true,
            })
            .then(res => {
                setPayData(res);
            });
    };

    useEffect(() => {
        getPayOrderDetail();
    }, []);

    // 分享配置
    useShareAppMessage(() => {
        const inviteCode = infoStore.newUserProfile.inviteCode;
        const { activityId, groupId, activityPics, marketingToolId } = payData;
        console.log("payData", payData);
        return {
            title: payData.activityName,
            path: `/pages/activity/group-buy/index?id=${activityId}&groupId=${groupId}&inviteCode=${inviteCode}&toolsId=${marketingToolId}`,
            imageUrl: (activityPics || [])[0] || "",
        };
    });
    return (
        <View className="pay_success">
            {/* 导航栏 */}
            <NavBar
                fixed
                className="nav-bar"
                border={false}
                title={"支付结果"}
                leftArrow={!isAlipay()}
                placeholder
                leftText=""
                onClickLeft={goBack}
            />
            <View className="icon">
                <View className="icon_status">
                    {success == "0" ? (
                        <Icon name={"checked"} size={100} color={"green"} />
                    ) : (
                        <Icon name={"service-o"} size={100} color={"red"} />
                    )}
                </View>
                {success == "0" ? (
                    <View className="success_doc ">支付成功</View>
                ) : (
                    <View className="success_doc ">支付失败</View>
                )}
            </View>
            {showPrice ? (
                <View className="pay_money">
                    <View className="block">
                        {/* {payData?.shopOrder?.type && payData?.shopOrder?.type == "1" ? ( */}
                        <View className="type fz30">
                            <Text className="price-label">￥</Text>
                            <Text className="price-value">{payData?.fee ? payData?.fee / 100 : ""}</Text>
                        </View>
                        {/* ) : null} */}
                        {/* {payData?.shopOrder?.type && payData?.shopOrder?.type != "1" ? (
                            <View className="type fz30">
                                总积分：
                                <Text className="price-value">
                                    {payData.shopOrder.fee ? payData.shopOrder.fee / 100 : ""}
                                </Text>
                            </View>
                        ) : null} */}
                    </View>
                </View>
            ) : null}
            <View className="pay_btn">
                <View className="block btn_group">
                    <View className="btn order_detail order_see" onClick={goOrderDetail}>
                        查看订单
                    </View>
                    {payData?.activityTag ? (
                        <Button
                            className="btn order_detail"
                            openType="share"
                            style={{ padding: 0, backgroundColor: "#fff" }}
                        >
                            <View style={{ width: "100%" }}>
                                <Icon name="share-o" size="18px" />
                                <Text style={{ marginLeft: "5px" }}>组团分享</Text>
                            </View>
                        </Button>
                    ) : (
                        <View className="btn order_detail" onClick={goIndex}>
                            继续购物
                        </View>
                    )}
                </View>
            </View>
        </View>
    );
};
