import { View, Text } from "@tarojs/components";
import { useRouter } from "@tarojs/taro";
import { useEffect, useState } from "react";

import "./index.less";
import { DDYNavigateTo, DDYSwitchTab } from "@/utils/route";
import { setStorage } from "@/utils/storage";
import { Icon } from "@antmjs/vantui";
import api from "@/utils/api";

definePageConfig({
    navigationBarTitleText: "支付结果",
    navigationBarBackgroundColor: "#fff",
});

export default () => {
    const route = useRouter();
    const success = route.params.success || "0";
    const orderNo = route.params.orderNo || "";
    const showPrice = route.params.orderNo && route.params.orderNo?.indexOf(",") > -1 ? false : true;
    const [payData, setPayData] = useState<any>({});

    const goOrderDetail = () => {
        // 如果有拆单，orderNo会有多个，此时就跳到我tab页，否则跳订单详情
        // const orderNo = route.params.orderNo
        if (orderNo?.indexOf(",") > -1) {
            DDYSwitchTab({ url: "/pages/info" });
        } else {
            setStorage("paySuccessViewOrder", "1");
            DDYNavigateTo({
                url:
                    "/pages/order/order-detail/index?from=paySucees&id=" +
                    orderNo +
                    "&status=" +
                    (payData?.shopOrder?.status || ""),
            });
        }
    };

    const goIndex = () => {
        DDYSwitchTab({
            url: "/pages/home/<USER>",
        });
    };

    const getPayOrderDetail = () => {
        api.getPayOrderDetail({
            data: {
                orderNo: orderNo.indexOf(",") > -1 ? orderNo.substring(0, orderNo.indexOf(",")) : orderNo,
            },
            filterCheck: true,
        }).then(res => {
            setPayData(res);
        });
    };

    useEffect(() => {
        getPayOrderDetail();
    }, []);

    return (
        <View className="pay_success">
            <View className="icon">
                <View className="icon_status">
                    {success == "0" ? (
                        <Icon name={"checked"} size={100} color={"green"} />
                    ) : (
                        <Icon name={"service-o"} size={100} color={"red"} />
                    )}
                </View>
                {success == "0" ? (
                    <View className="success_doc ">支付成功</View>
                ) : (
                    <View className="success_doc ">支付失败</View>
                )}
            </View>
            {showPrice ? (
                <View className="pay_money">
                    <View className="block">
                        {payData?.shopOrder?.type && payData?.shopOrder?.type == "1" ? (
                            <View className="type fz30">
                                <Text className="price-label">￥</Text>
                                <Text className="price-value">
                                    {payData?.shopOrder?.fee ? payData?.shopOrder?.fee / 100 : ""}
                                </Text>
                            </View>
                        ) : null}
                        {payData?.shopOrder?.type && payData?.shopOrder?.type != "1" ? (
                            <View className="type fz30">
                                总积分：
                                <Text className="price-value">
                                    {payData.shopOrder.fee ? payData.shopOrder.fee / 100 : ""}
                                </Text>
                            </View>
                        ) : null}
                    </View>
                </View>
            ) : null}
            <View className="pay_btn">
                <View className="block btn_group">
                    <View className="btn order_detail order_see" onClick={goOrderDetail}>
                        查看订单
                    </View>
                    <View className="btn order_detail" onClick={goIndex}>
                        继续购物
                    </View>
                </View>
            </View>
        </View>
    );
};
