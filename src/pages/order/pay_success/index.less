.pay_success {
    background: #fff;
    // height: 100vh;
    .pay_btn {
        display: flex;
        width: 100%;
    }
    .icon {
        margin: 0 auto;
        text-align: center;
        padding-bottom: 100rpx;
        //   padding-bottom: 200rpx;
        .icon_status {
            padding-top: 40rpx;
            i {
                color: #f5001d;
                font-size: 96rpx;
            }
        }
    }

    .success_icon {
        width: 96rpx;
        height: 96rpx;
        margin-top: 40rpx;
    }

    .success_doc {
        font-family: PingFangSC-Medium;
        font-size: 36rpx;
        color: #333;
        font-weight: 500;
        margin-top: 32rpx;
    }
    .type {
        margin-top: 40rpx;
        margin-bottom: 60rpx;
        text-align: center;
        font-family: PingFangSC-Regular;
        font-size: 26rpx;
        color: #3a3a3a;
        .price-label {
            color: #333;
        }
        .price-value {
            color: #333;
            font-size: 48rpx;
        }
    }
    .pay_type {
        padding-top: 24rpx;
        padding-bottom: 24rpx;
    }

    .btn_group {
        display: flex;
        flex: 1;
        margin-bottom: 80rpx;
        padding: 0 100rpx;
        justify-content: space-around;

        .btn {
            width: 220rpx;
            height: 68rpx;
            text-align: center;
            font-size: 28rpx;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #666666;
            line-height: 68rpx;
            background: #ffffff;
            border-radius: 34px;
            border: 1px solid #cccccc;
        }
        .order_detail {
        }
    }
}
