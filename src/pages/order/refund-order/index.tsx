import { View } from "@tarojs/components";
import { useState } from "react";
import NewTabs from "../../../components/new-tabs";
import OrderList from "./componens/order-list";
import "./index.less";

definePageConfig({
    navigationBarTitleText: "我的订单",
});
export default () => {
    const tabList = [
        { title: "已退款", status: "REFUND_SUCCESS" },
        { title: "退款中", status: "REFUND_IN_PROGRESS" },
        { title: "退款失败", status: "REFUND_FAIL" },
    ];
    const [current, setCurrent] = useState(0);

    return (
        <View className="refund-order">
            <NewTabs
                tabList={tabList}
                onchange={e => {
                    setCurrent(e.index);
                }}
                current={current ? Number(current) : 0}
            />
            <OrderList refundStatus={tabList[current].status} />
        </View>
    );
};
