import api from "@/utils/api";
import { STORE_ID } from "@/utils/constant";
import { getStorage } from "@/utils/storage";
import { Empty, Image, InfiniteScroll, PullToRefresh } from "@antmjs/vantui";
import { Text, View } from "@tarojs/components";
import { useEffect, useRef, useState } from "react";
import { DDYNavigateTo } from "@/utils/route";

export default ({ refundStatus }) => {
    const [data, setData] = useState<any[]>([]);
    const infiniteScrollRef = useRef<any>();
    const pageSize = 20;
    const pageNo = useRef(0);

    const getOrderDatas = () => {
        return api.getRefundList({
            data: {
                refundStatus: refundStatus,
                pageNum: pageNo.current || 1, //当前页码
                pageSize: pageSize, //分页大小
                shopId: getStorage(STORE_ID),
            },
            showError: false,
            showLoad: false,
            filterCheck: true,
        });
    };

    const onRefresh = () => {
        pageNo.current = 1;
        return new Promise(async (resolve: (value: undefined) => void) => {
            infiniteScrollRef.current?.reset();
            try {
                const result = await getOrderDatas();
                pageNo.current++;
                setData(result.data);
            } catch (error) {
                console.log("error:", error);
            }
            resolve(undefined);
        });
    };
    const loadMore = () => {
        return new Promise(async (resolve: (value: "loading" | "error" | "complete") => void) => {
            try {
                const result = await getOrderDatas();
                let newData: any[] = [];
                if (pageNo.current === 1) {
                    newData = result.data;
                } else {
                    newData = data.concat(result.data);
                }
                pageNo.current++;
                setData(newData);
                //  endStatus.current = newData.length >= result.total;
                resolve(newData.length >= result.total ? "complete" : "loading");
            } catch (error) {
                resolve("error");
            }
        });
    };

    useEffect(() => {
        pageNo.current = 1;
        infiniteScrollRef.current?.reset(true);
    }, [refundStatus]);

    return (
        <PullToRefresh onRefresh={onRefresh}>
            <View className="refund-order-list">
                {data.map(orderItem => {
                    return (
                        <View
                            className={"refund-order-list-item"}
                            key={orderItem.refundId}
                            onClick={() => {
                                DDYNavigateTo({
                                    url: `/pages/order/refund-detail/index?refundId=${orderItem.id}&status=${orderItem.status}&refundId=${orderItem.refundId}`,
                                });
                            }}
                        >
                            <Text className={"refundId"} maxLines={1}>
                                退款单号:{orderItem.refundId}
                            </Text>
                            <View className={"refund-order-list-item-goods-item"}>
                                {orderItem?.itemList.map(item => {
                                    return (
                                        <View className={"goods-cont"}>
                                            <Image src={item.skuImage} width={160} height={160} />
                                            <View className={"md-black-text name"}>{item.itemName}</View>
                                            <View style="text-align:right;padding-top: 6rpx;">
                                                <View style="font-size: 32rpx;color: #333333;">
                                                    ￥{item.originUnitFee}
                                                </View>
                                                <View style="font-size: 26rpx;color: #8D8D8D;">x{item.quantity}</View>
                                            </View>
                                        </View>
                                    );
                                })}
                            </View>
                            <View className="bottom-desc">
                                <Text className={"num"}>共{orderItem.totalItemQuantity}件商品</Text>
                                <Text className={"desc"}>退款金额:</Text>
                                <View className={"symbol"}>￥</View>
                                <Text className={"amount"}>{orderItem.fee}</Text>
                            </View>
                        </View>
                    );
                })}
            </View>
            <InfiniteScroll
                completeText={
                    <>
                        {data.length == 0 ? (
                            <Empty
                                description="暂无数据！"
                                image="https://dante-img.oss-cn-hangzhou.aliyuncs.com/55439458101.png"
                            />
                        ) : (
                            "没有更多了"
                        )}
                    </>
                }
                loadMore={loadMore}
                ref={infiniteScrollRef}
            />
        </PullToRefresh>
    );
};
