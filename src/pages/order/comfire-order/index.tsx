import { ScrollView } from "@tarojs/components";
import "./index.less";
import ShowAddress from "./components/show-address";
import ShopGoodList from "./components/shop-good-list";
import BottomRect from "./components/bottom-rect";
import { useDidHide, useDidShow, useRouter } from "@tarojs/taro";
import comfireStore from "./store";
import { observer } from "mobx-react-lite";
import { useEffect } from "react";
import { getStorage, removeStorage } from "@/utils/storage";
import { IS_SIGN_SUCCESS, SIGN_SCCESS_URL } from "@/utils/constant";
import { DDYNavigateTo } from "@/utils/route";
import DDYToast from "@/utils/toast";
export default observer(() => {
    const route = useRouter();
    useDidShow(() => {
        comfireStore.initData(route);
        if (getStorage(IS_SIGN_SUCCESS)) {
            DDYToast.info("签约成功，即将前往订单详情页", 1000);
            setTimeout(() => {
                DDYNavigateTo({
                    url: getStorage(SIGN_SCCESS_URL),
                });
                removeStorage(SIGN_SCCESS_URL);
                removeStorage(IS_SIGN_SUCCESS);
            }, 1000);
        }
    });

    useEffect(() => {
        return () => {
            comfireStore.resetEmpty();
        };
    }, []);

    return (
        <ScrollView
            style={{ width: "100vw", height: "100vh", backgroundColor: "#F3F3F3", paddingBottom: "100rpx" }}
            scrollY
        >
            <ShowAddress />
            <ShopGoodList />
            <BottomRect />
        </ScrollView>
    );
});
