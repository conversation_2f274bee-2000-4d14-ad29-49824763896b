import { observable, toJS } from "mobx";
import api from "@/utils/api";
import { ADDRESS_ID } from "@/utils/constant";
import { getStorage } from "@/utils/storage";
import DDYToast from "@/utils/toast";
import Taro from "@tarojs/taro";
import { DDYBack, DDYSwitchTab } from "@/utils/route";
import newAPi from "@/utils/newApi";
import { DDYObject } from "src/type/common";

const comfireStore = observable({
    address: {},
    isSelectAddress: false,
    orderGoodsList: [],
    selectCoupons: {},
    specId: null,
    orderNum: 0, //订单数量
    jdQStatus: 1,
    shipFee: 0,
    totalFee: 0,
    exchange: "", // integral 积分，还是商品
    actualPrice: 0,
    buyerNote: [],
    isAgreeInform: false,
    isAgreeEntrust: false,
    list: [],
    goodList: [],
    taxAlls: [],
    from: "none", //确认订单来源
    stockCheck: false,
    priceTotalObj: {
        itemTotalPrice: 0,
        shippingPrice: 0,
        taxPrice: 0,
        discountPrice: 0,
        couponPrice: 0,
        giftPrice: 0,
        giftBeanNum: 0,
        cashPrice: 0,
    },
    giftPriceFlag: true,
    usableCashPrice: 0,
    cashPriceFlag: true,
    usableGiftPrice: 0,
    shopOrderFeeDetailDTO: {},
    primePrice: 0,
    initCouponStatus: false, // 无可用优惠券，true: 有可用优惠券
    resetEmpty() {
        this.orderGoodsList = [];
        this.selectCoupons = {};
        this.orderNum = 0;
        this.shipFee = 0;
        this.totalFee = 0;
        this.list = [];
        this.taxAlls = [];
        this.buyerNote = [];
        this.actualPrice = [];
        this.shopOrderFeeDetailDTO = {};
        this.priceTotalObj = {
            itemTotalPrice: 0,
            shippingPrice: 0,
            taxPrice: 0,
            discountPrice: 0,
            couponPrice: 0,
            giftPrice: 0,
            cashPrice: 0,
        };
    },

    setBuyerNote(arr: string[]) {
        this.buyerNote = arr;
    },
    getAddress(id: number) {
        api.getAddress({
            data: {
                id,
            },
            showError: false,
            filterCheck: true,
        }).then(res => {
            if (res.id) {
                this.address = res;
                if (this.list.length > 0 && Object.keys(toJS(this.address)).length && this.stockCheck) {
                    this.jdQStatus = 3;
                    this.getJdStatus(toJS(this.address), toJS(this.list));
                }
                this.isSelectAddress = true;
                // this.getFee(res.id);
            } else {
                DDYToast.info("获取地址失败");
            }
        });
    },

    initData(route, type) {
        try {
            // goodsId=5981&orderNum=1&specId=68637&maxDiscount=0
            this.specId = route.params.specId;
            this.orderNum = route.params.orderNum;
            this.sourceType = route.params.sourceType;
            this.marketingToolId = route.params.marketingToolId;
            this.activityId = route.params.activityId;
            this.groupId = route.params.groupId;
            this.note = route.params.note;
            this.from = route.params.from;
            if (route.params.orderGoodsList) {
                this.orderGoodsList = JSON.parse(route.params.orderGoodsList as string);
            } else {
                this.orderGoodsList = [
                    {
                        skuId: this.specId,
                        quantity: this.orderNum,
                        sourceType: this.sourceType,
                        buyerNote: this.note,
                    },
                ];
            }
        } catch (error) {
            console.log(error);
        }
        if (getStorage(ADDRESS_ID)) {
            console.log("ADDRESS_ID", getStorage(ADDRESS_ID));
            let addressId = getStorage(ADDRESS_ID);
            this.getAddress(addressId);
        }
        this.initOrder(type, route);
    },
    initOrder(type, route) {
        // 数据处理，针对商品详情和购物车过来的订单
        let goodsList: any[] = this.getGoodList(route);
        console.log("goodsList:", goodsList);
        let queryData: DDYObject = {
            skuIdAndQuantity: JSON.stringify(goodsList),
            channel: 2,
            couponFlag: type === "init" ? 0 : 1,
            giftPriceFlag: 1,
            cashPriceFlag: 1,
            receiverInfoId: this.address?.id,
            marketingToolId: this.marketingToolId,
            activityInfo: {
                activityId: this.activityId,
                groupId: this.groupId,
                marketingToolId: this.marketingToolId,
            },
        };
        console.log("type:", type);
        if (type === "change") {
            // console.log("sss", toJS(this.selectCoupons));
            // queryData.couponIdList = this.selectCoupons;
            // queryData.
            queryData.shopCouponIdList = this.selectCoupons.shopCouponIdList;
            // itemCouponIdList: ids2
            queryData.itemCouponIdList = this.selectCoupons.itemCouponIdList;
            queryData.giftPriceFlag = this.giftPriceFlag ? 1 : 0;
            queryData.cashPriceFlag = this.cashPriceFlag ? 1 : 0;
        }
        if (comfireStore.exchange === "integral") {
            //@ts-ignore
            queryData.type = 1;
        }
        newAPi
            .orderPreview({
                data: queryData,
                method: "POST",
                // filterCheck: true,
                showLoad: true,
                showError: false,
            })
            .then(res => {
                if (res.richSkusByShops && res.richSkusByShops.length > 0) {
                    const newList = res.richSkusByShops.map(item => {
                        item.iconClick = true;
                        return item;
                    });
                    this.shopOrderFeeDetailDTO = res.richSkusByShops[0].shopOrderFeeDetailDTO;
                    // setList(newList);
                    this.list = newList;
                    this.stockCheck = res.checkStock;
                    // this.jdQStatus =
                    if (
                        this.list.length > 0 &&
                        this.stockCheck &&
                        (!getStorage(ADDRESS_ID) || (getStorage(ADDRESS_ID) && Object.keys(toJS(this.address)).length))
                    ) {
                        this.jdQStatus = 3;
                        this.getJdStatus(toJS(this.address), toJS(this.list));
                    }
                    const arr = [...this.taxAlls];
                    const goodList = [];

                    newList.forEach(element => {
                        let taxAll = 0;
                        element.richSkus.forEach((item, index) => {
                            taxAll += item.tax;
                            if (item.item.isBonded) {
                                // setShowModel(true)
                            }
                            if (goodList.filter(goodItem => goodItem.item.id === item.item.id).length === 0) {
                                goodList.push(item);
                            }

                            // console.log("item.discountDetail:", item.discountDetail);
                            this.orderGoodsList[index] = {
                                ...item,
                                ...this.orderGoodsList[index],
                                discountDetail: item.discountDetail,
                            };
                        });
                        arr.push(taxAll / 100);
                    });
                    this.goodList = goodList;
                    console.log("goodList2:", goodList);
                    this.taxAlls = arr;
                    // noShipPrice.current = (res.richSkusByShops[0].fee - res.richSkusByShops[0].shipFee) / 100;
                    // setActualPrice(res.fee / 100);
                    this.setActualprice(res.fee);
                    this.priceTotalObj = {
                        itemTotalPrice: res.itemTotalPrice,
                        shippingPrice: res.shippingPrice,
                        taxPrice: res.taxPrice,
                        discountPrice: res.discountPrice,
                        couponPrice: res.couponPrice,
                        giftPrice: res.giftPrice,
                        cashPrice: res.cashPrice,
                        giftBeanNum: res.giftBeanNum,
                    };
                    if (type === "init") {
                        this.initCouponStatus = res.couponPrice > 0;
                    }
                    console.log(toJS(this.priceTotalObj));
                    this.usableGiftPrice = res.usableGiftPrice;
                    this.usableCashPrice = res.usableCashPrice;
                    this.selectCoupons = {
                        shopCouponIdList: res.shopCouponIdList,
                        itemCouponIdList: res.itemCouponIdList,
                    };
                    // if (res.giftPrice > 0) {
                    this.giftPriceFlag = res.giftPrice > 0;
                    // }
                    // if (res.cashPrice > 0) {
                    this.cashPriceFlag = res.cashPrice > 0;
                    // }

                    // 用于记录最开始优惠
                    if (type === "init") {
                        this.primePrice = res.discountPrice;
                    }
                }
            })
            .catch(res => {
                console.log("res?:", res?.data?.message);
                if (this.from === "shop_cart") {
                    Taro.showModal({
                        showCancel: false,
                        title: "提示",
                        content: res?.data?.message ?? "订单预览失败",
                        success: () => {
                            DDYSwitchTab({ url: "/pages/shop_cart/index" });
                        },
                    });
                } else {
                    // DDYToast.info(res?.message ?? "订单预览失败");
                    Taro.showModal({
                        showCancel: false,
                        title: "提示",
                        content: res?.data?.message ?? "订单预览失败",
                        success: () => {
                            DDYBack();
                        },
                    });
                }
                this.jdQStatus = 1;
            });
    },
    getGoodList(route) {
        let goodsList: any[] = [];
        if (route.params.orderGoodsList) {
            try {
                goodsList = JSON.parse(route.params.orderGoodsList as string);
            } catch (error) {
                goodsList = [];
            }
            console.log("goodsList2222233333:", goodsList);
        } else {
            goodsList = [
                {
                    skuId: this.specId,
                    quantity: this.orderNum,
                    sourceType: this.sourceType,
                    buyerNote: this.note,
                },
            ];
        }
        return goodsList;
    },
    getFee(id: number) {
        const list = this.getGoodList();
        api.getFee({
            data: {
                skuInfo: JSON.stringify(list),
                receiverInfoId: id,
            },
            filterCheck: true,
        }).then(res => {
            // console.log("res:", res);
            Object.values(res)[0];
            // setShipFee(res?.[0])
            // console.log(Object.values(res)[0]);
            this.shipFee = Object.values(res)[0];
            // this.totalPrice = this.noShipPrice + this.shipFee;
            // setTotalPrice(noShipPrice.current + res?.[0] / 100)
        });
    },
    setActualprice(price) {
        this.actualPrice = price;
    },
    getJdStatus(address, list) {
        // setJdQStatus(3);
        this.jdQStatus = 3;
        api.queryJDYSkuStatus({
            method: "POST",
            data: {
                skuQuantityList: list.map(item => {
                    return { outerSkuId: item.richSkus[0].sku.outerSkuId, quantity: item.richSkus[0].quantity };
                }),
                address,
            },
            showError: false,
        })
            .then(res => {
                setTimeout(() => {
                    this.jdQStatus = res ? 1 : 2;
                }, 700);
            })
            .catch(e => {
                this.jdQStatus = 1;
            });
    },
});

export default comfireStore;
