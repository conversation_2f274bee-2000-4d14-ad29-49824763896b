import { observable, toJS } from "mobx";
import api from "@/utils/api";
import { ADDRESS_ID } from "@/utils/constant";
import { getStorage } from "@/utils/storage";
import DDYToast from "@/utils/toast";
import Taro from "@tarojs/taro";
import { DDYSwitchTab } from "@/utils/route";
import { isAlipay } from "@/utils/common";

const comfireStore = observable({
    address: {},
    isSelectAddress: false,
    orderGoodsList: [],
    selectCoupons: {},
    specId: null,
    orderNum: 0, //订单数量
    jdQStatus: 1,
    shipFee: 0,
    totalFee: 0,
    exchange: "", // integral 积分，还是商品
    actualPrice: 0,
    buyerNote: [],
    isAgreeInform: false,
    isAgreeEntrust: false,
    list: [],
    taxAlls: [],
    from: "none", //确认订单来源
    stockCheck: false,
    payMethods: "",

    resetEmpty() {
        this.orderGoodsList = [];
        this.selectCoupons = {};
        this.orderNum = 0;
        this.shipFee = 0;
        this.totalFee = 0;
        this.list = [];
        this.taxAlls = [];
        this.buyerNote = [];
        this.actualPrice = [];
    },

    setBuyerNote(arr: string[]) {
        this.buyerNote = arr;
    },
    getAddress(id: number) {
        api.getAddress({
            data: {
                id,
            },
            showError: false,
            filterCheck: true,
        }).then(res => {
            if (res.id) {
                this.address = res;
                if (this.list.length > 0 && Object.keys(toJS(this.address)).length && this.stockCheck) {
                    this.jdQStatus = 3;
                    this.getJdStatus(toJS(this.address), toJS(this.list));
                }
                this.isSelectAddress = true;
                this.getFee(res.id);
            } else {
                DDYToast.info("获取地址失败");
            }
        });
    },

    initData(route) {
        try {
            // goodsId=5981&orderNum=1&specId=68637&maxDiscount=0
            this.specId = route.params.specId;
            this.orderNum = route.params.orderNum;
            this.sourceType = route.params.sourceType;
            this.from = route.params.from;
            console.log(this.specId, this.orderNum, this.sourceType);
            if (route.params.orderGoodsList) {
                this.orderGoodsList = JSON.parse(route.params.orderGoodsList as string);
            }
        } catch (error) {
            console.log(error);
        }
        if (getStorage(ADDRESS_ID)) {
            let addressId = getStorage(ADDRESS_ID);
            this.getAddress(addressId);
        }
        this.initOrder();
    },
    initOrder() {
        // 数据处理，针对商品详情和购物车过来的订单
        let goodsList: any[] = this.getGoodList();
        console.log("goodsList:", goodsList);
        let queryData: Object = {
            data: JSON.stringify(goodsList),
            channel: 2,
        };
        if (comfireStore.exchange === "integral") {
            //@ts-ignore
            queryData.type = 1;
        }
        api.initOrder({
            data: queryData,
            filterCheck: true,
        })
            .then(res => {
                if (res.richSkusByShops && res.richSkusByShops.length > 0) {
                    const newList = res.richSkusByShops.map(item => {
                        item.iconClick = true;
                        return item;
                    });
                    // setList(newList);
                    this.list = newList;
                    this.stockCheck = res.checkStock;
                    // this.jdQStatus =
                    if (
                        this.list.length > 0 &&
                        this.stockCheck &&
                        (!getStorage(ADDRESS_ID) || (getStorage(ADDRESS_ID) && Object.keys(toJS(this.address)).length))
                    ) {
                        this.jdQStatus = 3;
                        this.getJdStatus(toJS(this.address), toJS(this.list));
                    }
                    const arr = [...this.taxAlls];
                    newList.forEach(element => {
                        let taxAll = 0;
                        element.richSkus.forEach(item => {
                            taxAll += item.tax;
                            if (item.item.isBonded) {
                                // setShowModel(true)
                            }
                        });
                        console.log("element.richSkus:", element.richSkus);
                        console.log("税费：", taxAll / 100);
                        arr.push(taxAll / 100);
                    });
                    this.taxAlls = arr;
                    // noShipPrice.current = (res.richSkusByShops[0].fee - res.richSkusByShops[0].shipFee) / 100;
                    // setActualPrice(res.fee / 100);
                    this.setActualprice(res.fee / 100);
                }
            })
            .catch(res => {
                if (this.from === "shop_cart") {
                    Taro.showModal({
                        showCancel: false,
                        title: "提示",
                        content: res?.message ?? "订单预览失败",
                        success: () => {
                            DDYSwitchTab({ url: "/pages/shop_cart/index" });
                        },
                    });
                }
                this.jdQStatus = 1;
            });
    },
    getGoodList() {
        let goodsList: any[] = [];
        if (this.orderGoodsList.length > 0) {
            goodsList = this.orderGoodsList.map(item => {
                const result = {
                    ...toJS(item),
                    sourceType: toJS(item).sourceType,
                };
                if (this.selectCoupons[result.skuId]) {
                    result.promotionId = this.selectCoupons[result.skuId];
                }
                return result;
            });
        } else {
            goodsList = [
                {
                    skuId: this.specId,
                    quantity: this.orderNum,
                    sourceType: this.sourceType,
                },
            ];
            console.log("goodsList:", goodsList);
            if (this.selectCoupons[this.specId]) {
                goodsList[0].promotionId = this.selectCoupons[this.specId];
            }
        }
        return goodsList;
    },
    getFee(id: number) {
        const list = this.getGoodList();
        api.getFee({
            data: {
                skuInfo: JSON.stringify(list),
                receiverInfoId: id,
            },
            filterCheck: true,
        }).then(res => {
            // console.log("res:", res);
            Object.values(res)[0];
            // setShipFee(res?.[0])
            // console.log(Object.values(res)[0]);
            this.shipFee = Object.values(res)[0];
            // this.totalPrice = this.noShipPrice + this.shipFee;
            // setTotalPrice(noShipPrice.current + res?.[0] / 100)
        });
    },
    setActualprice(price) {
        this.actualPrice = price;
    },
    setPayMethods(methods) {
        this.payMethods = methods;
    },
    getJdStatus(address, list) {
        // setJdQStatus(3);
        this.jdQStatus = 3;
        api.queryJDYSkuStatus({
            method: "POST",
            data: {
                skuQuantityList: list.map(item => {
                    return { outerSkuId: item.richSkus[0].sku.outerSkuId, quantity: item.richSkus[0].quantity };
                }),
                address,
            },
            showError: false,
        })
            .then(res => {
                setTimeout(() => {
                    this.jdQStatus = res ? 1 : 2;
                }, 700);
            })
            .catch(e => {
                this.jdQStatus = 1;
            });
    },
});

export default comfireStore;
