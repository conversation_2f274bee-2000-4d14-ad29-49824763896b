.address {
    margin: 24px;
    border-radius: 16px;
    background-color: #fff;
    padding: 32px;

    .address_info {
        width: 100%;
        .user_info {
            .name {
                font-size: 32px;
                font-family: PingFangSC-Medium, PingFang SC;
                font-weight: 500;
                color: #333333;
                line-height: 32px;
            }
            .phone {
                font-size: 26px;
                font-family: HelveticaNeue-Medium, HelveticaNeue;
                font-weight: 500;
                color: #333333;
                line-height: 26px;
                margin-left: 16px;
            }
        }
        .active_address {
            width: 100%;
            position: relative;
            padding-right: 40px;
            .active_address_text {
                font-size: 26px;
                color: #333333;
                font-weight: 400;
                word-break: break-word;
            }
            .active_address_icon {
                position: absolute;
                right: 16px;
                top: 50%;
                transform: translateY(-50%);
            }
        }
    }
}
.order-card {
    margin: 0 24px 20px 24px;
    padding: 32rpx 0;
    background-color: #fff;
    // min-height: 200px;
    border-radius: 16rpx;
    .order-card-title {
        padding-left: 30px;
        padding-bottom: 24px;
        font-size: 32px;
    }
}
.shop-good-list {
    // padding: 24px;
    padding: 0 24px 24px 24px;
    margin-bottom: 0;
    .prod_item {
        background-color: #fff;
        padding: 32px;
        border-radius: 16px;
        .list_box {
            .store_name {
                .store_last {
                    font-size: 26px;
                    font-family: PingFangSC-Regular, PingFang SC;
                    font-weight: 400;
                    color: #333333;
                    line-height: 26px;
                    margin-left: 24px;
                }
            }
            .goods-info {
                display: flex;
                // align-items:;
                flex-direction: row;
                margin-top: 20px;
                .img-box {
                    width: 160px;
                    height: 160px;
                    display: block;
                    margin-right: 24px;
                    .img {
                        width: 100%;
                        height: 100%;
                        display: block;
                        border-radius: 8rpx;
                    }
                }
                .text-box {
                    position: relative;
                    width: 454px;
                    .goods-title {
                        font-size: 26px;
                        font-family: PingFangSC-Regular, PingFang SC;
                        font-weight: 400;
                        color: #333333;
                        line-height: 38px;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        -webkit-line-clamp: 2;
                        max-height: 76px;
                    }
                    .goods-spec {
                        margin-top: 10px;
                        color: #999999;
                        overflow: hidden;
                        .goods-spec-list {
                            margin-right: 20px;
                            font-size: 26px;
                            float: left;
                        }
                    }
                    .goods-detail {
                        position: absolute;
                        bottom: 0;
                        left: 0;
                        right: 0;
                        display: flex;
                        flex-direction: row;
                        align-items: center;
                        justify-content: space-between;
                        .goods-price {
                            font-size: 32px;
                            font-family: Helvetica;
                            color: #333333;
                            line-height: 32px;
                        }
                        .buy-num {
                            font-size: 26px;
                            font-family: HelveticaNeue;
                            color: #999999;
                            line-height: 26px;
                        }
                    }
                }
            }
        }
        .info_block {
            margin-top: 48px;
            .item {
                display: flex;
                flex-direction: row;
                align-items: center;
                justify-content: space-between;
                padding: 16px 0;
                .title {
                    font-size: 26px;
                    font-weight: 400;
                    color: #333333;
                    line-height: 26px;
                }
                .coupon {
                    height: 68rpx;
                    line-height: 68rpx;
                    font-size: 26rpx;
                    color: red;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    .coupon_img {
                        width: 26rpx;
                        height: 26rpx;
                    }
                }
                .cost {
                    font-size: 26px;
                    font-weight: 400;
                    color: #333333;
                    line-height: 26px;
                }
            }
        }
    }
}

.bottom-tips {
    margin-left: 50px;
    margin-right: 50px;
    font-size: 22px;
    .tips-btn {
        color: rgb(52, 160, 251);
    }
}
.isEntrust_con {
    padding: 32px;
}
.order_info,
.block {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: flex-end;
}

.label-box {
    margin-right: 8px;
    .label-item {
        border: 2px solid #f50e0c;
        border-radius: 6px;
        padding: 2px 10px;
        // height: 36px;
        font-size: 24px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #de1600;
        display: inline-block;
    }
    .label-item-limit {
        border: 2px solid #f50e0c;
        background: #de1600;
        color: #fff;
        padding: 2px 10px;
        border-radius: 6px;
        // height: 36px;
        font-size: 24px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        display: inline-block;
    }
}
