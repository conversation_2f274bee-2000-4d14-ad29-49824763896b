.address {
    margin: 24px;
    border-radius: 16px;
    background-color: #fff;
    padding: 32px;

    .address_info {
        width: 100%;
        .user_info {
            .name {
                font-size: 32px;
                font-family: PingFangSC-Medium, PingFang SC;
                font-weight: 500;
                color: #333333;
                line-height: 32px;
            }
            .phone {
                font-size: 26px;
                font-family: HelveticaNeue-Medium, HelveticaNeue;
                font-weight: 500;
                color: #333333;
                line-height: 26px;
                margin-left: 16px;
            }
        }
        .active_address {
            width: 100%;
            position: relative;
            padding-right: 40px;
            .active_address_text {
                font-size: 26px;
                color: #333333;
                font-weight: 400;
                word-break: break-word;
            }
            .active_address_icon {
                position: absolute;
                right: 16px;
                top: 50%;
                transform: translateY(-50%);
            }
        }
    }
}
.shop-good-list {
    // padding: 24px;
    padding: 0 24px 24px 24px;
    .prod_item {
        background-color: #fff;
        padding: 32px;
        border-radius: 16px;
        .list_box {
            .store_name {
                .store_last {
                    font-size: 26px;
                    font-family: PingFangSC-Regular, PingFang SC;
                    font-weight: 400;
                    color: #333333;
                    line-height: 26px;
                    margin-left: 24px;
                }
            }
            .goods-info {
                display: flex;
                // align-items:;
                flex-direction: row;
                margin-top: 20px;
                .img-box {
                    width: 160px;
                    height: 160px;
                    display: block;
                    margin-right: 24px;
                    .img {
                        width: 100%;
                        height: 100%;
                        display: block;
                        border-radius: 8rpx;
                    }
                }
                .text-box {
                    position: relative;
                    width: 454px;
                    .goods-title {
                        font-size: 26px;
                        font-family: PingFangSC-Regular, PingFang SC;
                        font-weight: 400;
                        color: #333333;
                        line-height: 38px;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        -webkit-line-clamp: 1;
                        max-height: 38px;
                        white-space: nowrap;
                    }
                    .goods-bottom {
                        position: absolute;
                        bottom: 0;
                        left: 0;
                        right: 0;
                        display: flex;
                        flex-direction: column;
                        justify-content: flex-end;
                        .goods-specs {
                            display: flex;
                            flex-direction: row;
                            .goods-spec {
                                font-size: 26px;
                                font-family: HelveticaNeue;
                                color: #999999;
                                line-height: 26px;
                                overflow: hidden;
                                text-overflow: ellipsis;
                                -webkit-line-clamp: 1;
                                max-height: 38px;
                                white-space: nowrap;
                                max-width: 180px;
                                // .goods-spec-list {
                                //     margin-right: 20px;
                                //     font-size: 26px;
                                //     float: left;
                                // }
                            }
                            .buy-num {
                                font-size: 26px;
                                font-family: HelveticaNeue;
                                color: #999999;
                                line-height: 26px;
                                margin-left: 10px;
                            }
                        }

                        .goods-detail {
                            // position: absolute;
                            // bottom: 0;
                            // left: 0;
                            // right: 0;
                            display: flex;
                            flex-direction: row;
                            align-items: center;
                            justify-content: space-between;
                            .goods-price {
                                font-size: 32px;
                                font-family: Helvetica;
                                color: #999999;
                                line-height: 32px;
                            }
                        }
                        .goods-tax {
                            display: flex;
                            flex-direction: row;
                            align-items: center;
                            justify-content: space-between;
                            .goods-tax-text {
                                font-size: 26px;
                                font-family: HelveticaNeue;
                                color: #999999;
                                line-height: 26px;
                            }
                        }
                    }
                }
            }
        }
        .info_block {
            // margin-bottom: 24px;
            .item {
                display: flex;
                flex-direction: row;
                align-items: center;
                justify-content: space-between;
                padding: 16px 0;
                .title {
                    font-size: 26px;
                    font-weight: 400;
                    color: #333333;
                    line-height: 26px;
                }
                .coupon {
                    height: 68rpx;
                    line-height: 68rpx;
                    font-size: 26rpx;
                    color: red;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    .coupon_img {
                        width: 26rpx;
                        height: 26rpx;
                    }
                }
                .cost {
                    font-size: 26px;
                    font-weight: 400;
                    color: #333333;
                    line-height: 26px;
                }
            }
        }
        .prod_item-title {
            // padding-left: 10px;
            // margin: 10px 0;
            font-weight: 400;
            font-size: 32px;
            color: #000;
            line-height: 40px;
            text-align: left;
            font-style: normal;
        }
        .price-total {
            margin-top: 10px;
            .item {
                margin-bottom: 0;
                padding: 8px 0;
                .title {
                    font-size: 28px;
                    color: #000;
                }
                .title1 {
                    font-size: 26px;
                    margin-left: 20px;
                    color: #999999;
                }
                .text1 {
                    font-size: 32px;
                    margin-right: 22px;
                }
                .cost {
                    font-size: 32px;
                    margin-right: 22px;
                }
                .text2 {
                    color: #f50e0c;
                    line-height: 40px;
                }
                .text3 {
                    color: #f50e0c;
                    line-height: 40px;
                    font-size: 32px;
                    margin-right: 22px;
                }
            }
            .item-gray {
                .text1,
                .text2,
                .text3 {
                    color: #ccc;
                }
            }
        }
        .popup-item {
            width: 100%;
            display: flex;
            flex-direction: row;
            align-items: center;
            justify-content: space-between;
            padding: 10px 0;
            border-bottom: none;
            // margin: 12px 0;
            .item-title {
                margin: 12px 0;
                font-weight: 400;
                font-size: 26px;
                color: #5a6c91;
                line-height: 37px;
                text-align: left;
                font-style: normal;
            }
            .van-cell {
                width: 120px;
            }
            .item-right {
                display: flex;
                flex-direction: row;
                align-items: center;
                .item-limit {
                    font-weight: 400;
                    font-size: 22px;
                    color: #999999;
                    line-height: 30px;
                    text-align: left;
                    font-style: normal;
                    margin-right: 14px;
                }
            }
            .item-right-icon {
                position: relative;
                font-weight: 400;
                font-size: 22px;
                color: #999999;
                line-height: 38px;
                text-align: left;
                font-style: normal;
                padding-right: 24px;
                padding-top: 6px;
                display: inline-block;
                max-width: 100px;
                overflow: hidden;
                text-overflow: ellipsis;
                display: -webkit-box;
                -webkit-line-clamp: 1;
                -webkit-box-orient: vertical;
                margin: 0 10px;
                word-break: break-all;
                &:after {
                    position: absolute;
                    right: 0px;
                    top: 5px;
                    content: "\e678";
                    font-family: "iconfont-fx";
                }
            }
        }
    }
}

.group-header {
    .group-header-title {
        text-align: center;
        color: #f50e0c;
        line-height: 60px;
    }
    .group-header-content {
        background-color: #fff;
        padding: 20px;
        display: flex;
        flex-direction: row;
        align-items: center;
        .group-header-avator {
            width: 80px;
            height: 80px;
            border-radius: 40px;
            margin-right: 10px;
            background-color: #ccc;
        }
        .group-status {
            border-radius: 20px;
            color: #8080ff;
            padding: 8px 16px;
            background-color: #e9f0ff;
        }
    }
}

.bottom-tips {
    margin-left: 50px;
    margin-right: 50px;
    // display: flex;
    // flex-direction: row;
    // flex-wrap: wrap;
    // align-items: center;
    font-size: 22px;
    // padding-bottom: 120px;
    .tips-btn {
        color: rgb(52, 160, 251);
    }
}
.isEntrust_con {
    padding: 32px;
}
.order_info,
.block {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: flex-end;
}

.label-box {
    margin-right: 8px;
    .label-item {
        border: 2px solid #f50e0c;
        border-radius: 6px;
        padding: 2px 10px;
        // height: 36px;
        font-size: 24px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #de1600;
        display: inline-block;
    }
    .label-item-limit {
        border: 2px solid #f50e0c;
        background: #de1600;
        color: #fff;
        padding: 2px 10px;
        border-radius: 6px;
        // height: 36px;
        font-size: 24px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        display: inline-block;
    }
}

.gift-rect {
    position: relative;
    padding-bottom: calc(120px + env(safe-area-inset-bottom));
    .gift-goods {
        position: relative;
        min-height: 200px;
        .gift-scroll {
            width: 100%;
            height: 100px;
            padding: 10px 0;
            background-color: #999999;
        }
        .gift-checked {
            position: absolute;
            height: 80px;
            display: flex;
            flex-direction: row;
            align-items: center;
            right: 24px;
            top: 40px;
            .gift-checked-text {
                color: #f50e0c;
                font-size: 28px;
                line-height: 36px;
                margin-right: 12px;
            }
        }
    }
    .un-used {
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: space-between;
        border-top: 1px solid #f5f5f5;
        padding: 30px 24px;
        .un-used-text {
            // display;
            color: #333333;
        }
    }
    .gift-btn {
        position: absolute;
        right: 24px;
        background-color: #f50e0c;
        color: #fff;
        min-width: 170px;
        bottom: calc(20px + env(safe-area-inset-bottom));
    }
}
