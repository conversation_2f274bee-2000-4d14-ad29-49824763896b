import { ActionSheet, Checkbox } from "@antmjs/vantui";
import { View, Image, Text, Button, ScrollView } from "@tarojs/components";
import { useEffect, useState } from "react";

export default ({ open, closeFn, goodList, giftPrice, itemTotalPrice, initSelect }) => {
    const [select, setSelect] = useState(false);
    useEffect(() => {
        console.log("initSelect:", initSelect);
        setSelect(initSelect);
    }, [initSelect]);
    return (
        <ActionSheet
            show={open}
            title="福豆详情"
            onClose={() => {
                closeFn();
            }}
        >
            <View className="gift-rect">
                <View className="gift-goods">
                    {/* <ScrollView scrollX={true} className="gift-scroll"> */}
                    <View
                        style={{
                            width: "100vw",
                            display: "flex",
                            flexDirection: "row",
                            flexWrap: "nowrap",
                            paddingLeft: "24px",
                            overflowX: "scroll",
                            paddingTop: "50px",
                            boxSizing: "border-box",
                            paddingRight: "24px",
                        }}
                    >
                        {goodList.map((goodsItem, index) => {
                            return (
                                <View
                                    style={{
                                        position: "relative",
                                        marginRight: "24px",
                                        width: "80px",
                                        height: "80px",
                                        // backgroundColor: "red",
                                    }}
                                >
                                    <Image
                                        src={goodsItem.item.mainImage}
                                        mode="aspectFill"
                                        style={{ width: "80px", height: "80px", borderRadius: "8px" }}
                                    />
                                </View>
                            );
                        })}
                    </View>
                    <View className="goods-total-item">
                        <View className="goods-discount-price">省{giftPrice || 0}元</View>
                        <Text className="goods-total-info">
                            小记¥{itemTotalPrice}，减¥{giftPrice || 0}
                        </Text>
                    </View>
                    {/* </ScrollView> */}
                    <View
                        className="gift-checked"
                        onClick={() => {
                            setSelect(!select);
                        }}
                    >
                        <Text className="gift-checked-text">减{giftPrice || 0.0}</Text>
                        <Checkbox value={select} name={true} />
                    </View>
                </View>
                <View
                    className="un-used"
                    onClick={() => {
                        setSelect(!select);
                    }}
                >
                    <Text className="un-used-text">不使用</Text>
                    <Checkbox value={!select} name={true} />
                </View>
                <Button
                    className="gift-btn"
                    onClick={() => {
                        closeFn({ bol: select });
                    }}
                >
                    确定
                </Button>
            </View>
        </ActionSheet>
    );
};
