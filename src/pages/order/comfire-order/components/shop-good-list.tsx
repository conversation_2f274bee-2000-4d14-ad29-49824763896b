import { View, Text, Image } from "@tarojs/components";
import { useEffect, useRef, useState } from "react";
import { Field, Icon } from "@antmjs/vantui";
import { DDYNavigateTo } from "@/utils/route";
import { useDidShow, useRouter } from "@tarojs/taro";
import infoStore from "@/store/info-store";
import comfireStore from "../store";
import { observer, useObserver } from "mobx-react-lite";
import { toJS } from "mobx";
const rightIcon = require("../../../../images/arrow-down-line.png");
interface shopList {
    shipFee: number;
    fee: number;
    richSkus: {
        tax: number;
        fee: number;
        iconClick: any;
        type: number;
        discount: any;
        skuPromotions: any;
        quantity: number;
        item: any;
        sku: any;
        flagList: any;
    }[];
    iconClick?: boolean;
}

export default observer(() => {
    const route = useRouter();
    // const [list, setList] = useState<shopList[]>([]);
    const list: shopList[] = comfireStore.list as shopList[];
    //@ts-ignore
    const [storeName, setStoreName] = useState(infoStore?.subStore?.name);
    const coupon_id = useRef<number>(null);

    const [buyerNote, setBuyerNote] = useState<string[]>([]);

    const orderGoodsList = useRef<
        {
            skuId: number;
            promotionId: number;
        }[]
    >([]);

    const toCoupon = (index: number, prodIndex: number) => {
        try {
            const coupons = JSON.stringify(list[index].richSkus[prodIndex]);
            const couponSelectedSkuId = list[index].richSkus[prodIndex].sku.id;
            let promotionId;
            if (orderGoodsList.current.length > 0) {
                orderGoodsList.current.forEach(element => {
                    if (element.skuId === couponSelectedSkuId) {
                        promotionId = element.promotionId || "";
                    }
                });
            } else {
                promotionId = coupon_id.current;
            }
            DDYNavigateTo({
                url: `/pages/order/coupon-select/index?coupons=${coupons}&promotionId=${promotionId}`,
                events: {
                    handleCouponSelected: ({ id }) => {
                        comfireStore.selectCoupons[couponSelectedSkuId] = id;
                        comfireStore.initData(route);
                    },
                },
            });
        } catch (error) {}
    };
    const bindBuyerNote = (index: number, val: string) => {
        buyerNote[index] = val;
        //@ts-ignore
        // comfireStore.buyerNote = [...buyerNote];
        comfireStore.setBuyerNote([...buyerNote]);
        setBuyerNote([...buyerNote]);
    };

    const onClickShow = index => {
        // list[index].iconClick = list[index].iconClick;
        // setList([...list])
    };

    // useDidShow(() => {
    //     console.log(getGoodList());
    //     if (getGoodList().length > 0) initOrder();
    // });

    useEffect(() => {
        try {
            if (route.params.orderGoodsList) {
                orderGoodsList.current = JSON.parse(route.params.orderGoodsList as string);
            }
        } catch (error) {
            console.log(error);
        }
    }, []);
    // console.log(toJS(comfireStore.taxAlls))
    return useObserver(() => (
        <View className="shop-good-list">
            {list.map((shopItem, shopIndex) => {
                return (
                    <View key={shopIndex}>
                        {shopItem.richSkus.map((prodItem, prodIndex) => {
                            return (
                                <View key={prodIndex} className="prod_item" style={{ marginBottom: "20rpx" }}>
                                    <View className="list_box">
                                        <View className="store_name">
                                            <Icon name="shop-o" size="32rpx" className="icon"></Icon>
                                            {!storeName || storeName.length < 19 ? (
                                                <Text className="store_last">{storeName}</Text>
                                            ) : (
                                                <View>
                                                    {prodItem.iconClick ? (
                                                        <View>
                                                            <Text className="store_last">{storeName}</Text>
                                                            <Image
                                                                className="downline"
                                                                onClick={() => {
                                                                    onClickShow(shopIndex);
                                                                }}
                                                                data-index="{{index}}"
                                                                src={rightIcon}
                                                                mode="widthFix"
                                                            ></Image>
                                                        </View>
                                                    ) : (
                                                        <View>
                                                            <Text className="store_for">{storeName}</Text>
                                                            <Image
                                                                className="downline"
                                                                onClick={() => {
                                                                    onClickShow(shopIndex);
                                                                }}
                                                                data-index="{{index}}"
                                                                src={rightIcon}
                                                                mode="widthFix"
                                                            ></Image>
                                                        </View>
                                                    )}
                                                </View>
                                            )}
                                        </View>

                                        <View className="goods-info">
                                            <View className="img-box">
                                                <Image
                                                    src={prodItem.item.mainImage}
                                                    className="img"
                                                    mode="scaleToFill"
                                                />
                                            </View>
                                            <View className="text-box">
                                                <View className="goods-title">
                                                    {prodItem?.flagList?.map((flagItem, flagIndex) => (
                                                        <Text className="label-box" key={flagIndex}>
                                                            {flagItem == "promotionActivityItem" && (
                                                                <Text className="label-item">活动</Text>
                                                            )}
                                                            {flagItem == "promotionLimitedItem" && (
                                                                <Text className="label-item-limit">限定</Text>
                                                            )}
                                                        </Text>
                                                    ))}
                                                    {prodItem.item.name}
                                                </View>
                                                <View>
                                                    <View className="goods-spec">
                                                        {prodItem.sku?.attrs?.map((specItem, specIndex) => {
                                                            <View className="goods-spec-list">
                                                                <Text>{specItem.attrKey}:</Text>
                                                                <Text style="padding-left: 5rpx;">
                                                                    {specItem.attrVal}
                                                                </Text>
                                                            </View>;
                                                        })}
                                                    </View>
                                                    <View className="goods-detail">
                                                        <View className="goods-detail-left">
                                                            {comfireStore.exchange == "" && (
                                                                <View className="goods-price">
                                                                    ¥ {prodItem.sku.price / 100}
                                                                </View>
                                                            )}
                                                            {comfireStore.exchange == "integral" && (
                                                                <View className="goods-price">
                                                                    积分 {prodItem.sku.price / 100}
                                                                </View>
                                                            )}
                                                        </View>

                                                        <View className="buy-num">x{prodItem.quantity}</View>
                                                    </View>
                                                </View>
                                            </View>
                                        </View>
                                    </View>
                                    <View className="info_block">
                                        {comfireStore.exchange == "" && (
                                            <>
                                                <View className="item">
                                                    <Text className="title">运 费:</Text>
                                                    <Text className="cost">￥{comfireStore.shipFee / 100}</Text>
                                                </View>
                                                <View className="item">
                                                    <Text className="title">税 费:</Text>
                                                    <Text className="cost">￥{prodItem.tax / 100}</Text>
                                                </View>
                                            </>
                                        )}

                                        <View className="item">
                                            <Text className="title">优惠券:</Text>
                                            {prodItem.skuPromotions.length &&
                                            (!prodItem.discount || prodItem.discount == 0) ? (
                                                <View
                                                    className="coupon"
                                                    onClick={() => {
                                                        toCoupon(shopIndex, prodIndex);
                                                    }}
                                                    data-prodIndex="{{prodIndex}}"
                                                >
                                                    {prodItem.skuPromotions.length}张可用
                                                    {/* <Image
                                                        className="coupon_img"
                                                        src="../images/arrow-right-s-line.png"
                                                        mode="widthFix"
                                                    ></Image> */}
                                                </View>
                                            ) : null}
                                            {prodItem.skuPromotions.length &&
                                            prodItem.discount &&
                                            prodItem.discount != 0 ? (
                                                <View
                                                    className="coupon"
                                                    onClick={() => {
                                                        toCoupon(shopIndex, prodIndex);
                                                    }}
                                                >
                                                    -￥{prodItem.discount / 100}
                                                    {/* <Image
                                                        className="coupon_img"
                                                        src="../images/arrow-right-s-line.png"
                                                        mode="widthFix"
                                                    ></Image> */}
                                                </View>
                                            ) : null}
                                            {!prodItem.skuPromotions.length ||
                                            (prodItem.discount && prodItem.discount === 0) ? (
                                                <View className="coupon_on">
                                                    {prodItem.type === 4 ? <View>门店限定</View> : <View>0张可用</View>}
                                                </View>
                                            ) : null}
                                        </View>
                                        <View className="item">
                                            <Text className="title">买家留言:</Text>
                                            <Field
                                                type="text"
                                                value={buyerNote[shopIndex]}
                                                placeholder-style="color:#CFCFCF"
                                                placeholder="点此给商家留言"
                                                onChange={e => {
                                                    //@ts-ignore
                                                    const { value } = e.target;
                                                    bindBuyerNote(shopIndex, value);
                                                }}
                                            />
                                        </View>
                                    </View>
                                    <View className="order_info">
                                        <View className="block">
                                            <View className="left">小计:</View>
                                            {comfireStore.exchange == "" ? (
                                                <View className="price">
                                                    <Text style="font-size: 24rpx;">￥</Text>
                                                    {(prodItem.fee + (comfireStore.shipFee - shopItem.shipFee)) / 100}
                                                </View>
                                            ) : (
                                                <View className="price">
                                                    {(prodItem.fee + (comfireStore.shipFee - shopItem.shipFee)) / 100}{" "}
                                                    积分
                                                </View>
                                            )}
                                        </View>
                                    </View>
                                </View>
                            );
                        })}
                    </View>
                );
            })}
        </View>
    ));
});
