import { View, Text, Image, ScrollView } from "@tarojs/components";
import { useEffect, useRef, useState } from "react";
import { ActionSheet, Button, Checkbox, Dialog, Field, Icon } from "@antmjs/vantui";
import { DDYNavigateTo } from "@/utils/route";
import Taro, { useDidShow, useRouter } from "@tarojs/taro";
import infoStore from "@/store/info-store";
import comfireStore from "../store";
import { observer, useObserver } from "mobx-react-lite";
import { toJS } from "mobx";
import SelectGiftModal from "./select-gift-modal";
import SelectCashModal from "./select-cash-modal";
import DDYToast from "@/utils/toast";
import newAPi from "@/utils/newApi";
const rightIcon = require("../../../../images/arrow-down-line.png");

const Dialog_ = Dialog.createOnlyDialog();
interface shopList {
    buyerNote: string | number | undefined;
    shipFee: number;
    fee: number;
    richSkus: {
        tax: number;
        fee: number;
        iconClick: any;
        type: number;
        discount: any;
        skuPromotions: any;
        quantity: number;
        item: any;
        sku: any;
        flagList: any;
    }[];
    iconClick?: boolean;
}

export default observer(() => {
    const route = useRouter();
    // const [list, setList] = useState<shopList[]>([]);
    const list: shopList[] = comfireStore.list as shopList[];

    const priceTotalObj = comfireStore.priceTotalObj;
    //@ts-ignore
    const [storeName, setStoreName] = useState(infoStore?.subStore?.name);
    // const coupon_id = useRef<number>(null);

    const [buyerNote, setBuyerNote] = useState<string[]>([]);

    const [giftShow, setGiftShow] = useState(false);
    const [cashShow, setCashShow] = useState(false);

    const orderGoodsList = useRef<
        {
            skuId: number;
            promotionId: number;
        }[]
    >([]);

    const toNewCoupon = () => {
        const goods = JSON.stringify(
            toJS(comfireStore.orderGoodsList).map(item => {
                return {
                    skuId: item.skuId,
                    quantity: item.quantity,
                };
            }),
        );
        console.log("goods:", goods);
        // const coupons = JSON.stringify(toJS(comfireStore.selectCoupons))
        // if()
        const selectCoupons = toJS(comfireStore.selectCoupons);
        // if(selectCoupons.itemCouponIdList.length>0)
        const couponsStr = JSON.stringify({
            itemCouponIdList: selectCoupons.itemCouponIdList,
            shopCouponIdList: selectCoupons.shopCouponIdList,
        });
        let url = `/pages/order/coupon-select/index?goods=${goods}&coupons=${couponsStr}`;
        if (!comfireStore.marketingToolId || !comfireStore.activityId || !comfireStore.groupId) {
            url += `&marketingToolId=${comfireStore.marketingToolId || ""}&activityId=${
                comfireStore.activityId || ""
            }&groupId=${comfireStore.groupId || ""}`;
        }

        DDYNavigateTo({
            url: url,
            events: {
                handleCouponSelected: ({ ids }) => {
                    console.log("handleCouponSelected:", ids);
                    // comfireStore.selectCoupons[couponSelectedSkuId] = id;
                    comfireStore.selectCoupons = ids;

                    comfireStore.initData(route, "change");
                },
            },
        });
    };
    const bindBuyerNote = (index: number, val: string) => {
        buyerNote[index] = val;
        //@ts-ignore
        // comfireStore.buyerNote = [...buyerNote];
        comfireStore.setBuyerNote([...buyerNote]);
        setBuyerNote([...buyerNote]);
    };

    const onClickShow = index => {
        // list[index].iconClick = list[index].iconClick;
        // setList([...list])
    };

    useEffect(() => {
        try {
            if (route.params.orderGoodsList) {
                orderGoodsList.current = JSON.parse(route.params.orderGoodsList as string);
            }
        } catch (error) {
            console.log(error);
        }
    }, []);
    // console.log(toJS(comfireStore.taxAlls))
    return useObserver(() => (
        <View className="shop-good-list">
            {list.map((shopItem, shopIndex) => {
                let tax = 0;
                shopItem.richSkus.map(skuItem => {
                    tax += skuItem.tax;
                });
                return (
                    <View key={shopIndex}>
                        <View key={shopIndex} className="prod_item" style={{ marginBottom: "20rpx" }}>
                            <View className="list_box">
                                <View className="store_name">
                                    <Icon name="shop-o" size="32rpx" className="icon"></Icon>
                                    <Text className="store_last">{shopItem.shop.name}</Text>
                                    {/* {!storeName || storeName.length < 19 ? (
                                        <Text className="store_last">{storeName}</Text>
                                    ) : (
                                        <View>
                                            {prodItem.iconClick ? (
                                                <View>
                                                    <Text className="store_last">{storeName}</Text>
                                                    <Image
                                                        className="downline"
                                                        onClick={() => {
                                                            onClickShow(shopIndex);
                                                        }}
                                                        data-index="{{index}}"
                                                        src={rightIcon}
                                                        mode="widthFix"
                                                    ></Image>
                                                </View>
                                            ) : (
                                                <View>
                                                    <Text className="store_for">{storeName}</Text>
                                                    <Image
                                                        className="downline"
                                                        onClick={() => {
                                                            onClickShow(shopIndex);
                                                        }}
                                                        data-index="{{index}}"
                                                        src={rightIcon}
                                                        mode="widthFix"
                                                    ></Image>
                                                </View>
                                            )}
                                        </View>
                                    )} */}
                                </View>
                                {shopItem.richSkus.map((prodItem, prodIndex) => {
                                    return (
                                        <View className="goods-info">
                                            <View className="img-box">
                                                <Image
                                                    src={prodItem.sku.image || prodItem.item.mainImage}
                                                    className="img"
                                                    mode="scaleToFill"
                                                />
                                            </View>
                                            <View className="text-box">
                                                <View className="goods-title">
                                                    {prodItem?.flagList?.map((flagItem, flagIndex) => (
                                                        <Text className="label-box" key={flagIndex}>
                                                            {flagItem == "promotionActivityItem" && (
                                                                <Text className="label-item">活动</Text>
                                                            )}
                                                            {flagItem == "promotionLimitedItem" && (
                                                                <Text className="label-item-limit">限定</Text>
                                                            )}
                                                        </Text>
                                                    ))}
                                                    {prodItem.item.name}
                                                </View>
                                                <View>
                                                    {prodItem.sku.type === 0 ? (
                                                        <Image
                                                            mode="aspectFit"
                                                            style={{ width: "36.5px", height: "13pt" }}
                                                            src="https://dante-img.oss-cn-hangzhou.aliyuncs.com/19198843262.png"
                                                        />
                                                    ) : null}
                                                    {[1, 2].includes(prodItem.sku.type) ? (
                                                        <Image
                                                            mode="aspectFit"
                                                            style={{ width: "36.5px", height: "13px" }}
                                                            src="https://dante-img.oss-cn-hangzhou.aliyuncs.com/19194973267.png"
                                                        />
                                                    ) : null}
                                                </View>
                                                <View className="goods-bottom">
                                                    <View
                                                        style={{
                                                            display: "flex",
                                                            flexDirection: "row",
                                                            alignItems: "center",
                                                            justifyContent: "space-between",
                                                            marginBottom: "8px",
                                                        }}
                                                    >
                                                        <View className="goods-specs">
                                                            <View className="goods-spec">
                                                                {prodItem.sku.name || prodItem.sku.specification}
                                                            </View>
                                                            <View className="buy-num">x{prodItem.quantity}</View>
                                                        </View>
                                                        <View className="goods-detail">
                                                            <View className="goods-detail-left">
                                                                {comfireStore.exchange == "" && (
                                                                    <View className="goods-price">
                                                                        {prodItem.originFee !== prodItem.fee ? (
                                                                            <Text
                                                                                style={{
                                                                                    color: "#f50e0c",
                                                                                    fontSize: "10px",
                                                                                    marginRight: "8px",
                                                                                }}
                                                                            >
                                                                                折后价
                                                                            </Text>
                                                                        ) : null}
                                                                        ¥
                                                                        {prodItem.originFee !== prodItem.fee
                                                                            ? prodItem.fee / 100
                                                                            : prodItem.originFee / 100}
                                                                        {/* {prodItem.originFee !== prodItem.fee ? (
                                                                            <View
                                                                                style={{ color: "#ccc", fontSize: "10px" }}
                                                                            >
                                                                                ¥{prodItem.originFee / 100}
                                                                            </View>
                                                                        ) : null} */}
                                                                    </View>
                                                                )}
                                                            </View>
                                                        </View>
                                                    </View>
                                                    {prodItem.sku.type !== 0 && (
                                                        <View className="goods-tax">
                                                            <Text className="goods-tax-text">税费</Text>
                                                            <Text className="goods-tax-text">
                                                                ¥{prodItem.tax / 100}
                                                            </Text>
                                                        </View>
                                                    )}
                                                </View>
                                            </View>
                                        </View>
                                    );
                                })}
                            </View>
                            <View className="info_block">
                                <View className="item">
                                    <Text className="title">运费:</Text>
                                    <Text>{shopItem.shipFee / 100}</Text>
                                </View>
                            </View>
                            <View className="popup-item">
                                <Text className="item-title">备注</Text>
                                <View
                                    className="item-right-icon"
                                    onClick={() => {
                                        let str = shopItem.buyerNote;
                                        Dialog_.confirm({
                                            title: "备注",
                                            zIndex: 4100,
                                            message: (
                                                <Field
                                                    type="textarea"
                                                    value={shopItem.buyerNote}
                                                    style="width:100%"
                                                    placeholder-style="color:#CFCFCF"
                                                    placeholder="点此给商家备注"
                                                    onChange={e => {
                                                        //@ts-ignore
                                                        const { value } = e.target;
                                                        // setNote(value);
                                                        shopItem.buyerNote = value;
                                                        // comfireStore.list = [...list];
                                                        // currentNote.current = value;
                                                    }}
                                                />
                                            ),
                                        }).then(value => {
                                            if (value === "confirm") {
                                                // shopItem.buyerNote = value;
                                                comfireStore.list = [...list];
                                            } else {
                                                shopItem.buyerNote = str;
                                                // setNote(note);
                                                comfireStore.list = [...list];
                                            }
                                        });
                                    }}
                                >
                                    {shopItem.buyerNote || "无备注"}
                                </View>
                            </View>
                            <View className="order_info">
                                <View className="block">
                                    <View className="left">小计:</View>
                                    {comfireStore.exchange == "" ? (
                                        <View className="price">
                                            <Text style="font-size: 24rpx;">￥</Text>
                                            {/* {(prodItem.fee) / 100} */}
                                            {shopItem.shopOrderFeeDetailDTO.actualPrice}
                                        </View>
                                    ) : (
                                        <View className="price">
                                            {(prodItem.fee + (comfireStore.shipFee - shopItem.shipFee)) / 100} 积分
                                        </View>
                                    )}
                                </View>
                            </View>
                        </View>
                    </View>
                );
            })}
            {list.length ? (
                <View className="prod_item">
                    <View className="prod_item-title">价格明细</View>
                    <View className="info_block price-total">
                        <View className="item">
                            <Text className="title">商品总价</Text>
                            <Text className="cost">¥{priceTotalObj.itemTotalPrice}</Text>
                        </View>
                        <View className="item">
                            <Text className="title">运费</Text>
                            <Text className="text1">¥{priceTotalObj.shippingPrice}</Text>
                        </View>
                        {list[0].richSkus[0].sku.type !== 0 && (
                            <View className="item">
                                <Text className="title">税费</Text>
                                <Text className="text1">¥{priceTotalObj.taxPrice}</Text>
                            </View>
                        )}
                        <View className="item">
                            <View
                                onClick={() => {
                                    DDYToast.info("跨境订单大于5元的金额才可使用优惠");
                                }}
                                style={{
                                    display: "flex",
                                    flexDirection: "row",
                                    alignItems: "center",
                                    justifyContent: "center",
                                }}
                            >
                                <Text className="title">共减</Text>
                                {toJS(comfireStore.primePrice) === priceTotalObj.discountPrice && (
                                    <View
                                        style={{
                                            padding: "3px 10px",
                                            color: "#f50e0c",
                                            fontSize: "12px",
                                            borderRadius: "10px",
                                            marginLeft: "10px",
                                            backgroundColor: "#f5edec",
                                        }}
                                    >
                                        已选推荐优惠
                                    </View>
                                )}
                                <Icon name="question-o" size="14px" style={{ color: "#f50e0c", marginLeft: "10px" }} />
                            </View>

                            <Text className="text3">-¥{priceTotalObj.discountPrice}</Text>
                        </View>
                        <View
                            className={`item ${!comfireStore.initCouponStatus ? "item-gray" : ""}`}
                            onClick={() => {
                                if (!comfireStore.initCouponStatus) return;
                                toNewCoupon();
                            }}
                        >
                            <Text className="title1">优惠券:</Text>
                            <View className="text2">
                                -¥{priceTotalObj.couponPrice}
                                <Icon name="arrow" size="12px" />
                            </View>
                        </View>
                        <View
                            className={`item ${
                                !comfireStore.usableGiftPrice || comfireStore.usableGiftPrice === 0 ? "item-gray" : ""
                            }`}
                            onClick={() => {
                                if (!comfireStore.usableGiftPrice || comfireStore.usableGiftPrice === 0) {
                                    return;
                                }
                                setGiftShow(true);
                            }}
                        >
                            <Text className="title1">福豆:</Text>
                            <View className="text2">
                                -¥{priceTotalObj.giftPrice}({priceTotalObj.giftBeanNum || 0}个)
                                <Icon name="arrow" size="12px" />
                            </View>
                        </View>
                        <View
                            className={`item ${
                                !comfireStore.usableCashPrice || comfireStore.usableCashPrice === 0 ? "item-gray" : ""
                            }`}
                            onClick={() => {
                                if (!comfireStore.usableCashPrice || comfireStore.usableCashPrice === 0) {
                                    return;
                                }
                                setCashShow(true);
                            }}
                        >
                            <Text className="title1">福卡:</Text>
                            <View className="text2">
                                -¥{priceTotalObj.cashPrice}
                                <Icon name="arrow" size="12px" />
                            </View>
                        </View>
                    </View>
                </View>
            ) : null}

            <SelectGiftModal
                open={giftShow}
                closeFn={obj => {
                    if (obj) {
                        comfireStore.giftPriceFlag = obj.bol;
                        comfireStore.initOrder("change", route);
                    }
                    setGiftShow(false);
                }}
                initSelect={comfireStore.giftPriceFlag}
                goodList={toJS(comfireStore.goodList)}
                itemTotalPrice={priceTotalObj.itemTotalPrice}
                giftPrice={comfireStore.usableGiftPrice}
            />
            <SelectCashModal
                open={cashShow}
                closeFn={obj => {
                    if (obj) {
                        comfireStore.cashPriceFlag = obj.bol;
                        comfireStore.initOrder("change", route);
                    }
                    setCashShow(false);
                }}
                initSelect={comfireStore.cashPriceFlag}
                cashPrice={comfireStore.usableCashPrice}
            />
            <Dialog_ />
        </View>
    ));
});
