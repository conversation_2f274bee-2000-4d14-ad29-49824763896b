import api from "@/utils/api";
import { ADDRESS_ID } from "@/utils/constant";
import { getStorage } from "@/utils/storage";
import { View, Text } from "@tarojs/components";
import { useDidShow } from "@tarojs/taro";
import { useEffect, useState } from "react";
import comfireStore from "../store";
import { Cell, Icon } from "@antmjs/vantui";
import { DDYNavigateTo } from "@/utils/route";
export interface AddressObj {
    id: number;
    receiveUserName: string;
    mobile: string;
    province: string;
    city: string;
    region: string;
    street?: string;
    detail: string;
}
import "../index.less";
import { toJS } from "mobx";
import { observer, useObserver } from "mobx-react-lite";

export default observer(() => {
    // const store = useLocalStore(() => comfireStore);
    const store = comfireStore;
    const address = store.address as AddressObj;
    const openAddressSelect = () => {
        console.log("openAddressSelect:", toJS(store));
        DDYNavigateTo({
            url: "/pages/address/index?type=order",
        });
    };

    return useObserver(() => (
        <View className="address" onClick={openAddressSelect}>
            {!store.isSelectAddress ? (
                <Cell title="设置收货地址" isLink border={false} />
            ) : (
                <View className="address_info">
                    <View className="user_info">
                        <Text className="name">{address.receiveUserName}</Text>
                        <Text className="phone">{address.mobile}</Text>
                    </View>
                    <View className="active_address">
                        <Text className="active_address_text">
                            {address.province}
                            {address.city}
                            {address.region}
                            {address.street ? address.street : ""}
                            {address.detail}
                        </Text>
                        <Icon name="arrow" className="active_address_icon" />
                    </View>
                </View>
            )}
        </View>
    ));
});
