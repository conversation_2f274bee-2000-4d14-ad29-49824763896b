.gift-rect {
    position: relative;
    padding-bottom: calc(120px + env(safe-area-inset-bottom));
    .gift-goods {
        position: relative;
        min-height: 200px;
        .gift-scroll {
            width: 100%;
            height: 100px;
            padding: 10px 0;
            background-color: #999999;
        }
        .gift-checked {
            position: absolute;
            height: 80px;
            display: flex;
            flex-direction: row;
            align-items: center;
            right: 24px;
            top: 10px;
            .gift-checked-text {
                color: #f50e0c;
                font-size: 28px;
                line-height: 36px;
                margin-right: 12px;
            }
        }
        .goods-total-item {
            display: flex;
            align-items: center;
            flex-direction: row;
            padding: 20px;
            .goods-discount-price {
                border: 1px solid #e8b04e;
                border-radius: 4px;
                padding: 4px 10px;
                font-size: 24px;
                line-height: 26px;
            }
            .goods-total-info {
                padding: 4px 10px;
                font-size: 24px;
                line-height: 26px;
                color: #333;
            }
        }
    }
    .un-used {
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: space-between;
        border-top: 1px solid #f5f5f5;
        padding: 30px 24px;
        .un-used-text {
            // display;
            color: #333333;
            padding-left: 24px;
        }
    }
    .gift-btn {
        position: absolute;
        right: 24px;
        background-color: #f50e0c;
        color: #fff;
        min-width: 170px;
        bottom: calc(20px + env(safe-area-inset-bottom));
    }
}
