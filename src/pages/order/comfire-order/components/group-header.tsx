import infoStore from "@/store/info-store";
import { View, Text, Image } from "@tarojs/components";
import { useRouter } from "@tarojs/taro";
import { toJS } from "mobx";
import { useEffect } from "react";

export default () => {
    const router = useRouter();
    const { groupAction, activityName } = router.params;

    const userinfo = infoStore.newUserProfile.avatarUrl;

    useEffect(() => {
        // router
        console.log("router:", toJS(infoStore));
    }, []);

    return (
        <View className="group-header">
            <View className="group-header-title">您正在参加【{activityName}】的组团活动！</View>
            <View className="group-header-content">
                <Image src={infoStore.newUserProfile.avatarUrl} className="group-header-avator" />
                {groupAction === "7" || groupAction === "10" ? (
                    <View className="group-status">正在参团中</View>
                ) : (
                    <View className="group-status">正在开团中</View>
                )}
            </View>
        </View>
    );
};
