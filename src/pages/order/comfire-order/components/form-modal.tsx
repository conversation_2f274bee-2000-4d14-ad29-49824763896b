import RealNameForm from "@/components/real-name-form";
import { ActionSheet, Icon, NoticeBar } from "@antmjs/vantui";
import { View, Text } from "@tarojs/components";
import { useState } from "react";

export default ({ show, closeFn, onOk }) => {
    return (
        <>
            <ActionSheet
                show={show}
                title="实名认证"
                onClose={() => {
                    closeFn();
                }}
            >
                <View style={{ padding: "20rpx 20rpx 100px 20px" }}>
                    <View
                        style={{
                            padding: "10rpx 30rpx 10rpx 48rpx",
                            borderRadius: "8rpx",
                            position: "relative",
                            backgroundColor: "#FBF4FF",
                        }}
                    >
                        <Icon
                            name="info-o"
                            size="32rpx"
                            color="#9023d7"
                            className="icon"
                            style={{
                                position: "absolute",
                                left: "15rpx",
                                top: "14rpx",
                            }}
                        ></Icon>
                        <Text
                            style={{
                                color: "#9023d7",
                                fontSize: "24rpx",
                                lineHeight: "32rpx",
                            }}
                        >
                            因您购买的跨境商品清关需要，请填写您将用于支付的付款账号的实名信息
                        </Text>
                    </View>
                    <RealNameForm
                        onOk={() => {
                            onOk();
                        }}
                        onFail={() => {}}
                    />
                </View>
            </ActionSheet>
        </>
    );
};
