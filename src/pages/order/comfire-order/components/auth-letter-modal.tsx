import { ActionSheet, Button } from "@antmjs/vantui";
import { View } from "@tarojs/components";

export default ({ show, closeFn, onOk }) => {
    return (
        <>
            <ActionSheet
                show={show}
                title="进口个人委托申报委托函"
                onClose={() => {
                    closeFn();
                }}
            >
                <View className="isEntrust_con">
                    本人承诺所购买商品系个人合理自用，针对保税区发货的各类商品，现委托商家代理电报、代缴税款等通关事宜，本人保证将遵守《海关法》等国家相关法律法规的规定，保证所提供的身份信息和收货信息真实完整，无侵犯他人权益之行为。以上委托系本人真实意愿表示，本人愿意接受海关、检验检疫机构及其他监管部门的监管，并承担相应的法律责任。
                    <Button type="primary" color="red" style={{ marginTop: "10px" }} round block onClick={onOk}>
                        同意
                    </Button>
                </View>
            </ActionSheet>
        </>
    );
};
