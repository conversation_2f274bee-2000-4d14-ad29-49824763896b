import { ActionSheet, Checkbox } from "@antmjs/vantui";
import { View, Image, Text, Button } from "@tarojs/components";
import { useEffect, useState } from "react";
import "./select-gift-modal.less";
import newAPi from "@/utils/newApi";

export default ({ open, closeFn, cashPrice, initSelect }) => {
    const [totalCashPrice, settotalCashGift] = useState(0);
    const [select, setSelect] = useState(false);

    useEffect(() => {
        if (open) {
            getData();
        }
    }, [open]);
    useEffect(() => {
        setSelect(initSelect);
    }, [initSelect]);
    const getData = () => {
        newAPi
            .getUserAmount({
                method: "POST",
                data: {},
            })
            .then(res => {
                settotalCashGift(res.cashAmount);
            });
    };
    return (
        <ActionSheet
            show={open}
            title="福卡详情"
            onClose={() => {
                closeFn();
            }}
        >
            <View className="gift-rect">
                <View className="gift-goods">
                    {/* <ScrollView scrollX={true} className="gift-scroll"> */}
                    <View style={{ display: "flex", flexDirection: "column", flexWrap: "nowrap", paddingLeft: "24px" }}>
                        <View>福卡</View>
                        <View>余额：{totalCashPrice}</View>
                    </View>
                    {/* </ScrollView> */}
                    <View
                        className="gift-checked"
                        onClick={() => {
                            setSelect(!select);
                        }}
                    >
                        <Text className="gift-checked-text">{cashPrice}</Text>
                        {select ? <Checkbox value={true} name={true} /> : <Checkbox value={false} name={true} />}
                    </View>
                </View>
                <View
                    className="un-used"
                    onClick={() => {
                        setSelect(!select);
                    }}
                >
                    <Text className="un-used-text">不使用</Text>
                    {!select ? <Checkbox value={true} name={true} /> : <Checkbox value={false} name={true} />}
                </View>
                <Button
                    className="gift-btn"
                    onClick={() => {
                        closeFn({ bol: select });
                    }}
                >
                    确定
                </Button>
            </View>
        </ActionSheet>
    );
};
