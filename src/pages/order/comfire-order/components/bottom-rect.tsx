import { Cell, CellGroup, Dialog, DialogProps, Radio, RadioGroup, SubmitBar } from "@antmjs/vantui";
import { View, Text } from "@tarojs/components";
import FormModal from "./form-modal";
import { useEffect, useRef, useState } from "react";
import { DDYNavigateTo } from "@/utils/route";
import DDYToast from "@/utils/toast";
import Taro, { useDidShow } from "@tarojs/taro";
import { DDYObject } from "src/type/common";
import api from "@/utils/api";
import infoStore from "@/store/info-store";
import { getStorage, setStorage } from "@/utils/storage";
import { GUIDER_INFO, IDENTITY, SIGN_SCCESS_URL, STORE_ID, SUB_STORE_ID } from "@/utils/constant";
import comfireStore from "../store";
import { AddressObj } from "./show-address";
import AuthLetterModal from "./auth-letter-modal";
import AuthConsumerModal from "./auth-consumer-modal";
import { observer, useObserver } from "mobx-react-lite";
import { toJS } from "mobx";
import { isAlipay, queryInfo, saveSignback } from "@/utils/common";
import { ishasStreetByAreaCode } from "@/utils/judge";
import DtModal, { DtModalRef } from "@/components/dt-modal";
const jdBtnName = {
    1: "立即购买",
    2: "该地区无库存",
    3: "库存查询中...",
};
const Dialog_ = Dialog.createOnlyDialog();

export default observer(() => {
    const [authShow, setAuthShow] = useState(false);
    const [entrustShow, setEntrustShow] = useState(false);
    const [consumerShow, setConsumerShow] = useState(false);
    // const [jdQStatus, setJdQStatus] = useState(3);
    const isAgreeEntrust = useRef(false);
    const isAgreeInform = useRef(false);
    const orderInfo = useRef();
    const dtModalRef = useRef<DtModalRef>();

    const [types, setTypes] = useState([]);

    const replacePay = () => {
        api.getPayerInfo({
            method: "GET",
            data: {},
        }).then(res => {
            if (!Object.keys(res).length) {
                dtModalRef.current
                    ?.confirm({
                        title: (
                            <View style={{ textAlign: "left", paddingLeft: "20px", paddingRight: "20px" }}>
                                当前还未维护代付订购人实名身份信息，请先添加
                            </View>
                        ),
                        confirmButtonText: "取消",
                        cancelButtonText: "添加实名",
                        cancelButtonColor: "red",
                    })
                    .then(res => {
                        if (res !== "confirm") {
                            DDYNavigateTo({
                                url: "/pages/realname-certification/index?isReplay=true",
                            });
                        }
                    });
            } else {
                dtModalRef.current
                    ?.confirm({
                        title: (
                            <View style={{ textAlign: "left", paddingLeft: "20px", paddingRight: "20px" }}>
                                请分享给当前订购人代付,若更换代付人请先修改实名
                            </View>
                        ),
                        message: (
                            <>
                                <View style={{ textAlign: "left" }}>
                                    订购人:<Text style={{ color: "#333" }}>{res.friendName}</Text>
                                </View>
                                <View style={{ textAlign: "left" }}>
                                    订购人身份证:<Text style={{ color: "#333" }}>{res.friendIdentityCard}</Text>
                                </View>
                            </>
                        ),
                        confirmButtonText: "继续分享",
                        cancelButtonText: "修改实名",
                        cancelButtonColor: "red",
                        closeOnClickOverlay: true,
                    })
                    .then(async data => {
                        console.log("res:", res);
                        if (data !== "confirm") {
                            DDYNavigateTo({
                                url: `/pages/realname-certification/index?isReplay=true&name=${res.friendName}&idCardNo=${res.friendIdentityCard}`,
                            });
                        } else {
                            // ...
                            // 正式下单
                            // DDYNavigateTo({
                            //     url: "/pages/order/invite-pay/index?id=i"
                            // })
                            let extra = null;
                            console.log("sss");
                            if (!infoStore.isAuthed()) {
                                await infoStore.getStoreInfo();
                                extra = getExtra();
                            }
                            const address = comfireStore.address as AddressObj;
                            let outExtra: DDYObject = {};
                            extra = getExtra();
                            // 购物车过来的订单
                            if (comfireStore.orderGoodsList.length > 0) {
                                // 购物车过来的，提交订单要删除购物车
                                outExtra = { CartsDelete: "true" };
                            }
                            api.createRePayOrder({
                                method: "POST",
                                data: {
                                    receiverInfoId: address.id,
                                    submittedSkusByShops: [
                                        {
                                            shopId: getStorage(STORE_ID),
                                            buyerNote: toJS(comfireStore.buyerNote[0]),
                                            invoiceId: 341,
                                            shipmentPromotionId: null,
                                            submittedSkus: comfireStore.getGoodList(),
                                            extra: extra,
                                        },
                                    ],
                                    channel: 2,
                                    payType: 1, //支付方式 1-在线支付
                                    extra: outExtra,
                                    paymentWay: comfireStore.payMethods,
                                    shareShopOrderId: null,
                                    payerName: res.friendName,
                                    payerNo: res.friendIdentityCard,
                                },
                            }).then(res => {
                                DDYNavigateTo({
                                    url: `/pages/order/invite-pay/index?id=${res.shareQr}`,
                                });
                            });
                        }
                    });
            }
        });
    };

    // const get
    const onSubmit = async () => {
        //@ts-ignore
        if (!comfireStore?.address?.id) {
            DDYToast.info("你未设置收货地址，请设置地址");
            DDYNavigateTo({
                url: "/pages/address?type=order",
            });
            return false;
        }

        let hasStreetFlag = await ishasStreetByAreaCode(comfireStore?.address?.regionId);

        // 没有街道信息
        if (hasStreetFlag && (!comfireStore?.address?.street || !comfireStore?.address?.streetId)) {
            Dialog_.confirm({
                message: "当前四级地址为空，请先维护收货地址的街道信息",
                confirmButtonText: "去修改",
                cancelButtonText: "知道了",
                onConfirm: () => {
                    console.log("传入的地址信息", comfireStore?.address);
                    DDYNavigateTo({
                        url: `/pages/address/add-address/index?currentType=edit&type=order&receiverInfo=${encodeURIComponent(
                            JSON.stringify(comfireStore?.address),
                        )}&autoOpenFlag=1&needDelta=1`,
                        events: {
                            selectAddress: ({ id }) => {
                                console.log("selectAddress", id);

                                comfireStore.initData(route, "change");
                            },
                        },
                    });
                },
            });
            return false;
        }
        if (comfireStore.payMethods === "friendPay") {
            replacePay();

            return;
        }

        // 若是保税产品且没录入实名认证则要添加实名认证信息才能支付牛很鲜潮汕牛肉火锅
        // 获取实名认证信息
        const userCertInfo = await api.getUserCertification({
            data: {
                id: 0,
            },
            method: "GET",
            filterCheck: true,
        });
        console.log(userCertInfo);
        if (!userCertInfo) {
            setAuthShow(true);
            return;
        }

        const agrees = await api.getHasUserAgreeTerms({ method: "GET", data: {} });
        // 如果未同意协议
        if (!agrees) {
            if (isAgreeInform.current == false) {
                setConsumerShow(true);
                return;
            }
            if (isAgreeEntrust.current == false) {
                setEntrustShow(true);
                return;
            }
        }
        DDYToast.showLoading("跨境订单支付中，请稍后");
        goToPay();
    };

    const getExtra = () => {
        let extra;
        const subStoreId = getStorage(SUB_STORE_ID);
        // const subStoreInfo = infoStore.subStore;
        const guiderId = getStorage(GUIDER_INFO);
        console.log("infoStore.isAuthed():", infoStore.isAuthed());
        if (subStoreId != 0 && infoStore.isAuthed() && (!guiderId || guiderId == 0)) {
            extra = {
                outFrom: "subStore", //门店订单
                outShopId: subStoreId, //门店id
            };
        } else {
            extra = {
                outFrom: "subStore", //门店订单
            };
        }

        if (guiderId || guiderId != 0) {
            extra = {
                outFrom: "subStore", //门店订单
                outShopId: subStoreId, //门店id
                refererId: guiderId,
            };
        }
        return extra;
    };

    const goToPay = async () => {
        let extra = getExtra();
        if (!infoStore.isAuthed()) {
            await infoStore.getStoreInfo();
            extra = getExtra();
        }
        let outExtra: DDYObject = {};
        // 购物车过来的订单
        if (comfireStore.orderGoodsList.length > 0) {
            // 购物车过来的，提交订单要删除购物车
            outExtra = { CartsDelete: "true" };
        }
        // 如果是积分商品，则加上订单积分标识
        if (comfireStore.exchange == "integral") {
            extra.OrderOutType = "integral"; // 标识积分订单
        }

        // if (formId.current) {
        //     extra.formId = formId.current;
        // }
        DDYToast.showLoading("跨境订单支付中，请稍后");
        const address = comfireStore.address as AddressObj;
        // console.log("buyerNote:", comfireStore.buyerNote);

        api.submitOrder({
            method: "POST",
            data: {
                receiverInfoId: address.id,
                submittedSkusByShops: [
                    {
                        shopId: getStorage(STORE_ID),
                        buyerNote: toJS(comfireStore.buyerNote[0]),
                        invoiceId: 341,
                        shipmentPromotionId: null,
                        submittedSkus: comfireStore.getGoodList(),
                        extra: extra,
                    },
                ],
                channel: 2,
                payType: 1, //支付方式 1-在线支付
                extra: outExtra,
            },
            filterCheck: true,
        })
            .then(res => {
                if (res?.error) {
                    DDYToast.info(res.message);
                    return;
                }
                if ("success" in res && !res.success) {
                    DDYToast.info(res.errorMsg);
                    return;
                }
                const pages = Taro.getCurrentPages();
                const current = pages[pages.length - 1];
                const eventChannel = current.getOpenerEventChannel();
                eventChannel.emit("update", true);
                api.payOrder({
                    data: {
                        orderIds: res,
                        // channel: comfireStore.exchange === "integral" ? "Integral-pay" : "wechatpay-jsapi", // 如果是积分商品，则支付渠道选积分
                        usageChannel: 2,
                    },
                    method: "POST",
                    filterCheck: true,
                }).then(response => {
                    payBandle(response, res[0]);
                });
            })
            .catch(err => {});
    };

    const payBandle = (response, id) => {
        if (response.error === "需完善通商云账户信息") {
            setStorage(SIGN_SCCESS_URL, "/pages/order/order-detail/index?id=" + id);
            DDYToast.showModal({
                title: "实名制注册，请您先【填写实名信息】、【绑定银行卡】并【签署协议】",
                success: res => {
                    if (res.confirm) {
                        queryInfo({
                            go: true,
                            goSign: () => {
                                saveSignback();
                                DDYNavigateTo({
                                    url: "/pages/my/bank-list/index",
                                });
                            },
                        });
                    } else {
                    }
                },
            });
            DDYToast.hideLoading();
            return;
        }
        const { result } = response;
        if (result.channel == "Integral-pay" && result.redirectNow) {
            // 积分支付
            setTimeout(() => {
                DDYToast.hideLoading();
                comfireStore.resetEmpty();
                DDYNavigateTo({
                    url: "/pages/order/pay_success/index?success=0&orderNo=" + id,
                });
            }, 1000);
            return;
        }

        if (result.invokeMiniProgram) {
            DDYToast.hideLoading();
            Taro.openEmbeddedMiniProgram({
                appId: "wxef277996acc166c3",
                extraData: result.redirectInfo,
            });
            orderInfo.current = id;
            return;
        }
        if (result.invokeH5 && result.requestOriginalUrl) {
            // 通联测试环境h5支付
            try {
                const requestOriginalUrl = JSON.parse(result.requestOriginalUrl);
                DDYToast.showModal({
                    title: "是否使用微信h5支付",
                    content: requestOriginalUrl.payInfo,
                    success: res => {
                        if (res.confirm) {
                            Taro.setClipboardData({
                                data: requestOriginalUrl.payInfo,
                                success(res) {
                                    DDYToast.info("复制成功");
                                },
                            });
                        }
                    },
                });
            } catch (error) {}
            DDYToast.hideLoading();
            return;
        }
        // 支付宝支付
        if (isAlipay() && result.channel === "allinpay-yst" && result.redirectInfo) {
            Taro.tradePay({
                tradeNO: result.redirectInfo,
                success(res) {
                    DDYToast.hideLoading();
                    if (res.resultCode === "9000") {
                        setTimeout(() => {
                            comfireStore.resetEmpty();
                            DDYNavigateTo({
                                url: "/pages/order/pay_success/index?success=0&orderNo=" + id,
                            });
                        }, 1000);
                    } else {
                        DDYToast.info(res.memo);
                        setTimeout(() => {
                            gotoDetail(id);
                        }, 1000);
                    }
                },
                fail() {
                    DDYToast.info("支付失败");
                    DDYToast.hideLoading();
                    gotoDetail(id);
                },
            });
            return;
        }
        // 微信支付
        if (result.redirectInfo) {
            try {
                // 微信支付
                const payObj: DDYObject = JSON.parse(result.redirectInfo);
                if (payObj) {
                    Taro.requestPayment({
                        //@ts-ignore
                        appId: payObj.appId,
                        timeStamp: payObj.timeStamp,
                        nonceStr: payObj.nonceStr,
                        package: payObj.package,
                        signType: payObj.signType,
                        paySign: payObj.paySign,
                        success(successRes) {
                            DDYToast.hideLoading();
                            setTimeout(() => {
                                comfireStore.resetEmpty();
                                DDYNavigateTo({
                                    url: "/pages/order/pay_success/index?success=0&orderNo=" + id,
                                });
                            }, 1000);
                        },
                        fail(failRes) {
                            DDYToast.hideLoading();
                            /**
                             * 本因在调用支付之前判断的实名认证失败不知为何是在支付失败后提示
                             * */
                            if (payObj.code === "ID-ERROR") {
                                DDYToast.info("支付失败：实名认证失败");
                            } else {
                                DDYToast.info("支付失败");
                            }
                            gotoDetail(id);
                        },
                    });
                } else {
                    /**
                     * 很神奇的失败逻辑
                     */
                    DDYToast.hideLoading();
                    DDYToast.info("支付失败");
                    gotoDetail(id);
                }
            } catch (e) {
                /**
                 * 很神奇的失败逻辑
                 */
                DDYToast.hideLoading();
                DDYToast.info("支付失败");
                gotoDetail(id);
            }
            return;
        }

        DDYToast.hideLoading();
        DDYToast.info("未知支付方式");
        gotoDetail(id);
    };

    const agreeOut = () => {
        setConsumerShow(false);
        setEntrustShow(false);
        api.getAgreeConsumerTerms({ method: "POST", data: {} }).then(res => {});
    };
    const onClickInform = () => {
        setConsumerShow(true);
    };

    const onClickEntrust = () => {
        setEntrustShow(true);
    };

    const gotoDetail = orderNo => {
        // this.initOrder();
        setTimeout(() => {
            comfireStore.resetEmpty();
            DDYNavigateTo({
                url: "/pages/order/order-detail/index?id=" + orderNo,
            });
            orderInfo.current = undefined;
        }, 100);
    };

    const getTypesData = () => {
        api.getPayTypes({
            method: "GET",
            data: {},
        }).then(res => {
            console.log("res:", res);
            setTypes(res);
            comfireStore.setPayMethods(res[0].id);
        });
    };

    useDidShow(() => {
        let options = Taro.getEnterOptionsSync();
        if (options.scene === 1038 && options.referrerInfo.appId == "wxef277996acc166c3") {
            // 代表从收银台小程序返回
            let extraData = options.referrerInfo.extraData;
            DDYToast.hideLoading();
            if (!extraData) {
                // "当前通过物理按键返回，未接收到返参，建议自行查询交易结果";
                console.log("当前通过物理按键返回，未接收到返参，建议自行查询交易结果");
                orderInfo.current && gotoDetail(orderInfo.current);
            } else {
                DDYToast.hideLoading();
                if (extraData.code == "success") {
                    // "支付成功";
                    console.log("支付成功");
                    orderInfo.current &&
                        DDYNavigateTo({
                            url: "/pages/order/pay_success/index?success=0&orderNo=" + orderInfo.current,
                        });
                    orderInfo.current = undefined;
                } else if (extraData.code == "cancel") {
                    // "支付已取消";
                    console.log("支付已取消");
                    orderInfo.current && gotoDetail(orderInfo.current);
                    orderInfo.current = undefined;
                } else {
                    // "支付失败：" + extraData.errmsg;
                    console.log("支付失败");
                    orderInfo.current && gotoDetail(orderInfo.current);
                    orderInfo.current = undefined;
                }
            }
        }
    });

    useEffect(() => {
        getTypesData();
    }, []);

    // useEffect(() => {
    //     // console.log("comfireStore:", store);
    //     // if (Object.keys(store.address).length != 0 && store.list.length != 0) {
    //     //     getJdStatus(store.address, store.list);
    //     // }
    // }, [store]);
    const store = toJS(comfireStore);
    return useObserver(() => (
        <>
            {types.length > 0 ? (
                <View className="order-card">
                    <View className="order-card-title">支付方式</View>
                    <RadioGroup value={comfireStore.payMethods}>
                        <CellGroup>
                            {types.map((item: DDYObject) => {
                                return (
                                    <Cell
                                        title={item.name}
                                        clickable
                                        onClick={() => {
                                            comfireStore.setPayMethods(item.id);
                                        }}
                                        renderRightIcon={<Radio name={item.id}></Radio>}
                                    ></Cell>
                                );
                            })}
                        </CellGroup>
                    </RadioGroup>
                </View>
            ) : null}

            <View className="bottom-tips">
                提交订单则表示您已同意
                <Text onClick={onClickInform} className="tips-btn">
                    《消费者告知书》
                </Text>
                及
                <Text className="tips-btn" onClick={onClickEntrust}>
                    《进口个人委托申报委托函》
                </Text>
            </View>

            <SubmitBar
                price={comfireStore.actualPrice * 100}
                decimalLength={2}
                // buttonText="提交订单"
                buttonText={jdBtnName[store.jdQStatus]}
                disabled={store.jdQStatus !== 1}
                onSubmit={onSubmit}
            />

            {/** 实名认证 */}
            <FormModal
                show={authShow}
                closeFn={() => {
                    setAuthShow(false);
                }}
                onOk={() => {
                    console.log("实名认证通过");
                    setAuthShow(false);
                    goToPay();
                }}
            />

            {/**  进口个人委托申报委托函 */}
            <AuthLetterModal
                show={entrustShow}
                closeFn={() => {
                    setEntrustShow(false);
                }}
                onOk={() => {
                    setEntrustShow(false);
                    isAgreeInform.current = true;
                    // if (!isAgreeEntrust.current) {
                    //     setConsumerShow(true);
                    // } else {
                    agreeOut();
                    // }
                }}
            />

            {/** 消费者告知书 */}
            <AuthConsumerModal
                show={consumerShow}
                closeFn={() => {
                    setConsumerShow(false);
                }}
                onOk={() => {
                    setConsumerShow(false);
                    isAgreeEntrust.current = true;
                    // if (!isAgreeInform.current) {
                    //     setEntrustShow(true);
                    // } else {
                    agreeOut();
                    // }
                }}
            />
            {/* <Dialog id="vanDialog2" /> */}
            {/* 弹窗内容 */}
            <Dialog_ />
            <DtModal ref={dtModalRef} />
        </>
    ));
});
