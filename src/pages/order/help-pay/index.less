.help-pay {
    padding: 24px;
    font-size: 24px;
    .notice-head {
        display: flex;
        flex-direction: row;
        align-items: center;
        padding: 0 40px;
        justify-content: center;
        .notice-img {
            width: 88px;
            height: 88px;
        }
        .speech-bubble {
            position: relative;
            background: #f8a0a0; /* 对话框背景颜色 */
            border-radius: 40px; /* 圆角 */
            padding: 20px 40px; /* 内边距 */
            // max-width: 300px; /* 最大宽度 */
            margin: 40px; /* 外边距 */
            .speech-text {
                /* 这里可以添加文本样式 */
                font-size: 32px;
                font-weight: bold;
                color: #fff;
                text-align: center;
            }
        }

        /* 创建对话框的小尾巴 */
        .speech-bubble::after {
            content: "";
            position: absolute;
            bottom: -38px; /* 尾巴距离对话框底部的距离 */
            left: 31px; /* 尾巴距离对话框左边的距离 */
            width: 0;
            height: 0;
            border: 20px solid transparent; /* 透明边框 */
            border-top-color: #f8a0a0; /* 尾巴颜色 */
            border-top-width: 30x; /* 尾巴宽度 */
            // background: #f8a0a0; /* 对话框背景颜色 */
            border-left-width: 0;
        }
    }
    .notice {
        background-color: #fff;
        border-radius: 8px;
        padding: 30px 24px;
        margin-top: 40px;
        margin-bottom: 180px;
        .title {
            font-size: 32px;
            font-weight: bold;
            margin-bottom: 30px;
            color: #333;
        }

        .item {
            display: block;
            margin-bottom: 10px;
            color: #666;
            font-size: 28px;
        }
    }
    .order-payment {
        // padding: 40px;
        font-size: 28px;

        .amount {
            //   margin-bottom: 40px;
            padding: 20px 20px 0 20px;
            text-align: left;
            border-top-left-radius: 30px;
            border-top-right-radius: 30px;
            background-color: #f5a655;

            .label {
                font-size: 32px;
                color: #fff;
                margin-bottom: 20px;
            }

            .value {
                font-size: 48px;
                font-weight: bold;
                color: #fff;
            }
        }

        .amount-notice {
            padding: 20px 0;
            text-align: left;
            border-top: 2px solid #fff;

            .text {
                font-size: 24px;
                color: #fff;
            }
        }
        .shop-rect {
            background-color: #fff;
            padding: 10px;
            .product {
                .label {
                    font-size: 28px;
                    color: #333;
                    margin-bottom: 16px;
                }

                .name {
                    font-size: 32px;
                    color: #333;
                }
            }
            .shop-list {
                height: 262px;
                overflow-y: scroll;
                margin: 14px;
                background-color: #f5f5f5;
                .good-list {
                    margin-top: 10px;
                    .good-item {
                        display: flex;
                        flex-direction: row;
                        align-items: start;
                        margin-bottom: 20px;
                        .good-img {
                            width: 180px;
                            height: 180px;
                            margin-right: 18px;
                            border-radius: 10px;
                        }
                    }
                }
            }
        }
    }
    .actions {
        padding: 20px 0;
        display: flex;
        position: fixed;
        left: 0;
        right: 0;
        bottom: calc(env(safe-area-inset-bottom));
        align-items: center;
        justify-content: center;
        .button {
            background-color: #ff1a26;
            color: #fff;
            border-radius: 88px;
            font-size: 32px;
            width: 70%;
        }
    }
}
