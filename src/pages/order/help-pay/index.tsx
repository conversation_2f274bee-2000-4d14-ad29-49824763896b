import Taro, { useRouter } from "@tarojs/taro";
import { View, Text, Image, Button } from "@tarojs/components";
import "./index.less";
import ShopItemList from "@/components/shop-item-list";
import api from "@/utils/api";
import { useEffect, useRef, useState } from "react";
import { getStorage } from "@/utils/storage";
import { APP_ID, PROJECT_ID } from "@/utils/constant";
import { projectId } from "src/config";
import DDYToast from "@/utils/toast";
import { DDYNavigateTo } from "@/utils/route";
import { DDYObject } from "src/type/common";
import { isAlipay } from "@/utils/common";
import { getSessionId } from "@/utils/server/session";
import { jumpLogin } from "@/utils/PageUtils";
import { loginCheck } from "@/utils/ylogin";
import { Empty } from "@antmjs/vantui";
// import Empty from "src/pages/shop_cart/components/empty";
definePageConfig({
    // navigationStyle: "custom",
    navigationBarTitleText: "帮我付",
    // navigationBarTextStyle: "white"
});
const HelpPay = () => {
    const router = useRouter();
    const [price, setPrice] = useState(0.0);
    const [goods, setGoods] = useState([]);
    const [shopOrders, setShopOrders] = useState([]);
    const shareStr = decodeURIComponent(router.params.q);
    const orderIds = useRef([]);
    useEffect(() => {
        console.log(router, shareStr);
        // https://m.mall.yang800.com/store-distribution/help-pay?id=1&shareQr=B42DF98ADC-4C4A-E836-878E-75BD24EFBD
        if (shareStr) {
            const str = shareStr.split("?")?.[1]?.split("&")?.[1]?.split("=")[1];
            // console.log("arr:", arr);
            if (str) {
                getData(str);
            }
        }
    }, [shareStr]);

    useEffect(() => {
        if (isAlipay()) {
            let code = getStorage("shareQr");
            code && getData(code);
        }
    }, []);

    const handleConfirmPayment = () => {
        // if()
        if (orderIds.current.length === 0) {
            DDYToast.error("分享码已失效");
            return;
        }
        loginCheck().then(() => {
            api.payOrder({
                data: {
                    orderIds: orderIds.current,
                    usageChannel: 2,
                },
                method: "POST",
                filterCheck: true,
            }).then(response => {
                console.log("response:", response);
                if (response.error) {
                    DDYToast.info(response.message || response.error);
                }
                payBandle(response, orderIds.current[0]);
            });
        });
    };

    const payBandle = (response, id) => {
        const { result, error, message, success } = response;
        if (!success) {
            DDYToast.info(message || error);
            return;
        }
        if (result.channel == "Integral-pay" && result.redirectNow) {
            // 积分支付
            setTimeout(() => {
                DDYToast.hideLoading();
                DDYNavigateTo({
                    url: "/pages/order/pay_success/index?success=0&orderNo=" + id,
                });
            }, 1000);
            return;
        }

        if (result.invokeMiniProgram) {
            DDYToast.hideLoading();
            Taro.openEmbeddedMiniProgram({
                appId: "wxef277996acc166c3",
                extraData: result.redirectInfo,
            });
            return;
        }
        if (result.invokeH5 && result.requestOriginalUrl) {
            // 通联测试环境h5支付
            try {
                const requestOriginalUrl = JSON.parse(result.requestOriginalUrl);
                DDYToast.showModal({
                    title: "是否使用微信h5支付",
                    content: requestOriginalUrl.payInfo,
                    success: res => {
                        if (res.confirm) {
                            Taro.setClipboardData({
                                data: requestOriginalUrl.payInfo,
                                success(res) {
                                    DDYToast.info("复制成功");
                                },
                            });
                        }
                    },
                });
            } catch (error) {}
            DDYToast.hideLoading();
            return;
        }
        // 支付宝支付
        if (isAlipay() && result.channel === "allinpay-yst" && result.redirectInfo) {
            Taro.tradePay({
                tradeNO: result.redirectInfo,
                success(res) {
                    DDYToast.hideLoading();
                    if (res.resultCode === "9000") {
                        setTimeout(() => {
                            DDYNavigateTo({
                                url: "/pages/order/pay_success/index?success=0&orderNo=" + id,
                            });
                        }, 1000);
                    } else {
                        DDYToast.info(res.memo);
                        setTimeout(() => {
                            gotoDetail(id);
                        }, 1000);
                    }
                },
                fail() {
                    DDYToast.info("支付失败");
                    DDYToast.hideLoading();
                    gotoDetail(id);
                },
            });
            return;
        }
        // 微信支付
        if (result.redirectInfo) {
            try {
                // 微信支付
                const payObj: DDYObject = JSON.parse(result.redirectInfo);
                if (payObj) {
                    Taro.requestPayment({
                        //@ts-ignore
                        appId: payObj.appId,
                        timeStamp: payObj.timeStamp,
                        nonceStr: payObj.nonceStr,
                        package: payObj.package,
                        signType: payObj.signType,
                        paySign: payObj.paySign,
                        success(successRes) {
                            DDYToast.hideLoading();
                            setTimeout(() => {
                                DDYNavigateTo({
                                    url: "/pages/order/pay_success/index?success=0&orderNo=" + id,
                                });
                            }, 1000);
                        },
                        fail(failRes) {
                            DDYToast.hideLoading();
                            /**
                             * 本因在调用支付之前判断的实名认证失败不知为何是在支付失败后提示
                             * */
                            if (payObj.code === "ID-ERROR") {
                                DDYToast.info("支付失败：实名认证失败");
                            } else {
                                DDYToast.info("支付失败");
                            }
                            gotoDetail(id);
                        },
                    });
                } else {
                    /**
                     * 很神奇的失败逻辑
                     */
                    DDYToast.hideLoading();
                    DDYToast.info("支付失败");
                    gotoDetail(id);
                }
            } catch (e) {
                /**
                 * 很神奇的失败逻辑
                 */
                DDYToast.hideLoading();
                DDYToast.info("支付失败");
                gotoDetail(id);
            }
            return;
        }

        DDYToast.hideLoading();
        DDYToast.info("未知支付方式");
        gotoDetail(id);
    };

    const getData = key => {
        loginCheck().then((data: any) => {
            api.getRePayOrderInfo({
                method: "POST",
                data: {
                    key: key,
                    // wxCode: data.code,
                    openId: data.openId,
                    appId: getStorage(APP_ID),
                    projectId: getStorage(PROJECT_ID),
                },
            }).then(res => {
                // console.log("")
                orderIds.current = res.shopOrderIds;
                // setGoods(res.shopOrders[0].skuOrders);
                setPrice(res.fee);
                setShopOrders(res.shopOrders);
            });
        });
    };
    return (
        <View className="help-pay">
            {/* 委托购买提示 */}
            <View className="notice-head">
                <Image className="notice-img" src="https://dante-img.oss-cn-hangzhou.aliyuncs.com/30840666772.png" />
                <View className="speech-bubble">
                    <View className="speech-text">
                        <Text className="title">我挑了个超赞的宝贝～请你尽快帮我付个款吧</Text>
                    </View>
                </View>
            </View>
            <View className="order-payment">
                {/* 订单金额 */}
                <View className="amount">
                    <View className="label">代我付订单信息</View>
                    <Text className="value">￥ {(price / 100).toFixed(2)}</Text>
                    {/* 提示信息 */}
                    <View className="amount-notice">
                        <Text className="text">实际金额以付款人确认付款时为准</Text>
                    </View>
                </View>

                <View className="shop-rect">
                    {/* 商品名称 */}
                    <View className="shop-list">
                        {shopOrders.length ? (
                            <View style={{ padding: "10px 0 0 0" }}>
                                {shopOrders.map((shopItem: any) => {
                                    return (
                                        <View style={{ padding: "0 10px 0 10px" }}>
                                            <View>订单号：{shopItem.shopOrderId}</View>
                                            <View className="good-list">
                                                {shopItem.skuOrders &&
                                                    shopItem.skuOrders.map(goodsItem => {
                                                        return (
                                                            <View className="good-item">
                                                                {/* <View className="good-img"> */}
                                                                <Image
                                                                    className="good-img"
                                                                    src={goodsItem.skuImage}
                                                                    mode="aspectFill"
                                                                />
                                                                {/* </View> */}
                                                                <View className="good-content">
                                                                    {goodsItem.itemName}
                                                                </View>
                                                            </View>
                                                        );
                                                    })}
                                            </View>
                                        </View>
                                    );
                                })}
                            </View>
                        ) : (
                            <View
                                style={{
                                    width: "100%",
                                    paddingBottom: "20px",
                                    paddingTop: "20px",
                                    textAlign: "center",
                                }}
                            >
                                <Text style={{ textAlign: "center" }}>暂无商品</Text>
                            </View>
                        )}
                    </View>
                </View>
            </View>
            {/* 订购须知 */}
            <View className="notice">
                <View className="title">帮我付说明</View>
                <Text className="item">1. 您在为亲友完成付款后，将计入您的2.6万年度跨境购买限额</Text>
                <Text className="item">
                    2. 委托订购链接，仅可打开一次，页面关闭后失效，如需付款，请发起方重新生成链接
                </Text>
                <Text className="item" style={{ color: "eac537" }}>
                    3. 如果交易发生退款，已支付金额将按原路退回付款人的付款账户
                </Text>
            </View>
            {/* 确认付款按钮 */}
            <View className="actions">
                <Button className="button" onClick={handleConfirmPayment}>
                    确认付款
                </Button>
            </View>
        </View>
    );
};

export default HelpPay;
function gotoDetail(id: any) {
    throw new Error("Function not implemented.");
}
