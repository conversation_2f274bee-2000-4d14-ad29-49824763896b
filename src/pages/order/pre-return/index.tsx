import { View, Text, ScrollView } from "@tarojs/components";
import "./index.less";
import { useEffect, useState } from "react";
import { Button, Card, Divider, Field } from "@antmjs/vantui";
import { useRouter } from "@tarojs/taro";
import api from "@/utils/api";
import DDYToast from "@/utils/toast";
import { setStorage } from "@/utils/storage";
import { DDYBack } from "@/utils/route";
import { skuOrderObj } from "../order-detail/hooks/useDetail";
import { mall_event, mall_event_key, reportEvent } from "@/utils/track-report";

definePageConfig({
    navigationBarTitleText: "申请退款",
    navigationBarBackgroundColor: "#fff",
});
const REASON = ["七天无理由退款", "不想要", "与实际不符合的描述", "质量问题", "其它", "地址填错", "疫情停发"];
export default () => {
    const route = useRouter();
    const [reasonList, setReasonList] = useState([
        { id: 1, name: "质量问题" },
        { id: 2, name: "其他" },
        { id: 3, name: "描述与实际不符" },
    ]);
    const [reanson, setReason] = useState<number>();
    const [introduce, setIndroduce] = useState("");
    const [list, setList] = useState<skuOrderObj[]>([]);
    const [orderSn, setOrderSn] = useState("");
    const getOrder = () => {
        api.getOrderInfo({
            data: {
                id: route.params.orderId,
            },
            filterCheck: true,
        }).then(res => {
            if (!res.message) {
                setList(res.skuOrders);
                setOrderSn(res.shopOrder.declaredId);
            } else {
                DDYToast.info(res.message);
            }
        });
    };

    const refundApply = () => {
        if (!reanson) {
            DDYToast.info("请选择退款原因");
            return;
        }

        let map = new Map();
        const reansonStr = reasonList.filter(item => item.id === reanson)?.[0]?.name;
        map.set(mall_event_key.MALL_KEY_ORDER_IDS, route.params.orderIds);
        map.set(mall_event_key.MALL_KEY_ORDER_REFUND_REASON, reansonStr);
        map.set(mall_event_key.MALL_KEY_ORDER_REFUND_INTRODUCE, introduce);
        // map.set(mall_event_key.MALL_KEY_ORDER_PAY_CHANNEL, '积分')
        reportEvent(mall_event.MALL_EVENT_ORDER_APPLY_REFUND, map);
        api.jdApplyReturn({
            method: "POST",
            // contentType: "application/x-www-form-urlencoded",
            data: {
                imagesJson: "[]", // 退款图片
                orderId: route.params.orderId,
                orderType: 1,
                reasonType: reanson, // 退款原因
                buyerRemark: introduce, // 退款说明
                // refundId: route.params.refundId,
                refundType: 1, //仅退款
            },
            // filterCheck: true,
        }).then(res => {
            if (!res.message) {
                DDYToast.success("申请退款提交成功");
                setStorage("dealOrder", "true");
                setTimeout(() => {
                    DDYBack();
                }, 1000);
            } else {
                DDYToast.info(res.message);
            }
        });
    };

    useEffect(() => {
        getOrder();
    }, []);
    useEffect(() => {
        api.getJdReasonList({
            data: {},
        }).then(res => {
            setReasonList(res);
        });
    }, []);
    return (
        <>
            <ScrollView scrollY className="pre-return">
                <View className="order-good-info">
                    <View className="order-info">退款订单：{orderSn}</View>
                    <View className="order-info">退款商品：</View>
                    {list.map((goodItem, index) => (
                        <>
                            <Card num="" price="" desc="" title={goodItem.itemName} thumb={goodItem.skuImage} />
                            <View className="order-info order-num">
                                退款数量： <Text className="order-num-value">x{goodItem.quantity}</Text>
                            </View>
                            <View className="order-info order-num">
                                退款金额： <Text className="order-num-value">{goodItem.fee / 100}</Text>
                            </View>
                        </>
                    ))}
                </View>
                <View className="return-form">
                    <View className="form-title">退款原因：</View>
                    <View className="form-content">
                        {reasonList.map((item, index) => (
                            <View
                                className={reanson === item.id ? "reason-item selected" : "reason-item"}
                                onClick={() => {
                                    setReason(item.id);
                                }}
                            >
                                {item.name}
                            </View>
                        ))}
                    </View>
                    <Divider />
                    <View className="form-title">退款说明：</View>
                    <View className="form-content">
                        <Field
                            type="textarea"
                            className="extra-input"
                            placeholder="请输入退款说明，最多100个字"
                            maxlength={100}
                            placeholderStyle={"color:#BBBBBB;"}
                            value={introduce}
                            border={false}
                            onInput={e => {
                                setIndroduce(e.detail.value);
                            }}
                        />
                    </View>
                </View>
                <View style={{ height: "1rpx" }} />
            </ScrollView>
            <View className="return-bottom">
                <Button
                    block
                    round
                    color="linear-gradient(to right, #FF3B61 0%, #FF0940 100%)"
                    onClick={() => {
                        refundApply();
                    }}
                >
                    提交申请
                </Button>
            </View>
        </>
    );
};
