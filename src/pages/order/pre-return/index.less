.pre-return {
    padding: 24rpx;
    padding-bottom: calc(124rpx+ env(safe-area-inset-bottom));
    height: 100vh;
    box-sizing: border-box;
    margin-bottom: 200rpx;
    .order-good-info {
        background-color: #fff;
        border-radius: 16rpx;
        padding: 28rpx;
        .order-info {
            font-size: 26px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #333333;
            line-height: 26px;
            margin-bottom: 32rpx;
        }
        .order-num {
            margin-bottom: 0;
            padding-top: 32rpx;
            .order-num-value {
                float: right;
                font-size: 24px;
                font-family: HelveticaNeue;
                color: #999999;
                line-height: 24px;
            }
        }
    }
    .return-form {
        background-color: #fff;
        border-radius: 16rpx;
        padding: 28rpx;
        margin-top: 20rpx;
        // padding-bottom: 100px;
        margin-bottom: calc(120px + env(safe-area-inset-bottom));
        .form-title {
            padding-bottom: 30rpx;
        }
        .form-content {
            .reason-item {
                width: 565rpx;
                height: 64rpx;
                background: #ffffff;
                border-radius: 32rpx;
                border: 1px solid #cccccc;
                font-size: 28px;
                font-family: PingFangSC-Regular, PingFang SC;
                font-weight: 400;
                color: #333333;
                line-height: 64rpx;
                text-align: center;
                // margin-bottom: 20rpx;
                margin: 0 auto 20rpx;
            }
            .selected {
                border-color: #ff0940;
                color: #ff0940;
            }
            .extra-input {
                height: 200rpx;
            }
        }
    }
}
.return-bottom {
    position: fixed;
    bottom: 1rpx;
    left: 0;
    right: 0;
    // height: 100rpx;
    background-color: #fff;
    padding-left: 24rpx;
    padding-right: 24rpx;
    padding-top: 16rpx;
    padding-bottom: 16rpx;
    padding-bottom: calc(16rpx+ env(safe-area-inset-bottom));
}
