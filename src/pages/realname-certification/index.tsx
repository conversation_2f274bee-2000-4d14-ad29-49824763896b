import { View } from "@tarojs/components";
import "./index.less";
import RealNameForm, { RealNameFromRef } from "@/components/real-name-form";
import { useEffect, useRef } from "react";
import Taro, { useRouter } from "@tarojs/taro";
import { DDYBack } from "@/utils/route";
import api from "@/utils/api";
import DDYToast from "@/utils/toast";

definePageConfig({
    navigationBarTitleText: "实名认证",
});
export default () => {
    const formRef = useRef<RealNameFromRef>({} as RealNameFromRef);
    const route = useRouter();

    const isReplay = route.params.isReplay;
    console.log("代付实名认证标志", isReplay);
    return (
        <View className="realname-certification">
            {isReplay && <View className="replay-title">邀请好友代买请填写好友身份信息</View>}
            {isReplay ? (
                <View className="card">
                    <RealNameForm
                        onOk={() => {}}
                        onFail={() => {}}
                        color={"#f50050"}
                        ref={formRef}
                        submit={values => {
                            console.log("values:", values);
                            api.submitPayerInfo({
                                method: "POST",
                                data: {
                                    // shopId:''
                                    paperName: values.paperName,
                                    paperNo: values.paperNo,
                                },
                            }).then(res => {
                                console.log("res:", res);
                                DDYToast.info("提交成功");
                                DDYBack();
                            });
                        }}
                        certInfo={{
                            paperName: route.params.name || "",
                            paperNo: route.params.idCardNo || "",
                        }}
                    />
                </View>
            ) : (
                <View className="card">
                    <RealNameForm
                        onOk={() => {
                            const pages = Taro.getCurrentPages();
                            const current = pages[pages.length - 1];
                            const eventChannel = current.getOpenerEventChannel();
                            eventChannel.emit("changeReal", true);
                            DDYBack();
                        }}
                        onFail={() => {}}
                        color={"#f50050"}
                        ref={formRef}
                    />
                </View>
            )}
        </View>
    );
};
