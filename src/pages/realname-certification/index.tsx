import { View } from "@tarojs/components";
import "./index.less";
import RealNameForm, { RealNameFromRef } from "@/components/real-name-form";
import { useEffect, useRef } from "react";
import Taro from "@tarojs/taro";
import { DDYBack } from "@/utils/route";

definePageConfig({
    navigationBarTitleText: "实名认证",
});
export default () => {
    const formRef = useRef<RealNameFromRef>({} as RealNameFromRef);

    return (
        <View className="realname-certification">
            <View className="card">
                <RealNameForm
                    onOk={() => {
                        const pages = Taro.getCurrentPages();
                        const current = pages[pages.length - 1];
                        const eventChannel = current.getOpenerEventChannel();
                        eventChannel.emit("changeReal", true);
                        DDYBack();
                    }}
                    onFail={() => {}}
                    color={"#f50050"}
                    ref={formRef}
                />
            </View>
        </View>
    );
};
