import "./index.less";

import { DDYRedirectTo, DDYSwitchTab } from "@/utils/route";
import { useDidShow, useRouter } from "@tarojs/taro";

import { View } from "@tarojs/components";
import appStore from "@/store/app-store";
import { useEffect } from "react";

export default () => {
    const router = useRouter();
    const { q } = router.params;

    const getQrCodeParams = (qrCode: string) => {
        if (!qrCode) return {};
        const queryString = decodeURIComponent(qrCode).split("?")[1];
        if (!queryString) return {};

        return queryString.split("&").reduce((params, param) => {
            const [key, value] = param.split("=");
            return { ...params, [key]: value };
        }, {});
    };

    const navigateTo = () => {
        if (!q) {
            return;
        }
        // @ts-ignore
        const { pageUrl = "", pageId = "" } = getQrCodeParams(q);
        if (!pageUrl) {
            return "预览页面不存在";
        }
        if (pageUrl === "pages/index/index") {
            DDYSwitchTab({
                url: `/${pageUrl}?previewMode=1`,
            });
        } else {
            let url = `/${pageUrl}?previewMode=1`;
            if (pageId) {
                url += `&pageId=${pageId}`;
            }
            DDYRedirectTo({
                url: url,
            });
        }
    };
    useDidShow(() => {
        if (appStore.hasLaunch) {
            DDYSwitchTab({ url: "/pages/index/index" });
            appStore.hasLaunch = true;
        }
    });
    useEffect(() => {
        navigateTo();
    }, []);

    return <View className="desgin-preview"></View>;
};
