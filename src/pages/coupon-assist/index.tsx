import "./index.less";

import { Button, CountDown, ITimeData, Icon, NavBar } from "@antmjs/vantui";
import { DDYBack, DDYNavigateTo, DDYSwitchTab } from "@/utils/route";
import { Fragment, useEffect, useState } from "react";
import { INVITE_CODE } from "@/utils/constant";
import { Image, Text, View } from "@tarojs/components";
import Taro, { useRouter, useShareAppMessage } from "@tarojs/taro";
import { setStorage } from "@/utils/storage";

import DDYToast from "@/utils/toast";
import DesignPage from "@/components/desgin";
import infoStore from "@/store/info-store";
import newAPi from "@/utils/newApi";
import { PreviewMode } from "@/components/desgin/desginUtils";
import { MOCK_COUPON_ASSIST_DETAIL } from "./mock-data";
definePageConfig({
    navigationStyle: "custom",
    enableShareAppMessage: true,
});

export default () => {
    const router = useRouter();
    const { id, shareId, fissionActivityId, previewMode } = router.params;
    const [pageSet, setPageSet] = useState({});
    const [timeData, setTimeData] = useState<ITimeData | null>();
    const [times, setTimes] = useState();
    const [detail, setDetail] = useState({
        sharerCoupon: {},
        childrenCoupon: {},
    });
    const [invitedFriends, setinvitedFriends] = useState([]);
    const handleInvite = () => {
        newAPi
            .assistCoupon({
                method: "GET",
                data: {
                    fissionActivityId: shareId,
                },
            })
            .then(res => {
                DDYToast.info("助力成功");
                infoStore
                    .getNewUserProfile(false)
                    .then(resPonse => {
                        console.log("res:", resPonse);
                        if (resPonse) {
                            DDYNavigateTo({
                                url: `/pages/wool-herd/index?join=${resPonse.whetherGroup}`,
                            });
                        } else {
                            DDYNavigateTo({
                                url: `/pages/wool-herd/index?join=false`,
                            });
                        }
                    })
                    .catch(e => {
                        DDYNavigateTo({
                            url: `/pages/wool-herd/index?join=false`,
                        });
                    });
            });
    };

    const getDetail = id => {
        newAPi
            .getfissionDetail({
                method: "GET",
                data: {
                    fissionActivityId: id,
                },
            })
            .then(data => {
                setDetail(data);
                setTimes(data.endTime - new Date().getTime());
                setinvitedFriends(data.clickUserList || []);
            });
    };

    useShareAppMessage(callback => {
        const inviteCode = infoStore.newUserProfile.inviteCode;
        return {
            title: "助力",
            path: `/pages/coupon-assist/index?shareId=${id}&inviteCode=${inviteCode}`,
        };
    });

    useEffect(() => {
        const { id, shareId, inviteCode } = router.params;
        if (inviteCode) {
            setStorage(INVITE_CODE, inviteCode);
        }
        if (id) {
            // 分享者
            getDetail(id);
        }
        if (shareId) {
            // 被分享者
            getDetail(shareId);
        }
    }, []);

    const xinxi = couponObj => {
        if (couponObj.method === "DIRECT_REDUCTION") {
            return (
                <Fragment>
                    <Text className="coupon-text1">
                        ¥<Text className="coupon-text2">{couponObj.deductionAmount}</Text>
                    </Text>
                    <View className="coupon-text3">无门槛</View>
                </Fragment>
            );
        }
        if (couponObj.method === "FULL_REDUCTION") {
            return (
                <Fragment>
                    <Text className="coupon-text1">
                        ¥<Text className="coupon-text2">{couponObj.thresholdDeductionAmount}</Text>
                    </Text>
                    <View className="coupon-text3">满{couponObj.thresholdAmount}可用</View>
                </Fragment>
            );
        }
        return null;
    };

    return (
        <View className="coupon-assist">
            <NavBar
                fixed
                title={pageSet?.title || "邀请页"}
                leftArrow={shareId ? false : true}
                renderLeft={shareId ? <Icon name="wap-home-o" size="28px" /> : null}
                onClickLeft={() => {
                    if (shareId) {
                        DDYSwitchTab({
                            url: "/pages/index/index",
                        });
                    } else {
                        DDYBack();
                    }
                }}
                style={"background-color:transparent"}
                border={false}
            />
            <View
                style={{
                    height: Taro.pxTransform(482),
                    overflow: "hidden",
                }}
            >
                <DesignPage
                    pageType={60}
                    onGetPageConfig={(data: any, isPreview) => {
                        setPageSet(data.pageSet);
                        if (isPreview) {
                            setDetail(MOCK_COUPON_ASSIST_DETAIL);
                            setTimes(60000000);
                            setinvitedFriends([]);
                        }
                    }}
                />
            </View>

            {detail ? (
                <View className="main-content">
                    <View className="title-icon" />
                    {(id || previewMode === PreviewMode.PREVIEW_MODE_SAVE) && (
                        <View className="head">
                            <View className="head-icon"></View>
                            <View>邀请{detail.needClickCount || 4}位好友即可获得</View>
                            <View className="head-time">
                                <CountDown
                                    time={times}
                                    onChange={e => {
                                        setTimeData({ ...e.detail });
                                    }}
                                >
                                    <View className="text2">
                                        <View className="text1">{timeData?.hours + (timeData?.days || 0) * 24}</View>:
                                        <View className="text1">
                                            {timeData?.minutes < 10 ? `0${timeData?.minutes}` : timeData?.minutes}
                                        </View>
                                        :
                                        <View className="text1">
                                            {timeData?.seconds < 10 ? `0${timeData?.seconds}` : timeData?.seconds}
                                        </View>
                                        <Text className="text2">后失效</Text>
                                    </View>
                                </CountDown>
                            </View>
                        </View>
                    )}

                    {shareId && (
                        <View className="head">
                            <View className="head-icon"></View>
                            <Text>
                                您的好友还差 <Text>{detail?.needClickCount}人</Text> 助力即将获得奖励
                            </Text>
                        </View>
                    )}

                    {(id || previewMode === PreviewMode.PREVIEW_MODE_SAVE) && (
                        <View className="coupon-content">
                            <View className="coupon-left">{xinxi(detail?.sharerCoupon)}</View>
                            <View className="coupon-right">
                                <Image
                                    src={"https://dante-img.oss-cn-hangzhou.aliyuncs.com/21722498145.png"}
                                    className="coupon-img"
                                />
                                <Text className="coupon-text4">全家福助力劵</Text>
                            </View>
                        </View>
                    )}
                    {shareId && (
                        <View className="coupon-content-1">
                            <View className="coupon-left">{xinxi(detail?.childrenCoupon)}</View>
                            <View className="coupon-right">
                                <Image
                                    src={"https://dante-img.oss-cn-hangzhou.aliyuncs.com/21722498145.png"}
                                    className="coupon-img"
                                />
                                <View>
                                    <Text className="coupon-text4">帮他助力你将获得</Text>
                                    <View />
                                    <Text className="coupon-text5">全家福助力劵</Text>
                                </View>
                            </View>
                        </View>
                    )}

                    <View className="coupon-friends" style={{ paddingBottom: shareId ? "60px" : "0px" }}>
                        {(id || previewMode === PreviewMode.PREVIEW_MODE_SAVE) && (
                            <Fragment>
                                {previewMode === PreviewMode.PREVIEW_MODE_SAVE ? (
                                    <Button
                                        onClick={() => {
                                            DDYToast.info("预览模式下, 无法邀请好友助力");
                                            return;
                                        }}
                                        className="coupon-friends-btn"
                                    >
                                        邀请好友助力
                                    </Button>
                                ) : (
                                    <Button
                                        // onClick={handleInvite}
                                        className="coupon-friends-btn"
                                        openType="share"
                                    >
                                        邀请好友助力
                                    </Button>
                                )}
                            </Fragment>
                        )}
                        {shareId && (
                            <Button
                                onClick={handleInvite}
                                className="coupon-friends-btn"
                                // openType="share"
                            >
                                帮TA助力一下
                            </Button>
                        )}
                        {(id || previewMode === PreviewMode.PREVIEW_MODE_SAVE) && (
                            <View className="coupon-friends-rect">
                                {invitedFriends.map((friend, index) => (
                                    <View key={index} className="text-center">
                                        <Image src={friend.userImg} mode="aspectFit" className="friends-avatar" />
                                        <View className="friends-name">{friend.userName}</View>
                                    </View>
                                ))}
                                {[...Array((detail?.needClickCount || 4) - invitedFriends.length)].map((_, index) => (
                                    <View key={`empty-${index}`} className="text-center">
                                        <View className="friends-avatar">
                                            <Icon name="plus" color="rgba(177, 61, 61, 1)" size={40} />
                                        </View>
                                        <Text className="friends-text">待邀请</Text>
                                    </View>
                                ))}
                            </View>
                        )}
                    </View>
                </View>
            ) : null}
        </View>
    );
};
