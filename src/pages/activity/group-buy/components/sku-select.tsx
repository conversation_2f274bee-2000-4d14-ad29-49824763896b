import newAPi from "@/utils/newApi";
import { ActionSheet, Button, Dialog, Field, Popup, Stepper } from "@antmjs/vantui";
import { View, Text, Image, ScrollView } from "@tarojs/components";
import React, { useEffect, useImperativeHandle, useRef, useState } from "react";
import "./sku-select.less";
import { DDYNavigateTo, DDYReLaunch, DDYSwitchTab } from "@/utils/route";
import Taro, { reportEvent } from "@tarojs/taro";
import api from "@/utils/api";
import { mall_event, mall_event_key } from "@/utils/track-report";
import DDYToast from "@/utils/toast";
import { getStorage, removeStorage } from "@/utils/storage";
import { IS_LOGIN, SCAN_PAGE, STORE_ID } from "@/utils/constant";
import { handleGoodsLink, jumpLogin } from "@/utils/PageUtils";
import { DDYObject } from "src/type/common";
import GoodsItem from "../../components/goods-item";
import { spec } from "node:test/reporters";
const Dialog_ = Dialog.createOnlyDialog();

// 两个数组是否有交集
function hasIntersection(arr1, arr2) {
    return arr1.some(item => arr2.includes(item));
}

const isSubset = (a, b) => {
    // console.log(a, b);
    const setA = new Set(a);
    return b.every(item => setA.has(item)) && a.length - b.length <= 1;
};

function hasAtLeastNMinusOneCommonElements(A, B) {
    const n = A.length;
    const bLength = B.length;

    // 特殊情况处理
    if (n === 0) return true; // 空数组视为满足条件
    if (bLength < n - 1) return false; // B元素太少，不可能满足

    // 将数组A转换为集合以便快速查找
    const setA = new Set(A);
    let commonCount = 0;

    // 计算B中有多少元素在A中出现
    for (const elem of B) {
        if (setA.has(elem)) {
            commonCount++;
            // 如果已经满足条件，可以提前返回
            if (commonCount >= n - 1) {
                return true;
            }
        }
    }

    return commonCount >= n - 1;
}
export default React.forwardRef((props, ref) => {
    const {
        closeFn,
        hasGbActivity,
        // 0: 加入购物车 ； 1: 立即购买
        openType,
        goodsId,
        skuId,
        cartItemId,
        skuNote,
        skuLoad,
        groupAction,
        groupId,
        activityId,
        jumpLogin,
        isLogin,
        goods,
        setSelectId,
    } = props as any;
    const [mainPic, setMainPic] = useState("");
    const [price, setPrice] = useState<number | 0>(0);
    const [discountPrice, setDiscouponPrice] = useState(0.0);
    const [stockQuantity, setStockQuantity] = useState(0);
    const [limitQuantity, setLimitQuantity] = useState(0);
    const [orderNum, setOrderNum] = useState(1);
    const [skuNum, setSkuNum] = useState(null);
    const [isSelectdFinished, setIsSelectdFinished] = useState(false);
    const [skusAttr, setSkusAttr] = useState<any[]>([]);
    const [selectStr, setSelectStr] = useState("");
    const [note, setNote] = useState("");
    const selectSkuId = useRef<number | null>();
    // const [selectSku]
    const selectedSkuInfo = useRef<DDYObject>();
    const [sku, setSku] = useState([]);
    const [goodsItem, setGoodsItem] = useState<DDYObject>({});
    const latestGoodsItem = useRef(goodsItem);
    const currentNote = useRef(skuNote);

    useEffect(() => {
        latestGoodsItem.current = goodsItem;
    }, [goodsItem]);

    const autoGetCoupons = () => {
        newAPi
            .autoCoupons({
                method: "GET",
                data: {
                    goodsCodeList: goodsId,
                },
                showError: false,
            })
            .then(res => {});
    };

    const handleBtnClick = () => {
        if (orderNum == 0) {
            return DDYToast.info("请重新选择商品，商品数量不能为0");
        }
        takeCart(openType);
        autoGetCoupons();
    };

    // 暴露给父组件的方法
    useImperativeHandle(ref, () => ({
        handleBtnClick,
    }));

    const skuChange = (attrs, skus, clickIndex, goods) => {
        let bol = true;
        let strArr: any[] = [];
        let specIdArr: any[] = [];
        const selectAttrObj = {};
        let curGoods = goods || goodsItem;
        attrs.map((skusItem, index) => {
            if (curGoods.type === 2) {
                selectSkuId.current = skusItem.id;
            }
            if (!skusItem.selected) {
                bol = false;
            } else {
                selectAttrObj[index] = skusItem.frontId;
                const item: DDYObject = skusItem.itemSpecificationDetailParams.filter(
                    item => item.frontId === skusItem.selected,
                )[0];
                strArr.push(item.name);
                specIdArr.push(item.frontId);
            }
        });

        // 根据选中的规格反向推算未选中的规则是否还有库存，对状态做实时更新

        // 获取与当前选择的规格有交集的sku中没有库存的sku；
        const skuNoStock = skus.filter((item: any) => {
            const arr = specIdArr.filter(item => item !== selectAttrObj[clickIndex]);
            if (!arr.length) return false;
            return hasAtLeastNMinusOneCommonElements(item.specDetails || [], arr) && item.stockQuantity === 0;
        });

        // 下架商品
        const skuNoActiveStock = skus.filter((item: any) => {
            const arr = specIdArr.filter(item => item !== selectAttrObj[clickIndex]);
            if (!arr.length) return false;
            return (
                hasAtLeastNMinusOneCommonElements(item.specDetails || [], arr) &&
                (item.status === -1 || (!item.activityName && hasGbActivity))
            );
        });
        // 重置库存售罄状态
        attrs.map((attrsItem, attrsIndex) => {
            attrsItem.itemSpecificationDetailParams.map(specItem => {
                // 当前点击的属性组不重置状态
                if (clickIndex !== null && attrsIndex !== clickIndex) {
                    specItem.status = 1;
                    specItem.isHide = false;
                }
            });
        });
        if (skuNoActiveStock.length) {
            skuNoActiveStock.map(skuNoActiveStockItem => {
                const { specDetails = [] } = skuNoActiveStockItem;
                attrs.map(attrsItem => {
                    // 遍历它的属性组，找出在当前specDetails的规格属性，并将其标记成售罄
                    attrsItem.itemSpecificationDetailParams.map(specItem => {
                        if ((specDetails || []).includes(specItem.frontId) && attrsItem.selected !== specItem.frontId) {
                            specItem.isHide = true;
                        }
                    });
                });
            });
        }
        if (skuNoStock.length) {
            skuNoStock.map(skuNoStockItem => {
                const { specDetails = [] } = skuNoStockItem;
                attrs.map((attrsItem, attrsIndex) => {
                    // 遍历它的属性组，找出在当前specDetails的规格属性，并将其标记成售罄
                    attrsItem.itemSpecificationDetailParams.map(specItem => {
                        if ((specDetails || []).includes(specItem.frontId)) {
                            if (attrsItem.selected !== specItem.frontId) {
                                specItem.status = -1;
                            }
                        }
                    });
                });
            });

            setSkusAttr([...attrs]);
        }

        // 获取sku信息
        if (curGoods.type === 2) {
            selectSkuId.current = null;
            //组合商品
            attrs.map(item => {
                item.itemSpecificationDetailParams.map(specItem => {
                    if (item.selected === specItem.frontId) {
                        selectSkuId.current = specItem.id;
                        selectedSkuInfo.current = specItem;
                        if (specItem.extraMap && specItem.extraMap.extraActivityValid) {
                            setPrice(+specItem.extraMap.activitySalesPrice);
                            setDiscouponPrice(+specItem.extraMap.activitySalesPrice);
                        } else {
                            setPrice(specItem.skuPrice);
                            setDiscouponPrice(specItem.skuPrice);
                        }
                        if (curGoods && curGoods.extra && curGoods.extraActivityValid) {
                            setMainPic(specItem.image || curGoods.extra.activityMainImage || curGoods.mainImage);
                        } else {
                            setMainPic(specItem.image || curGoods.mainImage);
                        }
                    }
                });
            });
        } else {
            selectSkuId.current = null;
            skus.map(item => {
                if ((item.specDetails || []).join("+") === specIdArr.join("+")) {
                    selectSkuId.current = item.id;
                    if (item.extraMap && item.extraMap.extraActivityValid) {
                        setPrice(+item.extraMap.activitySalesPrice);
                        setDiscouponPrice(+item.extraMap.activitySalesPrice);
                    } else {
                        setPrice(item.skuPrice);
                        setDiscouponPrice(item.skuPrice);
                    }
                    if (curGoods && curGoods.extra && curGoods.extraActivityValid) {
                        setMainPic(item.image || curGoods.extra.activityMainImage || curGoods.mainImage);
                    } else {
                        setMainPic(item.image || curGoods.mainImage);
                    }
                    selectedSkuInfo.current = item;
                }
            });
        }
        // 完整选中规格时
        if (bol) {
            setSelectStr(strArr.join("/"));
            querySkuPriceInfo(skuNum || 1, true);
            if (!skuNum) {
                setOrderNum(1);
            }
        } else {
            if (curGoods && curGoods.extra && curGoods.extraActivityValid) {
                setMainPic(curGoods.extra.activityMainImage || curGoods.mainImage);
            } else {
                setMainPic(curGoods.mainImage);
            }
            setPrice(curGoods.itemLowPrice || curGoods.lowPrice);
            setDiscouponPrice(curGoods.itemLowPrice || curGoods.lowPrice);
        }
        setIsSelectdFinished(bol);
    };

    // 规格点击事件
    const handleSkuClick = (items, item, skusIndex) => {
        if (item.status === -1 || item.isHide) {
            return;
        }
        if (item.frontId === items.selected) {
            items.selected = null;
        } else {
            items.selected = item.frontId;
        }
        setSkusAttr(preSkusAttr => {
            setSkusAttr([...preSkusAttr]);
            skuChange(preSkusAttr, sku, skusIndex, null);
            return preSkusAttr;
        });
    };

    const setInitSku = (attrs, skus, index = 0) => {
        // 如果已经处理完所有规格，则返回
        if (index >= attrs.length) {
            return;
        }

        // 设置当前规格的第一项为选中状态
        if (attrs[index] && attrs[index].itemSpecificationDetailParams.length > 0) {
            let needItem = attrs[index].itemSpecificationDetailParams.find(m => !m.isHide);
            attrs[index].selected = needItem.frontId;

            // 调用 skuChange 更新状态
            setGoodsItem(cur => {
                skuChange(attrs, skus, index, cur);
                return cur;
            });

            // 延迟处理下一个规格，确保当前规格的状态更新和筛选已完成
            setTimeout(() => {
                setInitSku(attrs, skus, index + 1);
            }, 100);
        } else {
            // 如果当前规格没有选项，直接处理下一个规格
            setInitSku(attrs, skus, index + 1);
        }
    };

    const querySkuPriceInfo = (num, setMaxFlag = false) => {
        if (!selectSkuId.current) return;
        return newAPi
            .getSkuInfo({
                method: "GET",
                data: {
                    id: selectSkuId.current,
                    hasGbActivity: hasGbActivity,
                    count: num,
                },
            })
            .then(res => {
                const { stockQuantity, discountPrice, purchaseLimit } = res;
                setStockQuantity(stockQuantity); // 库存
                setDiscouponPrice(discountPrice);
                setLimitQuantity(purchaseLimit); // 限购
                if (setMaxFlag) {
                    let curNum = 0;
                    let goodsItem = latestGoodsItem.current;
                    let skuOrderSplitLine = goodsItem?.extra?.skuOrderSplitLine;
                    // 若活动限购和拆单都未配置，且库存>1：默认赋值1
                    if (!purchaseLimit && !skuOrderSplitLine) {
                        if (stockQuantity >= 1) {
                            curNum = 1;
                        } else {
                            curNum = 0;
                        }
                    } else {
                        curNum = Math.min(stockQuantity, purchaseLimit || Infinity, skuOrderSplitLine || Infinity);
                    }
                    if (curNum !== num) {
                        querySkuPriceInfo(curNum);
                        setOrderNum(curNum);
                    }
                }
            });
    };

    const getDetailInfo = () => {
        newAPi
            .getSkus({
                data: {
                    itemId: goodsId,
                    hasGbActivity: !!hasGbActivity,
                },
            })
            .then((res: any) => {
                // 组合品
                setGoodsItem(res.item);
                selectSkuId.current = skuId;
                if (res.item.type === 2) {
                    let selected = null;
                    if (skuId) {
                        selected = skuId;
                    }
                    const arr = [
                        {
                            name: "套餐名称",
                            itemSpecificationDetailParams: res.skus.map(item => {
                                return {
                                    ...item,
                                    frontId: item.id,
                                    isHide: item.status === -1,
                                    status: item.stockQuantity === 0 ? -1 : 1,
                                };
                            }),
                            selected: selected,
                        },
                    ];
                    setSkusAttr(arr);
                    // skuChange(arr, res.skus, null);
                } else {
                    let arr = res.itemSpecificationParams || [];
                    if (skuId) {
                        let specDetails = (res.skus || []).filter(item => item.id === skuId)[0].specDetails;

                        arr.map((item, index) => {
                            item.selected = specDetails[index];
                        });
                        setIsSelectdFinished(true);
                    } else {
                        // 初始化sku
                        Taro.nextTick(() => {
                            setInitSku(arr, res.skus);
                        });
                    }
                    if (arr.length === 1) {
                        arr = arr.map(specParam => {
                            specParam.itemSpecificationDetailParams = specParam.itemSpecificationDetailParams.map(
                                detail => {
                                    // 查找匹配的SKU
                                    const matchingSku = res.skus.find(
                                        sku => !!sku.specDetails && sku.specDetails[0] === detail.frontId,
                                    );
                                    return {
                                        ...detail,
                                        status: !!matchingSku && matchingSku.stockQuantity === 0 ? -1 : 1,
                                        isHide: !!matchingSku && matchingSku.status === -1,
                                    };
                                },
                            );
                            return specParam;
                        });
                    }
                    skuChange(arr, res.skus, null, null);
                    setSkusAttr(arr);
                    if (arr.length === 0) {
                        setIsSelectdFinished(true);
                        selectSkuId.current = res.skus[0].id;
                    }
                }
                skuLoad && skuLoad(res.skus);
                if (skuId) {
                    const arr = res.skus.filter(item => item.id === skuId)[0];
                    // if(res.ite)
                    if (arr.length) {
                        if (res.item.extra && res.item.extraActivityValid) {
                            setMainPic(arr[0].image || res.item.extra.activityMainImage || res.item.mainImage);
                        } else {
                            setMainPic(arr[0].image || res.item.mainImage);
                        }
                    } else {
                        if (res.item.extra && res.item.extraActivityValid) {
                            setMainPic(res.item.extra.activityMainImage || res.item.mainImage);
                        } else {
                            setMainPic(res.item.mainImage);
                        }
                    }
                    setIsSelectdFinished(true);
                } else {
                    if (res.item.extra && res.item.extraActivityValid) {
                        setMainPic(res.item.extra.activityMainImage || res.item.mainImage);
                    } else {
                        setMainPic(res.item.mainImage);
                    }
                    const validSkus = res.skus.filter(item => item.status !== -1);
                    console.log("validSkus:", validSkus);
                    // 特殊逻辑，当未售罄的sku只有一个时，默认选择当前sku对应的属性规格;
                    if (validSkus.length === 1) {
                        selectSkuId.current = validSkus[0].id;
                        setIsSelectdFinished(true);
                        selectedSkuInfo.current = validSkus[0];
                        if (res.item.type === 2) {
                            // 组合品默认选中逻辑
                            const arr = [
                                {
                                    name: "套餐名称",
                                    itemSpecificationDetailParams: res.skus.map(item => {
                                        return {
                                            ...item,
                                            frontId: item.id,
                                            isHide: item.status === -1,
                                            status: item.stockQuantity === 0 ? -1 : 1,
                                        };
                                    }),
                                    selected: selectSkuId.current,
                                },
                            ];
                            setSkusAttr(arr);
                        } else {
                            let arr = res.itemSpecificationParams || [];
                            let specDetails = validSkus[0].specDetails;
                            arr.map((item, index) => {
                                item.selected = specDetails[index];
                            });
                            setSkusAttr(arr);
                        }

                        setIsSelectdFinished(true);
                    }
                }

                setSku(res.skus);
                setPrice(res.item.itemLowPrice || res.item.lowPrice);
                setDiscouponPrice(res.item.itemLowPrice || res.item.lowPrice);
                if (selectSkuId.current) {
                    querySkuPriceInfo(skuNum || 1);
                }
            })
            .catch(err => {
                console.log(err);
            });
    };

    const takeCart = type => {
        // e 0:加购；1:下单
        const goodsName = goodsItem.name;

        for (let index = 0; index < skusAttr.length; index++) {
            const item = skusAttr[index];
            if (!item.selected) {
                return DDYToast.info(`请选择 ${item.name}`);
            }
        }

        if (goodsItem.status === -1) {
            DDYToast.error("商品已被下架, 无法购买！");
            let map = new Map();
            map.set(mall_event_key.MALL_KEY_GOOD_ID, goodsItem.id);
            map.set(mall_event_key.MALL_KEY_GOOD_NUM, orderNum);
            map.set(mall_event_key.MALL_KEY_ITEM_NAME, goodsName);
            map.set(mall_event_key.MALL_KEY_BUY_FAIL_REASON, "商品已被下架, 无法购买！");
            reportEvent(mall_event.MALL_EVENT_GOOD_IMMEDIATELY_BUY_FAIL, map);
            return;
        }

        if ((getStorage(IS_LOGIN) || "") != "1") {
            jumpLogin();
            let map = new Map();
            map.set(mall_event_key.MALL_KEY_GOOD_ID, goodsItem.id);
            map.set(mall_event_key.MALL_KEY_GOOD_NUM, orderNum);
            map.set(mall_event_key.MALL_KEY_ITEM_NAME, goodsName);
            map.set(mall_event_key.MALL_KEY_BUY_FAIL_REASON, "未登录，跳转到登录！");
            reportEvent(mall_event.MALL_EVENT_GOOD_IMMEDIATELY_BUY_FAIL, map);
            return false;
        } else {
            removeStorage(SCAN_PAGE);
        }

        confirmTake(type);
    };

    const confirmTake = type => {
        if (type === 0) {
            doTakeCart();
        } else {
            doTakeOrder();
        }
    };

    const doTakeOrder = () => {
        const type = goodsItem.type;
        const goodsId = goodsItem.id;
        const goodsName = goodsItem.name;
        const sourceType = goodsItem.sourceType;
        const specId = getSpecId();

        let max = 0;

        let map = new Map();
        map.set(mall_event_key.MALL_KEY_GOOD_ID, goodsId);
        map.set(mall_event_key.MALL_KEY_GOOD_NUM, orderNum);
        map.set(mall_event_key.MALL_KEY_ITEM_NAME, goodsName);
        map.set(mall_event_key.MALL_KEY_GOOD_SPEC_ID, specId);
        reportEvent(mall_event.MALL_EVENT_GOOD_IMMEDIATELY_BUY, map);

        const { activityName, marketingToolId, activityId } = selectedSkuInfo.current;
        const data = [
            {
                skuId: selectSkuId.current,
                quantity: orderNum,
                sourceType: sourceType,
                note: note,
                activityName: activityName || "",
                marketingToolId: marketingToolId || "",
                activityId: activityId,
            },
        ];
        api.preCheck({
            data: {
                data: JSON.stringify(data),
            },
        }).then(res => {
            if (type == "3" || type == "4" || type == "5") {
                DDYNavigateTo({
                    url:
                        "/pages/order/comfire-order/index?goodsId=" +
                        goodsId +
                        "&orderNum=" +
                        orderNum +
                        "&specId=" +
                        specId +
                        "&exchange=integral" +
                        "&maxDiscount=" +
                        max +
                        "&sourceType=" +
                        sourceType +
                        "&groupAction=" +
                        (groupAction || "") +
                        "&activityName=" +
                        (activityName || "") +
                        "&marketingToolId=" +
                        (marketingToolId || "") +
                        "&note=" +
                        (note || "") +
                        "&groupId=" +
                        (groupId || "") +
                        "&activityId=" +
                        (activityId || ""),
                });
            } else {
                DDYNavigateTo({
                    url:
                        "/pages/order/comfire-order/index?goodsId=" +
                        goodsId +
                        "&orderNum=" +
                        orderNum +
                        "&specId=" +
                        specId +
                        "&maxDiscount=" +
                        max +
                        "&sourceType=" +
                        sourceType +
                        "&groupAction=" +
                        (groupAction || "") +
                        "&activityName=" +
                        (activityName || "") +
                        "&marketingToolId=" +
                        (marketingToolId || "") +
                        "&note=" +
                        (note || "") +
                        "&groupId=" +
                        (groupId || "") +
                        "&activityId=" +
                        (activityId || ""),
                });
            }
        });
    };

    const getSpecId = () => {
        // todp
        return selectSkuId.current;
    };
    const doTakeCart = () => {
        const specId = getSpecId();
        if (!specId) {
            return DDYToast.info("请选择规格");
        }
        let map = new Map();
        map.set(mall_event_key.MALL_KEY_GOOD_ID, specId);
        map.set(mall_event_key.MALL_KEY_GOOD_NUM, orderNum);
        reportEvent(mall_event.MALL_EVENT_ADD_CART, map);
        if (!skuId) {
            api.addCart({
                method: "POST",
                contentType: "application/x-www-form-urlencoded",
                data: {
                    skuId: String(specId),
                    quantity: String(orderNum),
                    buyerNote: note,
                },
                filterCheck: true,
            })
                .then(res => {
                    if (res > 0) {
                        DDYToast.success("成功加入购物车");
                        closeFn(true);
                        getCartNumber();
                    } else {
                        DDYToast.info("加购失败");
                    }
                })
                .catch(err => {
                    DDYToast.info("加入购物车失败" + err.message);
                });
        } else {
            // 修改购物车skuid
            newAPi
                .updateCartsSku({
                    method: "POST",
                    data: {
                        id: cartItemId,
                        skuId: String(specId),
                        quantity: String(orderNum),
                        buyerNote: note,
                    },
                })
                .then(res => {
                    DDYToast.success("修改成功");
                    closeFn(true);
                    getCartNumber();
                });
        }
    };

    /**
     * 更新首页购物车 的数量标识
     */

    const getCartNumber = () => {
        newAPi
            .getCartCount({
                data: {
                    shopId: getStorage(STORE_ID),
                },
                method: "POST",
                // filterCheck: true,
            })
            .then(res => {
                const cart = res.data;
                if (cart > 0) {
                    Taro.setTabBarBadge({
                        index: 2,
                        text: cart,
                    });
                } else {
                    Taro.removeTabBarBadge({
                        index: 2,
                    });
                }
                DDYSwitchTab({
                    url: "/pages/shop_cart/index",
                });
            })
            .catch();
    };

    useEffect(() => {
        if (goodsItem) {
            setPrice(goodsItem.itemLowPrice || goodsItem.lowPrice);
        }
    }, [goodsItem]);

    useEffect(() => {
        if (!goodsId) return;
        if (skuLoad) {
            getDetailInfo();
        } else {
            if (!skuLoad && goodsId) {
                getDetailInfo();
                selectSkuId.current = skuId;
            }
        }
    }, [goodsId]);

    useEffect(() => {
        if (skuNum) {
            setOrderNum(skuNum);
        }
    }, [skuNum]);

    return (
        <>
            {/* <ActionSheet
                show={show}
                style={{ zIndex: 3000 }}
                title={<View style={{ height: "49px" }}>规格属性</View>}
                onClose={() => {
                    closeFn();
                }}
            > */}
            <View className="sku-add-popup">
                {goods.map(item => {
                    return (
                        <GoodsItem
                            isLogin={isLogin}
                            info={{
                                ...item,
                                mainImage: goodsId === item.id && isSelectdFinished ? mainPic : item.mainImage,
                                isSelected: goodsId === item.id,
                                selectPrice: price,
                                selectSku: sku,
                                selectDiscountPrice: discountPrice,
                            }}
                            isSelectdFinished={isSelectdFinished}
                            key={item.id}
                            jumpLogin={jumpLogin}
                            setSelectId={setSelectId}
                            setIsSelectdFinished={setIsSelectdFinished}
                        ></GoodsItem>
                    );
                })}
                <View className="sku-rect">
                    {/* 规格选择区域 */}
                    <View>
                        {skusAttr.map((items, skusIndex) => {
                            return (
                                <View className="sku-attr-item">
                                    <View className="sku-attr-item-title">{items.name}</View>
                                    <View className="sku-attr-item-selects">
                                        {items.itemSpecificationDetailParams.map((item, index) => {
                                            if (item.isHide) return null;
                                            return (
                                                <View
                                                    onClick={() => {
                                                        handleSkuClick(items, item, skusIndex);
                                                    }}
                                                    className={`sku-attr-item-selects-item
                                                                ${items.selected === item.frontId ? "selected" : ""}
                                                                ${item.status === -1 || item.isHide ? "expire" : ""}
                                                        `}
                                                >
                                                    {item.name}
                                                    {/* {item.price ? ` ¥${item.price / 100}` : null} */}
                                                    {item.status === -1 ? (
                                                        <View className="expire-tag">库存不足</View>
                                                    ) : null}
                                                </View>
                                            );
                                        })}
                                    </View>
                                </View>
                            );
                        })}
                    </View>
                    <View className="popup-item">
                        <Text className="item-title">
                            数量选择
                            <Text className="item-title-quantity">购买数量库存：{stockQuantity || 0}件</Text>
                        </Text>
                        <View className="item-right">
                            <Stepper
                                value={orderNum}
                                max={Math.min(
                                    stockQuantity,
                                    limitQuantity || Infinity,
                                    goodsItem?.extra?.skuOrderSplitLine || Infinity,
                                )}
                                // limitQuantity
                                //     ? stockQuantity > limitQuantity
                                //         ? limitQuantity
                                //         : stockQuantity
                                //     : stockQuantity
                                // }
                                onChange={val => {
                                    setOrderNum(val.detail as number);
                                    querySkuPriceInfo(val.detail);
                                }}
                            />
                        </View>
                    </View>
                </View>
            </View>
            {/* </ActionSheet> */}
            <Dialog_ />
        </>
    );
});
