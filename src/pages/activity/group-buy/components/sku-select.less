.sku-add-popup {
    max-height: 70%;
    padding: 10px 21px;
    --popup-z-index: 4100;
    --overlay-z-index: 4090;
    .actionSheet-goods-item {
        display: flex;
        flex-direction: row;
        align-items: center;
        padding: 20px;
        .actionSheet-goods-item-img {
            width: 152px;
            height: 152px;
            border-radius: 8px;
        }

        .actionSheet-goods-item-info {
            height: 152px;
            display: flex;
            flex-direction: column;
            width: 500px;
            justify-content: space-between;
            margin-left: 33px;
            .actionSheet-goods-item-name {
                // font-size: 32px;
                // line-height: 38px;
                // height: 76px;
                font-family: PingFangSC, PingFang SC;
                font-weight: 600;
                font-size: 32px;
                color: #323232;
                line-height: 38px;
                text-align: left;
                font-style: normal;
                display: -webkit-box;
                -webkit-box-orient: vertical;
                -webkit-line-clamp: 1; /* 限制为1行 */
                overflow: hidden;
                text-overflow: ellipsis; /* 超出部分显示省略号 */
            }
            .actionSheet-goods-item-skus-attr {
                font-weight: 400;
                font-size: 22px;
                color: #999999;
                line-height: 30px;
                text-align: left;
                font-style: normal;
                display: -webkit-box;
                -webkit-box-orient: vertical;
                -webkit-line-clamp: 1; /* 限制为1行 */
                overflow: hidden;
                text-overflow: ellipsis; /* 超出部分显示省略号 */
            }
            .cart-good-price {
                display: flex;
                flex-direction: row;
                align-items: center;
                .cart-good-origin {
                    font-weight: 500;
                    font-size: 24px;
                    color: #f50e0c;
                    line-height: 33px;
                    letter-spacing: 2px;
                    text-align: left;
                    font-style: normal;
                }
                .cart-good-discount {
                    display: inline-block;
                    border-radius: 69px;
                    // width: 183px;
                    height: 69px;

                    padding: 0 20px;
                    background: linear-gradient(90deg, #de1600 0%, #de1600 100%);
                    box-shadow: 0px 0px 8px 0px rgba(255, 22, 73, 0.4);
                    display: flex;
                    flex-direction: row;
                    align-items: center;
                    justify-content: center;
                    margin-left: 22px;
                    .discount-text1 {
                        font-weight: 400;
                        font-size: 18px;
                        color: #ffffff;
                        line-height: 17px;
                        text-align: left;
                        font-style: normal;
                    }
                    .discount-text2 {
                        margin-left: 6px;
                        font-weight: 500;
                        font-size: 40px;
                        color: #ffffff;
                        line-height: 17px;
                        text-align: left;
                        font-style: normal;
                    }
                }
            }
        }
    }
    .sku-scroll::-webkit-scrollbar {
        display: none;
    }
    .sku-rect {
        // height: 400px;
        padding: 10px;
        margin: 0 10px;
        position: relative;
        .sku-attr-item {
            .sku-attr-item-title {
                margin: 16px 0;
                font-family: PingFangSC, PingFang SC;
                font-weight: 400;
                font-size: 20px;
                color: #999999;
                line-height: 37px;
                text-align: left;
                font-style: normal;
                display: inline-block;
            }
            .sku-attr-item-selects {
                display: flex;
                flex-direction: row;
                align-items: center;
                flex-wrap: wrap;
                width: 100%;
                .sku-attr-item-selects-item {
                    padding: 15px 18px;
                    font-family: PingFangSC, PingFang SC;
                    font-weight: 400;
                    font-size: 20px;
                    color: #ff0940;
                    line-height: 28px;
                    border-radius: 10px;
                    border: 2px solid #dedede;
                    background-color: #fff;
                    margin-right: 21px;
                    box-sizing: border-box;
                    // overflow: hidden;
                    // text-overflow: ellipsis;
                    // word-wrap:normal;
                    // white-space: ;
                    margin-bottom: 9px;
                    color: #333;
                    // width: 303px;
                    // height: 59px;
                    position: relative;
                }
                .selected {
                    border: 2px solid #ff0940;
                    color: #ff0940;
                }
                .expire {
                    background-color: rgba(242, 242, 242, 1);
                    padding: 13px 16px;
                    color: #cfcfcf;
                    border: 1px solid #e5e4e4;
                    z-index: 100;
                    .expire-tag {
                        width: 87px;
                        height: 26px;
                        background: #b5b5b5;
                        border-radius: 8px;
                        font-weight: 400;
                        font-size: 15px;
                        color: #ffffff;
                        line-height: 26px;
                        text-align: left;
                        font-style: normal;
                        position: absolute;
                        text-align: center;

                        top: -13px;
                        right: -43px;
                    }
                }
            }
        }
    }
    .popup-item {
        width: 100%;
        padding: 10px 0;
        border-bottom: none;
        display: flex;
        justify-content: space-between;
        margin-bottom: 10px;
        // margin: 12px 0;
        .item-title {
            margin: 12px 0;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            font-size: 24px;
            color: #5a6c91;
            line-height: 33px;
            text-align: left;
            font-style: normal;
            &-quantity {
                margin-left: 24px;
                font-family: PingFangSC, PingFang SC;
                font-weight: 400;
                font-size: 20px;
                color: #999999;
                line-height: 28px;
            }
        }
        .van-cell {
            width: 120px;
        }
        .item-right {
            display: flex;
            flex-direction: row;
            align-items: center;
            .item-limit {
                font-weight: 400;
                font-size: 22px;
                color: #999999;
                line-height: 30px;
                text-align: left;
                font-style: normal;
                margin-right: 14px;
            }
        }
        .item-right-icon {
            position: relative;
            font-weight: 400;
            font-size: 22px;
            color: #999999;
            line-height: 38px;
            text-align: left;
            font-style: normal;
            padding-right: 24px;
            padding-top: 6px;
            display: inline-block;
            max-width: 100px;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 1;
            -webkit-box-orient: vertical;
            margin: 0 10px;
            word-break: break-all;
            &:after {
                position: absolute;
                right: 0px;
                top: 5px;
                content: "\e678";
                font-family: "iconfont-fx";
            }
        }
    }
}
