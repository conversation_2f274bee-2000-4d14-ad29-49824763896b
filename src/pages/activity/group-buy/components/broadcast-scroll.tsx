import { View, Image, Text } from "@tarojs/components";
import { defaultAvatar, ScrollStatus } from "../enum";
import { useEffect, useRef, useState } from "react";
import Taro from "@tarojs/taro";
import { resolve } from "path";

export default ({ scrollItems }) => {
    // 滚动
    const maxScroll = useRef(0);
    const maxContainerWidth = useRef(0);

    const [animationData, setAnimationData] = useState({}); // 动画数据
    let animation = useRef<Animation>(null);
    const [isAnimating, setIsAnimating] = useState(true); // 动画状态
    const [isShowItem, setIsShowItem] = useState(false);
    // 初始化获取滚动范围
    const getScrollRange = () => {
        /**
         * 获取滚动容器宽度+滚动内容宽度
         */
        Promise.all([
            new Promise((resolve, reject) => {
                Taro.createSelectorQuery()
                    .selectAll(`.broadcast-item`)
                    .boundingClientRect((rects: any) => {
                        console.log("rects:", rects);
                        if (!rects || rects.length === 0) return resolve(0);

                        // 2. 获取最后一个元素的信息
                        const lastElement = rects[rects.length - 1];
                        console.log(lastElement);
                        maxScroll.current = lastElement.right;
                        resolve(lastElement.right);
                    })
                    .exec();
            }),
            new Promise((resolve, reject) => {
                Taro.createSelectorQuery()
                    .selectAll(`#broadcast`)
                    .boundingClientRect((rects: any) => {
                        console.log("broadcastrects:", rects);
                        // 2. 获取最后一个元素的信息
                        const lastElement = rects[rects.length - 1];
                        console.log(lastElement);
                        // return lastElement.width;
                        resolve(lastElement.width);
                    })
                    .exec();
            }),
        ]).then((ress: [number, number]) => {
            console.log(ress);
            // let animateCount = Math.min(ress[0], ress[1])
            maxScroll.current = Math.max(ress[0], ress[1]);
            maxContainerWidth.current = ress[1];
            animation.current = Taro.createAnimation({
                duration: (maxScroll.current / 20) * 500, // 动画持续时间
                timingFunction: "linear", // 动画效果
                delay: 0, // 动画延迟
            });
            startAnimation();
        });
    };

    useEffect(() => {
        if (scrollItems.length) {
            Taro.nextTick(() => {
                getScrollRange();
            });
        }
    }, [scrollItems]);

    // 启动动画
    const startAnimation = () => {
        setIsAnimating(true);
        setIsShowItem(true);
        reAnimation();
    };

    // 停止动画
    const stopAnimation = () => {
        setIsAnimating(false);
    };
    // 动画逻辑
    const animate = () => {
        if (!isAnimating) return; // 如果动画已停止，则退出递归
        animation.current?.translateX(-maxScroll.current).step();

        // 更新动画数据
        setAnimationData(animation.current?.export());
    };

    const reAnimation = () => {
        // if (!isAnimating) return; // 如果动画已停止，则退出递归
        animation.current?.translateX(maxContainerWidth.current).step({ duration: 0 }); // 重置动画
        // this.animationData = this.animation.export()
        // 动画属性在动画执行阶段是可以自动更新的。
        // 但是走新的动画状态的时候，需要更改animationData引用地址，不然无法更新
        setAnimationData({ ...animation.current.export() });
        setTimeout(() => {
            console.log("重置动画可能会被覆盖");
            // 重置动画可能会被覆盖
            animate();
        }, 20);
    };
    return (
        <>
            <View className="b-container">
                <View
                    id="broadcast"
                    className={`broadcast`}
                    // ref={ref => contentRef.current = ref}
                    animation={animationData}
                    onTransitionEnd={reAnimation}
                >
                    {(scrollItems || []).map((item, index) => (
                        <View
                            className="broadcast-item"
                            key={index}
                            style={{ visibility: isShowItem ? "visible" : "hidden" }}
                        >
                            <Image className="broadcast-item-img" src={item.userAvatar || defaultAvatar}></Image>
                            <View className="broadcast-item-text">
                                {`${item.userGroup || ""} ${item.userGroup && item.nickname ? "-" : ""} ${
                                    item.nickname || ""
                                }`}
                                <Text className="broadcast-item-text-status">{ScrollStatus[item.groupStatus]}</Text>
                            </View>
                        </View>
                    ))}
                    {/* {(scrollItems || []).map((item, index) => (
                        <View className="broadcast-item" key={index}>
                            <Image className="broadcast-item-img" src={item.userAvatar || defaultAvatar}></Image>
                            <View className="broadcast-item-text">
                                {`${item.userGroup || ""} ${item.userGroup && item.nickname ? "-" : ""} ${item.nickname || ""
                                    }`}
                                <Text className="broadcast-item-text-status">{ScrollStatus[item.groupStatus]}</Text>
                            </View>
                        </View>
                    ))} */}
                </View>
            </View>
        </>
    );
};
