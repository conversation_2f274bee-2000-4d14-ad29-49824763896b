.activity-container {
    background-color: #f2f2f2;
    min-height: 100vh;
    padding-bottom: calc(98px + env(safe-area-inset-bottom));

    .nav-bar {
        background-color: transparent;

        .van-icon-arrow-left:before {
            content: "\e668";
            color: #000;
            font-size: 48px;
        }
    }

    .carousel {
        width: 100%;
        position: relative;

        .swiper {
            width: 100%;

            .swiper-item {
                width: 100%;
                height: 100%;

                &-img {
                    width: 100%;
                    height: 100%;
                }
            }
        }

        .custom-indicator {
            position: absolute;
            right: 26px;
            bottom: 19px;
            z-index: 2;
            background: #ffffff;
            border-radius: 14px;
            opacity: 0.61;
            font-family: PingFangSC, PingFang SC;
            font-weight: 600;
            font-size: 18px;
            color: #000000;
            line-height: 25px;
            text-align: center;
            font-style: normal;
            padding: 0 20px;
        }
    }

    > .desc {
        // margin-top: -4px;
        width: 750px;
        background: #f2f2f2;
        border-radius: 40px;
        box-shadow: 0px -6px 6px 0px rgba(175, 175, 175, 0.5);
        z-index: 2;
        position: relative;

        .shape {
            padding: 22px 0 6px 23px;
            display: flex;
            background: #ffffff;
            border-radius: 40rpx 40px 0 0;

            .circle {
                width: 12px;
                height: 12px;
                background: #ff4d23;
                border-radius: 50%;
                margin-right: 11px;
            }

            .ellipse {
                width: 50px;
                height: 12px;
                background: #ff4d23;
                border-radius: 6px;
            }
        }

        > .content {
            background: #ffffff;
            font-family: PingFangSC, PingFang SC;
            font-weight: 600;
            font-size: 30px;
            color: #323232;
            line-height: 42px;
            text-align: left;
            font-style: normal;
            display: -webkit-box;
            /* 设置为WebKit内核的弹性盒子模型 */
            -webkit-box-orient: vertical;
            /* 垂直排列 */
            -webkit-line-clamp: 2;
            /* 限制显示两行 */
            overflow: hidden;
            /* 隐藏超出范围的内容 */
            text-overflow: ellipsis;
            /* 使用省略号 */
            box-sizing: border-box;
            padding: 0 43px;
        }

        .group-detail {
            background: #ffffff;
            padding-top: 12px;
            margin-bottom: 25px;
            border-radius: 0 0 40px 40px;

            .general {
                line-height: 30px;
                font-family: PingFangSC, PingFang SC;
                font-weight: 600;
                font-size: 24px;
                color: #5a6c91;
                line-height: 30px;
                font-style: normal;
                margin-top: 10px;
                text-align: center;
            }

            .b-container {
                overflow: hidden;
                margin: 0 21px;

                .broadcast {
                    height: 89px;
                    display: flex;

                    &-item {
                        flex-shrink: 0;
                        margin-left: 20px;
                        display: flex;
                        align-items: center;

                        &:last-of-type {
                            margin-right: 20px;
                        }

                        &-img {
                            width: 42px;
                            height: 42px;
                            border-radius: 50%;
                            margin-right: 4px;
                            flex-shrink: 0;
                        }

                        &-text {
                            flex-shrink: 0;
                            padding: 0 19px;
                            height: 42px;
                            line-height: 42px;
                            background: #ffffff;
                            box-shadow: 0px 2px 4px 0px rgba(223, 223, 223, 0.5);
                            border-radius: 18px;
                            font-family: PingFangSC, PingFang SC;
                            font-weight: 400;
                            font-size: 22px;
                            color: #333333;
                            font-style: normal;
                            text-transform: uppercase;

                            &-status {
                                margin-left: 10px;
                                color: #ffae02;
                            }
                        }
                    }
                }
            }

            .activity {
                width: 708px;
                height: 334px;

                border-radius: 36px;
                margin: 0 auto;
                display: flex;
                flex-direction: column;

                &-top {
                    height: 67px;
                    background: #fef4f4;
                    border-radius: 36px 36px 0 0;
                    display: flex;
                    justify-content: space-between;
                    padding-left: 41px;
                    padding-right: 43px;
                    padding-bottom: 40px;

                    &-left {
                        height: 100%;
                        display: flex;
                        align-items: center;
                        font-family: PingFangSC, PingFang SC;
                        font-weight: 300;
                        font-size: 22px;
                        color: #181818;
                        line-height: 30px;
                        text-align: left;
                        font-style: normal;

                        .hot {
                            margin-right: 9px;
                            width: 28px;
                            height: 33px;
                        }
                    }

                    &-right {
                        height: 100%;
                        padding-top: 26px;
                        font-family: PingFangSC, PingFang SC;
                        font-weight: 300;
                        font-size: 22px;
                        color: #747474;
                        line-height: 30px;
                        text-align: left;
                        font-style: normal;
                    }
                }

                &-bottom {
                    display: flex;
                    padding-top: 22px;
                    box-shadow: 0px 3px 7px 0px rgba(225, 225, 225, 0.5);
                    border-radius: 27px;
                    flex-grow: 1;
                    background: #ffffff;
                    margin-top: -40px;

                    &-left {
                        width: 406px;
                        display: flex;
                        flex-direction: column;
                        padding: 0 22px 0 41px;
                        box-sizing: border-box;

                        .num {
                            font-family: PingFangSC, PingFang SC;
                            font-weight: 300;
                            font-size: 24px;
                            color: #999999;
                            line-height: 33px;
                            text-align: left;
                            font-style: normal;
                            padding-left: 5px;
                            display: flex;
                            align-items: center;

                            .group-success {
                                flex-grow: 0;
                                display: inline-block;
                                margin-left: 20px;
                                padding: 0 20px;
                                line-height: 33px;
                                text-align: center;
                                background: #f5c478;
                                border-radius: 34px;
                                font-family: PingFangSC, PingFang SC;
                                font-weight: 400;
                                font-size: 18px;
                                color: #ffffff;
                                font-style: normal;
                            }
                        }

                        .progress {
                            margin: 23px 0 30px 0;
                            width: 343px;
                            border-radius: 16px;
                        }

                        .peoples-container {
                            height: 83px;
                            width: 343px;

                            .peoples {
                                display: flex;

                                .people-item {
                                    margin: 0 16px;
                                    width: 70px;
                                    display: flex;
                                    flex-direction: column;
                                    align-items: center;

                                    .head-img {
                                        width: 70px;
                                        height: 70px;
                                        border-radius: 50%;
                                    }

                                    .group-icon {
                                        width: 49px;
                                        height: 20px;
                                        line-height: 20px;
                                        text-align: center;
                                        background: #fe4d2c;
                                        border-radius: 7px;
                                        border: 1px solid #ffffff;
                                        margin-top: -8px;
                                        font-family: PingFangSC, PingFang SC;
                                        font-weight: 300;
                                        font-size: 14px;
                                        color: #ffffff;
                                        line-height: 20px;
                                        font-style: normal;
                                        z-index: 2;
                                    }
                                }
                            }
                        }

                        .detail-tips {
                            margin-top: 10px;
                            font-family: PingFangSC, PingFang SC;
                            font-weight: 600;
                            font-size: 20px;
                            color: #5a6c91;
                            line-height: 28px;
                            text-align: left;
                            font-style: normal;
                        }
                    }

                    &-right {
                        width: 100%;
                        display: flex;
                        flex-direction: column;
                        justify-content: space-between;
                        height: 175px;
                        border-left: 1px solid #ececec;
                        margin-top: 18px;

                        &-top {
                            flex-grow: 1;

                            display: flex;

                            justify-content: space-evenly;

                            .block {
                                display: flex;
                                flex-direction: column;
                                align-items: center;

                                .title {
                                    margin-top: 7px;
                                    height: 30px;
                                    line-height: 30px;
                                    font-family: PingFangSC, PingFang SC;
                                    font-weight: 400;
                                    font-size: 22px;
                                    color: #413f3f;
                                    line-height: 30px;
                                    font-style: normal;
                                    margin-bottom: 24px;

                                    &.maringTop31 {
                                        margin-top: 31px;
                                    }
                                }

                                .val {
                                    height: 33px;
                                    line-height: 33px;
                                    font-family: PingFangSC, PingFang SC;
                                    font-weight: 300;
                                    font-size: 24px;
                                    color: #999999;
                                    line-height: 33px;
                                    font-style: normal;
                                }
                            }
                        }

                        &-bottom {
                            width: 120px;
                            height: 38px;
                            line-height: 38px;
                            background: #ff0940;
                            border-radius: 34px;
                            font-family: PingFangSC, PingFang SC;
                            font-weight: 400;
                            font-size: 18px;
                            color: #ffffff;
                            text-align: center;
                            font-style: normal;
                            margin: 0 auto;
                            padding: 0;
                        }

                        &-success {
                            display: flex;
                            justify-content: center;
                            height: 40px;
                            line-height: 40px;
                            font-family: PingFangSC, PingFang SC;
                            font-weight: 600;
                            font-size: 20px;
                            color: #5a6c91;
                            font-style: normal;

                            &-img {
                                height: 40px;
                                width: 40px;
                                margin-right: 14px;
                            }
                        }
                    }
                }
            }
        }

        .detail {
            width: 709px;
            display: flex;
            flex-direction: column;
            align-items: center;
            position: relative;
            z-index: 2;
            background-color: #f2f2f2;
            margin: 10px auto 0;
            box-sizing: content-box;
            padding: 0 20px;

            .content {
                width: 709px;
                border-radius: 49px 49px 0 0;
                padding-bottom: 30px;

                .goods-detail-img {
                    width: 100%;
                    padding: 0 30px;
                    box-sizing: border-box;
                }

                .rule-content {
                    margin: 20px 30px 10px;
                    height: 92px;
                    background: rgba(122, 122, 122, 0.04);
                    border-radius: 22px;
                    display: flex;
                    align-items: center;
                    justify-content: center;

                    &.margin0 {
                        margin-top: 0;
                    }

                    &-text {
                        width: 618px;
                        height: 62px;
                        line-height: 62px;
                        background: #ffffff;
                        border-radius: 8px;
                        font-family: PingFangSC, PingFang SC;
                        font-weight: 300;
                        font-size: 20px;
                        color: #999999;
                        line-height: 50px;
                        text-align: left;
                        font-style: normal;
                        display: flex;
                        align-items: center;
                        box-sizing: border-box;
                        padding-left: 13px;
                        &-xieyi {
                            color: #5f63e4;
                            margin-left: 10px;
                            flex-grow: 1;
                        }
                        &-arrow {
                            text-align: right;
                            padding-right: 14px;
                        }
                    }
                }

                .discount-content {
                    margin: 0 26px;
                    padding: 26px;
                    background: #f9f9f9;
                    border-radius: 27px;

                    &-item {
                        background: url(https://dante-img.oss-cn-hangzhou.aliyuncs.com/1302802331.png) no-repeat top
                            left;
                        background-size: 100% 100%;
                        height: 187px;
                        display: flex;
                        align-items: center;
                        margin: 0 0 26px;
                        &:last-of-type {
                            margin-bottom: 0;
                        }

                        &-left {
                            width: 161px;
                            height: 130rpx;
                            flex-shrink: 0;
                            font-family: PingFangSC, PingFang SC;
                            font-weight: 500;
                            font-size: 32px;
                            color: #ff0940;
                            line-height: 45px;
                            text-align: center;
                            font-style: normal;
                            display: flex;
                            align-items: center;
                            flex-direction: column;
                            justify-content: center;
                            border-right: 1px dashed #ffffff;
                        }

                        &-right {
                            display: flex;
                            width: 262px;
                            height: 79px;
                            border-left: 1px dashed #ffffff;
                            flex-direction: column;
                            justify-content: center;
                            align-items: center;
                            flex-shrink: 0;
                            font-family: PingFangSC, PingFang SC;
                            font-weight: 500;
                            font-size: 18px;
                            color: #ff0940;
                            line-height: 25px;
                            text-align: center;
                            font-style: normal;
                        }

                        &-middle {
                            display: flex;
                            justify-content: center;
                            flex-grow: 1;
                            align-items: center;

                            .num {
                                font-family: PingFangSC, PingFang SC;
                                font-weight: 600;
                                font-size: 32px;
                                color: #ff0940;
                                line-height: 72px;
                                font-style: normal;
                                margin-right: 10px;
                            }

                            .desc {
                                font-family: PingFangSC, PingFang SC;
                                font-weight: 400;
                                font-size: 18px;
                                color: #ff0940;
                                line-height: 25px;
                                font-style: normal;
                                writing-mode: vertical-rl;
                                /* 从右向左的垂直排列 */
                            }

                            .coupon {
                                display: flex;
                                flex-direction: column;
                                align-items: center;
                                text-align: center;
                                font-family: PingFangSC, PingFang SC;
                                font-weight: 400;
                                font-size: 22px;
                                color: #ff0940;
                                line-height: 30px;
                                font-style: normal;

                                &-name {
                                    width: 120px;
                                    display: -webkit-box;
                                    /* 设置为WebKit内核的弹性盒子模型 */
                                    -webkit-box-orient: vertical;
                                    /* 垂直排列 */
                                    -webkit-line-clamp: 1;
                                    /* 限制显示两行 */
                                    overflow: hidden;
                                    /* 隐藏超出范围的内容 */
                                    text-overflow: ellipsis;
                                    /* 使用省略号 */
                                }
                            }
                        }
                    }
                }

                .discount-time {
                    width: 100%;
                    text-align: center;
                    margin-top: 16px;
                    font-family: PingFangSC, PingFang SC;
                    font-weight: 400;
                    font-size: 22px;
                    color: #5a6c91;
                    line-height: 30px;
                    font-style: normal;
                }
            }
        }
    }

    .log-tips {
        color: #969799;
        font-size: 28px;
        margin: 0 auto;
        text-align: center;
    }

    .bottom {
        width: 100%;
        height: 98px;
        background: #ffffff;
        position: fixed;
        bottom: 0;
        left: 0;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding-bottom: calc(env(safe-area-inset-bottom));
        z-index: 3;

        .left-btns {
            flex-grow: 1;
            display: flex;
            justify-content: space-evenly;

            &-item {
                display: flex;
                flex-direction: column;
                align-items: center;

                &-icon {
                    width: 41px;
                    height: 40px;
                    margin-bottom: 6px;
                }

                &-title {
                    font-family: PingFangSC, PingFang SC;
                    font-weight: 400;
                    font-size: 20px;
                    color: #666666;
                    line-height: 20px;
                    text-align: center;
                    font-style: normal;
                }

                &.nostyle {
                    margin: 0;
                    padding-left: 0;
                    padding-right: 0;
                    border-radius: 0;
                    background-color: #fff;
                    border: none !important;
                }
            }
        }

        .right-btns {
            width: 420px;
            background: #ff0940;
            height: 75px;
            line-height: 75px;
            text-align: center;
            border-radius: 37px;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            font-size: 26px;
            color: #ffffff;
            font-style: normal;
            margin: 0 12px;
            flex-shrink: 0;
            position: relative;
            overflow: visible;

            .count-down {
                font-weight: 400;
                font-size: 26px;
                color: #ffffff;
                font-style: normal;
                display: inline-block;
                margin-left: 10px;
            }

            &::after {
                display: none;
            }

            .tips {
                height: 55px;
                position: absolute;
                right: 0;
                top: -18px;
                background: url(https://dante-img.oss-cn-hangzhou.aliyuncs.com/93126256868.png) no-repeat top right;
                background-size: auto 100%;

                &-content {
                    height: 34px;
                    border-radius: 48px;
                    background-color: #f9a626;
                    line-height: 34px;
                    text-align: center;
                    font-family: PingFangSC, PingFang SC;
                    font-weight: 400;
                    font-size: 18px;
                    color: #ffffff;
                    font-style: normal;
                    padding: 0 14px;
                }
            }
        }
    }

    .my-group-popup {
        // display: flex;
        // flex-direction: column;
        // align-items: center;
        height: 100%;

        &-title {
            width: 100%;
            text-align: center;
            font-size: 32px;
            font-family: PingFangSC-Medium, PingFang SC;
            line-height: 96px;
            // flex-shrink: 0;
        }

        &-content {
            box-sizing: border-box;
            background: #f2f2f2;
            padding: 20px 20px 20px;
            width: 100%;
            height: calc(100% - 96px);
            // flex-grow: 1;

            .my-group-item {
                box-sizing: border-box;
                padding-bottom: 20px;
                padding-top: 20px;
                background-color: #fff;
                border-radius: 32px;
                margin-bottom: 20px;
                display: flex;
                flex-direction: column;

                &-img {
                    width: 100%;
                }

                &-desc {
                    font-size: 24px;
                    line-height: 30px;
                    padding: 20px 20px;
                    display: flex;

                    &-status {
                        margin-right: 10px;
                        border-radius: 20px;
                        padding: 0 20px;
                        background-color: #fe4d2c;
                        color: #fff;
                        font-size: 18px;
                        height: 30px;
                        flex-shrink: 0;
                    }
                }

                &-btn {
                    width: 420px;
                    height: 75px;
                    line-height: 75px;
                    text-align: center;
                    background: #ff0940;
                    border-radius: 37px;
                    color: #fff;
                    font-size: 28px;
                    align-self: flex-end;
                    margin-right: 20px;
                    overflow: visible;
                    padding: 0;

                    &::after {
                        display: none;
                    }

                    .tips {
                        height: 55px;
                        position: absolute;
                        right: 0;
                        top: -18px;
                        background: url(https://dante-img.oss-cn-hangzhou.aliyuncs.com/93126256868.png) no-repeat top
                            right;
                        background-size: auto 100%;

                        &-content {
                            height: 34px;
                            border-radius: 48px;
                            background-color: #f9a626;
                            line-height: 34px;
                            text-align: center;
                            font-family: PingFangSC, PingFang SC;
                            font-weight: 400;
                            font-size: 18px;
                            color: #ffffff;
                            font-style: normal;
                            padding: 0 14px;
                        }
                    }
                }
            }
        }
    }
}

// @keyframes scroll {
//     0% {
//         transform: translateX(0);
//     }
//     100% {
//         transform: translateX(-100%); // 动态计算滚动距离
//     }
// }

.hide {
    visibility: hidden;
}
