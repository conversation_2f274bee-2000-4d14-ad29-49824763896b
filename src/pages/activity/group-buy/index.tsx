import { Progress, Loading, Dialog, NavBar, Popup, CountDown, Icon } from "@antmjs/vantui";
import { View, ScrollView, Image, Text, Button, Swiper, SwiperItem } from "@tarojs/components";
import Taro, { useRouter, useReachBottom, useShareAppMessage, usePageScroll, useDidShow } from "@tarojs/taro";
import React, { useEffect, useState, useRef, Fragment, useMemo } from "react";
import { mall_event, mall_event_key, reportEvent } from "@/utils/track-report";
import "./index.less";
import newAPi from "@/utils/newApi";
import { DDYNavigateTo, DDYBack, DDYSwitchTab } from "@/utils/route";
import { isAlipay, queryInfo, saveSignback, useSafeRouter } from "@/utils/common";
import CustomCard from "../components/card";
import RecordItem from "../components/record-item";
import infoStore from "@/store/info-store";
import { BottomBtns, PageStatus, defaultAvatar, <PERSON>, Reward, ScrollStatus, PeopleOrderStatus } from "./enum";
import { jumpLogin } from "@/utils/PageUtils";
import api from "@/utils/api";
import DDYToast from "@/utils/toast";
import SkuSelect from "./components/sku-select";
import { INVITE_CODE } from "@/utils/constant";
import { getStorage, setStorage } from "@/utils/storage";
import { LOGIN_BACK_URL } from "@/utils/constant";
import MyGroup from "../components/my-group";
import BroadcastScroll from "./components/broadcast-scroll";
import skuSelect from "./components/sku-select";

definePageConfig({
    navigationStyle: "custom",
    enableShareAppMessage: true,
});

const Dialog_ = Dialog.createOnlyDialog();

const initInfo = {
    anonymousInfo: {
        activityInfo: {
            activityPics: [],
            scrollItems: [],
        },
        itemsDTOS: [],
    },
    realNameInfo: {
        memberAvatars: [],
        groupInfo: {},
    },
    groupActionButton: PageStatus.No_LOGIN,
};

export default () => {
    const { id, groupId, toolsId, inviteCode } = useSafeRouter().params;

    const firstEnter = useRef(true);

    const skuSelectRef = useRef<any>(null);

    const windowWidth = useRef(Taro.getSystemInfoSync().windowWidth);

    // 奖励信息数组
    const [rewardArr, setRewardArr] = useState<any[]>([]);

    // 规格弹窗
    const [skuPop, setSkuPop] = useState(false);

    // 商品详情图
    const [detailImgs, setDetailImgs] = useState([]);

    // 选中的商品id
    const [selectId, setSelectId] = useState();

    const takeGroupId = useRef(true);

    const [info, setInfo] = useState<any>({ ...initInfo });

    const [loading, setLoading] = useState(false);

    const [isLastPage, setIsLastPage] = useState(false);

    // 活动日志
    const [activityRecord, setActivityRecord] = useState<any[]>([]);

    const orderInfo = useRef();

    // 我的团弹窗是否可见
    const [myGroupVisible, setMyGroupVisible] = useState(false);

    // 轮播图片当前位置
    const [currentImg, setCurrentImg] = useState(0);

    // 导航栏透明度
    const [navOpacity, setNavOpacity] = useState(0); // 透明度 0~1

    const [pageStatus, setPageStatus] = useState<PageStatus>(PageStatus.No_LOGIN);

    const myGroupGroupId = useRef<null | number | string>(null);

    useDidShow(() => {
        refresh();
    });

    // 登录验证
    const goLogin = function () {
        Dialog_.alert({
            title: "提示",
            message: "您还没有登录或登录已经过期，请先登录再进行操作",
            confirmButtonText: "立即登录",
        }).then(value => {
            toLogin();
        });
    };

    // 调用子组件方法
    const handleSkuSelect = () => {
        if (skuSelectRef.current) {
            skuSelectRef.current?.handleBtnClick();
        }
    };

    const toLogin = () => {
        setStorage(
            LOGIN_BACK_URL,
            `pages/activity/group-buy/index?id=${id}&groupId=${groupId || ""}&inviteCode=${
                inviteCode || ""
            }&toolsId=${toolsId}&reLaunchFlag=1`,
        );
        const queryStr = encodeURIComponent(`activityId=${id}&marketingToolId=${toolsId}&groupId=${groupId || ""}`);
        jumpLogin(queryStr);
    };

    // tips内容
    const getDetailTipsDom = tips => {
        let dom;
        if (tips) {
            dom = <View className="detail-tips">{tips}</View>;
            switch (tips) {
                case "memberNoPayDom":
                    dom = (
                        <View className="detail-tips">
                            仅差<Text style={{ color: "#FF0940" }}>{info.realNameInfo.lackMembers || 0}</Text>
                            人，马上参团~
                        </View>
                    );
                    break;
                case "share":
                    dom = (
                        <View className="detail-tips">
                            仅差<Text style={{ color: "#FF0940" }}>{info.realNameInfo.lackMembers || 0}</Text>
                            人，分享给好友成团更快哦~
                        </View>
                    );
                    break;
                case "notMemberWaitJoinDom":
                    dom = (
                        <View className="detail-tips">
                            仅差<Text style={{ color: "#FF0940" }}>{info.realNameInfo.lackMembers || 0}</Text>
                            人，马上参团~
                        </View>
                    );
                    break;
            }
        }
        return dom;
    };

    // 页面配置
    const pSetting = useMemo((): any => {
        // 按钮显示
        let res = {};
        // 已登录
        switch (pageStatus) {
            case PageStatus.No_LOGIN:
                res = {
                    btnText: "登录并开团",
                    hideGeneralBtn: true,
                    hideProgress: true,
                    showAllAvatar: true,
                };
                break;
            case PageStatus.MEMBER_NO_PAY:
                res = {
                    btnText: "立即支付",
                    hideProgress: info.realNameInfo.memberRole == Role.GROUP,
                    showAllAvatar: info.realNameInfo.memberRole == Role.GROUP,
                    hideMember: info.realNameInfo.memberRole == Role.GROUP,
                    detailTips:
                        info.realNameInfo.memberRole == Role.GROUP
                            ? "支付完成立马开团"
                            : toolsId == "2"
                            ? `${
                                  info.realNameInfo.lackMembers === 0 ? "团已满，" : "成团后，"
                              }待所有团员支付成功即可发货`
                            : "memberNoPayDom",
                    // showCountDown: toolsId == "2",
                };
                break;
            case PageStatus.MEMBER_PAYED:
                res = {
                    isShowOrderStatus: info.realNameInfo.memberRole == Role.GROUP,
                    btnText: "分享给好友",
                    detailBtnText: "去分享",
                    detailTips: "share",
                    showBottomTips: true,
                    shareType: true,
                    disableClickGoods: true,
                };
                break;
            case PageStatus.MEMBER_NO_GROUP:
                res = {
                    isShowOrderStatus: info.realNameInfo.memberRole == Role.GROUP,
                    btnText: "分享给好友",
                    detailBtnText: "去分享",
                    detailTips: "share",
                    showBottomTips: true,
                    shareType: true,
                    disableClickGoods: true,
                };
                break;
            case PageStatus.MEMBER_GROUPED:
                res = {
                    isShowOrderStatus: info.realNameInfo.memberRole == Role.GROUP,
                    btnText: `拼团成功（${info.realNameInfo.orderStatusDesc}）`,
                    groupSuccessFlag: true,
                    hideGeneralBtn: true,
                    disableClickGoods: true,
                };
                break;
            case PageStatus.NOT_MEMBER_WAIT_OPEN:
                res = {
                    hideProgress: true,
                    btnText: "直接开团",
                    showAllAvatar: true,
                };
                break;
            case PageStatus.NOT_MEMBER_WAIT_JOIN:
                res = {
                    btnText: toolsId == "2" ? "去下单" : "直接参团",
                    detailTips: "notMemberWaitJoinDom",
                };
                break;
            case PageStatus.NOT_MEMBER_GROUPED:
                res = {
                    hideProgress: true,
                    btnText: "直接开团",
                    detailTips: "来晚了，此团已拼成功了",
                };
                break;
            case PageStatus.NOT_MEMBER_GROUP_FAIL:
            case PageStatus.MEMBER_GROUP_FAIL:
                res = {
                    btnText: "直接开团",
                    detailTips: "拼团失败，可以再次开团哦~",
                };
                break;
            case PageStatus.MEMBER_GROUPING_NO_PAY:
                if (info.realNameInfo.paymentDeadline && info.realNameInfo.paymentDeadline < 0) {
                    res = {
                        hideBottomBtn: true,
                        hideGeneralBtn: true,
                        detailTips: "已过活动时间咯，可自行开团哦~",
                        disableClickGoods: true,
                        disableClickTips: "您未在有效时间内参与好友开团，已不能重复参与，稍候可直接开团",
                    };
                } else {
                    res = {
                        btnText: "去下单",
                        detailTips:
                            info.realNameInfo.memberRole == Role.GROUP ? "支付完成立马开团" : "请在活动时间内尽快下单~",
                        showCountDown: toolsId == "2",
                    };
                }
                break;
            case PageStatus.NOT_MEMBER_GROUP_FILL:
                res = {
                    btnText: "直接开团",
                };
                break;
            case PageStatus.MEMBER_GROUP_FILL:
                res = {
                    btnText: "团员-团员已满",
                    isShowOrderStatus: info.realNameInfo.memberRole == Role.GROUP,
                    detailTips:
                        info.realNameInfo.memberRole == Role.GROUP
                            ? "团已满，待团员支付完成立即成团"
                            : "团已满，待所有团员支付成功即可发货",
                    hideGeneralBtn: true,
                    hideBottomBtn: true,
                };
                break;
            case PageStatus.NEW_MEMBER_FAIL:
                Dialog_.alert({
                    message: "参团失败，您可自行开团",
                });
                res = {
                    hideProgress: true,
                    btnText: "直接开团",
                    showAllAvatar: true,
                };
                break;
            case PageStatus.OLD_MEMBER_FAIL:
                Dialog_.alert({
                    message: "您已是平台老客，无法参与他人的团，您可直接开团邀请新客",
                });
                res = {
                    hideProgress: true,
                    btnText: "直接开团",
                    showAllAvatar: true,
                };
                break;
        }
        return res;
    }, [info]);

    // 滚动
    const maxScroll = useRef(0);
    const contentRef = useRef<any>(null);
    const offset = useRef(0);
    const timer = useRef<NodeJS.Timeout>();

    // 分享配置
    useShareAppMessage(() => {
        const inviteCode = infoStore.newUserProfile.inviteCode;
        const groupId = myGroupGroupId.current ? myGroupGroupId.current : (info.realNameInfo.groupInfo || {}).id || "";
        myGroupGroupId.current = null;
        var imageArr = "";
        if (info.anonymousInfo && info.anonymousInfo.activityInfo) {
            imageArr = info.anonymousInfo.activityInfo.activityPics || [];
        }
        return {
            title: (info.anonymousInfo.activityInfo || {}).activityName || "",
            path: `/pages/activity/group-buy/index?id=${id}&groupId=${groupId}&inviteCode=${inviteCode}&toolsId=${toolsId}`,
            imageUrl: imageArr[0] || "",
        };
    });

    // 监听页面滚动
    usePageScroll(e => {
        const scrollTop = e.scrollTop;
        const opacity = Math.min(scrollTop / 200, 1); // 200px滚动距离完全变白
        setNavOpacity(opacity);
    });

    // 滚动到指定元素
    const scrollToElement = (elementClass: string) => {
        const query = Taro.createSelectorQuery();

        query.select(elementClass).boundingClientRect();
        query.selectViewport().scrollOffset();
        query.exec(res => {
            if (res[0] && res[1]) {
                const elementTop = res[0].top; // 元素距视口顶部距离
                const viewportScrollTop = res[1].scrollTop; // 当前滚动位置

                Taro.pageScrollTo({
                    scrollTop: viewportScrollTop + elementTop - 100, // 50px偏移
                    duration: 300, // 滚动动画时长
                });
            }
        });
    };

    // 预览图片-单张
    const handlePreview = (url: string) => {
        if (url) {
            Taro.previewImage({
                current: url, // 当前显示图片
                urls: [url], // 所有可预览图片
            });
        }
    };

    // 初始化获取滚动范围
    const getScrollRange = () => {
        Taro.createSelectorQuery()
            .selectAll(`.broadcast-item`)
            .boundingClientRect((rects: any) => {
                if (!rects || rects.length === 0) return;

                // 2. 获取最后一个元素的信息
                const lastElement = rects[rects.length - 1];
                maxScroll.current = lastElement.right;
            })
            .exec();
    };

    const [page, setPage] = useState({
        currentPage: 0,
        size: 20,
        total: 0,
    });

    const [myGroupPage, setMyGroupPage] = useState({
        currentPage: 0,
        size: 20,
        total: 0,
    });

    // 页面滚动到底部事件
    useReachBottom(() => {
        loadRecordData();
    });

    // 底部左侧按钮
    const btns = [
        {
            name: BottomBtns.KEFU,
            text: "客服",
            icon: "https://dante-img.oss-cn-hangzhou.aliyuncs.com/57561043768.png",
        },
        {
            name: BottomBtns.MY_GROUP,
            text: "我的团",
            icon: "https://dante-img.oss-cn-hangzhou.aliyuncs.com/57561045140.png",
        },
        {
            name: BottomBtns.SHARE,
            text: "分享",
            icon: "https://dante-img.oss-cn-hangzhou.aliyuncs.com/57533278769.png",
        },
    ];

    // 底部按钮点击事件
    const bottomBtnClick = async (name: string) => {
        switch (name) {
            case BottomBtns.KEFU:
                break;
            case BottomBtns.MY_GROUP:
                if (pageStatus === PageStatus.No_LOGIN) {
                    goLogin();
                } else {
                    setMyGroupVisible(true);
                }
                break;
            case BottomBtns.SHARE:
                break;
            default:
                break;
        }
    };

    // 操作按钮点击事件
    const operatorBtnClick = () => {
        takeGroupId.current = true;
        switch (pageStatus) {
            case PageStatus.No_LOGIN:
                toLogin();
                break;
            case PageStatus.MEMBER_NO_PAY:
                payMoney(info.realNameInfo.orderId);
                break;
            case PageStatus.MEMBER_PAYED:
                // res = {
                //     btnText: '分享给好友',
                //     detailBtnText: '去分享',
                //     detailTips: 'share',
                //     showBottomTips: true,
                // }
                break;
            case PageStatus.MEMBER_NO_GROUP:
                // res = {
                //     btnText: '分享给好友',
                //     detailBtnText: '去分享',
                //     detailTips: 'share',
                //     showBottomTips: true,
                // }
                break;
            case PageStatus.MEMBER_GROUPED:
                // todo 进入订单详情，传参待修改
                DDYNavigateTo({
                    url: `/pages/order/order-detail/index?id=${info.realNameInfo.orderId}`,
                });
                break;
            case PageStatus.NOT_MEMBER_WAIT_OPEN:
                handleSkuSelect();
                takeGroupId.current = false;
                break;
            case PageStatus.NOT_MEMBER_WAIT_JOIN:
                handleSkuSelect();
                break;
            case PageStatus.NOT_MEMBER_GROUPED:
                takeGroupId.current = false;
                handleSkuSelect();
                break;
            case PageStatus.NOT_MEMBER_GROUP_FAIL:
            case PageStatus.MEMBER_GROUP_FAIL:
                takeGroupId.current = false;
                handleSkuSelect();
                break;
            case PageStatus.MEMBER_GROUPING_NO_PAY:
                if (info.realNameInfo.paymentDeadline && info.realNameInfo.paymentDeadline < 0) {
                    takeGroupId.current = false;
                }
                handleSkuSelect();
                break;
            case PageStatus.NOT_MEMBER_GROUP_FILL:
                takeGroupId.current = false;
                handleSkuSelect();
                break;
            case PageStatus.MEMBER_GROUP_FILL:
                break;
            case PageStatus.NEW_MEMBER_FAIL:
            case PageStatus.OLD_MEMBER_FAIL:
                takeGroupId.current = false;
                handleSkuSelect();
                break;
        }
    };

    // 初始化活动日志
    const initRecords = () => {
        setLoading(false);
        setIsLastPage(false);
        setActivityRecord([]);
        setPage({
            currentPage: 0,
            size: 10,
            total: 0,
        });
    };

    // 加载活动日志数据
    const loadRecordData = async () => {
        // 数据加载完
        if (isLastPage && page.currentPage > 0) {
            return;
        }
        setLoading(true);
        let nextPage = page.currentPage + 1;
        const {
            dataList,
            page: { totalCount, currentPage },
        } = await newAPi.queryGroupRecordInfo({
            data: {
                marketingToolId: toolsId,
                activityId: id,
                currentPage: nextPage,
                pageSize: page.size,
            },
            method: "POST",
        });
        console.log("dataList", dataList);
        // 无数据了
        if (dataList.length < page.size || (currentPage > 0 && [...activityRecord, ...dataList].length >= totalCount)) {
            setIsLastPage(true);
        }
        // 对100条数据进行处理
        if (currentPage == 1) {
            setActivityRecord(dataList);
        } else {
            setActivityRecord([...activityRecord, ...dataList]);
        }
        setPage({
            ...page,
            currentPage: currentPage,
            total: totalCount,
        });
        setTimeout(() => {
            setLoading(false);
        }, 1000);
    };

    // 获取活动数据
    const getData = async () => {
        let params = {
            activityId: parseInt(id),
            groupId,
        };
        console.log("params", params);
        try {
            const data = await newAPi.queryGroupBuyActivity({ data: params, method: "POST", showLoad: true });
            console.log("返回参数：", data);
            // 处理活动图片
            if (data.anonymousInfo && data.anonymousInfo.activityInfo) {
                data.anonymousInfo.activityInfo.activityPics =
                    JSON.parse(data.anonymousInfo.activityInfo.activityPics || "[]") || [];
            }
            // 分享购处理奖励信息
            if (toolsId == "2") {
                let awardArr = data.anonymousInfo.gbActivityConfigRewardDTOS || [];
                awardArr.forEach(item => {
                    if (data.groupActionButton == PageStatus.No_LOGIN || !data.realNameInfo.memberRole) {
                        if (item.rewardRange == 1) {
                            dealAward(item);
                        }
                    } else {
                        if (item.rewardRange === data.realNameInfo.memberRole) {
                            dealAward(item);
                        }
                    }
                });
            }
            // 设置活动信息
            setInfo(data);
            // 设置页面状态
            setPageStatus(data.groupActionButton);
            Taro.nextTick(() => {
                getScrollRange();
            });
        } catch (error) {
            console.log("error", error);
            setTimeout(() => {
                goBack();
            }, 1500);
        }
    };

    // 处理奖励
    const dealAward = item => {
        if (item.fudouFlag) {
            // 福豆
            setRewardArr([
                {
                    type: Reward.FUDOU, // 奖励类型
                    num: item.fudouNum, // 奖励数量
                    time: item.fudouDelayHour, // 发放时间
                    expireTime: item.fudouExpireHours, // 过期时间
                },
            ]);
        } else if (item.fukaFlag) {
            // 福卡
            setRewardArr([
                {
                    type: Reward.FUKA, // 奖励类型
                    num: item.fukaNum, // 奖励数量
                    time: item.fukaDelayHour, // 发放时间
                },
            ]);
        } else if (item.couponFlag) {
            // 优惠券
            let arr = (item.couponActivityDTOs || []).map(m => {
                return {
                    type: Reward.COUPON, // 奖励类型
                    time: item.couponDelayHour, // 发放时间
                    scopeType: ((m.scopeList || [])[0] || {}).type,
                };
            });
            setRewardArr(arr);
        }
    };

    const payMoney = id => {
        // 查询是否同意协议

        let map = new Map();
        map.set(mall_event_key.MALL_KEY_ORDER_ID, id);
        // map.set(mall_event_key.MALL_KEY_GOOD_NUM, orderNum);
        // map.set(mall_event_key.MALL_KEY_GOOD_SPEC_ID, specId)
        reportEvent(mall_event.MALL_EVENT_ORDER_APPLY_PAY, map);
        // 支付请求
        api.payOrder({
            data: {
                orderIds: [id],
                // channel: shopOrder.type == 3 ? "Integral-pay" : "wechatpay-jsapi", // 增加积分支付判断
                usageChannel: 2,
            },
            method: "POST",
            filterCheck: true,
        }).then(res => {
            payBandle(res, id);
        });
    };

    const payBandle = (res, id) => {
        if (!res.success) {
            if (res.error === "需完善通商云账户信息") {
                DDYToast.showModal({
                    title: "实名制注册，请您先【填写实名信息】、【绑定银行卡】并【签署协议】",
                    success: res => {
                        if (res.confirm) {
                            queryInfo({
                                go: true,
                                goSign: () => {
                                    saveSignback();
                                    DDYNavigateTo({
                                        url: "/pages/my/bank-list/index",
                                    });
                                },
                            });
                        } else {
                        }
                    },
                });
                DDYToast.hideLoading();
                return;
            }
            DDYToast.info(res.error);
            return;
        }
        if (res.result.invokeMiniProgram) {
            DDYToast.hideLoading();
            Taro.openEmbeddedMiniProgram({
                appId: "wxef277996acc166c3",
                extraData: res.result.redirectInfo,
            });
            orderInfo.current = res[0];
            return;
        }
        if (res.result.invokeH5) {
            if (res.result.requestOriginalUrl) {
                try {
                    const requestOriginalUrl = JSON.parse(res.result.requestOriginalUrl);
                    DDYToast.showModal({
                        title: "是否使用微信h5支付",
                        content: requestOriginalUrl.payInfo,
                        success: res => {
                            if (res.confirm) {
                                Taro.setClipboardData({
                                    data: requestOriginalUrl.payInfo,
                                    success(res) {
                                        DDYToast.info("复制成功");
                                    },
                                });
                            }
                        },
                    });
                } catch (error) {}
                DDYToast.hideLoading();
                return;
            }
            return;
        }
        if (isAlipay() && res.result.channel === "allinpay-yst" && res.result.redirectInfo) {
            Taro.tradePay({
                tradeNO: res.result.redirectInfo,
                success(data) {
                    console.log("allinpay-yst:", res);
                    DDYToast.hideLoading();
                    //@ts-ignore
                    if (data.resultCode === "9000") {
                        goSuccess(id);
                    } else {
                        //@ts-ignore
                        DDYToast.info(data.memo);
                    }
                },
                fail(res) {
                    DDYToast.info("支付失败");
                    DDYToast.hideLoading();
                },
            });
            return;
        }
        if (res.result.redirectInfo) {
            try {
                const payObj = JSON.parse(res.result.redirectInfo);
                if (payObj) {
                    Taro.requestPayment({
                        //@ts-ignore
                        appId: payObj.appId,
                        timeStamp: payObj.timeStamp,
                        nonceStr: payObj.nonceStr,
                        package: payObj.package,
                        signType: payObj.signType,
                        paySign: payObj.paySign,
                        success: function (res) {
                            DDYToast.success("支付成功", 1000);
                            goSuccess(id);
                            reportEvent(
                                mall_event.MALL_EVENT_PAY_SUCCESS,
                                new Map()
                                    .set(mall_event_key.MALL_KEY_ORDER_ID, id)
                                    .set(mall_event_key.MALL_KEY_ORDER_PAY_CHANNEL, "微信"),
                            );
                        },
                        fail: function (res) {
                            DDYToast.info("支付失败");
                            reportEvent(
                                mall_event.MALL_EVENT_PAY_FAIL,
                                new Map()
                                    .set(mall_event_key.MALL_KEY_ORDER_ID, id)
                                    .set(mall_event_key.MALL_KEY_ORDER_PAY_CHANNEL, "微信")
                                    .set(mall_event_key.MALL_KEY_ORDER_PAY_ERROR_MESSAGE, JSON.stringify(res)),
                            );
                        },
                    });
                } else {
                    DDYToast.success("积分抵扣成功", 1000);
                    reportEvent(
                        mall_event.MALL_EVENT_PAY_SUCCESS,
                        new Map()
                            .set(mall_event_key.MALL_KEY_ORDER_ID, id)
                            .set(mall_event_key.MALL_KEY_ORDER_PAY_CHANNEL, "积分"),
                    );
                    goSuccess(id);
                }
            } catch (error) {
                console.log("支付信息解析异常");
                reportEvent(
                    mall_event.MALL_EVENT_PAY_FAIL,
                    new Map()
                        .set(mall_event_key.MALL_KEY_ORDER_ID, id)
                        .set(mall_event_key.MALL_KEY_ORDER_PAY_CHANNEL, "微信")
                        .set(mall_event_key.MALL_KEY_ORDER_PAY_ERROR_MESSAGE, JSON.stringify(error)),
                );
            }
            return;
        }

        DDYToast.success("积分抵扣成功", 1000);
        reportEvent(
            mall_event.MALL_EVENT_PAY_SUCCESS,
            new Map().set(mall_event_key.MALL_KEY_ORDER_ID, id).set(mall_event_key.MALL_KEY_ORDER_PAY_CHANNEL, "积分"),
        );
        goSuccess(id);
    };

    const goSuccess = id => {
        setTimeout(() => {
            getData();
            DDYNavigateTo({
                url: "/pages/order/pay_success?success=0&orderNo=" + id,
            });
        }, 1000);
    };

    const refresh = () => {
        if (firstEnter.current) {
            // 存在邀请码则设置邀请码
            if (inviteCode) {
                setStorage(INVITE_CODE, inviteCode);
            }
            firstEnter.current = false;
        }
        setSkuPop(false);

        initRecords();
        Taro.nextTick(() => {
            // 获取活动日志
            loadRecordData();
        });
        getData();
        clearInterval(timer.current);
    };

    const goBack = () => {
        if (Taro.getCurrentPages().length > 1) {
            DDYBack();
        } else {
            DDYSwitchTab({ url: "/pages/index/index" });
        }
    };

    useEffect(() => {
        return () => clearInterval(timer.current);
    }, []);
    const [scrollItems, setScrollItems] = useState<any[]>([]);
    useEffect(() => {
        setScrollItems(info.anonymousInfo.scrollItems || []);
        let selectId = info.anonymousInfo.itemsDTOS[0]?.id;
        setSelectId(selectId);
        // setScrollItems([{
        //     nickname: 'test1',
        //     groupStatus: 1,
        // }])
    }, [info]);

    useEffect(() => {
        if (selectId) {
            // 设置商品详情图
            let goods = (info.anonymousInfo.itemsDTOS || []).find(item => item.id === selectId);
            let imgs = goods?.detailImageList || [];
            setDetailImgs(imgs);
        }
    }, [selectId]);

    // const scrollItems = info.anonymousInfo.scrollItems || []
    return (
        <View className="activity-container">
            {/* 导航栏 */}
            <NavBar
                fixed
                className="nav-bar"
                border={false}
                title={info.anonymousInfo.activityInfo.activityName || ""}
                leftArrow={!isAlipay()}
                leftText=""
                onClickLeft={goBack}
                style={`background: rgba(255, 255, 255, ${navOpacity})`}
            />
            {/* 轮播 */}
            <View className="carousel">
                <Swiper
                    autoplay={false}
                    className="swiper"
                    style={{
                        height: windowWidth.current * 0.75,
                    }}
                    onChange={e => {
                        setCurrentImg(e.detail.current);
                    }}
                >
                    {info.anonymousInfo.activityInfo.activityPics.map((imgUrl, imgUrlIdx) => (
                        <SwiperItem key={imgUrlIdx} className="swiper-item">
                            <Image src={imgUrl} mode="aspectFill" className="swiper-item-img" />
                        </SwiperItem>
                    ))}
                </Swiper>
                {info.anonymousInfo.activityInfo.activityPics.length > 0 && (
                    <View className="custom-indicator">
                        {currentImg + 1}/{info.anonymousInfo.activityInfo.activityPics.length}
                    </View>
                )}
            </View>
            {/* 活动说明 */}
            <View className="desc">
                <View className="shape">
                    <View className="circle"></View>
                    <View className="ellipse"></View>
                </View>
                <View className="content">{info.anonymousInfo.activityInfo.activityDes}</View>
                {/* 拼团情况 */}
                <View className="group-detail">
                    {/* 活动概况 */}
                    <View className="general">
                        ·{info.anonymousInfo.motherCount || 0}位妈妈正在拼
                        {info.anonymousInfo.groupActivityCount || 0}个团·
                    </View>
                    {/* 广播 */}
                    {scrollItems.length > 0 && <BroadcastScroll scrollItems={scrollItems} />}

                    <View className="activity" style={{ marginTop: scrollItems.length ? "0" : "10px" }}>
                        <View className="activity-top">
                            <View className="activity-top-left">
                                <Image
                                    className="hot"
                                    src="https://dante-img.oss-cn-hangzhou.aliyuncs.com/57561042721.png"
                                ></Image>
                                活动火热进行中
                            </View>
                        </View>
                        <View className="activity-bottom">
                            <View className="activity-bottom-left">
                                <View className="num">
                                    {`${info.anonymousInfo.activityInfo.activityNum || ""}人成团`}
                                    {!pSetting.hideMember &&
                                        info.realNameInfo &&
                                        info.realNameInfo.joinedMemberCount !== info.realNameInfo.activityNum &&
                                        `/当前${info.realNameInfo.joinedMemberCount || ""}人参团`}
                                    {info.realNameInfo &&
                                        info.realNameInfo.joinedMemberCount !== null &&
                                        info.realNameInfo.joinedMemberCount !== 0 &&
                                        info.realNameInfo.joinedMemberCount == info.realNameInfo.activityNum && (
                                            <View className="group-success">已成团</View>
                                        )}
                                </View>
                                <Progress
                                    className="progress"
                                    showPivot={false}
                                    trackColor="#F8F8F8"
                                    strokeWidth={22.43}
                                    color="#FD2959"
                                    percentage={
                                        pSetting.hideProgress
                                            ? 0
                                            : Math.min(
                                                  ((info.realNameInfo.joinedMemberCount || 0) /
                                                      info.anonymousInfo.activityInfo.activityNum) *
                                                      100,
                                                  100,
                                              )
                                    }
                                />
                                <ScrollView className="peoples-container" scroll-x="true">
                                    <View className="peoples">
                                        {(pSetting.showAllAvatar
                                            ? new Array(info.anonymousInfo.activityInfo.activityNum || 0).fill("")
                                            : info.realNameInfo.memberAvatars || []
                                        ).map((people, peopleIdx) => (
                                            <View key={peopleIdx} className="people-item">
                                                <Image
                                                    className="head-img"
                                                    src={people.images || defaultAvatar}
                                                ></Image>
                                                {people.role == Role.GROUP && <View className="group-icon">团长</View>}
                                                {/* 分享购显示支付状态 */}
                                                {toolsId == "2" &&
                                                    pSetting.isShowOrderStatus &&
                                                    PeopleOrderStatus[people.status] &&
                                                    people.role == Role.MEMBER && (
                                                        <View className="group-icon">
                                                            {PeopleOrderStatus[people.status]}
                                                        </View>
                                                    )}
                                            </View>
                                        ))}
                                    </View>
                                </ScrollView>
                                {getDetailTipsDom(pSetting.detailTips) || null}
                            </View>
                            <View className="activity-bottom-right">
                                <View className="activity-bottom-right-top">
                                    <View className="block">
                                        <View className={`title ${pSetting.hideGeneralBtn ? "maringTop31" : ""}`}>
                                            商品总数(份)
                                        </View>
                                        <View className="val">{info.anonymousInfo.totalProducts || "0"}</View>
                                    </View>
                                    <View className="block">
                                        <View className={`title ${pSetting.hideGeneralBtn ? "maringTop31" : ""}`}>
                                            组团成功(人)
                                        </View>
                                        <View className="val">{info.anonymousInfo.successGroupCount || "0"}</View>
                                    </View>
                                </View>
                                {pSetting.groupSuccessFlag && (
                                    <View className="activity-bottom-right-success">
                                        <Image
                                            className="activity-bottom-right-success-img"
                                            src="https://dante-img.oss-cn-hangzhou.aliyuncs.com/62117857499.png"
                                        ></Image>
                                        拼团成功
                                    </View>
                                )}
                                {!pSetting.hideGeneralBtn && (
                                    <Button
                                        className="activity-bottom-right-bottom"
                                        onClick={operatorBtnClick}
                                        {...(pSetting.shareType ? { openType: "share" } : {})}
                                    >
                                        {pSetting.detailBtnText ? pSetting.detailBtnText : pSetting.btnText || ""}
                                    </Button>
                                )}
                            </View>
                        </View>
                    </View>
                </View>
                <View className="detail">
                    {/* 内容 */}
                    <View className="content">
                        {/* 选择商品 */}
                        <CustomCard
                            title="拼团商品"
                            isShow={info.anonymousInfo.itemsDTOS && info.anonymousInfo.itemsDTOS.length > 0}
                            elementId="slect-goods"
                            imgSrc="https://dante-img.oss-cn-hangzhou.aliyuncs.com/92078885174.png"
                        >
                            <SkuSelect
                                ref={skuSelectRef}
                                isLogin={!(pageStatus === PageStatus.No_LOGIN)}
                                goods={info.anonymousInfo.itemsDTOS || []}
                                jumpLogin={toLogin}
                                closeFn={load => {
                                    setSkuPop(false);
                                }}
                                skuLoad={datas => {
                                    // setSkus(datas);
                                }}
                                openType={1}
                                groupAction={info.groupActionButton || ""}
                                activityId={id || ""}
                                groupId={
                                    takeGroupId.current ? ((info.realNameInfo || {}).groupInfo || {}).id || "" : ""
                                }
                                goodsId={selectId}
                                setSelectId={setSelectId}
                                hasGbActivity={true}
                            />
                        </CustomCard>
                        {/* 组团规则 */}
                        <CustomCard
                            title="组团规则"
                            isShow={true}
                            imgSrc="https://dante-img.oss-cn-hangzhou.aliyuncs.com/2898565778.png"
                        >
                            {info.anonymousInfo.activityInfo.activityRules && (
                                <Image
                                    onClick={() => {
                                        // handlePreview(info.anonymousInfo.activityInfo.activityRules || "");
                                    }}
                                    src={info.anonymousInfo.activityInfo.activityRules || ""}
                                    mode="aspectFill"
                                    className="goods-detail-img"
                                />
                            )}

                            <View
                                className={`rule-content ${
                                    info.anonymousInfo.activityInfo.activityRules ? "" : "margin0"
                                }`}
                            >
                                <View
                                    className={`rule-content-text`}
                                    onClick={() => {
                                        DDYNavigateTo({ url: "/pages/h5/privacy-policy?type=14" });
                                    }}
                                >
                                    活动规则详情参见
                                    <View className="rule-content-text-xieyi">《筌家福的组团活动规则》</View>
                                    <Icon className="rule-content-text-arrow" name={"arrow"} />
                                </View>
                            </View>
                        </CustomCard>
                        {/* 商品详情 */}
                        <CustomCard
                            title="商品详情"
                            isShow={detailImgs && detailImgs.length > 0}
                            imgSrc="https://dante-img.oss-cn-hangzhou.aliyuncs.com/28985532120.png"
                        >
                            {detailImgs.map((url, urlIdx) => (
                                <Image key={urlIdx} src={url || ""} mode="widthFix" className="goods-detail-img" />
                            ))}
                        </CustomCard>
                        {/* 优惠介绍 todo 显示条件以及字段修改 */}
                        <CustomCard
                            title="优惠介绍"
                            isShow={rewardArr.length > 0 && toolsId == "2"}
                            imgSrc="https://dante-img.oss-cn-hangzhou.aliyuncs.com/28985533202.png"
                        >
                            <View className="discount-content">
                                {rewardArr.map((item, index) => {
                                    return (
                                        <View key={index} className="discount-content-item">
                                            <View className="discount-content-item-left">
                                                <View>参与</View>
                                                <View>就送</View>
                                            </View>
                                            <View className="discount-content-item-middle">
                                                {item.type != Reward.COUPON ? (
                                                    <Fragment>
                                                        <View className="num">{item.num}</View>
                                                        <View className="desc">{item.type}</View>
                                                    </Fragment>
                                                ) : (
                                                    <View className="coupon">
                                                        <View className="coupon-name">
                                                            {item.scopeType == "SHOP" ? "全店通用券" : "商品券"}
                                                        </View>
                                                        <View>
                                                            {item.scopeType == "SHOP" ? "所有商品可用" : "指定商品可用"}
                                                        </View>
                                                    </View>
                                                )}
                                            </View>
                                            <View className="discount-content-item-right">
                                                <View>存入余额账户</View>
                                                <View>下单时可抵扣</View>
                                            </View>
                                        </View>
                                    );
                                })}
                            </View>
                            {
                                <View className="discount-time">{`确认收货后${(rewardArr[0] || {}).time || 0}h，${
                                    (rewardArr[0] || {}).type
                                }立即到账`}</View>
                            }
                        </CustomCard>

                        {/* 活动日志 */}
                        <CustomCard
                            title="大家都在拼"
                            isShow={activityRecord.length > 0}
                            customStyle={{
                                boxShadow: "none",
                                borderRadius: "27px 27px 0 0",
                            }}
                            imgSrc="https://dante-img.oss-cn-hangzhou.aliyuncs.com/2898552729.png"
                        >
                            {(activityRecord || []).map((item, index) => (
                                <RecordItem key={index} info={item} />
                            ))}
                            {loading ? (
                                <Loading size="12px">加载中...</Loading>
                            ) : (activityRecord || []).length >= 100 ? (
                                <View className="log-tips">不能查看更多日志了</View>
                            ) : (
                                isLastPage && <View className="log-tips">已经到底了~</View>
                            )}
                        </CustomCard>
                    </View>
                </View>
            </View>

            {/* 底部按钮 */}
            <View className="bottom">
                <View className="left-btns">
                    {btns.map((btn, btnIdx) => {
                        let setting = {};
                        if (btn.name == BottomBtns.KEFU) {
                            setting["openType"] = "contact";
                        } else if (btn.name == BottomBtns.SHARE) {
                            setting["openType"] = "share";
                            setting["style"] = "visibility: hidden";
                        }
                        return (
                            <Button
                                className="left-btns-item nostyle"
                                {...setting}
                                plain
                                onClick={() => {
                                    bottomBtnClick(btn.name);
                                }}
                            >
                                {/* <View key={btnIdx} className="left-btns-item" onClick={() => { bottomBtnClick(btn.name) }}> */}
                                <Image className="left-btns-item-icon" src={btn.icon}></Image>
                                <View className="left-btns-item-title">{btn.text}</View>
                                {/* </View> */}
                            </Button>
                        );
                    })}
                </View>
                <Button
                    className={`right-btns ${pSetting.hideBottomBtn ? "hide" : ""}`}
                    {...(pSetting.shareType ? { openType: "share" } : {})}
                    onClick={operatorBtnClick}
                >
                    {pSetting.btnText || ""}
                    {/* 分享购倒计时 */}
                    {pSetting.showCountDown && info.realNameInfo.paymentDeadline > 0 && (
                        <CountDown
                            className="count-down"
                            time={info.realNameInfo.paymentDeadline}
                            onFinish={refresh}
                            format={
                                info.realNameInfo.paymentDeadline / 1000 > 24 * 60 * 60 * 1000
                                    ? "DD天HH:mm:ss"
                                    : "HH:mm:ss"
                            }
                        />
                    )}

                    {pSetting.showBottomTips && info.realNameInfo.lackMembers > 0 && (
                        <View className="tips">
                            <View className="tips-content">仅差{info.realNameInfo.lackMembers || ""}人成团</View>
                        </View>
                    )}
                </Button>
            </View>
            <MyGroup
                open={myGroupVisible}
                setMyGroupVisible={setMyGroupVisible}
                toolsId={toolsId}
                activityId={id}
                info={info}
                myGroupGroupId={myGroupGroupId}
                payMoney={payMoney}
            />
            {/* 弹窗内容 */}
            <Dialog_ />
        </View>
    );
};
