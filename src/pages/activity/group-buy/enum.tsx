// 底部按钮枚举
const enum BottomBtns {
    KEFU = "kefu",
    MY_GROUP = "myGroup",
    SHARE = "share",
}

// 页面状态枚举·
const enum PageStatus {
    No_LOGIN = 1, // 未登录
    MEMBER_NO_PAY = 2, // 团员-未支付
    MEMBER_PAYED = 3, // 团员-已支付
    MEMBER_NO_GROUP = 4, // 团员-未成团
    MEMBER_GROUPED = 5, // 团员-已成团
    NOT_MEMBER_WAIT_OPEN = 6, // 非团员-待开团
    NOT_MEMBER_WAIT_JOIN = 7, // 非团员-待参团
    NOT_MEMBER_GROUPED = 8, // 非团员-开团成功
    NOT_MEMBER_GROUP_FAIL = 9, // 非团员-开团失败
    MEMBER_GROUPING_NO_PAY = 10, // 团员-占位待下单
    NOT_MEMBER_GROUP_FILL = 11, // 非团员-团员已满
    MEMBER_GROUP_FILL = 12, // 团员-团员已满
    NEW_MEMBER_FAIL = 13, // 新用户-参团失败
    OLD_MEMBER_FAIL = 14, // 老用户-参团失败
    MEMBER_GROUP_FAIL = 15, // 团员-开团失败
}

// 角色
const enum Role {
    GROUP = 1, // 团长
    MEMBER = 2, // 团员
}

// 奖励枚举
const enum Reward {
    FUKA = "福卡",
    FUDOU = "福豆",
    COUPON = "优惠券",
}

// 团状态枚举
const GroupStatus = {
    1: "未支付", // 团长待支付
    2: "进行中", // 进行中
    3: "拼团成功", // 开团成功
    4: "拼团失败", // 开团失败
    5: "待成团", // 团员已满
};

// 滚动条状态枚举
const ScrollStatus = {
    1: "开团成功",
    2: "参团成功",
    3: "拼团成功",
};

const PeopleOrderStatus = {
    1: "待支付", // 待支付
    2: "已支付", // 已支付
    // 3: "退出 ", // 退出
    // 4: " 开团成功", //  开团成功
    // 5: " 已发货", //  已发货
    // 6: " 已签收", //  已签收
    10: " 待下单", //  待下单
};

const defaultAvatar = "https://dante-img.oss-cn-hangzhou.aliyuncs.com/4905389872.png";

export { BottomBtns, PageStatus, defaultAvatar, Role, Reward, GroupStatus, PeopleOrderStatus, ScrollStatus };
