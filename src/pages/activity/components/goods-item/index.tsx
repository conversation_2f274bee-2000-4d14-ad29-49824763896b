import { Image } from "@antmjs/vantui";
import { View, Text } from "@tarojs/components";
import "./index.less";
import Taro from "@tarojs/taro";

export default ({ info, isLogin = false, jumpLogin, setSelectId, setIsSelectdFinished, isSelectdFinished }) => {
    const { isSelected, selectPrice, selectSku, selectDiscountPrice } = info;
    const handleClick = id => {
        setSelectId(id);
        if (!isSelected) {
            setIsSelectdFinished(false);
        }
    };

    return (
        <View className={`goods-item ${isSelected ? "selected" : ""}`} onClick={() => handleClick(info.id)}>
            <Image
                radius={24}
                className="goods-item-left"
                src={info.mainImage || ""}
                onClick={() => {
                    let current = info.mainImage || "";
                    Taro.previewImage({
                        urls: [current],
                        current,
                    });
                }}
            ></Image>
            <View className="goods-item-right">
                <View className="name">{info.name}</View>
                <View className="box1">
                    <View className="price">
                        {`¥${info.itemHighPrice === info.itemLowPrice ? info.itemLowPrice : info.itemLowPrice + " 起"}`}
                    </View>
                    {/* 折扣价 */}
                    {isSelectdFinished && isSelected && selectPrice !== selectDiscountPrice ? (
                        <View className="cart-good-discount">
                            <Text className="discount-text1">折后</Text>
                            <Text className="discount-text2">¥{selectDiscountPrice}</Text>
                        </View>
                    ) : null}
                </View>
            </View>
        </View>
    );
};
