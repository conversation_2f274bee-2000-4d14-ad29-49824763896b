.goods-item {
    height: 206px;
    margin: 0 auto 20px;
    display: flex;
    padding: 18px 0 28px 20px;
    box-sizing: border-box;
    border-radius: 22px;
    background: rgba(42, 42, 42, 0.04);

    &-left {
        width: 171px;
        height: 160px;
        flex-shrink: 0;
        margin-right: 33px;
    }

    &-right {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        flex-grow: 1;
        padding-top: 10px;

        .name {
            display: -webkit-box;
            /* 设置为WebKit内核的弹性盒子模型 */
            -webkit-box-orient: vertical;
            /* 垂直排列 */
            -webkit-line-clamp: 1;
            /* 限制显示两行 */
            overflow: hidden;
            /* 隐藏超出范围的内容 */
            text-overflow: ellipsis;
            /* 使用省略号 */
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            font-size: 22px;
            color: #323232;
            line-height: 33px;
            font-style: normal;
        }

        .box1 {
            display: flex;
            justify-content: space-between;
            align-items: flex-end;
            width: 100%;

            .price {
                font-family: PingFangSC, PingFang SC;
                font-weight: 600;
                font-size: 20px;
                color: #666666;
                line-height: 33px;
                font-style: normal;
                display: flex;
                align-items: center;
                margin-bottom: 3px;
            }

            .cart-good-discount {
                height: 47px;
                line-height: 45px;
                box-sizing: border-box;
                padding: 0 30px;
                background: url(https://dante-img.oss-cn-hangzhou.aliyuncs.com/72230051408.png) no-repeat top left;
                background-size: 100% 100%;
                border-radius: 34px;
                text-align: center;
                font-family: PingFangSC, PingFang SC;
                font-weight: 400;
                font-size: 20px;
                color: #fff;
                margin-right: 10px;
            }
        }
    }

    &.selected {
        background: #fef4f4;
    }
}
