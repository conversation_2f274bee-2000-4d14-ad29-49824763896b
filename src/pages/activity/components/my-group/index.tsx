import { View, ScrollView, <PERSON>ton, Image } from "@tarojs/components";
import { Popup } from "@antmjs/vantui";
import "./index.less";
import React, { useEffect, useState } from "react";
import newAPi from "@/utils/newApi";
import { GroupStatus } from "../../group-buy/enum";
import { DDYNavigateTo, DDYRedirectTo } from "@/utils/route";

export default ({ open, setMyGroupVisible, toolsId, info, payMoney, activityId, myGroupGroupId }) => {
    // 我的团信息
    const [myGroupInfo, setMyGroupInfo] = useState([]);

    useEffect(() => {
        if (open) {
            fetchData();
        } else {
            setMyGroupInfo([]);
        }
    }, [open]);

    const fetchData = async () => {
        // 获取我的团的信息
        const data = await newAPi.queryMyGroupInfo({
            data: {
                marketingToolId: toolsId,
                activityId: activityId,
            },
            method: "POST",
            showError: false,
        });
        console.log("data", data);
        setMyGroupInfo(data || []);
    };

    return (
        <Popup
            show={open}
            style={{ height: "70%" }}
            position="bottom"
            onClose={() => {
                setMyGroupVisible(false);
            }}
            closeable
            round
            catchMove // 防止触摸穿透
            lockScroll={true}
        >
            <View className="my-group-popup">
                <View className="my-group-popup-title">我的组团</View>
                <ScrollView className="my-group-popup-content" scrollY>
                    {myGroupInfo.map((item: any, index) => (
                        <View
                            className="my-group-item"
                            key={index}
                            onClick={() => {
                                DDYRedirectTo({
                                    url: `/pages/activity/group-buy/index?id=${activityId}&toolsId=${toolsId}&groupId=${item.id}`,
                                });
                            }}
                        >
                            <Image
                                className="my-group-item-img"
                                mode="aspectFill"
                                src={(info.anonymousInfo.activityInfo.activityPics || [])[0] || ""}
                                style={{ height: 710 * 0.75 + "rpx" }}
                            ></Image>
                            <View className="my-group-item-desc">
                                <View className="my-group-item-desc-status">{GroupStatus[item.groupStatus] || ""}</View>
                                {info.anonymousInfo.activityInfo.activityDes}
                            </View>
                            {item.groupStatus === 1 && (
                                <View
                                    className="my-group-item-btn"
                                    onClick={e => {
                                        e.stopPropagation();
                                        payMoney(item.orderId);
                                    }}
                                >
                                    立即支付
                                </View>
                            )}
                            {item.groupStatus === 2 && (
                                <Button
                                    openType="share"
                                    className="my-group-item-btn"
                                    onClick={e => {
                                        e.stopPropagation();
                                        myGroupGroupId.current = item.id;
                                    }}
                                >
                                    分享给好友
                                    {item.lackMembers > 0 && (
                                        <View className="tips">
                                            <View className="tips-content">仅差{item.lackMembers || ""}人成团</View>
                                        </View>
                                    )}
                                </Button>
                            )}
                            {item.groupStatus === 3 && (
                                <View
                                    className="my-group-item-btn"
                                    onClick={e => {
                                        e.stopPropagation();
                                        DDYNavigateTo({
                                            url: `/pages/order/order-detail/index?id=${item.orderId}`,
                                        });
                                    }}
                                >
                                    拼团成功（{item.orderStatusDesc}）
                                </View>
                            )}
                        </View>
                    ))}
                </ScrollView>
            </View>
        </Popup>
    );
};
