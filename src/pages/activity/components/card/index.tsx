import { View, Image } from "@tarojs/components";
import "./index.less";

export default ({ children, title, customStyle = {}, isShow = false, elementId = "", imgSrc = "" }) => {
    return (
        isShow && (
            <View className="card-title" style={{ ...customStyle }} id={elementId || ""}>
                <View className="card-top"></View>
                <View className="title-container">
                    <Image className="img" src={imgSrc}></Image>
                    <View className="title">{title}</View>
                </View>
                <View className="content-container">{children}</View>
            </View>
        )
    );
};
