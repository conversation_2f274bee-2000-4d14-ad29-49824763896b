.record-item {
    width: 643px;
    height: 102px;
    background: rgba(122, 122, 122, 0.04);
    border-radius: 22px;
    margin: 0 auto 14px;
    display: flex;
    align-items: center;

    &-head {
        flex-shrink: 0;
        margin-left: 25px;
        margin-right: 31px;
        box-sizing: border-box;
        height: 100%;
        padding-top: 12px;
        display: flex;
        flex-direction: column;
        align-items: center;

        &-img {
            width: 70px;
            height: 70px;
            border-radius: 50%;
            border: 4px solid #fe4d31;
        }

        &-sign {
            width: 49px;
            height: 20px;
            background: #fe4d2c;
            border-radius: 7px;
            border: 1px solid #ffffff;
            font-family: PingFangSC, PingFang SC;
            font-weight: 300;
            font-size: 14px;
            color: #ffffff;
            line-height: 20px;
            font-style: normal;
            text-align: center;
            margin-top: -7px;
            z-index: 2;
            position: relative;
        }
    }

    &-info {
        flex-grow: 1;
        padding-right: 20px;

        &-name {
            font-family: PingFangSC, PingFang SC;
            font-weight: 300;
            font-size: 24px;
            color: #333333;
            line-height: 44px;
            font-style: normal;
            display: -webkit-box;
            /* 设置为WebKit内核的弹性盒子模型 */
            -webkit-box-orient: vertical;
            /* 垂直排列 */
            -webkit-line-clamp: 1;
            /* 限制显示两行 */
            overflow: hidden;
            /* 隐藏超出范围的内容 */
            text-overflow: ellipsis;
            /* 使用省略号 */
        }

        &-group {
            height: 20px;
            background: #c6c6c6;
            border-radius: 3px;
            padding: 0 6px;
            font-family: PingFangSC, PingFang SC;
            font-weight: 300;
            font-size: 14px;
            color: #ffffff;
            line-height: 20px;
            font-style: normal;
            white-space: nowrap;
            max-width: 300px;
            /* 不换行 */
            overflow: hidden;
            /* 隐藏溢出内容 */
            text-overflow: ellipsis;
            /* 使用省略号 */
        }

        &-time {
            font-family: PingFangSC, PingFang SC;
            font-weight: 300;
            font-size: 20px;
            color: #b9b9b9;
            line-height: 26px;
            text-align: left;
            font-style: normal;
            margin: 0 24px 0 0;
            flex-shrink: 0;
        }

        &-btn {
            width: 115px;
            height: 53px;
            line-height: 53px;
            text-align: center;
            border-radius: 34px;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            font-size: 22px;
            color: #ffffff;
            font-style: normal;
            flex-shrink: 0;

            &.yellow {
                background-color: #f5c478;
            }

            &.red {
                background-color: #ff0940;
            }
        }
    }
}
