import { View, Image } from "@tarojs/components";
import "./index.less";

export default ({ info }) => {
    const showTime = (time: number) => {
        var str = "";
        const now = Date.now(); // 当前时间的时间戳，单位为毫秒
        const minutesDifference = Math.floor((time - now) / 60000); // 分钟差
        if (minutesDifference < 1) {
            str = "刚刚参加";
        } else if (minutesDifference < 60) {
            str = minutesDifference + "分钟前";
        } else if (minutesDifference < 60 * 24) {
            str = Math.floor(minutesDifference / 60) + "小时前";
        } else {
            str = Math.floor(minutesDifference / (60 * 24)) + "天前";
        }
        return str;
    };

    return (
        <View className="record-item">
            <View className="record-item-head">
                <Image className="record-item-head-img" src={info.headImg || ""}></Image>
                <View className="record-item-head-sign">团长</View>
            </View>
            <View className="record-item-info">
                <View className="record-item-info-name">{info.nickName || ""}</View>
                {info.lastGroupName && (
                    <View style={{ display: "flex" }}>
                        <View className="record-item-info-group">{info.lastGroupName || ""}</View>
                    </View>
                )}
            </View>
            <View className="record-item-info-time">{info.joinTimeDesc}</View>
            <View className={`record-item-info-btn ${info.isGroupLeader ? "red" : "red"}`}>{info.groupStatusDesc}</View>
        </View>
    );
};
