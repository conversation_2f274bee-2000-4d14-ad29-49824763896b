.submit-commit-page {
    background: #f0f0f0;
    min-height: 100vh;
    .card-rect {
        padding: 24px;
        margin: 20px;
        border-radius: 12px;
        background-color: #fff;
        .goods-info {
            display: flex;
            flex-direction: row;
            align-items: center;
            .goods-img {
                width: 82px;
                height: 82px;
            }
            .goods-name {
                font-weight: 400;
                font-size: 24px;
                color: #999999;
                line-height: 33px;
                margin-left: 40px;
            }
        }
        .form-title {
            display: flex;
            flex-direction: row;
            align-items: center;
            padding-bottom: 24px;
        }
        .card-text {
            font-weight: 500;
            font-size: 26px;
            color: #1b1b1b;
            line-height: 34px;
            margin-left: 13px;
        }
        .van-cell {
            background-color: rgba(248, 248, 248, 1);
        }
        .commit-pic-list {
            display: flex;
            flex-direction: row;
            flex-wrap: wrap;
            padding-top: 22px;
            .commit-pic-item {
                width: 148px;
                height: 148px;
                margin-right: 27px;
                margin-bottom: 24px;
                border-radius: 25px;
                position: relative;
                .commit-pic-img {
                    width: 148px;
                    height: 148px;
                }
                .commit-pic-del {
                    position: absolute;
                    right: -14px;
                    top: -14px;
                    color: #f5001d;
                    border: 1px solid #f5001d;
                    font-size: 10px;
                    border-radius: 20px;
                }
            }
            .commit-pic-add {
                width: 148px;
                height: 148px;
                border-radius: 8px;
                border: 2px dashed #5a6c91;
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                .commit-pic-add-text {
                    font-weight: 300;
                    font-size: 24px;
                    color: #1b1b1b;
                    line-height: 34px;
                }
            }
        }
    }
    // .btn-rect{
    .submit-btn {
        width: 591px;
        height: 87px;
        background: linear-gradient(90deg, #ff4d23 0%, #ff423a 100%);
        border-radius: 87px;
        margin: 24px auto calc(50px + env(safe-area-inset-bottom)) auto;
        color: #fff;
        line-height: 87px;
        text-align: center;
        display: block;
    }
    // }
}
