import { View, Image, Text } from "@tarojs/components";
import { useRouter } from "@tarojs/taro";
import { useEffect, useState } from "react";
import "./index.less";
import { Button, Checkbox, Field, Icon, Rate, Row } from "@antmjs/vantui";
import Taro from "@tarojs/taro";
import DDYToast from "@/utils/toast";
import { PROJECT_CONFIG } from "@/utils/env";
import newAPi from "@/utils/newApi";
import { DDYBack, DDYNavigateTo } from "@/utils/route";
import { list } from "postcss";
definePageConfig({
    navigationBarTitleText: "发表评价",
});

export default () => {
    const route = useRouter();

    const orderId = route.params.id;
    const [anonymity, setAnonymity] = useState(1); // 1 否, 2 是
    const [skuOrders, setskuOrders] = useState([]);
    // const props = {
    //     value: value,
    //     onChange: (e) => setValue(e.detail),
    //   }
    const selectImg = skuindex => {
        Taro.chooseImage({
            count: 6 - (skuOrders[skuindex].fileList || []).length,
            success(result) {
                var tempFilePaths = result.tempFilePaths;
                DDYToast.showLoading("图片上传中");
                tempFilePaths.map(item => {
                    Taro.uploadFile({
                        url: `${PROJECT_CONFIG.API_MALL}/api/file/upload`, //仅为示例，非真实的接口地址
                        filePath: item,
                        name: "file",
                        success: function (res) {
                            console.log(res.data);
                            DDYToast.hideLoading();
                            try {
                                const json = JSON.parse(res.data);
                                console.log("json:", json);
                                if (!json.success) {
                                    return DDYToast.info(json.errorMsg);
                                }
                                skuOrders[skuindex].fileList.push(json.data);
                                setskuOrders([...skuOrders]);
                            } catch (error) {
                                return DDYToast.info("上传失败");
                            }
                        },
                        fail: function () {
                            DDYToast.hideLoading();
                        },
                    });
                });
            },
        });
    };

    const del = (imgindex, skuindex) => {
        skuOrders[skuindex].fileList.splice(imgindex, 1);
        // setFilelist([...fileList]);
        setskuOrders([...skuOrders]);
    };

    const submit = () => {
        for (let index = 0; index < skuOrders.length; index++) {
            const element = skuOrders[index];
            if (!element.content) {
                return DDYToast.info("请输入评论");
            }
            if (!element.score) {
                return DDYToast.info("请选择评分");
            }
        }
        const evaluationRequestList = (skuOrders || []).map(item => {
            return {
                scoreCode: item.score * 20,
                evaluationContent: item.content,
                evaluationUrl: JSON.stringify(item.fileList),
                orderId: orderId || 1090334,
                anonymity: anonymity,
                itemId: item.itemId,
                skuId: item.skuId,
                skuName: item.itemName,
                specification: item.specification,
            };
        });
        newAPi
            .submitComment({
                method: "POST",
                data: {
                    evaluationRequestList: evaluationRequestList,
                },
            })
            .then(res => {
                // TODO
                // 去发布成功页
                DDYToast.success("评价成功", 2000);
                setTimeout(() => {
                    DDYBack();
                }, 2000);
            });
    };

    const getDetailInfo = () => {
        newAPi
            .getskusInfo({
                // method: 'GET',
                data: {
                    orderId: orderId || 1088682,
                },
                filterCheck: true,
            })
            .then(res => {
                console.log("res:", res);
                // setDetail(res);
                setskuOrders(
                    res.data.map(item => {
                        return { ...item, fileList: [], score: 5 };
                    }),
                );
            });
    };
    useEffect(() => {
        getDetailInfo();
    }, []);

    return (
        <View className="submit-commit-page">
            {(skuOrders || []).map((skuItem, skuindex) => {
                return (
                    <>
                        <View className="card-rect">
                            <View className="goods-info">
                                <Image src={skuItem.skuImage} mode="aspectFill" className="goods-img" />
                                <View className="goods-name">{skuItem.itemName}</View>
                            </View>
                        </View>
                        <View className="card-rect">
                            <View className="form-title">
                                <Icon name="chat-o" size="26px" className="icon"></Icon>
                                <Text className="card-text">评论商品</Text>
                            </View>
                            <View className="form-content">
                                <Field
                                    // label="留言"
                                    type="textarea"
                                    value={skuItem.content}
                                    placeholder="感受怎么样？跟大家一起分享一下~"
                                    maxlength={255}
                                    showWordLimit
                                    autosize={{ minHeight: "120px", backgroundColor: "rgba(248, 248, 248, 1)" }}
                                    onChange={e => {
                                        console.log("e.detail.value:", e.detail);
                                        // setContent(e.detail);
                                        skuItem.content = e.detail;
                                        setskuOrders([...skuOrders]);
                                    }}
                                    border={false}
                                />
                            </View>
                            {/* </View>
                    <View className="card-rect"> */}
                            <Row style={{ margin: "12px 0" }}>
                                <Text className="card-text">商品评分</Text>
                                <Rate
                                    value={skuItem.score || 0}
                                    style={{ marginLeft: "20px" }}
                                    color="rgba(245, 196, 120, 1)"
                                    onChange={e => {
                                        skuItem.score = e.detail;
                                        setskuOrders([...skuOrders]);
                                    }}
                                />
                                {/* <Text className="score-level">非常好</Text> */}
                            </Row>
                            {/* </View>
                    <View className="card-rect"> */}
                            <View style={{ margin: "12px 0" }}>
                                {" "}
                                <Text className="card-text">商品图片</Text>
                            </View>
                            <View className="commit-pic-list">
                                {/* <View></View> */}
                                {skuItem.fileList.map((item, imgIndex) => {
                                    return (
                                        <View
                                            className="commit-pic-item"
                                            onClick={() => {
                                                Taro.previewImage({
                                                    urls: skuItem.fileList,
                                                    current: item,
                                                });
                                            }}
                                        >
                                            <Image src={item} className="commit-pic-img" />
                                            <Icon
                                                name="cross"
                                                size="14px"
                                                className="commit-pic-del"
                                                onClick={() => {
                                                    del(imgIndex, skuindex);
                                                }}
                                            />
                                            {/* <Icon name='cross' size="32px" /> */}
                                        </View>
                                    );
                                })}
                                {skuItem.fileList.length < 6 ? (
                                    <View className="commit-pic-add" onClick={() => selectImg(skuindex)}>
                                        <Icon name="photograph" size="20px" />
                                        <View className="commit-pic-add-text">
                                            可再加{6 - skuItem.fileList.length} 张
                                        </View>
                                    </View>
                                ) : null}
                            </View>
                        </View>
                    </>
                );
            })}
            <View className="card-rect">
                <Checkbox
                    value={anonymity == 2}
                    // style={{ marginTop: "26rpx" }}
                    onChange={e => {
                        setAnonymity(e.detail ? 2 : 1);
                    }}
                    shape="square"
                >
                    匿名评价
                </Checkbox>
            </View>

            {/* <View className="btn-rect"> */}
            <Button
                className="submit-btn"
                onClick={() => {
                    submit();
                }}
            >
                发布评价
            </Button>
            {/* </View> */}
        </View>
    );
};
