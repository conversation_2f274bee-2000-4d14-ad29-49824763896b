import newAPi from "@/utils/newApi";
import React, { useState } from "react";
import { optimizeCoupons } from "../wallet-card/fn/optimizeCoupons";
import Coupon from "../wallet-card//coupon";
import GiftVoucher from "../wallet-card/giftVoucher";
import { View } from "@tarojs/components";
import "./index.less";
import Taro, { useDidShow } from "@tarojs/taro";
import { isAlipay } from "@/utils/common";
import DesginPage from "@/components/desgin";
import { localLoginMask, reLogin } from "@/utils/ylogin";
import api from "@/utils/api";
import identityStore from "@/store/identity-store";
import infoStore from "@/store/info-store";

export default () => {
    const [showPopup, setShowPopup] = useState(false);
    const [showGiftVoucher, setShowGiftVoucher] = useState(false);
    const [coupons, setCoupons] = useState<any>();
    const [giftVouchers, setGiftVouchers] = useState<any>([]);
    const [totalAmount, setTotalAmount] = useState(0);

    useDidShow(() => {
        // 更新任务派发
        Taro.eventCenter.trigger("indexUpdate");
        if (isAlipay()) {
            //@ts-ignore
            my.hideBackHome();
        }
        checkLogin().then(() => {});
    });
    async function checkLogin() {
        let hasLogin = localLoginMask();
        let hold = await newAPi.loginCheck({ data: {}, showError: true, method: "POST" });
        //如果信息过期,且以前登录过，则自动登录，以保持登录
        try {
            //如果信息过期,且以前登录过，则自动登录，以保持登录
            if (!hold && hasLogin) {
                try {
                    await reLogin();
                } catch (e) {
                    hasLogin = false;
                }
            }
            if (hasLogin) {
                getCouponsSend();
                // getGiftVouchers();
            }
        } catch (e) {}
    }

    // 领取优惠券
    const getCouponsSend = () => {
        newAPi
            .getAutoGetCoupons({
                method: "GET",
                data: {},
            })
            .then(res => {
                if (res?.length > 0) {
                    const optimizedCoupons = optimizeCoupons(res, "popup");
                    setCoupons(optimizedCoupons);
                    setShowPopup(true);
                } else {
                    getGiftVouchers();
                }
            })
            .catch(() => {
                getGiftVouchers();
            });
    };

    // 领取福豆券
    const getGiftVouchers = () => {
        newAPi
            .getUnReadAmountSend({
                method: "POST",
                data: {},
            })
            .then(res => {
                if (res?.userAmountSendVOList?.length > 0) {
                    setGiftVouchers(res?.userAmountSendVOList);
                    setTotalAmount(res?.totalAmount);
                    setShowGiftVoucher(true);
                }
            });
    };

    return (
        <View className="home-page">
            <DesginPage pageType={10} />
            {/* 福豆券 */}
            {!showPopup && showGiftVoucher && giftVouchers?.length > 0 && (
                <GiftVoucher
                    visible={showGiftVoucher}
                    dataList={giftVouchers}
                    totalAmount={totalAmount}
                    onClose={() => setShowGiftVoucher(false)}
                />
            )}
            {/* 优惠券 */}
            {showPopup && coupons?.length > 0 && (
                <Coupon
                    visible={showPopup}
                    dataList={coupons}
                    onClose={() => {
                        setShowPopup(false);
                        getGiftVouchers();
                    }}
                />
            )}
        </View>
    );
};
