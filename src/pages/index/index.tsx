import React, { useState } from "react";
import { View } from "@tarojs/components";
import video from "../index/components/video";
import guide from "../index/components/guide";
import hotArea from "../index/components/hot-area";
import image from "../index/components/image";
import itemList from "../index/components/item-list";
import search from "../index/components/search";
import slider from "../index/components/slider";
import navigation from "../index/components/navigation";
import "./index.less";
import api from "@/utils/api";
import { getStorage } from "@/utils/storage";
import { STORE_ID } from "@/utils/constant";
import { useDidShow } from "@tarojs/taro";
import { isAlipay } from "@/utils/common";

const components = {
    video: video,
    search: search,
    guide: guide,
    "hot-area": hotArea,
    "item-list": itemList,
    slider: slider,
    image: image,
    navigation: navigation,
};

export default () => {
    const [modules, setModules] = useState<any[]>([]);
    const getData = () => {
        api.getShopDiyData({
            data: { shopId: getStorage(STORE_ID) },
            filterCheck: true,
        }).then(res => {
            try {
                setModules(JSON.parse(res.publishData).pages[0].modules);
            } catch (error) {}
        });
    };

    useDidShow(() => {
        getData();
        if (isAlipay()) {
            //@ts-ignore
            my.hideBackHome();
        }
    });

    return (
        <View className="page">
            {modules.map((item, index) => {
                const Component = components[item.name];
                if (!Component) return null;
                return <Component {...item} key={index} />;
            })}
        </View>
    );
};
