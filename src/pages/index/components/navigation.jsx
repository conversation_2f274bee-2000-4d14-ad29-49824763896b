import { Image, View, NavigationBar } from "@tarojs/components";
import React, { useState, useEffect } from "react";
import Taro from "@tarojs/taro";

export default ({ set = {}, commonSet = {}, children }) => {
    const [statusBarHeight, setStatusBarHeight] = useState(Taro.getSystemInfoSync().statusBarHeight);
    useEffect(() => {}, []);
    return (
        <View className="navigation">
            <View style={{ height: statusBarHeight + "px" }}></View>
            <View className="navigation-status" style={{ height: statusBarHeight + "px", top: 0 }}></View>
            <View className="navigation-content" style={{ top: statusBarHeight + "px" }}>
                <View className="title">{set.title}</View>
            </View>
            <View className="fill"></View>
        </View>
    );
};
