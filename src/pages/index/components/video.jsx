import { Image, View } from "@tarojs/components";
import React, { useState, useEffect } from "react";

export default ({ set = {}, commonSet = {} }) => {
    const [style, setStyle] = useState({
        marginTop: `${commonSet.marginTop}px`,
        marginBottom: `${commonSet.marginBottom}px`,
        backgroundColor: `${commonSet.bgColor}`,
    });
    return (
        <View class="video-module" style={{ style }}>
            <video
                src={set.src}
                style={{ height: `${set.height}rpx`, width: "100%" }}
                showFullscreenBtn={true}
                showPlayBtn={true}
                showCenterPlayBtn={true}
                controls={true}
                poster={`${set.pic}?x-oss-process=image/resize,w_750,h_${set.height}`}
            />
        </View>
    );
};
