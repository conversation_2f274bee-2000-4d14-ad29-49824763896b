import { Image, View } from "@tarojs/components";
import React, { useState, useEffect } from "react";
import { DDYSwitchTab, go } from "@/utils/route";
export default ({ set = {}, commonSet = {} }) => {
    const [style, setStyle] = useState({
        marginTop: `${commonSet.marginTop}px`,
        marginBottom: `${commonSet.marginBottom}px`,
        backgroundColor: `${commonSet.bgColor}`,
    });
    return (
        <View style={style}>
            <View
                className="search"
                onClick={() => {
                    DDYSwitchTab({ url: "/pages/home/<USER>" });
                }}
            >
                <View className="search-panel">
                    <View className="search-words">请输入你要搜索的宝贝</View>
                </View>
            </View>
        </View>
    );
};
