import React, { useState } from "react";
import Taro from "@tarojs/taro";
import { Image, View } from "@tarojs/components";
import { go } from "@/utils/route";
export default ({ set = {}, commonSet = {} }) => {
    const [style, setStyle] = useState({
        marginTop: `${commonSet.marginTop}px`,
        marginBottom: `${commonSet.marginBottom}px`,
        backgroundColor: `${commonSet.bgColor}`,
        height: `${commonSet.height}rpx`,
    });

    return (
        <>
            <View className="image-container" style={{ style }}>
                <Image
                    className="image-cp"
                    src={`${set.pic}?x-oss-process=image/resize,w_750`}
                    onClick={() => {
                        go(set);
                    }}
                    mode="widthFix"
                    lazy-load="true"
                />
            </View>
        </>
    );
};
