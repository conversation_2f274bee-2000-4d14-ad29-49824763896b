import { Image, View, Text } from "@tarojs/components";
import React, { useState, useEffect } from "react";
import Taro from "@tarojs/taro";
import { go } from "@/utils/route";
import { getStorage } from "@/utils/storage";
import { STORE_ID, SUB_STORE_ID, MEMBER_SHIP_LEVEL } from "@/utils/constant";
import { WaterfallFlow } from "@antmjs/vantui";
import api from "@/utils/api";
// import Price from "@/components/price";
// import GoodsGradeLabel from "@/components/goodsGradeLabel";
export default ({ set = {}, commonSet = {} }) => {
    const [style, setStyle] = useState({
        marginTop: `${commonSet.marginTop}px`,
        marginBottom: `${commonSet.marginBottom}px`,
        backgroundColor: `${commonSet.bgColor}`,
    });

    const [list, setList] = useState([]);
    const subStoreId = getStorage(SUB_STORE_ID);
    const getData = () => {
        const data = {
            shopId: getStorage(STORE_ID),
            ids: set.ids.join("_"),
        };
        if (subStoreId) {
            data.ssid = subStoreId;
        }
        api.getSubStoreGoodsList({ data, showError: false }).then(res => {
            try {
                setList(res.list);
            } catch (error) {
                console.log(error);
            }
        });
    };

    const renderItem = (item, index) => {
        return (
            <View
                className="item"
                // style={{ marginTop: index === 0 || index === 1 ? "20rpx" : "" }}
                key={index}
                onClick={() => {
                    go({ url: "/pages/goods_detail?id=" + item.id });
                }}
            >
                <View className="pic">
                    <Image mode="scaleToFill" src={`${item.mainPic}?x-oss-process=image/resize,l_350`} />
                </View>
                {item.sellOutStatus == 2 && (
                    <View className="sell-out">
                        <Image
                            mode="scaleToFill"
                            src="https://dante-img.oss-cn-hangzhou.aliyuncs.com/71940503337.png"
                        />
                    </View>
                )}

                <View className="title" style={{ color: item.sellOutStatus == 2 ? "#999999" : "" }}>
                    {item.flagList?.map((flagItem, flagIndex) => {
                        return (
                            <Text className="label-box" key={flagItem}>
                                {flagItem == "promotionActivityItem" && <Text className="label-item">活动</Text>}
                                {flagItem == "promotionLimitedItem" && <Text className="label-item-limit">限定</Text>}
                            </Text>
                        );
                    })}
                    {item.title}
                </View>
                <View className="info">
                    <View className="price" style={{ color: item.sellOutStatus == 2 ? "#999999" : "" }}>
                        <Text className="price-num">¥</Text>
                        {item.price / 100}
                        {!!item.crossedPrice && <Text className="original-price">¥{item.crossedPrice / 100}</Text>}
                    </View>
                    <View className="info-right">
                        {item.promotionList?.map((couponItem, couponIndex) => {
                            return (
                                <View className="coupon-box" key={couponIndex}>
                                    {couponItem.couponType == "CASH_REBATE" && (
                                        <Text className="cash-rebate">
                                            满{couponItem.conditionFee / 100}减{couponItem.reduceFee / 100}
                                        </Text>
                                    )}
                                    {couponItem.couponType == "LADDER" && <Text className="cash-rebate">满减</Text>}
                                </View>
                            );
                        })}
                    </View>
                </View>
                {!!item.origin && (
                    <View className="from">
                        <View className="txt" style={{ color: item.sellOutStatus == 2 ? "#999999" : "" }}>
                            {" "}
                            {item.origin.name}
                        </View>
                    </View>
                )}
            </View>
        );
    };

    useEffect(() => {
        if (set?.ids) {
            getData();
        }
    }, [set]);

    return (
        <View className="item-list" style={{ style }}>
            {set.title && set.title.type == 1 && (
                <View className="item-list-title">
                    <View className="text">{set.title.text}</View>
                    <View className="bg"></View>
                </View>
            )}
            {set.title && set.title.type == 2 && (
                <View className="item-list-title-img">
                    <Image src={`${set.title.pic}?x-oss-process=image/resize,l_750`} mode="widthFix" />
                </View>
            )}
            <View className={"content"}>
                {list.map((item, index) => {
                    return renderItem(item, index);
                })}
            </View>
        </View>
    );
};
