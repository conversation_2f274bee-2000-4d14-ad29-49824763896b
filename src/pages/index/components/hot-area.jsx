import React, { useState } from "react";
import Taro from "@tarojs/taro";
import { View } from "@tarojs/components";
import { go } from "@/utils/route";
export default ({ set = {}, commonSet = {} }) => {
    const [style, setStyle] = useState({
        marginTop: `${commonSet.marginTop}px`,
        marginBottom: `${commonSet.marginBottom}px`,
        backgroundColor: `${commonSet.bgColor}`,
        height: `${set.height}rpx`,
        backgroundImage: `url(${set.pic}?x-oss-process=image/resize,w_750)`,
    });

    return (
        <>
            <View className="hot-area" style={style}>
                {Array.isArray(set.list) &&
                    set.list.map((item, index) => {
                        return (
                            <View
                                onClick={() => {
                                    go(item);
                                }}
                                key={index}
                                style={{
                                    left: Taro.pxTransform(item.left),
                                    top: Taro.pxTransform(item.top),
                                    width: Taro.pxTransform(item.width),
                                    height: Taro.pxTransform(item.height),
                                }}
                            ></View>
                        );
                    })}
            </View>
        </>
    );
};
