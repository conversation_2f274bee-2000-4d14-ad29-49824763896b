import { Image, View, Swiper, SwiperItem, Video } from "@tarojs/components";
import React, { useState, useEffect } from "react";
import { go } from "@/utils/route";
import Taro from "@tarojs/taro";
export default ({ set = {}, commonSet = {} }) => {
    const [style, setStyle] = useState({
        marginTop: `${commonSet.marginTop}px`,
        marginBottom: `${commonSet.marginBottom}px`,
        backgroundColor: `${commonSet.bgColor}`,
    });
    const [isPlaying, setIsPlaying] = useState(false);

    const startPlay = () => {
        setIsPlaying(true);
    };

    const pausePlay = () => {
        setIsPlaying(false);
    };

    const swiperChange = e => {
        const videoContext = Taro.createVideoContext("myVideo");
        if (videoContext) {
            videoContext.pause();
            Array.isArray(set.list) &&
                set.list.map((item, index) => {
                    if (item.type == "video" && index == e.detail.current) {
                        videoContext.play();
                        pausePlay();
                    }
                });
        }
    };

    useEffect(() => {
        if (Array.isArray(set.list) && set.list[0].type == "video") {
            const videoContext = Taro.createVideoContext("myVideo");
            videoContext.play();
        }
    }, []);
    return (
        <View className="slider" style={style}>
            <Swiper
                className="swiper"
                indicator-dots="true"
                indicator-color="rgba(187,187,187,0.8)"
                indicator-active-color="rgba(255,255,255,0.8)"
                autoplay={!isPlaying}
                circular="true"
                onChange={swiperChange}
                interval="3000"
                duration="300"
                style={{ height: `${set.height}rpx` }}
            >
                {set.list.map((item, index) => {
                    return (
                        <SwiperItem key={index}>
                            {item.type === "pic" && (
                                <View
                                    className="swiper-item"
                                    onClick={() => {
                                        go(item);
                                    }}
                                    style={{
                                        height: `${set.height}rpx`,
                                        backgroundImage: `url(${item.pic}?x-oss-process=image/resize,w_750)`,
                                        width: "100%",
                                    }}
                                ></View>
                            )}
                            {item.type == "video" && (
                                <View>
                                    <Video
                                        id="myVideo"
                                        src={item.url}
                                        onPlay={() => startPlay()}
                                        onPause={() => pausePlay()}
                                        onEnded={() => pausePlay()}
                                        enableProgressGesture={false}
                                        style={{ height: `${set.height}rpx`, width: "100%" }}
                                        showFullScreen-btn={true}
                                        showPlayBtn={true}
                                        showCenterPlayBtn={true}
                                        controls={true}
                                        poster={`${item.pic}?x-oss-process=image/resize,w_750,h_${set.height}`}
                                    />
                                </View>
                            )}
                        </SwiperItem>
                    );
                })}
            </Swiper>
        </View>
    );
};
