@font-face {
    font-family: "D-DIN";
    src: url("https://dante-img.oss-cn-hangzhou.aliyuncs.com/96575305317.ttf");
}
@font-face {
    font-family: "iconDd";
    src: url("//at.alicdn.com/t/font_1212595_27pac7bicny.eot?t=1570872420434"); /* IE9 */
    src: url("//at.alicdn.com/t/font_1212595_27pac7bicny.eot?t=1570872420434#iefix") format("embedded-opentype"),
        /* IE6-IE8 */ url("//at.alicdn.com/t/font_1212595_27pac7bicny.woff?t=1570872420434") format("woff"),
        url("//at.alicdn.com/t/font_1212595_27pac7bicny.ttf?t=1570872420434") format("truetype"),
        /* chrome, firefox, opera, Safari, Android, iOS 4.2+ */
            url("//at.alicdn.com/t/font_1212595_27pac7bicny.svg?t=1570872420434#iconDd") format("svg"); /* iOS 4.1- */
}

.iconDd {
    font-family: "iconDd" !important;
    font-size: 16px;
    font-style: normal;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}
.content {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: space-between;
    padding: 0 18rpx;
    background-color: #f1f2f3;
}
.box {
    // border: 1px red solid;
    margin-top: 17px;
    // margin-left: 10px;
    width: 342px;
    // height: 514px;
    background: #ffffff;
    border-radius: 12px;
    position: relative;

    .itemImage {
        width: 100%;
        height: 342px;
        border-radius: 12px 12px 0 0;
    }

    .itemName {
        width: 310px;
        height: fit-content;
        height: 80px;
        overflow: hidden;
        font-size: 26px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: #333333;
        margin: 5px auto 10px;
        line-height: 38px;
        // white-space: nowrap;
        text-overflow: ellipsis;

        .autarky {
            font-size: 20px;
            font-weight: 400;
            display: inline-block;
            text-align: center;
            width: 52px;
            height: 28px;
            line-height: 28px;
            border-radius: 4px;
        }

        .tax-payment {
            color: #ffffff;
            background: linear-gradient(270deg, #c04ad8 0%, #894ad8 51%, #794ad8 100%);
        }

        .bonded {
            color: #794ad8;
            background: #f6edfc;
            border: 1px solid #794ad8;
        }

        .isautarky {
            display: inline-block;
        }
    }

    // .itemPrice {
    //     margin-left: 24px;
    // }

    .itemPriceText {
        font-size: 40px;
    }

    .price-box {
        display: flex;
        flex-direction: column;
        // justify-content: space-between;
        margin: 0 16px 11px 16px;

        .original-price {
            color: #888888;
            font-size: 26px;
            text-decoration: line-through;
        }

        .commission {
            width: fit-content;
            height: 36px;
            font-size: 24px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;

            display: flex;

            .comm-title {
                width: 43px;
                height: 36px;
                color: #ffffff;
                text-align: center;
                border-radius: 4px 0px 0px 4px;
                background: linear-gradient(270deg, #c04ad8 0%, #894ad8 51%, #794ad8 100%);
            }

            .comm-num {
                color: #b542df;
                padding-left: 10px;
                padding-right: 10px;
                border-radius: 0px 4px 4px 0px;
                border: 1px solid #a84ad8;
            }
        }

        .price-box-hot {
            color: #794ad8;
            font-size: 24px;
            font-weight: bold;
            margin-right: 16px;
        }

        .price-box-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
    }
}

.guide {
    .list {
        display: flex;
        flex-direction: row;
        flex-wrap: wrap;
        padding-bottom: 40px;
    }
    .num-3 {
        width: 250px !important;
    }
    .num-4 {
        width: 187px !important;
    }
    .type-2 {
        .icon {
            border-radius: 100px;
            width: 100px;
            height: 100px;
        }
    }
    .node {
        padding-top: 40px;
        width: 150px;

        .icon {
            width: 90px;
            height: 90px;
            margin: 0 auto;
            background-repeat: no-repeat;
            background-position: center center;
            background-size: contain;
            overflow: hidden;
        }

        .title {
            margin-top: 16px;
            font-size: 26px;
            line-height: 26px;
            text-align: center;
            height: 26px;
            overflow: hidden;
            color: #555;
        }
    }
}

.hot-area {
    width: 750px;
    background-repeat: no-repeat;
    background-size: 100% auto;
    position: relative;
    .hot-area view {
        position: absolute;
    }
}

.image-container {
    .image-cp {
        display: block;
        width: 750px;
    }
}

.item-list {
    .item-list-title {
        height: 112px;
        position: relative;
    }
    .item-list-title .text {
        line-height: 112px;
        font-size: 36px;
        color: #333;
        padding: 0 40px;
        position: absolute;
        z-index: 1;
        left: 50%;
        transform: translate(-50%, 0);
        // background: #efefef;
    }

    .item-list-title .bg {
        position: absolute;
        border-top: solid 1px #ccc;
        left: 50%;
        bottom: 40px;
        width: 137px;
        height: 12px;
        background: #ffdbda;
        border-radius: 12px;
        transform: translateX(-50%);
    }
    .item-list-title-img img {
        display: block;
        width: 750px;
    }
    .list {
        display: flex;
        flex-direction: row;
        flex-wrap: wrap;
        justify-content: flex-start;
        padding-left: 17px;
        z-index: 10;
    }
    .item {
        width: 350px;
        // height: 582px;
        padding-bottom: 20px;
        border-radius: 10px;
        overflow: hidden;
        background: #fff;
        margin-bottom: 20px;
        //   margin-right: 16px;
        position: relative;
    }
    .item .pic {
        width: 350px;
        height: 350px;
        //background: url(http://x.eat163.com/98278485230.png?x-oss-process=image/resize,m_fill,h_350,w_350) no-repeat
        //    center center;
        //background-size: cover;
    }
    .item .sell-out {
        width: 350px;
        height: 350px;
        background: rgba(0, 0, 0, 0.4);
        position: absolute;
        top: 0;
        left: 0;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    .item .sell-out image {
        display: block;
        width: 174px;
        height: 138px;
    }
    .item .pic image {
        display: block;
        width: 350px;
        height: 350px;
    }
    .item .title {
        margin: 10px 20px;
        max-height: 72px;
        line-height: 36px;
        font-size: 24px;
        overflow: hidden;
        color: #555;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
    }
    .item .info {
        margin: 10px 13px 0 18px;
        display: flex;
        flex-direction: row;
        flex-wrap: wrap;
        justify-content: space-between;
        align-items: center;
    }
    .item .price {
        font-size: 32rpx;
        font-family: D-DIN, D;
        font-weight: normal;
        color: #fe3300;
        line-height: 40rpx;
        letter-spacing: 2px;
        padding-top: 8rpx;
    }
    .item .original-price {
        font-size: 18px;
        font-family: D-DIN;
        font-weight: normal;
        color: #888888;
        line-height: 35px;
        margin-left: 5px;
        text-decoration: line-through;
    }

    .item .coupon-box {
        // height: 36px;
        font-size: 24px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #de1600;
        display: inline-block;
        margin-left: 5px;
    }
    .coupon-box .cash-rebate {
        border-radius: 4px;
        border: 1px solid #ffeff2;
        padding: 0 6px;
        background-color: #ffeff2;
    }
    .item .price-num {
        font-size: 24px;
    }
    .item .price view {
        display: inline-block;
        font-size: 32px;
        font-weight: bold;
    }
    .from .buys {
        font-size: 22px;
        color: #888;
    }
    .item .from {
        margin-left: 25px;
        font-size: 22px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        display: flex;
        flex-direction: row;
        flex-wrap: wrap;
        justify-content: space-between;
        // position: absolute;
        // bottom: 15px;
        line-height: 30px;
        width: 320px;
        color: #888888;
    }
    .item .profit {
        font-size: 24px;
    }
    .item .info .country_img {
        width: 36px;
        height: 36px;
    }
    .item .info .country_word {
        margin-left: 9px;
        color: #8f8f8f;
        font-size: 20px;
        line-height: 21px;
    }
}

.navigation {
    .fill {
        height: 100px;
    }
    .navigation-content {
        background: #fff;
        position: fixed;
        left: 0;
        top: 0;
        width: 750px;
        height: 100px;
        box-sizing: border-box;
        z-index: 100;
        border-bottom: solid 1px #ccc;
    }
    .navigation-status {
        position: fixed;
        left: 0;
        top: 0;
        width: 750px;
        z-index: 100;
        background: #fff;
    }
    .navigation-content .title {
        padding-top: 30px;
        width: 600px;
        overflow: hidden;
        margin: 0 auto;
        text-align: center;
        line-height: 36px;
        height: 36px;
        font-size: 36px;
    }
    .navigation-content .back {
        font-family: "iconfont";
        position: absolute;
        left: 0;
        bottom: 12px;
        line-height: 90px;
        height: 90px;
        font-size: 40px;
        width: 90px;
        text-align: center;
    }
    .navigation-content .back:before {
        font-family: "iconDT";
        content: "\e649";
    }
}

.search .search-panel {
    height: 64px;
    border-radius: 64px;
    overflow: hidden;
    background: #eee;
    padding: 0 15px;
    margin: 20px;
    position: relative;
    line-height: 64px;
    .search-words {
        font-size: 30px;
        color: #999;
    }
    .search-words:before {
        font-family: "iconDd";
        margin-right: 10px;
        content: "\e639";
        color: #999;
        font-weight: bold;
        font-size: 30px;
    }
}

.slider {
    margin: 0;
    padding: 0;
    .swiper {
        width: 750px;
        .swiper-item {
            background-repeat: no-repeat;
            background-position: center center;
            background-size: cover;
            width: 750px;
        }
    }
}

.video-module {
    display: block;
    width: 750px;
}

.label-box {
    margin-right: 10px;
    display: inline-block;
    .label-item {
        border: 1rpx solid #f50e0c;
        border-radius: 6px;
        padding: 0 4px;
        // height: 36px;
        font-size: 24px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #f50e0c;
        display: inline-block;
    }
    .label-item-limit {
        border: 1rpx solid #f50e0c;
        background: #f50e0c;
        color: #fff;
        padding: 0 4px;
        border-radius: 6px;
        // height: 36px;
        font-size: 24px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        display: inline-block;
    }
}
