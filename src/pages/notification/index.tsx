import api from "@/utils/api";
import { STORE_ID } from "@/utils/constant";
import { getStorage } from "@/utils/storage";
import { Tab, Tabs } from "@antmjs/vantui";
import { View } from "@tarojs/components";
import { useLoad } from "@tarojs/taro";
import { useState } from "react";
import Tabulation from "./component/Tabulation";

definePageConfig({
    navigationBarTitleText: "消息中心",
});

const Index = () => {
    const [data, setData] = useState([]);

    useLoad(async () => {
        try {
            const response = await api.getNoticebarList({ data: { shopId: getStorage(STORE_ID) } });
            setData(response);
        } catch (e) {}
    });

    return (
        <View className="notification">
            <Tabs sticky={true}>
                {data.map((item, index) => {
                    return (
                        <Tab key={index} title={item[0]}>
                            <Tabulation type={JSON.stringify(item)} />
                        </Tab>
                    );
                })}
            </Tabs>
        </View>
    );
};

export default Index;
