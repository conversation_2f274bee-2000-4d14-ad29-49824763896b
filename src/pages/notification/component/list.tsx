import React from "react";
import { View, Text } from "@tarojs/components";
import "./list.less";
import dayjs from "dayjs";

type ListType = {
    content: string;
    sentAt: number;
    read?: boolean;
    title?: string;
    uuid?: string;
};

const List: React.FC<ListType> = props => {
    const { content, sentAt } = props;

    const formatTime = dayjs(sentAt).format("YYYY-MM-DD HH:mm:ss");
    return (
        <View className="notification__list">
            <Text className="notification__list--content">{content}</Text>
            <Text className="notification__list--sentAt">{formatTime}</Text>
        </View>
    );
};

export default List;
