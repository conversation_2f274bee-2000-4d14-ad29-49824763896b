import api from "@/utils/api";
import { STORE_ID } from "@/utils/constant";
import { getStorage } from "@/utils/storage";
import {
    IPullToRefreshProps,
    InfiniteScroll,
    InfiniteScrollInstance,
    InfiniteScrollProps,
    PullToRefresh,
    Empty,
} from "@antmjs/vantui";
import { View } from "@tarojs/components";
import { useEffect, useRef, useState } from "react";
import List from "./list";

interface TabulationType {
    type: string;
}

const Tabulation: React.FC<TabulationType> = props => {
    const { type } = props;

    const pageNo = useRef(1);
    const infiniteScrollInstance = useRef<InfiniteScrollInstance>();

    const [dataList, setDataList] = useState<any[]>([]);

    useEffect(() => {
        pageNo.current = 1;
        infiniteScrollInstance.current?.reset(true);
    }, []);

    function getList() {
        let data = {
            shopId: getStorage(STORE_ID),
            type,
            page: pageNo.current,
            pageSize: 10,
        };
        return api.getNoticebar({ data });
    }

    const onRefresh: IPullToRefreshProps["onRefresh"] = () => {
        return new Promise(resolve => {
            setTimeout(() => {
                pageNo.current = 1;
                infiniteScrollInstance.current?.reset(true);
                resolve(undefined);
            }, 300);
        });
    };

    const loadMore: InfiniteScrollProps["loadMore"] = async () => {
        return new Promise(async resolve => {
            getList()
                .then(res => {
                    let newData: any[];
                    if (pageNo.current === 1) {
                        newData = res.msgViewList;
                    } else {
                        newData = dataList.concat(res.msgViewList);
                    }
                    pageNo.current++;
                    setDataList(newData);
                    if (res.length == 0) {
                        resolve("complete");
                    } else {
                        resolve(res.length < 10 ? "loading" : "complete");
                    }
                })
                .catch(e => {
                    resolve("error");
                });
        });
    };

    return (
        <View>
            <PullToRefresh onRefresh={onRefresh}>
                {dataList?.map((item: any) => {
                    return <List {...item} key={item.uuid} />;
                })}
                <InfiniteScroll
                    loadMore={loadMore}
                    ref={infiniteScrollInstance}
                    completeText={
                        <>
                            {dataList.length == 0 ? (
                                <Empty
                                    description="暂无数据！"
                                    image="https://dante-img.oss-cn-hangzhou.aliyuncs.com/53286339194.png"
                                />
                            ) : (
                                "没有更多了"
                            )}
                        </>
                    }
                />
            </PullToRefresh>
        </View>
    );
};
export default Tabulation;
