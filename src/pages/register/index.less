.register-container,
.login-warp {
    min-height: 100%;
    padding: 32px;
    // background-color: #f8f8f8;

    .login-content {
        background-color: #ffffff;
        padding: 32px;
        border-radius: 16px;
    }

    .header {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding-bottom: 24px;
    }

    .title {
        font-weight: 500;
        font-size: 38px;
        color: #333333;
        margin: 24px 0;
    }

    .form-container {
        display: flex;
        flex-direction: column;
        gap: 16px;
    }

    .form-item {
        display: flex;
        align-items: center;
        border-bottom: 1px solid rgba(241, 242, 243, 1);
        padding: 24px 0;
    }

    .label {
        width: 130px;
        text-align: left;
        font-size: 28px;
        color: #333333;
    }

    .input {
        // height: 100px;
        font-size: 28px;
        padding: 0;
        border: none;
        outline: none;

        &.van-cell::after {
            border-bottom: none;
        }
    }

    .password-input {
        display: flex;
        width: 100%;
        flex: 1;
    }

    .verify-container {
        display: flex;
        flex: 1;
    }

    .verify-input {
        flex: 1;
        // height: 88px;
        border: 2px solid #eeeeee;
        border-radius: 8px;
        padding: 0 24px;
        font-size: 28px;
    }

    .verify-button {
        width: 200px;
        // height: 88px;
        background-color: #ffffff;
        font-size: 28px;
        border-radius: 8px;
        padding: 0;
        // line-height: 88px;
        border: none;
        outline: none;
        color: #ff0940;

        &::after {
            border: none;
            border-left: 1px solid rgba(0, 0, 0, 0.2);
            border-radius: 0;
        }
    }

    .verify-button[disabled] {
        border-color: #eeeeee;
        background-color: #f5f5f5;
        color: #888;
    }

    .submit-button {
        margin: 32px 0 12px;
        width: 100%;
        background: linear-gradient(90deg, #ff3b61 0%, #ff0940 100%);
        color: #ffffff;
        font-size: 32px;
        border-radius: 48px;
    }

    .input:focus {
        border-color: #ff0940;
        box-shadow: 0 0 0 2px rgba(255, 0, 0, 0.1);
    }

    .bottom-links {
        color: #888;
        position: fixed;
        bottom: 64px;
        left: 0;
        width: 100%;
        padding: 0 48px;
        display: flex;
        align-items: center;
        justify-content: center;
        box-sizing: border-box;

        .link-item {
            display: flex;
            align-items: center;
            flex-direction: row;
            padding: 0 32px;

            &:not(:last-child) {
                border-right: 1px solid #dddddd;
            }
        }
    }
    .agreement {
        margin-top: 48px;
        display: flex;
        align-items: flex-start;
        flex-direction: row;
        color: #888;
    }

    .checkbox {
        width: 32px;
        height: 32px;
        border: 1px solid #dddddd;
        border-radius: 4px;
        margin-right: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .checkbox-inner {
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .agreement-text {
        flex: 1;
        display: flex;
        flex-direction: row;
    }

    .agreement-desc {
        font-size: 24px;
        color: #888;
    }

    .link {
        font-size: 24px;
        color: #ff4d4f;
    }
}
