/**
 * @description 注册
 */
import { loginSuccessRediction, registerSuccess, useSafeRouter } from "@/utils/common";
import newAPi from "@/utils/newApi";
import { DDYNavigateTo } from "@/utils/route";
import DDYToast from "@/utils/toast";
import { ActionSheet, Checkbox, Field, Icon } from "@antmjs/vantui";
import { Button, View } from "@tarojs/components";
import Taro, { useRouter } from "@tarojs/taro";
import React, { useEffect, useState } from "react";
import "./index.less";
import { getStorage, setStorage } from "@/utils/storage";
import { INVITE_CODE, LOGIN_BACK_URL, USER_INFO } from "@/utils/constant";
import useCheckedAgreement from "../login/useCheckedAgreement";
import routes from "src/router";

const App: React.FC = () => {
    const { inviteCode, activityId, marketingToolId, groupId } = useSafeRouter().params;
    const [phone, setPhone] = useState("");
    const [password, setPassword] = useState("");
    const [verifyCode, setVerifyCode] = useState("");
    const [inviteCodeS, setInviteCodeS] = useState(inviteCode || "");
    const [showPassword, setShowPassword] = useState(false);
    const [countdown, setCountdown] = useState(0);
    const [show, setShow] = useState(false);
    const [value, setValue] = useState("");
    const [sources, setSources] = useState([]);
    const { isChecked, node } = useCheckedAgreement();
    const togglePassword = () => {
        setShowPassword(!showPassword);
    };

    // useEffect(()=>)

    const startCountdown = () => {
        setCountdown(60);
        const timer = setInterval(() => {
            setCountdown(prev => {
                if (prev <= 1) {
                    clearInterval(timer);
                    return 0;
                }
                return prev - 1;
            });
        }, 1000);
    };

    const getVerifyCode = () => {
        console.log("phone", phone);

        if (!/^1[3-9]\d{9}$/.test(phone)) {
            DDYToast.info("请输入正确的手机号");
            return;
        }
        newAPi
            .sendegisterMsg({
                method: "POST",
                data: {
                    phone,
                },
            })
            .then(res => {
                console.log("res:", res);
                DDYToast.info("发送成功");
                startCountdown();
            });
    };

    const handleSubmit = () => {
        if (!isChecked) {
            DDYToast.info("请阅读用户注册协议和隐私保护");
            return;
        }
        if (!/^1[3-9]\d{9}$/.test(phone)) {
            DDYToast.info("请输入正确的手机号");
            return;
        }
        // if (password.length < 6) {
        //     DDYToast.info("密码不能少于6位");
        //     return;
        // }
        if (verifyCode.length !== 6) {
            DDYToast.info("请输入6位验证码");
            return;
        }
        if (!value || !value.id) {
            DDYToast.info("请选择来源渠道");
            return;
        }
        Taro.login().then(res => {
            newAPi
                .inviteRejister({
                    method: "POST",
                    data: {
                        wxCode: res.code,
                        inviteCode: inviteCodeS,
                        phone: phone,
                        smsCode: verifyCode,
                        password: password,
                        channelType: value.id,
                        activityId: activityId || "",
                        marketingToolId: marketingToolId || "",
                        groupId: groupId || "",
                    },
                })
                .then(data => {
                    console.log("res:", data);
                    DDYToast.success("注册成功");
                    setStorage(USER_INFO, data);
                    const url = getStorage(LOGIN_BACK_URL);
                    if (!url && !data.inGroup && data.isJumpAllowed) {
                        setStorage(LOGIN_BACK_URL, `pages/wool-herd/index?join=false`);
                    }
                    loginSuccessRediction();
                });
        });
    };

    const getSource = () => {
        newAPi
            .getRegisterSource({
                method: "POST",
                data: {},
            })
            .then(res => {
                setSources(res);
            });
    };

    useEffect(() => {
        // setStorage(INVITE_CODE, inviteCode)
        if (
            !inviteCode &&
            getStorage(INVITE_CODE) &&
            getStorage(INVITE_CODE) !== "null" &&
            getStorage(INVITE_CODE) !== "undefined"
        ) {
            setInviteCodeS(getStorage(INVITE_CODE));
        }
        getSource();
    }, []);

    useEffect(() => {
        // if(useRouter())
        // { id: "FRIEND", name: "朋友推荐" }
        if (groupId) {
            setValue({ id: "FRIEND", name: "朋友推荐" });
        }
    }, []);

    return (
        <View className="register-container">
            <View className="login-content">
                <View className="header">
                    <View className="title">注册账号</View>
                </View>

                <View className="form-container">
                    <View className="form-item">
                        <View className="label">手机号</View>
                        <View>
                            <Field
                                maxlength={11}
                                placeholder="请输入手机号"
                                value={phone}
                                onChange={e => setPhone(e.detail)}
                                className="input"
                            />
                        </View>
                    </View>

                    {/* <View className="form-item">
                        <View className="label">密码</View>
                        <View className="password-input">
                            <Field
                                placeholder="请输入密码"
                                value={password}
                                onChange={e => setPassword(e.detail)}
                                className="input"
                                type={showPassword ? "text" : "password"}
                                renderButton={
                                    <View onClick={togglePassword} className="password-icon">
                                        <Icon name={showPassword ? "eye-o" : "closed-eye"} size={42} />
                                    </View>
                                }
                            />
                        </View>
                    </View> */}

                    <View className="form-item">
                        <View className="label">验证码</View>
                        <View className="verify-container">
                            <Field
                                // type="text"
                                maxlength={6}
                                placeholder="请输入验证码"
                                value={verifyCode}
                                className="input"
                                onChange={e => setVerifyCode(e.detail)}
                                renderButton={
                                    <Button onClick={getVerifyCode} disabled={countdown > 0} className="verify-button">
                                        {countdown > 0 ? `${countdown}s后重试` : "获取验证码"}
                                    </Button>
                                }
                            />
                        </View>
                    </View>

                    <View className="form-item">
                        <View className="label">来源</View>
                        <View
                            className="input"
                            style={{ lineHeight: "24px", flex: 1, height: "24px" }}
                            onClick={() => {
                                if (groupId) {
                                    return;
                                }
                                setShow(true);
                            }}
                        >
                            {value?.name}
                        </View>
                        <ActionSheet
                            show={show}
                            actions={sources}
                            onClose={() => setShow(false)}
                            onSelect={e => {
                                console.log(e.detail);
                                setValue(e.detail);
                                setShow(false);
                            }}
                        />
                    </View>

                    <View className="form-item">
                        <View className="label">邀请码</View>
                        <Field
                            // type="tel"
                            maxlength={8}
                            placeholder="请输入邀请码"
                            value={inviteCodeS}
                            onChange={e => setInviteCodeS(e.detail)}
                            className="input"
                        />
                    </View>
                </View>
                {/* 用户协议 */}
                {node}

                <Button onClick={handleSubmit} className="submit-button">
                    完成注册
                </Button>
            </View>
        </View>
    );
};

export default App;
