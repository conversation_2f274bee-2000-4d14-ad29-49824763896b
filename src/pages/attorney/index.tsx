import { View } from "@tarojs/components";
import { PROJECT_CONFIG } from "@/utils/env";
import { Cell, CellGroup, Icon } from "@antmjs/vantui";
import Toast from "@/utils/toast";
import Taro, { pxTransform } from "@tarojs/taro";
import { useEffect, useState } from "react";
import api from "@/utils/api";
import { STORE_ID } from "@/utils/constant";
import { getStorage } from "@/utils/storage";
definePageConfig({
    navigationBarTitleText: "授权书",
});

export default function () {
    const [list, setList] = useState<any>([]);
    const openPDF = (url: string) => {
        Toast.showLoading("打开中");
        if (url) {
            Taro.downloadFile({
                url: url,
                success: function (res) {
                    const Path = res.tempFilePath;
                    Taro.openDocument({
                        showMenu: true,
                        filePath: Path,
                        success: () => {
                            Toast.hideLoading();
                        },
                    });
                },
                fail: () => {
                    Toast.error("下载失败");
                },
            });
        }
    };
    PROJECT_CONFIG.ATTORNEY_LIST;

    const getData = () => {
        api.getAuthLetter({
            method: "GET",
            data: {
                current: 1,
                size: 20,
                shopId: getStorage(STORE_ID),
            },
        }).then(res => {
            if (res.data && res.data.length > 0) {
                const arr = res.data.map(item => {
                    return {
                        url: item.authorizationUrl,
                        name: item.authorizationName,
                    };
                });
                setList(arr);
            } else {
                setList(PROJECT_CONFIG.ATTORNEY_LIST);
            }
        });
    };

    useEffect(() => {
        getData();
    }, []);
    return (
        <View className="attorney" style={{ marginTop: pxTransform(24) }}>
            <CellGroup inset>
                {list.map(item => {
                    return (
                        <Cell
                            renderIcon={
                                <Icon
                                    classPrefix={"iconfont"}
                                    style={{ marginRight: pxTransform(30) }}
                                    name={" icon-draft-line"}
                                    size={40}
                                    color={"#CBCCCD"}
                                />
                            }
                            title={item.name}
                            isLink
                            onClick={() => openPDF(item.url)}
                        />
                    );
                })}
            </CellGroup>
        </View>
    );
}
