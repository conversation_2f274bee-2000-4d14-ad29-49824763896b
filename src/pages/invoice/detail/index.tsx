import React, { useState, useEffect } from "react";
import { View, Image, Button } from "@tarojs/components";
import { Icon } from "@antmjs/vantui";
import Taro, { useLoad, useRouter } from "@tarojs/taro";
import "./index.less";

// 路由参数类型
type RouterParams = {
    id?: string;
};

// 发票详情数据类型
interface InvoiceDetail {
    id: string;
    invoiceNumber: string;
    invoiceCode: string;
    amount: string;
    taxAmount: string;
    totalAmount: string;
    issueDate: string;
    buyerName: string;
    buyerTaxNumber?: string;
    sellerName: string;
    sellerTaxNumber: string;
    invoiceType: string;
    status: string;
    imageUrl: string;
    pdfUrl?: string;
}

const InvoiceDetail: React.FC = () => {
    const { id } = useRouter<RouterParams>().params;
    const [invoiceDetail, setInvoiceDetail] = useState<InvoiceDetail | null>(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(false);
    const [showFullImage, setShowFullImage] = useState(false);

    useLoad(() => {
        Taro.setNavigationBarTitle({ title: "发票预览" });
        loadInvoiceDetail();
    });

    // 模拟加载发票详情数据
    const loadInvoiceDetail = async () => {
        try {
            setLoading(true);
            setError(false);

            // 模拟API调用延迟
            await new Promise(resolve => setTimeout(resolve, 1000));

            // 模拟发票数据
            const mockDetail: InvoiceDetail = {
                id: id || "1",
                invoiceNumber: "12345678901234567890",
                invoiceCode: "144031909111",
                amount: "11.33",
                taxAmount: "1.47",
                totalAmount: "12.80",
                issueDate: "2024年03月08日",
                buyerName: "张三",
                buyerTaxNumber: "",
                sellerName: "江苏省苏宁易购电子商务有限公司",
                sellerTaxNumber: "91320100MA1MABCD12",
                invoiceType: "增值税电子普通发票",
                status: "已开票",
                imageUrl: "https://via.placeholder.com/800x1200/f0f0f0/666?text=发票预览图",
                pdfUrl: "https://example.com/invoice.pdf"
            };

            setInvoiceDetail(mockDetail);
        } catch (err) {
            console.error("加载发票详情失败:", err);
            setError(true);
        } finally {
            setLoading(false);
        }
    };

    // 下载发票
    const downloadInvoice = async () => {
        if (!invoiceDetail?.pdfUrl) {
            Taro.showToast({
                title: "暂无可下载的文件",
                icon: "none"
            });
            return;
        }

        try {
            Taro.showLoading({ title: "下载中..." });

            // 这里应该调用实际的下载API
            await new Promise(resolve => setTimeout(resolve, 2000));

            Taro.hideLoading();
            Taro.showToast({
                title: "下载成功",
                icon: "success"
            });
        } catch (error) {
            Taro.hideLoading();
            Taro.showToast({
                title: "下载失败",
                icon: "none"
            });
        }
    };

    // 复制文本
    const copyText = (text: string, label: string) => {
        Taro.setClipboardData({
            data: text,
            success: () => {
                Taro.showToast({
                    title: `${label}已复制`,
                    icon: "success"
                });
            }
        });
    };

    // 分享发票
    const shareInvoice = () => {
        Taro.showShareMenu({
            withShareTicket: true
        });
    };

    // 重试加载
    const retryLoad = () => {
        loadInvoiceDetail();
    };

    if (loading) {
        return (
            <View className="invoice-detail">
                <View className="loading-state">
                    <Icon name="loading" className="loading-icon" />
                    <View className="loading-text">加载中...</View>
                </View>
            </View>
        );
    }

    if (error || !invoiceDetail) {
        return (
            <View className="invoice-detail">
                <View className="error-state">
                    <Icon name="warning-o" className="error-icon" />
                    <View className="error-text">加载失败，请重试</View>
                    <Button className="retry-btn" onClick={retryLoad}>
                        重新加载
                    </Button>
                </View>
            </View>
        );
    }

    return (
        <View className="invoice-detail">
            {/* 发票预览 */}
            <View className="invoice-preview">
                <View className="preview-header">
                    <Button className="download-btn" onClick={downloadInvoice}>
                        <Icon name="down" className="download-icon" />
                        下载
                    </Button>
                    <View className="page-indicator">1/1</View>
                </View>

                <View className="invoice-image">
                    <Image
                        src={invoiceDetail.imageUrl}
                        className="invoice-img"
                        mode="widthFix"
                        onClick={() => setShowFullImage(true)}
                    />
                    <View className="zoom-controls">
                        <Button className="zoom-btn">
                            <Icon name="plus" />
                        </Button>
                        <Button className="zoom-btn">
                            <Icon name="minus" />
                        </Button>
                    </View>
                </View>
            </View>

            {/* 发票信息 */}
            <View className="invoice-info">
                <View className="info-title">发票详情</View>

                <View className="info-item">
                    <View className="info-label">发票号码</View>
                    <View className="info-value">{invoiceDetail.invoiceNumber}</View>
                    <Icon
                        name="copy"
                        className="copy-btn"
                        onClick={() => copyText(invoiceDetail.invoiceNumber, "发票号码")}
                    />
                </View>

                <View className="info-item">
                    <View className="info-label">发票代码</View>
                    <View className="info-value">{invoiceDetail.invoiceCode}</View>
                    <Icon
                        name="copy"
                        className="copy-btn"
                        onClick={() => copyText(invoiceDetail.invoiceCode, "发票代码")}
                    />
                </View>

                <View className="info-item">
                    <View className="info-label">开票日期</View>
                    <View className="info-value">{invoiceDetail.issueDate}</View>
                </View>

                <View className="info-item">
                    <View className="info-label">发票类型</View>
                    <View className="info-value">{invoiceDetail.invoiceType}</View>
                </View>

                <View className="info-item">
                    <View className="info-label">购买方</View>
                    <View className="info-value">{invoiceDetail.buyerName}</View>
                </View>

                {invoiceDetail.buyerTaxNumber && (
                    <View className="info-item">
                        <View className="info-label">购买方税号</View>
                        <View className="info-value">{invoiceDetail.buyerTaxNumber}</View>
                    </View>
                )}

                <View className="info-item">
                    <View className="info-label">销售方</View>
                    <View className="info-value">{invoiceDetail.sellerName}</View>
                </View>

                <View className="info-item">
                    <View className="info-label">销售方税号</View>
                    <View className="info-value">{invoiceDetail.sellerTaxNumber}</View>
                </View>

                <View className="info-item">
                    <View className="info-label">金额</View>
                    <View className="info-value">¥{invoiceDetail.amount}</View>
                </View>

                <View className="info-item">
                    <View className="info-label">税额</View>
                    <View className="info-value">¥{invoiceDetail.taxAmount}</View>
                </View>

                <View className="info-item">
                    <View className="info-label">价税合计</View>
                    <View className="info-value highlight">¥{invoiceDetail.totalAmount}</View>
                </View>
            </View>

            {/* 操作链接 */}
            <View className="action-links">
                <View className="action-link" onClick={shareInvoice}>
                    分享
                </View>
                <View className="action-link primary" onClick={downloadInvoice}>
                    下载发票
                </View>
            </View>

            {/* 全屏图片预览 */}
            {showFullImage && (
                <View className="image-preview-modal" onClick={() => setShowFullImage(false)}>
                    <Image
                        src={invoiceDetail.imageUrl}
                        className="preview-image"
                        mode="aspectFit"
                    />
                    <Button className="close-btn" onClick={() => setShowFullImage(false)}>
                        <Icon name="cross" />
                    </Button>
                </View>
            )}
        </View>
    );
};

export default InvoiceDetail;
