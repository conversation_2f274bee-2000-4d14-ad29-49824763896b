.invoice-detail {
    background-color: #f1f2f3;
    min-height: 100vh;

    .invoice-preview {
        background: #fff;
        margin: 20px;
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

        .preview-header {
            padding: 20px;
            border-bottom: 1px solid #f0f0f0;
            display: flex;
            align-items: center;
            justify-content: space-between;

            .download-btn {
                background: #fff;
                border: 1px solid #ec3a4a;
                color: #ec3a4a;
                border-radius: 20px;
                padding: 8px 20px;
                font-size: 24px;
                display: flex;
                align-items: center;

                .download-icon {
                    margin-right: 8px;
                    font-size: 20px;
                }
            }

            .page-indicator {
                background: #f5f5f5;
                border-radius: 20px;
                padding: 8px 16px;
                font-size: 24px;
                color: #666;
            }
        }

        .invoice-image {
            width: 100%;
            position: relative;
            background: #fff;

            .invoice-img {
                width: 100%;
                height: auto;
                display: block;
            }

            .zoom-controls {
                position: absolute;
                bottom: 20px;
                right: 20px;
                display: flex;
                flex-direction: column;
                gap: 10px;

                .zoom-btn {
                    width: 60px;
                    height: 60px;
                    border-radius: 50%;
                    background: rgba(0, 0, 0, 0.6);
                    color: #fff;
                    border: none;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-size: 24px;
                }
            }
        }
    }

    .invoice-info {
        background: #fff;
        margin: 20px;
        border-radius: 12px;
        padding: 20px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

        .info-title {
            font-size: 32px;
            color: #333;
            font-weight: 600;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid #f0f0f0;
        }

        .info-item {
            display: flex;
            align-items: center;
            padding: 15px 0;
            border-bottom: 1px solid #f8f9fa;

            &:last-child {
                border-bottom: none;
            }

            .info-label {
                width: 160px;
                font-size: 28px;
                color: #666;
                flex-shrink: 0;
            }

            .info-value {
                flex: 1;
                font-size: 28px;
                color: #333;
                word-break: break-all;

                &.highlight {
                    color: #ec3a4a;
                    font-weight: 600;
                }
            }

            .copy-btn {
                margin-left: 10px;
                color: #ec3a4a;
                font-size: 24px;
                padding: 5px;
            }
        }
    }

    .action-buttons {
        padding: 20px;
        display: flex;
        gap: 20px;

        .action-btn {
            flex: 1;
            height: 88px;
            border-radius: 50px;
            font-size: 32px;
            font-weight: 600;
            border: none;

            &.primary {
                background: #ec3a4a;
                color: #fff;
            }

            &.secondary {
                background: #fff;
                color: #ec3a4a;
                border: 2px solid #ec3a4a;
            }
        }
    }

    .loading-state {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 400px;

        .loading-icon {
            font-size: 60px;
            color: #ccc;
            margin-bottom: 20px;
            animation: spin 1s linear infinite;
        }

        .loading-text {
            font-size: 28px;
            color: #999;
        }
    }

    .error-state {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 400px;

        .error-icon {
            font-size: 80px;
            color: #ff4d4f;
            margin-bottom: 20px;
        }

        .error-text {
            font-size: 28px;
            color: #999;
            margin-bottom: 30px;
        }

        .retry-btn {
            background: #ec3a4a;
            color: #fff;
            border: none;
            border-radius: 20px;
            padding: 15px 30px;
            font-size: 28px;
        }
    }
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 图片预览全屏模式 */
.image-preview-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: #000;
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: center;

    .preview-image {
        max-width: 100%;
        max-height: 100%;
        object-fit: contain;
    }

    .close-btn {
        position: absolute;
        top: 40px;
        right: 40px;
        color: #fff;
        font-size: 40px;
        background: rgba(0, 0, 0, 0.5);
        border-radius: 50%;
        width: 60px;
        height: 60px;
        display: flex;
        align-items: center;
        justify-content: center;
        border: none;
    }
}
