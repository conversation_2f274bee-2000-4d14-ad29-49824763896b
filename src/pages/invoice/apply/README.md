# 申请开票页面

## 功能概述

这是一个完整的申请开票页面，支持个人和企业两种发票类型的申请。页面设计参考了用户提供的 UI 设计图，实现了以下功能：

## 主要功能

### 1. 订单信息展示

-   显示订单编号
-   显示开票金额
-   支持通过路由参数传递订单信息

### 2. 发票类型选择

-   **个人或事业单位**：适用于个人用户
-   **企业**：适用于企业用户
-   支持点击切换发票类型

### 3. 发票抬头信息填写

#### 个人发票信息

-   姓名（必填）
-   手机号（必填，支持格式验证）
-   邮箱（必填，支持格式验证）

#### 企业发票信息

-   企业名称（必填）
-   纳税人识别号（必填，支持格式验证）
-   企业地址（选填）
-   企业电话（选填）
-   开户银行（选填）
-   银行账号（选填）

### 4. 发票内容选择

-   明细
-   商品类别

### 5. 表单验证

-   必填字段验证
-   手机号格式验证
-   邮箱格式验证
-   纳税人识别号格式验证

## 页面路由

```
/pages/invoice/apply/index
```

## 路由参数

| 参数名      | 类型   | 必填 | 默认值               | 说明     |
| ----------- | ------ | ---- | -------------------- | -------- |
| orderNumber | string | 否   | "428543596954032573" | 订单编号 |
| amount      | string | 否   | "199.40"             | 开票金额 |

## 使用方式

### 1. 从其他页面跳转

```typescript
import { DDYNavigateTo } from "@/utils/route";

// 基本跳转
DDYNavigateTo({
    url: "/pages/invoice/apply/index",
});

// 带参数跳转
DDYNavigateTo({
    url: "/pages/invoice/apply/index?orderNumber=123456789&amount=299.00",
});
```

### 2. 在菜单中添加入口

页面已经在 `src/pages/info/menu-list/index.tsx` 中添加了菜单入口，用户可以通过"我的"页面访问。

## 文件结构

```
src/pages/invoice/apply/
├── index.tsx          # 主页面组件
├── index.less         # 页面样式
├── index.config.ts    # 页面配置
└── README.md          # 说明文档
```

## 样式特点

-   采用卡片式设计，与项目整体风格保持一致
-   使用步骤式布局，清晰展示申请流程
-   支持响应式设计，适配不同屏幕尺寸
-   使用项目主题色（#ec3a4a）作为强调色

## 技术实现

-   基于 Taro 3.6.15 框架
-   使用 React + TypeScript
-   使用 Vant UI 组件库
-   支持微信小程序和支付宝小程序

## 后续扩展

1. **API 集成**：将模拟的提交逻辑替换为真实的 API 调用
2. **发票历史**：添加查看历史开票记录的功能
3. **发票状态**：添加开票状态跟踪功能
4. **文件上传**：支持上传相关证明文件
5. **模板保存**：支持保存常用的开票信息模板

## 注意事项

1. 页面已添加完整的表单验证，确保数据准确性
2. 支持加载状态提示，提升用户体验
3. 错误处理完善，包含网络异常等情况
4. 代码结构清晰，便于维护和扩展
