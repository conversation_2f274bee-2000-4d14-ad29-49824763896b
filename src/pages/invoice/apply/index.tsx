import React, { useState, useEffect } from "react";
import { View, Input, Button } from "@tarojs/components";
import { Icon } from "@antmjs/vantui";
import Taro, { useLoad, useRouter } from "@tarojs/taro";
import "./index.less";

// 发票类型枚举
enum InvoiceType {
    PERSONAL = "personal",
    COMPANY = "company",
}

// 发票内容枚举
enum InvoiceContent {
    DETAIL = "detail",
    CATEGORY = "category",
}

// 路由参数类型
type RouterParams = {
    orderNumber?: string;
    amount?: string;
};

// 表单数据类型
interface FormData {
    invoiceType: InvoiceType;
    invoiceContent: InvoiceContent;
    // 个人发票信息
    personalName?: string;
    personalPhone?: string;
    personalEmail?: string;
    // 企业发票信息
    companyName?: string;
    taxNumber?: string;
    companyAddress?: string;
    companyPhone?: string;
    bankName?: string;
    bankAccount?: string;
}

const InvoiceApply: React.FC = () => {
    const { orderNumber = "428543596954032573", amount = "199.40" } = useRouter<RouterParams>().params;

    const [formData, setFormData] = useState<FormData>({
        invoiceType: InvoiceType.PERSONAL,
        invoiceContent: InvoiceContent.DETAIL,
    });

    useLoad(() => {
        Taro.setNavigationBarTitle({ title: "申请开票" });
    });

    // 更新表单数据
    const updateFormData = (key: keyof FormData, value: any) => {
        setFormData(prev => ({
            ...prev,
            [key]: value,
        }));
    };

    // 选择发票类型
    const selectInvoiceType = (type: InvoiceType) => {
        updateFormData("invoiceType", type);
    };

    // 选择发票内容
    const selectInvoiceContent = (content: InvoiceContent) => {
        updateFormData("invoiceContent", content);
    };

    // 验证邮箱格式
    const validateEmail = (email: string) => {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    };

    // 验证手机号格式
    const validatePhone = (phone: string) => {
        const phoneRegex = /^1[3-9]\d{9}$/;
        return phoneRegex.test(phone);
    };

    // 验证纳税人识别号格式（简单验证）
    const validateTaxNumber = (taxNumber: string) => {
        // 纳税人识别号通常为15位、17位或18位
        return /^[0-9A-Z]{15}$|^[0-9A-Z]{17}$|^[0-9A-Z]{18}$/.test(taxNumber);
    };

    // 提交申请
    const handleSubmit = async () => {
        try {
            // 表单验证
            if (formData.invoiceType === InvoiceType.PERSONAL) {
                if (!formData.personalName?.trim()) {
                    Taro.showToast({
                        title: "请输入姓名",
                        icon: "none",
                    });
                    return;
                }
                if (!formData.personalPhone?.trim()) {
                    Taro.showToast({
                        title: "请输入手机号",
                        icon: "none",
                    });
                    return;
                }
                if (!validatePhone(formData.personalPhone.trim())) {
                    Taro.showToast({
                        title: "请输入正确的手机号",
                        icon: "none",
                    });
                    return;
                }
                if (!formData.personalEmail?.trim()) {
                    Taro.showToast({
                        title: "请输入邮箱地址",
                        icon: "none",
                    });
                    return;
                }
                if (!validateEmail(formData.personalEmail.trim())) {
                    Taro.showToast({
                        title: "请输入正确的邮箱地址",
                        icon: "none",
                    });
                    return;
                }
            } else {
                if (!formData.companyName?.trim()) {
                    Taro.showToast({
                        title: "请输入企业名称",
                        icon: "none",
                    });
                    return;
                }
                if (!formData.taxNumber?.trim()) {
                    Taro.showToast({
                        title: "请输入纳税人识别号",
                        icon: "none",
                    });
                    return;
                }
                if (!validateTaxNumber(formData.taxNumber.trim())) {
                    Taro.showToast({
                        title: "请输入正确的纳税人识别号",
                        icon: "none",
                    });
                    return;
                }
            }

            // 显示加载提示
            Taro.showLoading({
                title: "提交中...",
            });

            // 这里调用API提交申请
            console.log("提交申请:", {
                ...formData,
                orderNumber,
                amount,
            });

            // 模拟API调用延迟
            await new Promise(resolve => setTimeout(resolve, 1000));

            Taro.hideLoading();

            Taro.showToast({
                title: "申请提交成功",
                icon: "success",
            });

            // 延迟返回上一页
            setTimeout(() => {
                Taro.navigateBack();
            }, 1500);
        } catch (error) {
            Taro.hideLoading();
            console.error("提交失败:", error);
            Taro.showToast({
                title: "提交失败，请重试",
                icon: "none",
            });
        }
    };

    return (
        <View className="invoice-apply">
            {/* 订单信息 */}
            <View className="order-info">
                <View className="order-number">
                    <View className="label">订单编号</View>
                    <View className="value">{orderNumber}</View>
                </View>
                <View className="order-amount">
                    <View className="label">开票金额</View>
                    <View className="amount">¥{amount}</View>
                    <Icon name="info-o" className="info-icon" />
                </View>
            </View>

            {/* 发票申请表单 */}
            <View className="invoice-form">
                {/* 第一步：选择发票类型 */}
                <View className="form-section">
                    <View className="section-title">
                        <View className="step-number">1</View>
                        发票类型
                    </View>
                    <View className="invoice-type">
                        <View
                            className={`type-option ${formData.invoiceType === InvoiceType.PERSONAL ? "active" : ""}`}
                            onClick={() => selectInvoiceType(InvoiceType.PERSONAL)}
                        >
                            <View className="type-text">个人或事业单位</View>
                            {formData.invoiceType === InvoiceType.PERSONAL && (
                                <Icon name="success" className="check-icon" />
                            )}
                        </View>
                        <View
                            className={`type-option ${formData.invoiceType === InvoiceType.COMPANY ? "active" : ""}`}
                            onClick={() => selectInvoiceType(InvoiceType.COMPANY)}
                        >
                            <View className="type-text">企业</View>
                            {formData.invoiceType === InvoiceType.COMPANY && (
                                <Icon name="success" className="check-icon" />
                            )}
                        </View>
                    </View>
                </View>

                {/* 第二步：填写发票抬头 */}
                <View className="form-section">
                    <View className="section-title">
                        <View className="step-number">2</View>
                        发票抬头
                    </View>

                    {formData.invoiceType === InvoiceType.PERSONAL ? (
                        // 个人发票表单
                        <>
                            <View className="form-item">
                                <View className="form-label">
                                    <View className="required">*</View>
                                    姓名
                                </View>
                                <Input
                                    className="form-input"
                                    placeholder="请输入姓名"
                                    value={formData.personalName || ""}
                                    onInput={e => updateFormData("personalName", e.detail.value)}
                                />
                            </View>
                            <View className="form-item">
                                <View className="form-label">
                                    <View className="required">*</View>
                                    手机号
                                </View>
                                <Input
                                    className="form-input"
                                    placeholder="请输入手机号"
                                    value={formData.personalPhone || ""}
                                    onInput={e => updateFormData("personalPhone", e.detail.value)}
                                />
                            </View>
                            <View className="form-item">
                                <View className="form-label">
                                    <View className="required">*</View>
                                    邮箱
                                </View>
                                <Input
                                    className="form-input"
                                    placeholder="请输入邮箱地址"
                                    value={formData.personalEmail || ""}
                                    onInput={e => updateFormData("personalEmail", e.detail.value)}
                                />
                            </View>
                        </>
                    ) : (
                        // 企业发票表单
                        <>
                            <View className="form-item">
                                <View className="form-label">
                                    <View className="required">*</View>
                                    企业名称
                                </View>
                                <Input
                                    className="form-input"
                                    placeholder="请输入企业名称"
                                    value={formData.companyName || ""}
                                    onInput={e => updateFormData("companyName", e.detail.value)}
                                />
                            </View>
                            <View className="form-item">
                                <View className="form-label">
                                    <View className="required">*</View>
                                    纳税人识别号
                                </View>
                                <Input
                                    className="form-input"
                                    placeholder="请输入纳税人识别号"
                                    value={formData.taxNumber || ""}
                                    onInput={e => updateFormData("taxNumber", e.detail.value)}
                                />
                            </View>
                            <View className="form-item">
                                <View className="form-label">企业地址</View>
                                <Input
                                    className="form-input"
                                    placeholder="请输入企业地址"
                                    value={formData.companyAddress || ""}
                                    onInput={e => updateFormData("companyAddress", e.detail.value)}
                                />
                            </View>
                            <View className="form-item">
                                <View className="form-label">企业电话</View>
                                <Input
                                    className="form-input"
                                    placeholder="请输入企业电话"
                                    value={formData.companyPhone || ""}
                                    onInput={e => updateFormData("companyPhone", e.detail.value)}
                                />
                            </View>
                            <View className="form-item">
                                <View className="form-label">开户银行</View>
                                <Input
                                    className="form-input"
                                    placeholder="请输入开户银行"
                                    value={formData.bankName || ""}
                                    onInput={e => updateFormData("bankName", e.detail.value)}
                                />
                            </View>
                            <View className="form-item">
                                <View className="form-label">银行账号</View>
                                <Input
                                    className="form-input"
                                    placeholder="请输入银行账号"
                                    value={formData.bankAccount || ""}
                                    onInput={e => updateFormData("bankAccount", e.detail.value)}
                                />
                            </View>
                        </>
                    )}
                </View>

                {/* 第三步：选择发票内容 */}
                <View className="form-section">
                    <View className="section-title">
                        <View className="step-number">3</View>
                        发票内容
                    </View>
                    <View className="invoice-content">
                        <View className="content-option" onClick={() => selectInvoiceContent(InvoiceContent.DETAIL)}>
                            <Icon
                                name={formData.invoiceContent === InvoiceContent.DETAIL ? "checked" : "circle"}
                                className="radio-icon"
                            />
                            <View className="content-text">明细</View>
                        </View>
                        <View className="content-option" onClick={() => selectInvoiceContent(InvoiceContent.CATEGORY)}>
                            <Icon
                                name={formData.invoiceContent === InvoiceContent.CATEGORY ? "checked" : "circle"}
                                className="radio-icon"
                            />
                            <View className="content-text">商品类别</View>
                        </View>
                    </View>
                </View>
            </View>

            {/* 提交按钮 */}
            <Button className="submit-btn" onClick={handleSubmit}>
                提交申请
            </Button>
        </View>
    );
};

export default InvoiceApply;
