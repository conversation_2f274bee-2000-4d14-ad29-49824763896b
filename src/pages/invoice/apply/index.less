.invoice-apply {
    background-color: #f1f2f3;
    min-height: 100vh;
    padding: 20px;

    .order-info {
        background: #fff;
        border-radius: 12px;
        padding: 20px;
        margin-bottom: 20px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

        .order-number {
            display: flex;
            align-items: center;
            margin-bottom: 10px;

            .label {
                color: #333;
                font-size: 28px;
                font-weight: 500;
                margin-right: 20px;
            }

            .value {
                color: #666;
                font-size: 28px;
            }
        }

        .order-amount {
            display: flex;
            align-items: center;

            .label {
                color: #333;
                font-size: 28px;
                font-weight: 500;
                margin-right: 20px;
            }

            .amount {
                color: #ec3a4a;
                font-size: 32px;
                font-weight: 600;
            }

            .info-icon {
                margin-left: 10px;
                color: #999;
                font-size: 24px;
            }
        }
    }

    .invoice-form {
        background: #fff;
        border-radius: 12px;
        padding: 20px;
        margin-bottom: 20px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

        .form-section {
            margin-bottom: 30px;

            &:last-child {
                margin-bottom: 0;
            }

            .section-title {
                color: #333;
                font-size: 28px;
                font-weight: 500;
                margin-bottom: 20px;
                display: flex;
                align-items: center;

                .step-number {
                    background: #ec3a4a;
                    color: #fff;
                    width: 36px;
                    height: 36px;
                    border-radius: 50%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-size: 24px;
                    margin-right: 15px;
                }
            }

            .invoice-type {
                display: flex;
                gap: 20px;
                margin-bottom: 20px;

                .type-option {
                    flex: 1;
                    padding: 20px;
                    border: 2px solid #e5e5e5;
                    border-radius: 12px;
                    text-align: center;
                    background: #fff;
                    transition: all 0.3s ease;

                    &.active {
                        border-color: #ec3a4a;
                        background: #fff5f5;
                    }

                    .type-text {
                        color: #333;
                        font-size: 28px;
                        font-weight: 500;
                    }

                    .check-icon {
                        margin-top: 10px;
                        color: #ec3a4a;
                        font-size: 32px;
                    }
                }
            }

            .form-item {
                margin-bottom: 30px;

                &:last-child {
                    margin-bottom: 0;
                }

                .form-label {
                    color: #333;
                    font-size: 28px;
                    margin-bottom: 15px;
                    display: flex;
                    align-items: center;

                    .required {
                        color: #ec3a4a;
                        margin-right: 5px;
                    }
                }

                .form-input {
                    background: #f8f9fa;
                    border: 2px solid #e5e5e5;
                    border-radius: 12px;
                    padding: 24px 20px;
                    font-size: 28px;
                    color: #333;
                    width: 100%;
                    min-height: 88px;
                    line-height: 40px;
                    box-sizing: border-box;
                    display: flex;
                    align-items: center;

                    &:focus {
                        border-color: #ec3a4a;
                        background: #fff;
                    }

                    &::placeholder {
                        color: #999;
                    }
                }

                .form-select {
                    background: #f8f9fa;
                    border: 2px solid #e5e5e5;
                    border-radius: 12px;
                    padding: 24px 20px;
                    font-size: 28px;
                    color: #333;
                    min-height: 88px;
                    line-height: 40px;
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    box-sizing: border-box;

                    &:focus {
                        border-color: #ec3a4a;
                        background: #fff;
                    }

                    .placeholder {
                        color: #999;
                    }

                    .arrow-icon {
                        color: #999;
                        font-size: 24px;
                    }
                }
            }

            .invoice-content {
                .content-option {
                    display: flex;
                    align-items: center;
                    padding: 20px 0;
                    border-bottom: 1px solid #f0f0f0;

                    &:last-child {
                        border-bottom: none;
                    }

                    .radio-icon {
                        margin-right: 15px;
                        color: #ec3a4a;
                        font-size: 32px;
                    }

                    .content-text {
                        color: #333;
                        font-size: 28px;
                    }
                }
            }
        }
    }

    .submit-btn {
        background: #ec3a4a;
        color: #fff;
        border-radius: 50px;
        height: 88px;
        font-size: 32px;
        font-weight: 600;
        margin: 40px 0;
        border: none;
        width: 100%;

        &:disabled {
            background: #ccc;
        }
    }
}
