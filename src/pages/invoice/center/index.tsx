import React, { useState } from "react";
import { View, Input } from "@tarojs/components";
import { Icon } from "@antmjs/vantui";
import Taro, { useLoad } from "@tarojs/taro";
import { DDYNavigateTo } from "@/utils/route";
import "./index.less";

// 发票状态枚举
enum InvoiceStatus {
    COMPLETED = "completed",
    PROCESSING = "processing",
    FAILED = "failed"
}

// 发票类型枚举
enum InvoiceType {
    PERSONAL = "personal",
    COMPANY = "company"
}

// 发票数据类型
interface InvoiceItem {
    id: string;
    storeName: string;
    productName: string;
    amount: string;
    invoiceType: InvoiceType;
    status: InvoiceStatus;
    date: string;
    invoiceNumber?: string;
}

// Tab类型
enum TabType {
    COMPLETED = "completed",
    PROCESSING = "processing"
}

const InvoiceCenter: React.FC = () => {
    const [activeTab, setActiveTab] = useState<TabType>(TabType.COMPLETED);
    const [showSearch, setShowSearch] = useState(false);
    const [searchKeyword, setSearchKeyword] = useState("");
    const [invoiceList, setInvoiceList] = useState<InvoiceItem[]>([]);

    useLoad(() => {
        Taro.setNavigationBarTitle({ title: "发票中心" });
        loadInvoiceData();
    });

    // 模拟加载发票数据
    const loadInvoiceData = () => {
        const mockData: InvoiceItem[] = [
            {
                id: "1",
                storeName: "",
                productName: "【天天低价】顺手【官方未拆封】...",
                amount: "¥12.8",
                invoiceType: InvoiceType.PERSONAL,
                status: InvoiceStatus.COMPLETED,
                date: "2024.3.8",
                invoiceNumber: "12345678901234567890"
            },
            {
                id: "2",
                storeName: "",
                productName: "个人抬头",
                amount: "",
                invoiceType: InvoiceType.PERSONAL,
                status: InvoiceStatus.PROCESSING,
                date: ""
            },
            {
                id: "3",
                storeName: "",
                productName: "发票类型",
                amount: "",
                invoiceType: InvoiceType.PERSONAL,
                status: InvoiceStatus.PROCESSING,
                date: ""
            },
            {
                id: "4",
                storeName: "",
                productName: "开票日期",
                amount: "",
                invoiceType: InvoiceType.PERSONAL,
                status: InvoiceStatus.PROCESSING,
                date: ""
            }
        ];
        setInvoiceList(mockData);
    };

    // 获取过滤后的发票列表
    const getFilteredInvoices = () => {
        return invoiceList.filter(item => {
            const matchesTab = activeTab === TabType.COMPLETED
                ? item.status === InvoiceStatus.COMPLETED
                : item.status !== InvoiceStatus.COMPLETED;

            const matchesSearch = searchKeyword === "" ||
                item.storeName.includes(searchKeyword) ||
                item.productName.includes(searchKeyword);

            return matchesTab && matchesSearch;
        });
    };

    // 跳转到发票详情
    const goToInvoiceDetail = (invoice: InvoiceItem) => {
        DDYNavigateTo({
            url: `/pages/invoice/detail/index?id=${invoice.id}`
        });
    };

    const filteredInvoices = getFilteredInvoices();
    const completedCount = invoiceList.filter(item => item.status === InvoiceStatus.COMPLETED).length;
    const processingCount = invoiceList.filter(item => item.status !== InvoiceStatus.COMPLETED).length;

    return (
        <View className="invoice-center">
            {/* 头部搜索 */}
            <View className="header">
                <View></View>
                <Icon
                    name="search"
                    className="search-icon"
                    onClick={() => setShowSearch(true)}
                />
            </View>

            {/* Tab切换 */}
            <View className="tabs">
                <View
                    className={`tab-item ${activeTab === TabType.COMPLETED ? 'active' : ''}`}
                    onClick={() => setActiveTab(TabType.COMPLETED)}
                >
                    已开票
                    <View className="count">({completedCount})</View>
                </View>
                <View
                    className={`tab-item ${activeTab === TabType.PROCESSING ? 'active' : ''}`}
                    onClick={() => setActiveTab(TabType.PROCESSING)}
                >
                    申请中
                    <View className="count">({processingCount})</View>
                </View>
            </View>

            {/* 发票列表 */}
            <View className="invoice-list">
                {activeTab === TabType.COMPLETED ? (
                    <View className="invoice-item completed">
                        <View className="item-left">
                            <View className="invoice-icon">1</View>
                        </View>
                        <View className="item-content">
                            <View className="invoice-title">【天天低价】顺手【官方未拆封】...</View>
                            <View className="invoice-amount">实付：¥12.8</View>
                        </View>
                        <View className="item-right">
                            <View
                                className="action-link"
                                onClick={() => goToInvoiceDetail(filteredInvoices[0])}
                            >
                                发票详情
                            </View>
                        </View>
                    </View>
                ) : (
                    <>
                        <View className="invoice-item processing">
                            <View className="item-left">
                                <View className="invoice-icon">1</View>
                            </View>
                            <View className="item-content">
                                <View className="invoice-title">个人抬头</View>
                            </View>
                        </View>

                        <View className="invoice-item processing">
                            <View className="item-left">
                                <View className="invoice-icon">2</View>
                            </View>
                            <View className="item-content">
                                <View className="invoice-title">发票类型</View>
                                <View className="invoice-subtitle">普通发票-电子</View>
                                <View className="invoice-date">开票日期 2024.3.8</View>
                            </View>
                        </View>

                        <View className="invoice-item processing">
                            <View className="item-left">
                                <View className="invoice-icon">3</View>
                            </View>
                            <View className="item-content">
                                <View className="invoice-title">开票日期</View>
                            </View>
                        </View>
                    </>
                )}
            </View>

            {/* 搜索弹窗 */}
            {showSearch && (
                <View className="search-modal" onClick={() => setShowSearch(false)}>
                    <View className="search-content" onClick={(e) => e.stopPropagation()}>
                        <Input
                            className="search-input"
                            placeholder="搜索店铺或商品名称"
                            value={searchKeyword}
                            onInput={(e) => setSearchKeyword(e.detail.value)}
                            focus
                        />
                        <View className="search-history">
                            <View className="history-title">搜索历史</View>
                            <View className="history-item">苏宁易购</View>
                            <View className="history-item">京东自营</View>
                        </View>
                    </View>
                </View>
            )}
        </View>
    );
};

export default InvoiceCenter;