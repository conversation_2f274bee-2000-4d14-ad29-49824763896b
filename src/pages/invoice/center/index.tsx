import React, { useState } from "react";
import { View, Input, Button } from "@tarojs/components";
import { Icon } from "@antmjs/vantui";
import Taro, { useLoad } from "@tarojs/taro";
import { DDYNavigateTo } from "@/utils/route";
import "./index.less";

// 发票状态枚举
enum InvoiceStatus {
    COMPLETED = "completed",
    PROCESSING = "processing",
    FAILED = "failed"
}

// 发票类型枚举
enum InvoiceType {
    PERSONAL = "personal",
    COMPANY = "company"
}

// 发票数据类型
interface InvoiceItem {
    id: string;
    storeName: string;
    productName: string;
    amount: string;
    invoiceType: InvoiceType;
    status: InvoiceStatus;
    date: string;
    invoiceNumber?: string;
}

// Tab类型
enum TabType {
    COMPLETED = "completed",
    PROCESSING = "processing"
}

const InvoiceCenter: React.FC = () => {
    const [activeTab, setActiveTab] = useState<TabType>(TabType.COMPLETED);
    const [showSearch, setShowSearch] = useState(false);
    const [searchKeyword, setSearchKeyword] = useState("");
    const [invoiceList, setInvoiceList] = useState<InvoiceItem[]>([]);

    useLoad(() => {
        Taro.setNavigationBarTitle({ title: "发票中心" });
        loadInvoiceData();
    });

    // 模拟加载发票数据
    const loadInvoiceData = () => {
        const mockData: InvoiceItem[] = [
            {
                id: "1",
                storeName: "苏宁易购官方旗舰店",
                productName: "【天天低价】顺手【官方未拆封】...",
                amount: "¥12.80",
                invoiceType: InvoiceType.PERSONAL,
                status: InvoiceStatus.COMPLETED,
                date: "2024.3.8",
                invoiceNumber: "12345678901234567890"
            },
            {
                id: "2",
                storeName: "京东自营",
                productName: "小米手机13 Pro",
                amount: "¥3999.00",
                invoiceType: InvoiceType.COMPANY,
                status: InvoiceStatus.PROCESSING,
                date: "2024.3.10"
            },
            {
                id: "3",
                storeName: "天猫超市",
                productName: "农夫山泉饮用水",
                amount: "¥25.60",
                invoiceType: InvoiceType.PERSONAL,
                status: InvoiceStatus.FAILED,
                date: "2024.3.12"
            }
        ];
        setInvoiceList(mockData);
    };

    // 获取过滤后的发票列表
    const getFilteredInvoices = () => {
        return invoiceList.filter(item => {
            const matchesTab = activeTab === TabType.COMPLETED
                ? item.status === InvoiceStatus.COMPLETED
                : item.status !== InvoiceStatus.COMPLETED;

            const matchesSearch = searchKeyword === "" ||
                item.storeName.includes(searchKeyword) ||
                item.productName.includes(searchKeyword);

            return matchesTab && matchesSearch;
        });
    };

    // 获取状态显示文本
    const getStatusText = (status: InvoiceStatus) => {
        switch (status) {
            case InvoiceStatus.COMPLETED:
                return "已开票";
            case InvoiceStatus.PROCESSING:
                return "申请中";
            case InvoiceStatus.FAILED:
                return "开票失败";
            default:
                return "";
        }
    };

    // 获取发票类型显示文本
    const getInvoiceTypeText = (type: InvoiceType) => {
        return type === InvoiceType.PERSONAL ? "个人" : "企业";
    };

    // 跳转到发票详情
    const goToInvoiceDetail = (invoice: InvoiceItem) => {
        DDYNavigateTo({
            url: `/pages/invoice/detail/index?id=${invoice.id}`
        });
    };

    // 跳转到申请开票
    const goToApplyInvoice = () => {
        DDYNavigateTo({
            url: "/pages/invoice/apply/index"
        });
    };

    // 重新申请开票
    const reapplyInvoice = (invoice: InvoiceItem) => {
        DDYNavigateTo({
            url: `/pages/invoice/apply/index?reapply=true&id=${invoice.id}`
        });
    };

    const filteredInvoices = getFilteredInvoices();
    const completedCount = invoiceList.filter(item => item.status === InvoiceStatus.COMPLETED).length;
    const processingCount = invoiceList.filter(item => item.status !== InvoiceStatus.COMPLETED).length;

    return (
        <View className="invoice-center">
            {/* 头部搜索 */}
            <View className="header">
                <View></View>
                <Icon
                    name="search"
                    className="search-icon"
                    onClick={() => setShowSearch(true)}
                />
            </View>

            {/* Tab切换 */}
            <View className="tabs">
                <View
                    className={`tab-item ${activeTab === TabType.COMPLETED ? 'active' : ''}`}
                    onClick={() => setActiveTab(TabType.COMPLETED)}
                >
                    已开票
                    <View className="count">({completedCount})</View>
                </View>
                <View
                    className={`tab-item ${activeTab === TabType.PROCESSING ? 'active' : ''}`}
                    onClick={() => setActiveTab(TabType.PROCESSING)}
                >
                    申请中
                    <View className="count">({processingCount})</View>
                </View>
            </View>

            {/* 发票列表 */}
            <View className="invoice-list">
                {filteredInvoices.length > 0 ? (
                    filteredInvoices.map(invoice => (
                        <View key={invoice.id} className="invoice-item">
                            <View className="item-header">
                                <View className="store-icon">
                                    😊
                                </View>
                                <View className="store-info">
                                    <View className="store-name">{invoice.storeName}</View>
                                    <View className="product-name">{invoice.productName}</View>
                                </View>
                                <View className="amount">{invoice.amount}</View>
                            </View>

                            <View className="item-content">
                                <View className="invoice-info">
                                    <View className="invoice-type">
                                        <View className="type-badge">
                                            {getInvoiceTypeText(invoice.invoiceType)}
                                        </View>
                                        <View className="type-text">
                                            普通发票-电子
                                        </View>
                                    </View>
                                    <View className="invoice-date">
                                        开票日期 {invoice.date}
                                    </View>
                                </View>
                                <View className={`status ${invoice.status}`}>
                                    {getStatusText(invoice.status)}
                                </View>
                            </View>

                            <View className="item-footer">
                                <View className="invoice-number">
                                    {invoice.invoiceNumber || ""}
                                </View>
                                {invoice.status === InvoiceStatus.COMPLETED ? (
                                    <Button
                                        className="action-btn"
                                        onClick={() => goToInvoiceDetail(invoice)}
                                    >
                                        发票详情
                                    </Button>
                                ) : invoice.status === InvoiceStatus.FAILED ? (
                                    <Button
                                        className="action-btn outline"
                                        onClick={() => reapplyInvoice(invoice)}
                                    >
                                        重新申请
                                    </Button>
                                ) : null}
                            </View>
                        </View>
                    ))
                ) : (
                    <View className="empty-state">
                        <View className="empty-icon">📄</View>
                        <View className="empty-text">
                            {activeTab === TabType.COMPLETED ? "暂无已开票记录" : "暂无申请中的发票"}
                        </View>
                        <Button className="apply-btn" onClick={goToApplyInvoice}>
                            申请开票
                        </Button>
                    </View>
                )}
            </View>

            {/* 搜索弹窗 */}
            {showSearch && (
                <View className="search-modal" onClick={() => setShowSearch(false)}>
                    <View className="search-content" onClick={(e) => e.stopPropagation()}>
                        <Input
                            className="search-input"
                            placeholder="搜索店铺或商品名称"
                            value={searchKeyword}
                            onInput={(e) => setSearchKeyword(e.detail.value)}
                            focus
                        />
                        <View className="search-history">
                            <View className="history-title">搜索历史</View>
                            <View className="history-item">苏宁易购</View>
                            <View className="history-item">京东自营</View>
                        </View>
                    </View>
                </View>
            )}
        </View>
    );
};

export default InvoiceCenter;