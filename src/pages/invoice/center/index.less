.invoice-center {
    background-color: #f1f2f3;
    min-height: 100vh;

    .header {
        background: #fff;
        padding: 20px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        border-bottom: 1px solid #f0f0f0;

        .search-icon {
            font-size: 36px;
            color: #666;
        }
    }

    .tabs {
        background: #fff;
        display: flex;
        border-bottom: 1px solid #f0f0f0;

        .tab-item {
            flex: 1;
            padding: 30px 0;
            text-align: center;
            position: relative;
            font-size: 28px;
            color: #666;

            &.active {
                color: #333;
                font-weight: 500;

                &::after {
                    content: '';
                    position: absolute;
                    bottom: 0;
                    left: 50%;
                    transform: translateX(-50%);
                    width: 60px;
                    height: 4px;
                    background: #ec3a4a;
                    border-radius: 2px;
                }
            }

            .count {
                margin-left: 8px;
                color: #999;
            }
        }
    }

    .invoice-list {
        padding: 20px;

        .invoice-item {
            background: #fff;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

            .item-header {
                display: flex;
                align-items: center;
                margin-bottom: 20px;

                .store-icon {
                    width: 60px;
                    height: 60px;
                    border-radius: 50%;
                    background: #ffd700;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    margin-right: 15px;
                    font-size: 24px;
                }

                .store-info {
                    flex: 1;

                    .store-name {
                        font-size: 28px;
                        color: #333;
                        font-weight: 500;
                        margin-bottom: 5px;
                    }

                    .product-name {
                        font-size: 24px;
                        color: #666;
                    }
                }

                .amount {
                    font-size: 32px;
                    color: #ec3a4a;
                    font-weight: 600;
                }
            }

            .item-content {
                display: flex;
                align-items: center;
                justify-content: space-between;
                margin-bottom: 20px;

                .invoice-info {
                    .invoice-type {
                        display: flex;
                        align-items: center;
                        margin-bottom: 10px;

                        .type-badge {
                            background: #1989fa;
                            color: #fff;
                            padding: 4px 12px;
                            border-radius: 20px;
                            font-size: 20px;
                            margin-right: 15px;
                        }

                        .type-text {
                            font-size: 24px;
                            color: #333;
                        }
                    }

                    .invoice-date {
                        font-size: 24px;
                        color: #666;
                    }
                }

                .status {
                    padding: 8px 16px;
                    border-radius: 20px;
                    font-size: 24px;

                    &.completed {
                        background: #f0f9ff;
                        color: #1989fa;
                    }

                    &.processing {
                        background: #fff7e6;
                        color: #ff9500;
                    }

                    &.failed {
                        background: #fff2f0;
                        color: #ff4d4f;
                    }
                }
            }

            .item-footer {
                display: flex;
                align-items: center;
                justify-content: space-between;

                .invoice-number {
                    font-size: 24px;
                    color: #999;
                }

                .action-btn {
                    background: #ec3a4a;
                    color: #fff;
                    border: none;
                    border-radius: 20px;
                    padding: 8px 20px;
                    font-size: 24px;

                    &.outline {
                        background: transparent;
                        color: #ec3a4a;
                        border: 1px solid #ec3a4a;
                    }
                }
            }
        }
    }

    .empty-state {
        text-align: center;
        padding: 100px 20px;

        .empty-icon {
            font-size: 120px;
            color: #ddd;
            margin-bottom: 20px;
        }

        .empty-text {
            font-size: 28px;
            color: #999;
            margin-bottom: 40px;
        }

        .apply-btn {
            background: #ec3a4a;
            color: #fff;
            border: none;
            border-radius: 50px;
            padding: 20px 40px;
            font-size: 28px;
        }
    }

    .search-modal {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.5);
        z-index: 1000;
        display: flex;
        align-items: flex-start;
        padding-top: 100px;

        .search-content {
            background: #fff;
            margin: 0 20px;
            border-radius: 12px;
            padding: 20px;
            width: calc(100% - 40px);

            .search-input {
                border: 1px solid #e5e5e5;
                border-radius: 8px;
                padding: 15px;
                font-size: 28px;
                width: 100%;
                margin-bottom: 20px;
            }

            .search-history {
                .history-title {
                    font-size: 24px;
                    color: #666;
                    margin-bottom: 15px;
                }

                .history-item {
                    padding: 10px 0;
                    font-size: 26px;
                    color: #333;
                    border-bottom: 1px solid #f0f0f0;

                    &:last-child {
                        border-bottom: none;
                    }
                }
            }
        }
    }
}
