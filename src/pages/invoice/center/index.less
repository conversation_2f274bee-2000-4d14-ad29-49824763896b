.invoice-center {
    background-color: #f1f2f3;
    min-height: 100vh;

    .header {
        background: #fff;
        padding: 20px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        border-bottom: 1px solid #f0f0f0;

        .search-icon {
            font-size: 36px;
            color: #666;
        }
    }

    .tabs {
        background: #fff;
        display: flex;
        border-bottom: 1px solid #f0f0f0;

        .tab-item {
            flex: 1;
            padding: 30px 0;
            text-align: center;
            position: relative;
            font-size: 28px;
            color: #666;

            &.active {
                color: #333;
                font-weight: 500;

                &::after {
                    content: '';
                    position: absolute;
                    bottom: 0;
                    left: 50%;
                    transform: translateX(-50%);
                    width: 60px;
                    height: 4px;
                    background: #ec3a4a;
                    border-radius: 2px;
                }
            }

            .count {
                margin-left: 8px;
                color: #999;
            }
        }
    }

    .invoice-list {
        padding: 0;

        .invoice-item {
            background: #fff;
            padding: 30px 20px;
            border-bottom: 1px solid #f0f0f0;
            display: flex;
            align-items: center;

            .item-left {
                margin-right: 20px;

                .invoice-icon {
                    width: 60px;
                    height: 60px;
                    border-radius: 50%;
                    background: #1989fa;
                    color: #fff;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-size: 28px;
                    font-weight: 600;
                }
            }

            .item-content {
                flex: 1;

                .invoice-title {
                    font-size: 28px;
                    color: #333;
                    margin-bottom: 8px;
                    font-weight: 500;
                }

                .invoice-amount {
                    font-size: 24px;
                    color: #666;
                    margin-bottom: 8px;
                }

                .invoice-subtitle {
                    font-size: 24px;
                    color: #666;
                    margin-bottom: 8px;
                }

                .invoice-date {
                    font-size: 24px;
                    color: #666;
                }
            }

            .item-right {
                .action-link {
                    color: #1989fa;
                    font-size: 24px;
                    text-decoration: underline;
                    cursor: pointer;
                    padding: 8px 0;

                    &:active {
                        opacity: 0.7;
                    }
                }
            }

            &.completed {
                .item-left .invoice-icon {
                    background: #52c41a;
                }
            }

            &.processing {
                .item-left .invoice-icon {
                    background: #1989fa;
                }
            }
        }
    }

    .empty-state {
        text-align: center;
        padding: 100px 20px;

        .empty-icon {
            font-size: 120px;
            color: #ddd;
            margin-bottom: 20px;
        }

        .empty-text {
            font-size: 28px;
            color: #999;
            margin-bottom: 40px;
        }

        .apply-btn {
            background: #ec3a4a;
            color: #fff;
            border: none;
            border-radius: 50px;
            padding: 20px 40px;
            font-size: 28px;
        }
    }

    .search-modal {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.5);
        z-index: 1000;
        display: flex;
        align-items: flex-start;
        padding-top: 100px;

        .search-content {
            background: #fff;
            margin: 0 20px;
            border-radius: 12px;
            padding: 20px;
            width: calc(100% - 40px);

            .search-input {
                border: 1px solid #e5e5e5;
                border-radius: 8px;
                padding: 15px;
                font-size: 28px;
                width: 100%;
                margin-bottom: 20px;
            }

            .search-history {
                .history-title {
                    font-size: 24px;
                    color: #666;
                    margin-bottom: 15px;
                }

                .history-item {
                    padding: 10px 0;
                    font-size: 26px;
                    color: #333;
                    border-bottom: 1px solid #f0f0f0;

                    &:last-child {
                        border-bottom: none;
                    }
                }
            }
        }
    }
}
