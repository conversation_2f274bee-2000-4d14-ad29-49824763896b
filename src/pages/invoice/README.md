# 发票管理系统

## 功能概述

这是一个完整的发票管理系统，包含发票申请、发票中心和发票详情三个主要页面，为用户提供完整的发票管理体验。

## 页面结构

```
src/pages/invoice/
├── apply/              # 申请开票页面
│   ├── index.tsx
│   ├── index.less
│   ├── index.config.ts
│   └── README.md
├── center/             # 发票中心页面
│   ├── index.tsx
│   ├── index.less
│   └── index.config.ts
├── detail/             # 发票详情页面
│   ├── index.tsx
│   ├── index.less
│   └── index.config.ts
└── README.md           # 总体说明文档
```

## 主要功能

### 1. 申请开票页面 (`/pages/invoice/apply/index`)
- **个人和企业发票申请**：支持两种发票类型
- **完整表单验证**：包括必填字段、格式验证等
- **订单信息展示**：显示订单编号和开票金额
- **发票内容选择**：明细或商品类别

### 2. 发票中心页面 (`/pages/invoice/center/index`)
- **发票列表展示**：分为"已开票"和"申请中"两个Tab
- **发票状态管理**：已开票、申请中、开票失败
- **搜索功能**：支持按店铺名称或商品名称搜索
- **操作功能**：查看详情、重新申请等

### 3. 发票详情页面 (`/pages/invoice/detail/index`)
- **发票预览**：显示发票图片，支持缩放和全屏查看
- **发票信息**：完整的发票详细信息展示
- **操作功能**：下载发票、分享发票、复制信息

## 页面路由

| 页面 | 路由路径 | 说明 |
|------|----------|------|
| 申请开票 | `/pages/invoice/apply/index` | 发票申请表单页面 |
| 发票中心 | `/pages/invoice/center/index` | 发票列表管理页面 |
| 发票详情 | `/pages/invoice/detail/index` | 发票详细信息和预览页面 |

## 菜单入口

发票功能已集成到用户菜单中：
- **发票中心**：在"我的"页面菜单中，所有用户角色都可访问
- **申请开票**：在"我的"页面菜单中，提供快速申请入口

## 数据流转

```
申请开票 → 发票中心(申请中) → 发票详情(已开票)
    ↓           ↓                    ↓
  提交申请    状态跟踪              查看/下载
```

## 技术特点

### 1. 响应式设计
- 适配不同屏幕尺寸
- 卡片式布局，视觉层次清晰
- 统一的设计语言和交互规范

### 2. 用户体验优化
- **加载状态**：显示加载动画和进度
- **错误处理**：友好的错误提示和重试机制
- **表单验证**：实时验证和错误提示
- **操作反馈**：成功/失败状态提示

### 3. 功能完整性
- **状态管理**：完整的发票状态流转
- **搜索筛选**：支持多维度筛选和搜索
- **图片预览**：支持缩放、全屏等操作
- **文件操作**：下载、分享、复制等功能

## 使用示例

### 1. 跳转到发票中心
```typescript
import { DDYNavigateTo } from "@/utils/route";

DDYNavigateTo({ 
    url: "/pages/invoice/center/index" 
});
```

### 2. 跳转到申请开票（带参数）
```typescript
DDYNavigateTo({ 
    url: "/pages/invoice/apply/index?orderNumber=123456&amount=199.00" 
});
```

### 3. 跳转到发票详情
```typescript
DDYNavigateTo({ 
    url: "/pages/invoice/detail/index?id=invoice123" 
});
```

## 扩展功能

### 已实现
- ✅ 发票申请表单
- ✅ 发票列表管理
- ✅ 发票详情预览
- ✅ 搜索和筛选
- ✅ 状态管理
- ✅ 文件操作

### 可扩展
- 📋 发票模板管理
- 📊 发票统计报表
- 🔔 开票状态通知
- 📱 发票二维码验证
- 💾 发票批量下载
- 🏷️ 发票标签分类

## 注意事项

1. **图片资源**：发票预览图片需要确保可访问性
2. **文件下载**：需要配置正确的文件服务器地址
3. **权限控制**：根据用户角色控制功能访问权限
4. **数据安全**：发票信息涉及敏感数据，需要加密传输
5. **性能优化**：大量发票列表需要考虑分页加载

## 兼容性

- ✅ 微信小程序
- ✅ 支付宝小程序
- ✅ H5 (需要适配)
- ✅ React Native (需要适配)
