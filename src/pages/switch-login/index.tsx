import { useState } from "react";
import { View } from "@tarojs/components";
import { Button, Checkbox, Image } from "@antmjs/vantui";
import Taro, { useRouter } from "@tarojs/taro";
import Toast from "@/utils/toast";
import { loginCheck } from "@/utils/ylogin";
import { DDYBack, DDYNavigateTo, DDYReLaunch } from "@/utils/route";
import { getStorage, setStorage } from "@/utils/storage";
import "./index.less";
import identityStore from "../../store/identity-store";
import { PROJECT_CONFIG } from "@/utils/env";
import api from "@/utils/api";
import { GUIDER_INFO, STORE_ID, SUB_STORE_ID } from "@/utils/constant";
import DDYToast from "@/utils/toast";
import LoginBtn from "./login-btn";

definePageConfig({
    navigationBarTitleText: "登录",
});
/**
 * 登录
 */
export default function () {
    const router = useRouter();
    const formPage = router.params?.formPage;
    const [value, setValue] = useState(false);

    function commitCurrentReach() {
        api.commitReach({
            data: {
                shopId: getStorage(STORE_ID),
                refererId: getStorage(GUIDER_INFO) ?? "",
                subStoreId: getStorage(SUB_STORE_ID),
            },
            filterCheck: true,
        });
    }

    const bindGetUserInfo = () => {
        if (!value) {
            Toast.info("请先阅读协议");
            return;
        }
        clearStorage();
        DDYToast.showLoading("登录中");
        loginCheck()
            .then(() => {
                commitCurrentReach();
                // 登录成功,若是新用户则跳到验证手机号页面，否则跳到首页
                if (getStorage("IsNewUser") === 1) {
                    DDYReLaunch({ url: "/pages/my/bind-mobile/index?type=register" });
                } else {
                    // 跳到扫描页
                    if (formPage) {
                        DDYNavigateTo({ url: "/pages/" + formPage });
                    } else {
                        identityStore.getIdentityInfo();
                        DDYBack();
                    }
                }
            })
            .finally(() => {
                DDYToast.hideLoading();
            });
    };
    const clearStorage = () => {
        Taro.removeStorageSync("userSpecialInfo");
        Taro.removeStorageSync("isLogin");
        Taro.removeStorageSync("userInfo");
        Taro.removeStorageSync("IsNewUser");
        Taro.removeStorageSync("addressId");
        Taro.removeStorageSync("catIndex");
        Taro.removeStorageSync("systemInfo");
        Taro.removeStorageSync("userCertInfo");
        Taro.removeStorageSync("pid");
    };

    return (
        <View className="login">
            <View className="login-header">
                <Image radius={80} width={160} height={160} src={PROJECT_CONFIG.LOGIN_LOGO} />
            </View>
            <View className="login-name">{PROJECT_CONFIG.MINI_APP_NAME}</View>
            <LoginBtn bindGetUserInfo={bindGetUserInfo} />
            <View className="private-note">
                <Checkbox
                    value={value}
                    iconSize={24}
                    onChange={e => setValue(e.detail)}
                    checkedColor={process.env.TARO_ENV === "weapp" ? "#07c160" : "#1778FF"}
                />
                我已阅读并同意{" "}
                <View
                    className="private-note-privacy"
                    style={{ color: process.env.TARO_ENV === "weapp" ? "#07c160" : "#1778FF" }}
                    onClick={() => DDYNavigateTo({ url: "/pages/h5/privacy-policy?type=1" })}
                >
                    《隐私条款》
                </View>
                和
                <View
                    className="private-note-privacy"
                    style={{ color: process.env.TARO_ENV === "weapp" ? "#07c160" : "#1778FF" }}
                    onClick={() => DDYNavigateTo({ url: "/pages/h5/privacy-policy?type=2" })}
                >
                    《服务协议》
                </View>
            </View>
        </View>
    );
}
