import { Button } from "@antmjs/vantui";
import Taro from "@tarojs/taro";

export default ({ bindGetUserInfo }) => {
    Taro.ENV_TYPE;
    return (
        <>
            {process.env.TARO_ENV === "weapp" && (
                <Button
                    className="login-btn"
                    type="primary"
                    round
                    color="#04c060"
                    open-type="getUserInfo"
                    onGetUserInfo={bindGetUserInfo}
                >
                    一键快捷登录
                </Button>
            )}
            {process.env.TARO_ENV === "alipay" && (
                <Button
                    className="login-btn"
                    type="primary"
                    round
                    color="#1778FF"
                    // open-type="getUserInfo"
                    open-type="getAuthorize"
                    scope="userInfo"
                    onGetUserInfo={bindGetUserInfo}
                >
                    支付宝会员登录
                </Button>
            )}
        </>
    );
};
