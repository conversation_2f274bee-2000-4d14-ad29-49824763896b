.address-list {
    &-wrapper {
        padding: 24px 20px;
        margin-bottom: 200px;

        .item {
            margin-top: 20px;
            background: #ffffff;
            border-radius: 16px;
            padding: 24px;
            display: flex;
            align-items: center;

            .address-wrapper {
                flex: 1;

                .adr_info {
                    display: flex;
                    flex-direction: row;
                    align-items: center;

                    .default {
                        border-radius: 4px;
                        margin-right: 10px;
                    }

                    .address {
                        display: block;
                        font-weight: 400;
                        font-size: 26px;
                        color: #666666;
                        line-height: 40px;
                    }
                }

                .address_detail {
                    margin-top: 10px;
                    font-size: 32px;
                    font-weight: 500;
                    color: #333333;
                    line-height: 48px;
                    word-break: break-word;
                }

                .usr_info {
                    font-weight: 400;
                    color: #333333;
                    margin-top: 16px;
                    display: flex;

                    .usr_name {
                    }

                    .usr_tel {
                        margin-left: 16px;
                    }
                }
            }

            .edit {
                width: 48px;
                height: 48px;
            }
        }
    }
    .add-address {
        width: 686px;
        margin-left: 32px;
        position: fixed;
        bottom: 48px;
        background: linear-gradient(90deg, #f50050 0%, #f5001d 100%);
        font-size: 32px;
        font-weight: 400;
        color: #ffffff;
        line-height: 32px;
    }
}
