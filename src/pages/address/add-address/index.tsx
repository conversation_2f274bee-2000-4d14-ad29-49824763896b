import React, { useEffect, useRef } from "react";
import { View, Input, Textarea } from "@tarojs/components";
import { Button, Form, FormItem, Icon, Switch } from "@antmjs/vantui";
import Toast from "@/utils/toast";
import { FormPicker } from "@/components/form-picker";
import "./index.less";
import api from "@/utils/api";
import { getStorage, setStorage } from "@/utils/storage";
import { ADDRESS_ID, USER_INFO } from "@/utils/constant";
import { DDYBack } from "@/utils/route";
import Taro, { useLoad, useRouter } from "@tarojs/taro";
import { isString } from "@tarojs/shared";
import { isAlipay } from "@/utils/common";
import DDYToast from "@/utils/toast";
import { ishasStreetByAreaCode } from "@/utils/judge";

type RouterParams = {
    currentType?: string;
    /**
     * 来源页面（旧代码迁移）order 来自确认订单
     */
    type?: string;
    receiverInfo?: string;
    autoOpenFlag?: string; // 是否自动打开地址选择
    needDelta?: string;
};
const EditAddress: React.FC = () => {
    const formIt = Form.useForm();
    const { currentType, type, receiverInfo, autoOpenFlag, needDelta } = useRouter<RouterParams>().params;
    let receiverInfoObject = useRef(
        typeof receiverInfo === "string" ? JSON.parse(decodeURIComponent(receiverInfo)) : null,
    );
    useLoad(() => {
        Taro.setNavigationBarTitle({ title: receiverInfo ? "编辑地址" : "新增地址" });
    });
    useEffect(() => {
        const { province, provinceId, city, cityId, region, regionId, street, streetId } =
            receiverInfoObject.current || {};
        if (receiverInfoObject.current) {
            let address = {
                value: [province, city, region],
                code: [provinceId, cityId, regionId],
            };
            if (street && streetId) {
                address.value.push(street);
                address.code.push(streetId);
            }
            receiverInfoObject.current.address = address;
            formIt.setFields(receiverInfoObject.current);
        }
    }, []);
    const save = async (value: Record<string, any>) => {
        if (value.address) {
            value.province = value.address.value[0];
            value.city = value.address.value[1];
            value.region = value.address.value[2];
            value.street = value.address.value[3];
            value.provinceId = value.address.code[0];
            value.cityId = value.address.code[1];
            value.regionId = value.address.code[2];
            value.streetId = value.address.code[3];
            delete value.address;

            let hasStreetFlag = await ishasStreetByAreaCode(value.regionId);

            // 有街道
            if (hasStreetFlag && (!value.street || !value.streetId)) {
                DDYToast.info("请选择街道");
                return;
            }
        }
        if (currentType === "add") {
            api.saveAddress({
                method: "POST",
                data: {
                    ...value,
                    user_id: getStorage(USER_INFO).userId,
                },
                filterCheck: true,
            }).then(data => {
                if (data.error) {
                    DDYToast.info(data.message);
                    return;
                }
                saveSuccess(data);
            });
        } else if (currentType === "edit") {
            const id = receiverInfoObject.current?.id;
            api.editAddress({
                method: "PUT",
                data: {
                    ...value,
                    user_id: getStorage(USER_INFO).userId,
                    id,
                },
                filterCheck: true,
            }).then(data => {
                if (data.error) {
                    DDYToast.info(data.message);
                    return;
                }
                saveSuccess(id);
            });
        }
    };
    const saveSuccess = (addressId: number) => {
        let delta = 1;
        if (type === "order") {
            setStorage(ADDRESS_ID, addressId);
            try {
                const pages = Taro.getCurrentPages();
                const current = pages[pages.length - 2];
                const eventChannel = current.getOpenerEventChannel();
                console.log("tapSelAddress", addressId);
                eventChannel.emit("selectAddress", { id: addressId });
                delta = needDelta ? parseInt(needDelta) : 2;
            } catch (e) {
                console.log("设置地址发生异常", e);
            }
        }
        DDYBack({ delta });
        Toast.success("保存成功");
    };
    const valueTrim = name => {
        let fieldValue = formIt.getFieldValue(name);
        if (isString(fieldValue)) {
            formIt.setFieldsValue(name, fieldValue.trim());
        }
    };
    return (
        <View className="add-address">
            <Form form={formIt} onFinish={(errs, res) => save(res)}>
                <View className="list_block">
                    <FormItem
                        label="收货人"
                        name="receiveUserName"
                        required
                        borderBottom
                        trigger="onInput"
                        valueFormat={e => e.detail.value}
                    >
                        <Input placeholder="请输入收货人姓名" onBlur={() => valueTrim("receiveUserName")} />
                    </FormItem>
                    <FormItem
                        label="手机号码"
                        name="mobile"
                        required
                        borderBottom
                        trigger="onInput"
                        valueFormat={e => e.detail.value}
                    >
                        <Input
                            placeholder="请输入手机号码"
                            maxlength={11}
                            type="number"
                            onBlur={() => valueTrim("mobile")}
                        />
                    </FormItem>
                    <FormItem
                        label="所在地区"
                        name={"address"}
                        required
                        mutiLevel
                        borderBottom
                        valueFormat={e => e.detail}
                        renderRight={<Icon name="arrow" />}
                    >
                        <FormPicker mode={"region"} showStreet={true} defaultVisible={autoOpenFlag == "1"} />
                    </FormItem>
                    <FormItem
                        label="详细地址"
                        name="detail"
                        required
                        trigger="onInput"
                        valueFormat={e => e.detail.value}
                    >
                        <Textarea placeholder="请输入详细地址信息" maxlength={200} onBlur={() => valueTrim("detail")} />
                    </FormItem>
                </View>
                <View className="list_block">
                    <FormItem
                        label="设为默认地址"
                        name="isDefault"
                        valueKey="checked"
                        labelClassName={"vant-form-label-width-auto"}
                        className={"vant-form-formItem-justify-content-right"}
                    >
                        <Switch activeColor="#F5001D" />
                    </FormItem>
                </View>
            </Form>
            <Button type="primary" className="submit-btn" round onClick={() => formIt.submit()}>
                保存
            </Button>
        </View>
    );
};
export default EditAddress;
