import React, { useRef, useState } from "react";
import { View } from "@tarojs/components";
import {
    Empty,
    InfiniteScroll,
    InfiniteScrollProps,
    InfiniteScrollInstance,
    IPullToRefreshProps,
    PullToRefresh,
    Tag,
    Icon,
    pxTransform,
    Button,
    Cell,
    Dialog,
} from "@antmjs/vantui";
import api from "@/utils/api";
import { getStorage, setStorage } from "@/utils/storage";

import "./index.less";
import { chooseAddress, useDidShow, useLoad, useRouter } from "@tarojs/taro";
import { ADDRESS_ID, USER_INFO } from "@/utils/constant";
import { DDYBack, DDYNavigateTo } from "@/utils/route";
import Toast from "@/utils/toast";
import { isAlipay } from "@/utils/common";
import { ishasStreetByAreaCode } from "@/utils/judge";

const Dialog_ = Dialog.createOnlyDialog();

const AddressList: React.FC = () => {
    const pageSize = 20;
    const [record, setRecord] = useState<any[]>([]);
    const pageNo = useRef(1);
    const infiniteScrollInstance = useRef<InfiniteScrollInstance>();
    const { type } = useRouter().params;
    useDidShow(() => {
        pageNo.current = 1;
        infiniteScrollInstance.current?.reset(true);
    });

    const onRefresh: IPullToRefreshProps["onRefresh"] = () => {
        return new Promise(resolve => {
            pageNo.current = 1;
            infiniteScrollInstance.current?.reset(true);
            resolve(undefined);
        });
    };
    const loadMore: InfiniteScrollProps["loadMore"] = () => {
        console.log("loadMore", loadMore);
        return new Promise(resolve => {
            getUserAddress()
                .then(data => {
                    let newData: any[];
                    if (pageNo.current === 1) {
                        newData = data;
                    } else {
                        newData = record.concat(data);
                    }
                    pageNo.current++;
                    setRecord(newData);
                    resolve(data.length < pageSize ? "complete" : "loading");
                })
                .catch(e => {
                    resolve("error");
                });
        });
    };

    const getUserAddress = () => {
        return api.getUserAddress({
            data: {
                user_id: getStorage(USER_INFO).userId,
                pageSize: pageSize,
                pageNo: pageNo.current,
            },
            filterCheck: true,
        });
    };
    const edit = item => {
        DDYNavigateTo({
            url: `/pages/address/add-address/index?currentType=edit&type=${type}&receiverInfo=${encodeURIComponent(
                JSON.stringify(item),
            )}`,
        });
    };
    const tapSelAddress = async (id, address) => {
        let hasStreetFlag = await ishasStreetByAreaCode(address?.regionId);
        if (hasStreetFlag && (!address?.street || !address?.streetId)) {
            Dialog_.confirm({
                message: "当前四级地址为空，请先维护收货地址的街道信息",
                confirmButtonText: "去修改",
                cancelButtonText: "知道了",
                onConfirm: () => {
                    DDYNavigateTo({
                        url: `/pages/address/add-address/index?currentType=edit&type=order&receiverInfo=${encodeURIComponent(
                            JSON.stringify(address),
                        )}&autoOpenFlag=1`,
                    });
                },
            });
            return;
        }

        if (type != "order") {
            return;
        }
        setStorage(ADDRESS_ID, id);
        DDYBack();
    };
    const getWeiXinAddress = () => {
        chooseAddress({
            success: res => {
                console.log("res:", res);
                let userInfo = getStorage(USER_INFO);
                api.saveAddress({
                    method: "POST",
                    data: {
                        user_id: userInfo.userId,
                        receiveUserName: res.userName,
                        mobile: res.telNumber,
                        detail: res.detailInfo,
                        paperNo: "",
                        provinceId: -1,
                        cityId: -1,
                        regionId: -1,
                        province: res.provinceName,
                        city: res.cityName,
                        region: res.countyName,
                    },
                    filterCheck: true,
                }).then(data => {
                    if (type === "order") {
                        setStorage(ADDRESS_ID, data);
                        DDYBack({ delta: 1 });
                    }
                    Toast.success("保存成功");
                });
            },
            fail: function (err) {
                console.log(err);
            },
        });
    };
    return (
        <View className="address-list">
            <PullToRefresh onRefresh={onRefresh}>
                <View className="address-list-wrapper">
                    {!isAlipay() && (
                        <Cell
                            onClick={getWeiXinAddress}
                            className={"item"}
                            title={isAlipay() ? "获取支付宝地址簿" : "获取微信地址"}
                            renderIcon={
                                isAlipay() ? (
                                    <Icon name={"alipay"} color={"#1778FF"} size={pxTransform(48)} />
                                ) : (
                                    <Icon name={"wechat"} color={"#26bf50"} size={pxTransform(48)} />
                                )
                            }
                            isLink
                        />
                    )}

                    <View className="list">
                        <View className="items">
                            {record?.map((item, index) => {
                                return (
                                    <View
                                        className={`item ${item.isDefault ? "selected" : ""}`}
                                        key={item.id}
                                        onClick={() => tapSelAddress(item.id, item)}
                                    >
                                        <View className={"address-wrapper"}>
                                            <View className="adr_info">
                                                {item.isDefault && (
                                                    <Tag color="#F50050" className={"default"}>
                                                        默认
                                                    </Tag>
                                                )}
                                                <View className="address">
                                                    {item.province} {item.city} {item.region}{" "}
                                                    {item.street ? item.street : ""}
                                                </View>
                                            </View>
                                            <View className="address_detail">{item.detail}</View>
                                            <View className="usr_info">
                                                <View className="usr_name">{item.receiveUserName}</View>
                                                <View className="usr_tel">{item.mobile}</View>
                                            </View>
                                        </View>
                                        <View
                                            onTap={event => {
                                                event.stopPropagation();
                                                edit(item);
                                            }}
                                            className={"edit"}
                                        >
                                            <Icon name="edit" color={"#CBCCCD"} size={pxTransform(32)} />
                                        </View>
                                    </View>
                                );
                            })}
                        </View>
                        <InfiniteScroll
                            loadMore={loadMore}
                            ref={infiniteScrollInstance}
                            completeText={
                                <>
                                    {record.length == 0 ? (
                                        <Empty
                                            description="暂无数据！"
                                            image="https://dante-img.oss-cn-hangzhou.aliyuncs.com/55439458101.png"
                                        />
                                    ) : (
                                        "没有更多了"
                                    )}
                                </>
                            }
                        />
                    </View>
                </View>
            </PullToRefresh>
            <Button
                className={"add-address"}
                onClick={() => DDYNavigateTo({ url: "/pages/address/add-address/index?currentType=add" })}
            >
                <Icon name={"plus"} color={"#fff"} /> 添加收货地址
            </Button>
            {/* 弹窗内容 */}
            <Dialog_ />
        </View>
    );
};
export default AddressList;
