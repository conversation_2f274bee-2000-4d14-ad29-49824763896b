.page {
    height: 100vh;
    background-color: #f5f5f5;
    display: flex;
    align-items: center;
    // justify-content: center;
    flex-direction: column;
    padding: 30px;
    .container {
        // height: 80%;
        // width:fit-content;
        // width: 100%;
        background-color: #fff;
        border: 2rpx solid #ccc;
        margin: 30px;
        border-radius: 16px;
        display: flex;
        // align-items: flex-start;
        // justify-content: flex-start;
        flex-direction: column;
        padding: 40rpx 30rpx 0 30rpx;
        .title {
            padding: 10rpx 30rpx;
        }
        .content {
            padding: 30rpx;
        }
        .btn {
            margin: 30rpx auto;
            display: flex;
            width: 50%;
            border: 2rpx solid #f3eeee;
            background-color: #f3eeee;
        }
    }
}
