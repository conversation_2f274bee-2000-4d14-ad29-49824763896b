import api from "@/utils/api";
import { But<PERSON> } from "@antmjs/vantui";
import { View, WebView, Text, ScrollView, RichText } from "@tarojs/components";
import Taro, { useDidShow } from "@tarojs/taro";
import { useEffect, useRef, useState } from "react";
import "./index.less";
import DDYToast from "@/utils/toast";
import { getStorage, setStorage } from "@/utils/storage";
import { DDYBack, DDYBackToURL } from "@/utils/route";
import { isAlipay, removeSignback } from "@/utils/common";
import { IS_SIGN_SUCCESS, SING_BACK_URL } from "@/utils/constant";

export default () => {
    const loopTimer = useRef<NodeJS.Timeout>();
    const clockBol = useRef(false);
    const sign = () => {
        api.getSignUrl({
            data: {},
            method: "POST",
        }).then(res => {
            if (isAlipay()) {
                try {
                    //@ts-ignore
                    my.ap.openURL({
                        url: res,
                        success(res) {
                            // 打开成功
                            console.log(res);
                        },
                        fail(err) {
                            // 打开成功
                            console.log(err);
                        },
                    });
                } catch (error) {
                    console.log(error);
                }

                return;
            }
            Taro.navigateToMiniProgram({
                appId: "wxc46c6d2eed27ca0a", //通联小程序 appId
                path: "pages/merchantAddress/merchantAddress", //通联小程序页面路径
                extraData: {
                    targetUrl: res,
                },
                envVersion: "release", //develop 开发版 trial 体验版 release 正式版
                success(res) {
                    // 打开成功
                    console.log(res);
                },
            });
        });
    };

    /** 门店角色通商云签约成功后需要轮询签约状态 */
    const loopQuery = () => {
        loopTimer.current && clearInterval(loopTimer.current);
        let timers = 5;
        DDYToast.showLoading("签约结果查询中");
        loopTimer.current = setInterval(() => {
            if (timers <= 0) {
                DDYToast.hideLoading();
                clearInterval(loopTimer.current);
            }
            timers--;
            api.querySignStatus({
                method: "POST",
                data: {},
                contentType: "application/json",
                filterCheck: true,
                showError: false,
            }).then(res => {
                // console.log("res:", res)
                if (res.data === 2) {
                    clearInterval(loopTimer.current);
                    DDYToast.hideLoading();
                    DDYToast.success("你已签约成功");
                    setStorage(IS_SIGN_SUCCESS, true);
                    setTimeout(() => {
                        const signBackUrl = getStorage(SING_BACK_URL);
                        removeSignback();
                        DDYBackToURL(signBackUrl);
                    }, 1000);
                }
            });
        }, 1000);
    };

    useDidShow(() => {
        clockBol.current && loopQuery();
    });

    return (
        <View className="page">
            <View className="container">
                <View className="title">亲爱的门店用户，欢迎使用提现功能</View>
                <View className="content">
                    {" "}
                    为履行法律法规规定的相关义务，同时为您提供提现及帐单查询服务，通商云支付需要对交易信息进行记录和管理。请您先签署相应协议进行授权。
                </View>
                <Button
                    round
                    className="btn"
                    onClick={() => {
                        sign();
                        clockBol.current = true;
                    }}
                >
                    已阅读，去签约
                </Button>
            </View>
        </View>
    );
};
