import FormCell from "@/components/form-cell";
import { View } from "@tarojs/components";
import { useDidShow, useRouter } from "@tarojs/taro";
import { useEffect, useState } from "react";
import "./index.less";
import AgreeModal from "@/components/agree-modal";
import api from "@/utils/api";
import DDYToast from "@/utils/toast";
import { queryInfo } from "@/utils/common";
import { DDYBack } from "@/utils/route";

interface BankCardInfo {
    realName: string;
    idCard: string;
    bankCard: string;
    bindCardPhone: string;
}

export default () => {
    const route = useRouter();
    const [detail, setDetail] = useState<BankCardInfo>({} as BankCardInfo);
    const getDetail = () => {
        queryInfo({
            callback: data => {
                setDetail({
                    realName: data.authUserName,
                    idCard: data.authUserIdNumber,
                    bankCard: data.cardNo,
                    bindCardPhone: data.bindCardPhone,
                });
            },
            go: false,
        });
    };

    useDidShow(() => {
        getDetail();
    });

    const unbindModal = () => {
        DDYToast.showModal({
            title: "解除绑定",
            content: "解除绑定后当前银行卡不可用，请谨慎操作",
            success: function (res) {
                if (res.confirm) {
                    console.log("用户点击确定");
                    api.unBindBankCard({
                        method: "POST",
                        data: {},
                        contentType: "application/json",
                        filterCheck: true,
                        showLoad: true,
                    }).then(res => {
                        if (res.success) {
                            DDYToast.success("解绑成功");
                            DDYBack();
                        } else {
                            DDYToast.success(res.errorMsg || "操作异常");
                        }
                    });
                } else if (res.cancel) {
                    console.log("用户点击取消");
                }
            },
        });
    };

    return (
        <View className="bank-detail">
            <View className="page-title">银行卡</View>
            <FormCell title={"真实姓名"} value={detail.realName} />
            <FormCell title={"身份证号"} value={detail.idCard} />
            <FormCell title={"卡号"} value={detail.bankCard} />
            <FormCell title={"预留手机号"} value={detail.bindCardPhone} />
            <View className="unbind-btn" onClick={unbindModal}>
                解除绑定
            </View>
            <AgreeModal />
        </View>
    );
};
