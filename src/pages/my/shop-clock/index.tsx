import api from "@/utils/api";
import { Button, Cell, Field, Row, Form, FormItem, Uploader } from "@antmjs/vantui";
import { OpenData, View, Image, Text, ITouchEvent } from "@tarojs/components";
import Taro, { useDidShow } from "@tarojs/taro";
import { useEffect, useState } from "react";
import "./index.less";
import DDYToast from "@/utils/toast";
import { getStorage } from "@/utils/storage";
import { STORE_ID, SUB_STORE_ID, USER_INFO } from "@/utils/constant";
import { DDYBack, DDYNavigateTo } from "@/utils/route";
import { PROJECT_CONFIG } from "@/utils/env";
import { isAlipay } from "@/utils/common";
import { icon_login } from "@/images";

definePageConfig({
    backgroundTextStyle: "light",
    navigationBarTitleText: "陈列打卡",
});

export default () => {
    const form = Form.useForm();
    const [time, setTime] = useState(null);
    const valueFormatUpload = event => {
        // DDYToast.showLoading("上传中...");

        // 异步更新
        return new Promise(resolve => {
            const { file } = event.detail;
            console.log(file, file.url);
            resolve([file]);
            // Taro.uploadFile({
            //     url: `${PROJECT_CONFIG.API_MALL}/api/storeMonthlyPunch/upload/picture`, //仅为示例，非真实的接口地址
            //     name: "file",
            //     filePath: file.url,
            //     formData: {
            //         subStoreId: getStorage(SUB_STORE_ID),
            //     },
            //     success(res) {
            //         DDYToast.hideLoading();
            //         // file.url = JSON.parse(res.data).data;
            //         resolve([file]);
            //     },
            // });
        });
    };

    const deleteFile = (event: ITouchEvent, name: string | string[]) => {
        const { index, fileList } = event.detail;
        fileList.splice(index, 1);
        form?.setFieldsValue(name, fileList);
    };

    const getTime = () => {
        api.getLastClockTime({
            data: {
                subStoreId: getStorage(SUB_STORE_ID),
            },
            method: "GET",
        }).then(res => {
            console.log("res:", res);
            setTime(res);
        });
    };

    const submit = () => {
        form.validateFields((err, res) => {
            if (!err?.length) {
                const urls = [res.doorPictureUrl[0].thumb, res.displayPictureUrl[0].thumb];
                DDYToast.showLoading("图片上传中...");
                Promise.all(
                    urls.map((item, index) => {
                        console.log("item:", item);
                        return new Promise((resolve, reject) => {
                            Taro.uploadFile({
                                url: `${PROJECT_CONFIG.API_MALL}/api/storeMonthlyPunch/upload/picture`, //仅为示例，非真实的接口地址
                                name: "file",
                                filePath: item,
                                formData: {
                                    subStoreId: getStorage(SUB_STORE_ID),
                                    type: index + 1,
                                },
                                success(res) {
                                    console.log(res.data);
                                    try {
                                        const data = JSON.parse(res.data);
                                        if (data.success) {
                                            const url = data.data;
                                            resolve([url]);
                                        } else {
                                            reject();
                                        }
                                    } catch (e) {
                                        reject();
                                    }

                                    // DDYToast.hideLoading();
                                },
                                fail(res) {
                                    console.log(res);
                                    reject();
                                },
                            });
                        });
                    }),
                )
                    .then(res => {
                        // console.log('res:', res)
                        api.submitClock({
                            data: {
                                subStoreId: getStorage(SUB_STORE_ID),
                                doorPictureUrl: res[0]?.[0],
                                displayPictureUrl: res[1]?.[0],
                            },
                            method: "POST",
                        }).then(response => {
                            DDYToast.hideLoading();
                            console.log("response:", response);
                            DDYToast.success("打卡成功");
                            form.resetFields();
                            setTimeout(() => {
                                DDYBack();
                            }, 1000);
                            getTime();
                        });
                    })
                    .catch(e => {
                        DDYToast.hideLoading();
                        DDYToast.info("文件上传失败");
                    });
            }
        });
    };

    useEffect(() => {
        getTime();
    }, []);
    return (
        <View className="shop-clock">
            <Form
                form={form}
                style={{
                    position: "relative",
                    top: "-24rpx",
                    // paddingBottom: '80rpx'
                }}
            >
                <View>
                    <FormItem
                        name="doorPictureUrl"
                        required
                        mutiLevel
                        // layout="vertical"
                        label="门头照"
                        valueKey="fileList"
                        valueFormat={valueFormatUpload}
                        trigger="onAfterRead"
                        validateTrigger="onAfterRead"
                        className="image-uploader-form-item-not-padding"
                    >
                        <Uploader
                            maxCount={1}
                            className="uploader-img"
                            imageFit="widthFix"
                            onDelete={event => deleteFile(event, "doorPictureUrl")}
                            uploadText="门头照"
                            // deletable={!disabled}
                            // disabled={disabled}
                        ></Uploader>
                    </FormItem>
                    <FormItem
                        name="displayPictureUrl"
                        required
                        mutiLevel
                        // layout="vertical"
                        label="陈列照"
                        valueKey="fileList"
                        valueFormat={valueFormatUpload}
                        trigger="onAfterRead"
                        validateTrigger="onAfterRead"
                        className="image-uploader-form-item-not-padding"
                    >
                        <Uploader
                            maxCount={1}
                            className="uploader-img"
                            imageFit="widthFix"
                            onDelete={event => deleteFile(event, "displayPictureUrl")}
                            uploadText="陈列照"
                            // deletable={!disabled}
                            // disabled={disabled}
                        ></Uploader>
                    </FormItem>
                </View>
            </Form>
            {time && <View style={{ textAlign: "center", color: "#666", marginTop: "10px" }}>最近打卡时间:{time}</View>}

            <Button
                className="bottom-btn"
                onClick={() => {
                    submit();
                }}
            >
                提交打卡
            </Button>
        </View>
    );
};
