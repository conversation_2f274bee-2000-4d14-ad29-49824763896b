import AgreeModal from "@/components/agree-modal";
import api from "@/utils/api";
import { removeSignback } from "@/utils/common";
import { IS_SIGN_SUCCESS, SING_BACK_URL } from "@/utils/constant";
import { DDYBack, DDYBackToURL } from "@/utils/route";
import { getStorage, setStorage } from "@/utils/storage";
import DDYToast from "@/utils/toast";
import { Result } from "@antmjs/vantui";
import { useRouter } from "@tarojs/taro";
import { useEffect } from "react";

export default () => {
    const route = useRouter();
    const type = route.params.type;

    const querySignStatus = () => {
        api.querySignStatus({
            method: "POST",
            data: {},
            contentType: "application/json",
            filterCheck: true,
            showError: false,
        }).then(res => {
            // console.log("res:", res)
            if (res.data === 2) {
                DDYToast.success("你已签约成功");
                setStorage(IS_SIGN_SUCCESS, true);
                setTimeout(() => {
                    const signBackUrl = getStorage(SING_BACK_URL);
                    removeSignback();
                    DDYBackToURL(signBackUrl);
                }, 1000);
            }
        });
    };

    useEffect(() => {
        querySignStatus();
    }, []);

    return (
        <>
            {type === "success" ? (
                <Result title="银行卡添加成功" message="" type="success" />
            ) : (
                <Result title="银行卡添加失败" message="请核对并修改银行卡信息后，再重新提交。" type="error" />
            )}
            <AgreeModal />
        </>
    );
};
