import api from "@/utils/api";
import { queryInfo } from "@/utils/common";
import DDYToast from "@/utils/toast";
import { FormRender, Form, Button } from "@antmjs/vantui";
import { View } from "@tarojs/components";
import "./index.less";
type IParams = {
    // account: number
    name: string;
    identityNo: string;
};
export default () => {
    const form = Form.useForm();
    const onSubmit = () => {
        form.validateFields((err, res) => {
            if (err && err?.length > 0) {
                return;
            }
            api.authentication({
                method: "POST",
                data: {
                    name: res.name,
                    identityNo: res.identityNo,
                },
                filterCheck: true,
                showLoad: true,
            })
                .then(res => {
                    if (res.success) {
                        //成功回调
                        queryInfo({ go: true, showError: true });
                    } else {
                        DDYToast.info(res.errorMsg || "操作失败");
                    }
                })
                .catch(() => {});
        });
    };
    return (
        <View className="bank-auth-real">
            <View className="page-head">
                <View className="page-title">实名认证</View>
                <View className="page-info">请填写银行卡持有人实名信息</View>
            </View>
            <FormRender<IParams>
                form={form}
                config={[
                    {
                        fields: "name",
                        type: "input",
                        required: true,
                        label: "真实姓名",
                    },
                    {
                        fields: "identityNo",
                        type: "input",
                        required: true,
                        label: "身份证号",
                        rules: [
                            {
                                rule: values => {
                                    const reg =
                                        /^[1-9]\d{5}(18|19|([23]\d))\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/;
                                    if (!reg.test(values)) {
                                        return Promise.resolve("请输入合法的身份证号");
                                        return;
                                    }
                                    return Promise.resolve("");
                                },
                            },
                        ],
                    },
                ]}
            />
            <Button className="btn-next" onClick={onSubmit} type="primary" round block>
                下一步
            </Button>
        </View>
    );
};
