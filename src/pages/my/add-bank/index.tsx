import { Form, Button, Dialog, FormRender, FormItem } from "@antmjs/vantui";
import { Icon, Input, View } from "@tarojs/components";

import "./index.less";
import { DDYNavigateTo } from "@/utils/route";
import { queryInfo } from "@/utils/common";
import api from "@/utils/api";
import DDYToast from "@/utils/toast";
import { useEffect } from "react";

type IParams = {
    phone: string;
    cardNo: string;
    branchName: string;
};
definePageConfig({
    backgroundTextStyle: "light",
    navigationBarTitleText: "",
});

export default () => {
    const form = Form.useForm();

    const bindCard = data => {
        api.applyNewBindBankCard({
            method: "POST",
            data: {
                phone: data.phone,
                cardNo: data.cardNo,
                branchName: data.branchName,
            },
            filterCheck: true,
            showLoad: true,
        }).then(res => {
            console.log("res:", res);
            if (res.success) {
                queryInfo({ go: true, showError: true });
            } else {
                DDYToast.info(res.errorMsg);
            }
        });
    };

    const customGetBankName = () => {
        const data = form.getFieldsValue();
        console.log("data:", data);
        if (/^\d{16,19}$/.test(data.cardNo)) {
            api.cardBin({
                method: "POST",
                data: {
                    cardNo: data.cardNo,
                },
                filterCheck: true,
                showError: false,
            }).then(res => {
                console.log("res:", res, form);
                if (res.success) {
                    form.setFields({
                        branchName: res.data.bankName,
                    });
                } else {
                    DDYToast.info(res.errorMsg);
                    form.setFields({
                        branchName: "",
                    });
                }
            });
        } else {
            DDYToast.info("请输入16～19位银行卡号");
        }
    };

    useEffect(() => {
        queryInfo({
            go: false,
            callback: data => {
                console.log(data);
                if (data.cardNo) {
                    form.setFields({ cardNo: data.cardNo });
                }
                if (data.branchName) {
                    form.setFields({ branchName: data.branchName });
                }
            },
        });
    }, []);
    return (
        <View className="add-bank">
            <View className="page-head">
                <View className="page-title">银行卡</View>
                <View className="page-info">请填写到帐银行卡信息</View>
            </View>
            <Form
                initialValues={{
                    userName: "我是初始值",
                    singleSelect: "1",
                    rate: 2,
                    slider: "50",
                }}
                form={form}
                onFinish={(errs, res) => console.info(errs, res)}
            >
                <FormItem
                    label="银行卡号"
                    name="cardNo"
                    required
                    valueFormat={e => e.detail.value}
                    rules={[
                        {
                            rule: values => {
                                var regex = /^\d{16,19}$/;
                                if (!regex.test(values)) {
                                    return Promise.resolve("请输入16到19位银行卡号");
                                }
                                return Promise.resolve("");
                            },
                        },
                    ]}
                    trigger="onInput"
                >
                    <Input
                        placeholder={"请输入银行卡号"}
                        onBlur={e => {
                            console.log("e:", e);
                            customGetBankName();
                        }}
                    />
                </FormItem>
                <FormItem
                    label="银行名称"
                    name="branchName"
                    required
                    valueFormat={e => e.detail.value}
                    trigger="onInput"
                >
                    <Input disabled placeholder={"银行名称"} />
                </FormItem>
                <FormItem
                    label="银行预留手机号"
                    name="phone"
                    required
                    valueFormat={e => e.detail.value}
                    rules={[
                        {
                            rule: values => {
                                // if(!values)
                                if (values.length !== 11) {
                                    return Promise.resolve("请输入11位手机号");
                                    return;
                                }
                                return Promise.resolve("");
                            },
                        },
                    ]}
                    trigger="onInput"
                >
                    <Input
                        placeholder={"请输入银行预留手机号"}
                        onBlur={e => {
                            console.log("e:", e);
                            customGetBankName();
                        }}
                    />
                </FormItem>
            </Form>

            {/* <FormRender<IParams>
                    form={form}
                    config={[
                        {
                            fields: "cardNo",
                            type: "inputNumber",
                            required: true,
                            label: "银行卡号",
                            // onBlur: (e) => {
                            //     console.log("e:", e)
                            // }
                        },
                        {
                            fields: "branchName",
                            type: "input",
                            required: true,
                            label: "银行名称",
                        },
                        {
                            fields: "phone",
                            type: "input",
                            required: true,
                            label: "预留手机号",
                            // onBlur: (e) => {
                            //     console.log("e:", e)
                            // }
                        },
                    ]}
                /> */}
            <Button
                // style={{ width: '100%', marginTop: '20px', backgroundColor: '#fff' }}
                className="btn-next"
                type="primary"
                onClick={() => {
                    form.validateFields((err, res) => {
                        if (!err?.length) {
                            // queryInfo({ go: true })
                            bindCard(res);
                        }
                    });
                }}
            >
                下一步
            </Button>
        </View>
    );
};
