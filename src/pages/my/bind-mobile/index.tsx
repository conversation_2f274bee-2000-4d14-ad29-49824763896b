import api from "@/utils/api";
import { APP_ID, PROJECT_ID, SCAN_PAGE, STORE_ID } from "@/utils/constant";
import { DDYBack, DDYRedirectTo, DDYSwitchTab } from "@/utils/route";
import { getStorage, removeStorage, setStorage } from "@/utils/storage";
import { ActionSheet, Button, Field, Picker } from "@antmjs/vantui";
import { View } from "@tarojs/components";
import { useState } from "react";
import Taro, { useRouter } from "@tarojs/taro";
import { getSessionId } from "@/utils/server/session";
import { PROJECT_CONFIG } from "@/utils/env";
import DDYToast from "@/utils/toast";
definePageConfig({
    backgroundTextStyle: "light",
    navigationBarTitleText: "绑定手机号码",
});

const columns = [
    { name: "+86  大陆地区", value: "+86", realValue: "" },
    { name: "+61  澳大利亚", value: "+61", realValue: "0061" },
];

export default () => {
    const route = useRouter();
    const [phone, setPhone] = useState("");
    // const [countryCode, setCoutryCode] = useState('');
    const [verifyCode, setVerifyCode] = useState("");
    const [show, setShow] = useState(false);
    const [index, setIndex] = useState(0);
    const sendCode = () => {
        const type = route.params.type || "setting";
        switch (type) {
            case "setting":
                api.sendRandCode({
                    method: "POST",
                    contentType: "application/x-www-form-urlencoded",
                    data: {
                        countryCode: columns[index].value,
                        mobile: phone,
                        code: getStorage(STORE_ID),
                    },
                    filterCheck: true,
                    showError: false,
                })
                    .then(res => {
                        if (res.error || !res.success) {
                            DDYToast.info(res.message || res.errorMsg);
                        } else {
                            DDYToast.info("发送成功!");
                        }
                    })
                    .catch(err => {
                        console.log("err:", err);
                        DDYToast.info(err.message);
                    });
                break;
            case "register":
                api.sendSmsLogin({
                    method: "POST",
                    contentType: "application/x-www-form-urlencoded",
                    data: {
                        countryCode: columns[index].value,
                        mobile: phone,
                        shopId: getStorage(STORE_ID),
                    },
                    showError: false,
                    filterCheck: true,
                })
                    .then(res => {
                        if (res.error || !res.success) {
                            DDYToast.info(res.message || res.errorMsg);
                        } else {
                            DDYToast.info("发送成功!");
                        }
                    })
                    .catch(err => {
                        console.log("err:", err);
                        DDYToast.info(err.message);
                    });
                break;
        }
    };

    const onChange = event => {
        console.log("event:", event.detail.index);
        setIndex(event.detail.index);
        setShow(false);
    };

    const bindMobile = () => {
        if (!phone) return;
        if (!verifyCode) return;
        const type = route.params.type || "setting";
        switch (type) {
            case "setting":
                api.registerUser({
                    method: "POST",
                    contentType: "application/x-www-form-urlencoded",
                    data: {
                        countryCode: columns[index].value,
                        mobile: phone,
                        code: verifyCode,
                    },
                    filterCheck: true,
                })
                    .then(res => {
                        console.log("res:", res);
                        if (res.status !== 500) {
                            const pages = Taro.getCurrentPages();
                            const current = pages[pages.length - 1];
                            const eventChannel = current.getOpenerEventChannel();
                            eventChannel.emit("bindMobile", phone);
                            DDYBack();
                        } else {
                            DDYToast.info(res.message);
                        }
                    })
                    .catch(err => {
                        console.log("err:", err);
                        DDYToast.info(err.message);
                    });
                break;
            case "register":
                let sessionId = getSessionId();
                sessionId = "msid=" + sessionId;
                api.bindPhone({
                    method: "GET",
                    contentType: "application/x-www-form-urlencoded",
                    data: {
                        countryCode: columns[index].value,
                        mobile: phone,
                        code: verifyCode,
                        appId: getStorage(APP_ID) ? getStorage(APP_ID) : PROJECT_CONFIG.APP_ID, // 部分用户绑定手机报系统异常，后端说是appId为空，所以为空时再取一下
                        sessionId: sessionId,
                        wxaProjectId: getStorage(PROJECT_ID),
                    },
                    filterCheck: true,
                })
                    .then(res => {
                        console.log("res1:", res);
                        if (res.status !== 500) {
                            setStorage("IsNewUser", 0);
                            // 跳到扫描页
                            if (getStorage(SCAN_PAGE)) {
                                const scanPage = getStorage(SCAN_PAGE);
                                removeStorage(SCAN_PAGE);
                                DDYRedirectTo({
                                    url: "/pages/" + scanPage,
                                });
                            } else {
                                DDYSwitchTab({
                                    url: "/pages/home/<USER>",
                                });
                            }
                        } else {
                            DDYToast.info(
                                res.errorMsg || res?.message || res?.message?.errorMsg || "绑定异常，请稍后重试",
                            );
                        }
                    })
                    .catch(err => {
                        console.log("err:", err);
                        DDYToast.info(err.message);
                    });

            default:
                break;
        }
    };

    return (
        <View>
            <Field
                value={phone}
                renderTitle={
                    <View
                        onClick={() => {
                            setShow(true);
                        }}
                    >
                        {columns[index].name}
                    </View>
                }
                placeholder="请输入手机号"
                onChange={e => {
                    setPhone(e.detail);
                }}
                // errorMessage="手机号格式错误"
                border={false}
            />
            <Field
                center
                clearable
                label="短信验证码"
                placeholder="请输入短信验证码"
                border
                value={verifyCode}
                onChange={e => {
                    setVerifyCode(e.detail);
                }}
                renderButton={
                    <Button
                        size="small"
                        type="primary"
                        onClick={() => {
                            sendCode();
                        }}
                    >
                        发送验证码
                    </Button>
                }
            />
            <Button
                type="primary"
                style={{
                    margin: "100px 25rpx",
                    width: "700rpx",
                }}
                block
                round
                onClick={() => {
                    bindMobile();
                }}
            >
                提交
            </Button>
            <ActionSheet show={show} onClose={() => setShow(false)}>
                <Picker columns={columns.map(item => item.name)} onConfirm={onChange}></Picker>
            </ActionSheet>
        </View>
    );
};
