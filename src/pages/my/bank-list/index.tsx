import { DDYNavigateTo } from "@/utils/route";
import { Icon, NoticeBar } from "@antmjs/vantui";
import { View, Text } from "@tarojs/components";
import { useEffect, useState } from "react";
import "./index.less";
import { queryInfo } from "@/utils/common";
import { useDidShow } from "@tarojs/taro";
definePageConfig({
    backgroundTextStyle: "light",
    navigationBarTitleText: "银行卡",
});

export default () => {
    const [banks, setBanks] = useState<any[]>([]);
    const [tips, setTips] = useState("");

    useDidShow(() => {
        queryInfo({
            callback: data => {
                console.log("data:", data);

                if (data?.accountInfo?.signContractStatus !== 2) {
                    setTips("当前银行卡不可用，请先进行验证");
                } else {
                    if (data.cardList && data.cardList[0]) {
                        data.cardList[0].sign = 2;
                    }
                    setTips("");
                }
                setBanks(data.cardList || []);
            },
            go: false,
        });
    });

    return (
        <View className="bank-list">
            {/** 警告信息区域 */}
            {tips && (
                <NoticeBar
                    color="red"
                    background="#f1dbdd"
                    leftIcon="info-o"
                    text={tips}
                    onClick={() => {
                        queryInfo({ go: true, showError: true });
                    }}
                />
            )}

            {/** 银行卡区域 */}
            {banks.map((bankItem: any, bankIndex) => {
                return (
                    <View
                        className="bank-item"
                        key={bankIndex}
                        onClick={() => {
                            DDYNavigateTo({
                                url: `/pages/my/bank-detail/index`,
                            });
                        }}
                    >
                        <View className="bank-item-title">
                            <Text className="bank-name">{bankItem.cardName}</Text>
                            {/* <Text className="card-name">{bankItem.cardName}</Text> */}
                        </View>
                        <View className="bank-item-card">
                            <Text className="card-no">{bankItem.cardNo}</Text>
                        </View>
                        {bankItem.sign !== 2 && <View className="no-sign-babel">未签约</View>}
                    </View>
                );
            })}

            {banks.length == 0 && (
                <View
                    className="add-btn"
                    onClick={() => {
                        queryInfo({ go: true, showError: true });
                    }}
                >
                    <Icon name={"plus"} size="30px" style={{ marginRight: "32rpx" }} />
                    添加银行卡
                </View>
            )}
        </View>
    );
};
