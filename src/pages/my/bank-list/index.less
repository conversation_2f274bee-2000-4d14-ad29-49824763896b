.bank-list {
    background-color: #f5f5f5;
    .bank-item {
        background-color: #fff;
        border-radius: 16rpx;
        position: relative;
        padding: 32rpx;
        margin: 24rpx;
        .bank-item-title {
            padding-bottom: 30rpx;
            .bank-name {
                font-size: 32rpx;
                line-height: 40rpx;
                color: #333;
            }
            .card-name {
                font-size: 24rpx;
                line-height: 40rpx;
                color: #333;
                margin-left: 32rpx;
            }
        }
        .bank-item-card {
            .card-no {
                font-size: 32rpx;
                line-height: 40rpx;
                color: #333;
            }
        }
        .no-sign-babel {
            position: absolute;
            right: 0;
            top: 0;
            padding: 16rpx 40rpx;
            font-size: 20rpx;
            color: #ff0940;
            background-color: #f4e0e0;
            // border-radius: ;
        }
    }
    .add-btn {
        background-color: #fff;
        border-radius: 16rpx;
        display: flex;
        align-items: center;
        justify-content: flex-start;
        padding: 50rpx 32rpx;
        margin: 32rpx;
    }
}
