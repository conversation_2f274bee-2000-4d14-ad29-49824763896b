import api from "@/utils/api";
import { Button, Cell, Field, Row } from "@antmjs/vantui";
import { OpenData, View, Image, Text } from "@tarojs/components";
import Taro, { useDidShow } from "@tarojs/taro";
import { useEffect, useState } from "react";
import SexModal from "./components/sex-modal";
import "./index.less";
import BirthdayModal from "./components/birthday-modal";
import DDYToast from "@/utils/toast";
import { getStorage } from "@/utils/storage";
import { USER_INFO } from "@/utils/constant";
import { DDYBack, DDYNavigateTo } from "@/utils/route";
import { PROJECT_CONFIG } from "@/utils/env";
import { isAlipay } from "@/utils/common";
import { icon_login } from "@/images";
import newAPi from "@/utils/newApi";

definePageConfig({
    backgroundTextStyle: "light",
    navigationBarTitleText: "账户设置",
});

export default () => {
    const [useInfo, setUseInfo] = useState({
        // realName: "",
        gender: 1,
        birthday: "",
        phone: "",
        avatar: "",
        nickName: "",
        verifiedStatus: false,
    });

    const [isCert, setIsCert] = useState(false);
    const [sexShow, setSexShow] = useState(false);
    const [birthdayShow, setBirthdayShow] = useState(false);

    const getUserInfo = () => {
        newAPi
            .getUserInfo({ data: {}, method: "POST" })
            .then(res => {
                console.log("getUserInfo:", res);
                let str = "";
                if (res.birthday) {
                    const dateObj = new Date(res.birthday);

                    // 获取年、月、日
                    const year = dateObj.getFullYear();
                    const month = dateObj.getMonth() + 1; // 月份从 0 开始，需要加 1
                    const day = dateObj.getDate();
                    str = `${year}-${month}-${day}`;
                }

                // 格式化为 "2003-1-1" 这种格式

                setUseInfo({
                    avatar: res.headImg,
                    // realName: res.nickname,
                    gender: res.sex ? res.sex : 1,
                    birthday: str || "2000-01-01",
                    phone: res.phone,
                    nickName: res.nickName,
                    verifiedStatus: res.verifiedStatus,
                });
            })
            .catch(err => {});
    };

    const getCertification = () => {
        api.getUserCertification({
            data: {
                id: 0,
            },
            method: "GET",
            filterCheck: true,
        }).then(res => {
            setIsCert(res);
        });
    };

    const bindAvatar = () => {
        Taro.chooseImage({
            success(result) {
                var tempFilePaths = result.tempFilePaths;
                DDYToast.showLoading("图片上传中");
                Taro.uploadFile({
                    url: `${PROJECT_CONFIG.API_MALL}/api/user/files/upload?folderId=0`, //仅为示例，非真实的接口地址
                    filePath: tempFilePaths[0],
                    name: "file",
                    success: function (res) {
                        console.log(res.data);
                        DDYToast.hideLoading();
                        try {
                            const json = JSON.parse(res.data);
                            setUseInfo({ ...useInfo, avatar: json.image });
                        } catch (error) {}
                    },
                    fail: function () {
                        DDYToast.hideLoading();
                    },
                });
            },
        });
    };

    const submitInfo = () => {
        // const userInfo2 = getStorage(USER_INFO);
        const dateStr = useInfo.birthday;

        // 将字符串转换为 Date 对象
        const dateObj = new Date(dateStr);

        // 获取时间戳（以毫秒为单位）
        const timestamp = dateObj.getTime();

        console.log(timestamp); // 输出时间戳
        newAPi
            .saveUserInfo({
                method: "POST",
                data: {
                    name: useInfo.nickName,
                    headImg: useInfo.avatar,
                    sex: useInfo.gender,
                    birthday: timestamp,
                },
                // filterCheck: true,
            })
            .then(res => {
                DDYToast.success("保存成功");
                setTimeout(() => {
                    DDYBack();
                }, 1000);
            });
    };

    const bindGetUserInfo = () => {
        if (isAlipay()) {
            DDYToast.showLoading("加载中");
            Taro.getUserInfo({
                success: function (res) {
                    DDYToast.hideLoading();
                    setUseInfo({ ...useInfo, avatar: res.userInfo.avatarUrl });
                },
                fail: function () {
                    DDYToast.hideLoading();
                },
            });
        }
    };

    useEffect(() => {
        getUserInfo();
        getCertification();
    }, []);

    return (
        <View className="setting">
            <Cell
                renderTitle={
                    <Row
                        style={{
                            display: "flex",
                            alignItems: "center",
                        }}
                    >
                        <Image
                            src={useInfo.avatar || icon_login}
                            style="marginRight:20rpx;width:90rpx;height:90rpx;border-radius:50rpx;object-fit:cover;display: inline-block;overflow:hidden;background-color:#ccc;"
                        />
                    </Row>
                }
                isLink
                value="更改头像"
                onClick={bindAvatar}
            />
            <Cell
                title="昵称"
                renderExtra={
                    <Field
                        value={useInfo.nickName}
                        style={"width: 400rpx;text-align:right;"}
                        border={false}
                        //@ts-ignore
                        type="nickname"
                        maxlength={30}
                        onChange={event => {
                            setUseInfo({ ...useInfo, nickName: event.detail });
                        }}
                    />
                }
            />
            <Cell
                title="性别"
                isLink
                value={useInfo.gender === 1 ? "男" : "女"}
                onClick={() => {
                    setSexShow(true);
                }}
            />
            <Cell
                title="生日"
                isLink
                value={useInfo.birthday}
                onClick={() => {
                    setBirthdayShow(true);
                }}
            />
            <Cell
                title="手机号码"
                isLink
                value={useInfo.phone || "未绑定手机号码"}
                onClick={() => {
                    // if (useInfo.mobile) return;
                    // DDYNavigateTo({
                    //     url: "/pages/my/bind-mobile/index?type=setting",
                    //     events: {
                    //         bindMobile: value => {
                    //             console.log(":::", value);
                    //             setUseInfo({ ...useInfo, mobile: value });
                    //         },
                    //     },
                    // });
                }}
            />
            <Cell
                title="实名认证"
                isLink
                value={useInfo.verifiedStatus ? "已认证" : "未认证 "}
                onClick={() => {
                    DDYNavigateTo({
                        url: "/pages/realname-certification/index",
                        events: {
                            changeReal: value => {
                                setIsCert(value);
                            },
                        },
                    });
                }}
            />
            <View className="bottom-btn">
                <Button
                    className={"submit-btn"}
                    style={{ marginTop: Taro.pxTransform(40) }}
                    block
                    round
                    onClick={() => {
                        submitInfo();
                    }}
                >
                    保存
                </Button>
            </View>

            {/** 弹框区域 */}

            {/** 性别选择弹框 */}
            <SexModal
                valueKey={useInfo.gender}
                show={sexShow}
                onClose={index => {
                    console.log(index);
                    setSexShow(false);
                    if (index !== undefined) {
                        setUseInfo({ ...useInfo, gender: index + 1 });
                    }
                }}
            />

            {/** 生日选择弹框 */}
            <BirthdayModal
                valueKey={useInfo.birthday}
                show={birthdayShow}
                onClose={val => {
                    setBirthdayShow(false);
                    if (val) {
                        setUseInfo({ ...useInfo, birthday: val });
                    }
                }}
            />
        </View>
    );
};
