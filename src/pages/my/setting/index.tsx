import api from "@/utils/api";
import { Button, Cell, Field, Row } from "@antmjs/vantui";
import { OpenData, View, Image, Text } from "@tarojs/components";
import Taro, { useDidShow } from "@tarojs/taro";
import { useEffect, useState } from "react";
import SexModal from "./components/sex-modal";
import "./index.less";
import BirthdayModal from "./components/birthday-modal";
import DDYToast from "@/utils/toast";
import { getStorage } from "@/utils/storage";
import { USER_INFO } from "@/utils/constant";
import { DDYBack, DDYNavigateTo } from "@/utils/route";
import { PROJECT_CONFIG } from "@/utils/env";
import { isAlipay } from "@/utils/common";
import { icon_login } from "@/images";

definePageConfig({
    backgroundTextStyle: "light",
    navigationBarTitleText: "账户设置",
});

export default () => {
    const [useInfo, setUseInfo] = useState({
        realName: "",
        gender: 1,
        birthday: "",
        mobile: "",
        avatar: "",
        username: "",
    });

    const [isCert, setIsCert] = useState(false);
    const [sexShow, setSexShow] = useState(false);
    const [birthdayShow, setBirthdayShow] = useState(false);

    const getUserInfo = () => {
        api.getUserInfo({ data: {}, filterCheck: true })
            .then(res => {
                setUseInfo({
                    avatar: res.avatar,
                    realName: res.realName,
                    gender: res.gender ? res.gender : 1,
                    birthday: res.birthday ? res.birthday : "2000-01-01",
                    mobile: res.mobile,
                    username: res.username,
                });
            })
            .catch(err => {});
    };

    const getCertification = () => {
        api.getUserCertification({
            data: {
                id: 0,
            },
            method: "GET",
            filterCheck: true,
        }).then(res => {
            setIsCert(res);
        });
    };

    const bindAvatar = () => {
        Taro.chooseImage({
            success(result) {
                var tempFilePaths = result.tempFilePaths;
                DDYToast.showLoading("图片上传中");
                Taro.uploadFile({
                    url: `${PROJECT_CONFIG.API_MALL}/api/user/files/upload?folderId=0`, //仅为示例，非真实的接口地址
                    filePath: tempFilePaths[0],
                    name: "file",
                    success: function (res) {
                        console.log(res.data);
                        DDYToast.hideLoading();
                        try {
                            const json = JSON.parse(res.data);
                            setUseInfo({ ...useInfo, avatar: json.image });
                        } catch (error) {}
                    },
                    fail: function () {
                        DDYToast.hideLoading();
                    },
                });
            },
        });
    };

    const submitInfo = () => {
        const userInfo2 = getStorage(USER_INFO);
        api.saveUserInfo({
            method: "PUT",
            data: {
                realName: useInfo.realName,
                mobile: useInfo.mobile,
                gender: useInfo.gender,
                birthday: useInfo.birthday,
                avatar: useInfo.avatar || userInfo2.avatarUrl,
            },
            filterCheck: true,
        }).then(res => {
            DDYToast.success("保存成功");
            setTimeout(() => {
                DDYBack();
            }, 1000);
        });
    };

    const bindGetUserInfo = () => {
        if (isAlipay()) {
            DDYToast.showLoading("加载中");
            Taro.getUserInfo({
                success: function (res) {
                    DDYToast.hideLoading();
                    setUseInfo({ ...useInfo, avatar: res.userInfo.avatarUrl });
                },
                fail: function () {
                    DDYToast.hideLoading();
                },
            });
        }
    };

    useEffect(() => {
        getUserInfo();
        getCertification();
    }, []);

    return (
        <View className="setting">
            <Cell
                renderTitle={
                    <Row
                        style={{
                            display: "flex",
                            alignItems: "center",
                        }}
                    >
                        <Image
                            src={useInfo.avatar || icon_login}
                            style="marginRight:20rpx;width:90rpx;height:90rpx;border-radius:50rpx;object-fit:cover;display: inline-block;overflow:hidden;background-color:#ccc;"
                        />
                        <Text>{useInfo.username}</Text>
                    </Row>
                }
                isLink
                value="更改头像"
                onClick={bindAvatar}
            />
            <Cell
                title="昵称"
                renderExtra={
                    <Field
                        value={useInfo.realName}
                        style={"width: 400rpx;text-align:right;"}
                        border={false}
                        //@ts-ignore
                        type="nickname"
                        onChange={event => {
                            setUseInfo({ ...useInfo, realName: event.detail });
                        }}
                    />
                }
            />
            <Cell
                title="性别"
                isLink
                value={useInfo.gender === 1 ? "男" : "女"}
                onClick={() => {
                    setSexShow(true);
                }}
            />
            <Cell
                title="生日"
                isLink
                value={useInfo.birthday}
                onClick={() => {
                    setBirthdayShow(true);
                }}
            />
            <Cell
                title="手机绑定"
                isLink
                value={useInfo.mobile || "未绑定手机号码"}
                onClick={() => {
                    if (useInfo.mobile) return;
                    DDYNavigateTo({
                        url: "/pages/my/bind-mobile/index?type=setting",
                        events: {
                            bindMobile: value => {
                                console.log(":::", value);
                                setUseInfo({ ...useInfo, mobile: value });
                            },
                        },
                    });
                }}
            />
            <Cell
                title="实名认证"
                isLink
                value={isCert ? "已认证" : "未认证 "}
                onClick={() => {
                    DDYNavigateTo({
                        url: "/pages/realname-certification/index",
                        events: {
                            changeReal: value => {
                                setIsCert(value);
                            },
                        },
                    });
                }}
            />
            <View className="bottom-btn">
                <Button
                    block
                    style={{ margin: "40rpx 24rpx", width: "702rpx" }}
                    round
                    onClick={() => {
                        submitInfo();
                    }}
                >
                    保存
                </Button>
            </View>

            {/** 弹框区域 */}

            {/** 性别选择弹框 */}
            <SexModal
                valueKey={useInfo.gender}
                show={sexShow}
                onClose={index => {
                    console.log(index);
                    setSexShow(false);
                    if (index !== undefined) {
                        setUseInfo({ ...useInfo, gender: index + 1 });
                    }
                }}
            />

            {/** 生日选择弹框 */}
            <BirthdayModal
                valueKey={useInfo.birthday}
                show={birthdayShow}
                onClose={val => {
                    setBirthdayShow(false);
                    if (val) {
                        setUseInfo({ ...useInfo, birthday: val });
                    }
                }}
            />
        </View>
    );
};
