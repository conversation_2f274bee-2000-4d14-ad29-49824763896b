import { ActionSheet, Picker } from "@antmjs/vantui";

const columns = ["男", "女"];
export default ({ show, onClose, valueKey }) => {
    const onChange = event => {
        const { value, index } = event.detail;
        onClose(index || 0);
    };
    return (
        <ActionSheet show={show} onClose={() => onClose()}>
            <Picker
                columns={columns}
                valueKey={(valueKey - 1).toString()}
                // onChange={onChange}
                onConfirm={onChange}
                onCancel={() => onClose()}
            />
        </ActionSheet>
    );
};
