import { ActionSheet, DatetimePicker, Picker } from "@antmjs/vantui";
import { View } from "@tarojs/components";
import { useCallback, useEffect, useState } from "react";

// const columns = ["男", "女"]
export default ({ show, onClose, valueKey }) => {
    const [date, setDate] = useState(new Date(1960, 0, 0).getTime());
    const [currentDate, setCurrentDate] = useState<any>();
    const formatter = useCallback((type, value) => {
        if (type === "year") {
            return `${value}年`;
        }

        if (type === "month") {
            return `${value}月`;
        }

        return value;
    }, []);

    const onInput = event => {
        console.log(event.detail);
        setCurrentDate(event.detail.value);
        // const selectDate = new Date(event.detail.value);
        // const result = selectDate.getFullYear() + "-" + (selectDate.getMonth() + 1) + "-" + selectDate.getDate();
        const chineseDate = event.detail.value;
        const formattedDate = chineseDate.replace(/年|月/g, "-").replace(/-+/g, "-");
        onClose(formattedDate);
    };

    useEffect(() => {
        if (valueKey) {
            const arr = valueKey.split("-");
            setCurrentDate(new Date(arr[0], arr[1] - 1, arr[2]).getTime());
        }
    }, [valueKey]);

    return (
        <ActionSheet show={show} onClose={() => onClose()}>
            <DatetimePicker
                type="date"
                value={currentDate}
                minDate={date}
                // onInput={onInput}
                formatter={formatter}
                onConfirm={onInput}
            />
        </ActionSheet>
    );
};
