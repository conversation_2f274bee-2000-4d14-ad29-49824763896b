import api from "@/utils/api";
import { Form, Button, Dialog, FormRender, Field, CountDown } from "@antmjs/vantui";
import { View } from "@tarojs/components";
import { useRouter } from "@tarojs/taro";
import { useRef, useState } from "react";
import "./index.less";
import { DDYNavigateTo } from "@/utils/route";
import DDYToast from "@/utils/toast";
import { queryInfo } from "@/utils/common";

definePageConfig({
    backgroundTextStyle: "light",
    navigationBarTitleText: "",
});

export default () => {
    const route = useRouter();
    const [phone, setPhone] = useState("");
    const [verifyCode, setVerifyCode] = useState("");
    const [isSend, setIsSend] = useState(false);
    const isClick = useRef(false);

    const sendCode = () => {
        if (!phone) {
            DDYToast.info("请输入手机号码");
            return;
        }
        setIsSend(true);
        api.sendPhoneCode({
            method: "POST",
            contentType: "application/json",
            data: {
                phone: phone,
                verificationCodeType: 9, // 验证码类型 9-绑定手机，6-解绑手机
            },
            filterCheck: true,
            showLoad: true,
        }).then(res => {
            if (res.success) {
                DDYToast.info("发送成功");
                isClick.current = true;
            } else {
                DDYToast.info(res.errorMsg);
            }
        });
    };
    const bindMobile = () => {
        if (!phone) {
            DDYToast.info("请输入手机号码");
            return;
        }
        if (!verifyCode) {
            DDYToast.info("请输入验证码");
            return;
        }
        api.bindingPhone({
            method: "POST",
            contentType: "application/json",
            data: {
                phone: phone,
                verificationCode: verifyCode,
            },
            showLoad: true,
        }).then(res => {
            queryInfo({ go: true });
        });
    };

    return (
        <View className="bind-bank-phone">
            <View className="page-head">
                <View className="page-title">绑定手机号</View>
                {/* <View className="page-info">验证银行预留手机号</View> */}
            </View>
            <Field
                value={phone}
                placeholder="请输入手机号"
                label="手机号+86"
                onChange={e => {
                    setPhone(e.detail);
                }}
                // errorMessage="手机号格式错误"
                border={false}
            />
            <Field
                center
                clearable
                label="验证码"
                placeholder="请输入验证码"
                border
                value={verifyCode}
                onChange={e => {
                    setVerifyCode(e.detail);
                }}
                renderButton={
                    <>
                        {isSend ? (
                            <CountDown
                                millisecond
                                time={60 * 1000}
                                format="已发送(sss)"
                                onFinish={() => {
                                    console.log("onFinish");
                                    setIsSend(false);
                                }}
                            />
                        ) : (
                            <Button
                                size="small"
                                type="primary"
                                // style={{ backgroundColor: "#ccc", color: "#333" }}
                                onClick={() => {
                                    sendCode();
                                }}
                            >
                                发送验证码
                            </Button>
                        )}
                    </>
                }
            />
            <Button
                type="primary"
                className="btn-next"
                // block
                round
                onClick={() => {
                    if (!isClick.current) {
                        DDYToast.info("请先发送验证码");
                        return;
                    }
                    bindMobile();
                }}
            >
                提交
            </Button>
        </View>
    );
};
