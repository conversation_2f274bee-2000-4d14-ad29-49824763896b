import api from "@/utils/api";
import { But<PERSON> } from "@antmjs/vantui";
import { View, WebView, Text, ScrollView, RichText } from "@tarojs/components";
import Taro, { useDidShow } from "@tarojs/taro";
import { useEffect, useRef, useState } from "react";
import "./index.less";
import DDYToast from "@/utils/toast";
import { getStorage, setStorage } from "@/utils/storage";
import { DDYBack, DDYBackToURL } from "@/utils/route";
import { isAlipay, removeSignback } from "@/utils/common";
import { IS_SIGN_SUCCESS, SING_BACK_URL } from "@/utils/constant";

export default () => {
    let [time, setTime] = useState(5);
    const timer = useRef<NodeJS.Timeout>();
    const loopTimer = useRef<NodeJS.Timeout>();
    const sign = () => {
        api.getSignUrl({
            data: {},
            method: "POST",
        }).then(res => {
            if (isAlipay()) {
                try {
                    //@ts-ignore
                    my.ap.openURL({
                        url: res,
                        success(res) {
                            // 打开成功
                            console.log(res);
                        },
                        fail(err) {
                            // 打开成功
                            console.log(err);
                        },
                    });
                } catch (error) {
                    console.log(error);
                }

                return;
            }

            Taro.navigateToMiniProgram({
                appId: "wxc46c6d2eed27ca0a", //通联小程序 appId
                path: "pages/merchantAddress/merchantAddress", //通联小程序页面路径
                extraData: {
                    targetUrl: res,
                },
                envVersion: "release", //develop 开发版 trial 体验版 release 正式版
                success(res) {
                    // 打开成功
                    console.log(res);
                },
                fail(res) {
                    // 打开成功
                    console.log(res);
                },
            });
        });
    };

    const onScroll = e => {
        const { scrollTop, scrollHeight } = e.detail;
        if (scrollTop + 400 + 20 > scrollHeight) {
            setTime(0);
            clearTimeout(timer.current);
        }
    };

    useEffect(() => {
        timer.current = setTimeout(() => {
            if (time <= 0) {
                clearTimeout(timer.current);
                return;
            }
            let newTime = time - 1;
            setTime(newTime);
        }, 1000);
    }, [time]);

    /** 门店角色通商云签约成功后需要轮询签约状态 */
    const loopQuery = () => {
        loopTimer.current && clearInterval(loopTimer.current);
        let timers = 5;
        loopTimer.current = setInterval(() => {
            if (timers <= 0) {
                clearInterval(loopTimer.current);
            }
            timers--;
            api.querySignStatus({
                method: "POST",
                data: {},
                contentType: "application/json",
                filterCheck: true,
                showError: false,
            }).then(res => {
                // console.log("res:", res)
                if (res.data === 2) {
                    clearInterval(loopTimer.current);
                    DDYToast.success("你已签约成功");
                    setStorage(IS_SIGN_SUCCESS, true);
                    setTimeout(() => {
                        const signBackUrl = getStorage(SING_BACK_URL);
                        removeSignback();
                        DDYBackToURL(signBackUrl);
                    }, 1000);
                }
            });
        }, 1000);
    };
    useDidShow(() => {
        loopQuery();
    });

    return (
        <View className="page">
            <View className="container">
                <ScrollView
                    className=""
                    style={{ height: "400px", overflow: "hidden" }}
                    onScroll={onScroll}
                    scrollY={true}
                >
                    <RichText
                        nodes={`<div style="padding:20px;">
                <h2 style="text-align: center; padding: 20px;" }}>门店推广服务协议</h2>
                <p>甲方：杭州显志电子商务有限公司</p>
                <p>注册地址：浙江省杭州市上城区九盛路 9 号 25 幢 A 座 3 楼 302 室</p>
                <p>乙方：</p>
                <p>身份证号码：</p>
                <p>联系电话：</p>
                <p>甲乙双方根据《中华人民共和国民法典》及其他相关法律、法规之规定，经友好
                    协商，就乙方为甲方提供商品推广服务事宜签订本协议，以资共同信守。</p>
                <p>1.推广服务事项</p>
                <p>1.1.乙方作为推广方，为甲方商品做市场推广，寻找、介绍客户，并指导客户在
                    甲方指定的 跨境电子商务 平台下单购买商品。</p>
                <p> 1.2.区域与推广渠道限制： 无特殊限制 ;符合本协议约定条件时，乙方即可获
                    取推广服务费。</p>
                <p>1.3.双方确认：乙方提供的是非独家服务，甲方有权同时委托第三方提供服务。</p>
                <p>2.服务期限</p>
                <p>服务期限：协议的有效期为 壹 年，从协议签署之日生效。如果在该有效期到期
                    或在任何延长期到期前十五天内，任何一方均未提出终止本协议，本协议将自动
                    延期 壹 年，以此类推。</p>
                <p>3.服务费</p>
                <p>3.1.双方同意按本协议约定的标准向乙方结算推广服务费。乙方介绍的客户下单
                    后退货，且产生退款的，相应商品甲方无需支付乙方推广服务费(已支付应予以
                    退还)。</p>
                <p>3.2.结算周期：按订单为确认收货状态结算。订单确认收货后，乙方核对订单产
                    生的推广服务费，双方对结算数据确认无误后，甲方于 5 日内将推广服务费打
                    至乙方账户</p>
                <p> 3.3 结算数据：以跨境平台通关成功的数据为依据结算。</p>
                <p>3.4.乙方指定收款账号：以跨境平台绑定的账户信息为依据。</p>
                <p>4.服务费支付的其他约定</p>
                <p>4.1.非乙方介绍的客户，乙方不得要求支付推广服务费。</p>
                <p>4.2.服务期限届满或提前解除后，乙方向甲方介绍客户的，甲方无需支付服务费，双方另有约定的除外。</p>
                <p>4.3.双方对乙方出现的违约行为有扣款、赔偿或违约金约定的，甲方有权从应付
                    推广服务费中直接予以扣除。</p>
                <p>5.其他费用</p>
                <p>无论推广是否成功，乙方是否取得推广服务费，乙方在推广服务过程中所支出的
                    其他费用均由乙方自行承担。</p>
                <p>6.甲方权利与义务</p>
                <p>6.1.甲方应根据乙方要求提供商品相关资料。</p>
                <p>6.2.甲方有权监督乙方的推广行为，对于不符合要求的，有权要求改正。</p>
                <p>6.3.甲方有权自行决定与乙方介绍的客户的交易事宜。</p>
                <p>6.4.甲方负责向乙方介绍的客户提供商品发货、售后服务。</p>
                <p>7.乙方权利与义务</p>
                <p>7.1.乙方在履行本协议的过程中，应本着诚信、专业、高效的职业精神，通过在
                    各行业的丰富资源，尽力为甲方推广商品。</p>
                <p>7.2.乙方应按照甲方提供的资料以及甲方的要求向客户推荐甲方商品，不得有夸
                    大、虚假的推广行为。</p>
                <p>7.3.甲方对乙方推广行为提出合理改正意见的，乙方应予执行。</p>
                <p>7.4.乙方有义务向甲方汇报说明推广情况，不得向甲方提供虚假信息。</p>
                <p>7.5.乙方需主动宣导消费者购买须知：</p>
                <p>（1）相关商品符合原产地有关质量、安全、卫生、环保、标识等法规标准或技
                    术规范要求，但可能与中国法规标准或技术规范要求存在差异。消费者需自行承
                    担在购买和使用产品过程中可能由此产生的危害、损失或其他风险。</p>
                <p> （2）相关商品直接购自境外，可能无中文标筌或中文标签不完整，消费者可通
                    过跨境电商以及其平台网站查看商品中文电子标及。中文电子标签内容系根据英
                    文标签内容翻译。标签内容系根据原产地法律法规和监管要求制作，不一定完全
                    符合中国法律法规和监管要求。如对标签内容有疑问，请与商家联系确认。</p>
                <p> （3）根据中国相关法律法规和监管要求，消费者购买的商品仅限个人自用，不
                    得再次销售。</p>
                <p>（4）消费者购买跨境保税商品时须如实填写相关信息，以便进行清关出库。</p>
                <p>（5）消费者购买的跨境保税商品，商品已在海关备案，无需国食注册号，按个
                    人自用品进境物品监管，不执行有关商品首次进口许可批件、注册或备案需求，
                    无购物小票或发票，线上下单即可以享受售后保障。</p>
                <p>8.双方关系</p>
                <p>8.1.本协议的签订及履行，不代表双方建立劳动关系、劳务关系、劳务派遣关系、
                    雇佣关系或类似关系，甲方不向乙方承担任何雇主或用人单位性质的责任。</p>
                <p>8.2.乙方在服务过程中造成自身或第三方任何人身、财产损害，应由乙方自行承
                    担责任，甲方不承担责任。</p>
                <p>9.保密</p>
                <p>9.1.乙方保证对在讨论、签订、履行本协议过程中所获悉的属于甲方及甲方关联
                    方的且无法自公开渠道的文件及资料(包括但不限于商业秘密、公司计划、运营
                    活动、财务信息、技术信息、经营信息及其他商业秘密)予以保密。未经甲方同
                    意，乙方不得超出本协议约定的目的和范围使用该商业秘密，不得向任何第三方
                    泄露该商业秘密的全部或部分内容</p>
                <p>9.2.上述保密义务，在本协议终止或解除之后仍需履行。</p>
                <p>11.违约责任</p>
                <p>11.1.任一方违反本协议约定给守约方造成损失的，应赔偿对守约方造成的全部
                    损失。</p>
                <p>11.2.任一方发生本协议约定的任一违约行为，并在接到守约方通知后 7 日内不
                    予继续履行或予以纠正的，守约方有权解除本协议，并有权要求违约方赔偿守约
                    方由此造成的全部损失。</p>
                <p>11.3.乙方需遵守《中华人民共和国电子商务法》的规定，不得针对推广商品进
                    行二次销售，任何出现的二次销售的行为均属于乙方行为，甲方有权利在不需要
                    书面通知和乙方签字确认的情况下解除协议，后续因二次销售产生的任何法律责
                    任由乙方承担。</p>
                <p>12.其他</p>
                <p>12.1.不可抗力</p>
                <p>12.1.1.不可抗力定义：指地震、台风、水灾、火灾、战争、国际或国内运输中
                    断、疫情等在本协议签署后发生的、本协议签署时不能预见的、其发生与后果是
                    无法避免或克服的、妨碍任何一方全部或部分履约的所有事件。</p>
                <p>12.1.2.不可抗力的后果：
                    受不可抗力影响的一方应及时书面通知另一方，并尽力减少另一方损失。如因一
                    方怠于通知而造成另一方损失的扩大，一方应就该扩大的损失承担全部赔偿责任。
                    在此前提下，一方在本协议项下受不可抗力影响的义务在不可抗力造成的延误期
                    间自动中止，并且其履行期限应自动延长，延长期间为中止的期间，该方无须为
                    此承担违约责任。</p>
                <p>13.争议解决</p>
                <p>因本协议以及本协议项下订单/附件/补充协议等(如有)引起或有关的任何争议，
                    由协议各方协商解决，协商不成的，应向甲方所在地有管辖权的人民法院起诉。</p>
                <p>14.附则</p>
                <p>15.1.本协议一式二份，协议各方各执一份，具有同等法律效力。</p>
                <p>15.2.本协议未尽事宜，双方应另行协商并签订补充协议。</p>
                <p>15.3.本协议经各方签名或盖章后生效。</p>
                <p>甲方（签章）： 杭州显志电子商务有限公司 乙方（签章）：</p>
                <p>签订日期： 签订日期：</p></div>
                `}
                    />
                </ScrollView>
                <Button
                    round
                    style={{ margin: "30rpx auto", display: "flex", width: "50%", border: "2rpx solid #333" }}
                    disabled={time > 0}
                    onClick={() => {
                        sign();
                    }}
                >
                    {time > 0 ? `已阅读，去签约（${time}）s` : "已阅读，去签约"}
                </Button>
            </View>
        </View>
    );
};
