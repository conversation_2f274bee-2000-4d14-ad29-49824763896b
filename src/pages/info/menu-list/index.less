.menu-list {
    margin-top: 20px;

    .custome-cell-title {
        display: flex;

        .open-button {
            position: absolute;
            height: 89px;
            width: 90%;
            opacity: 0;
        }
    }
    .info-menu {
        position: relative;
        padding: var(--grid-item-content-padding, 32rpx 16rpx);
        .open-button {
            position: absolute;
            height: 100%;
            width: 100%;
            opacity: 0;
            top: 0;
            right: 0;
        }
        .van-grid-item__content {
            padding: 0;
            position: relative;
        }
    }
    .text-icon {
        color: #f51240;
        font-size: 48px;
        display: inline-block;
        margin-top: 16px;
        position: relative;
    }
    .text-icon-list {
        font-size: 32px;
        color: rgb(203, 204, 205);
        margin-right: 15px;
        display: inline-block;
    }
}
