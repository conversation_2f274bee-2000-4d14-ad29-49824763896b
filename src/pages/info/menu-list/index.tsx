import { View, Text } from "@tarojs/components";
import { Button, Cell, CellGroup, CellProps, Grid, GridItem, Icon } from "@antmjs/vantui";
import { useMemo, useState } from "react";
import { DDYNavigateTo } from "@/utils/route";
import "./index.less";
import identityStore from "../../../store/identity-store";
import Taro, { pxTransform, useDidShow } from "@tarojs/taro";
import { isAlipay, queryInfo } from "@/utils/common";
import { getSessionId } from "@/utils/server/session";
import { jumpLogin } from "@/utils/PageUtils";
import { observer } from "mobx-react-lite";
import { toJS } from "mobx";
import infoStore from "@/store/info-store";
import DDYToast from "@/utils/toast";

const MenuList = observer(() => {
    const [dot, setDot] = useState(false);
    const buyerMenuList = useMemo(() => {
        return [
            // {
            //     title: "优惠券",
            //     url: "/pages/coupon/index",
            //     icon: "icon-coupon-2-line",
            // },
            {
                title: "我的收藏",
                url: "/pages/collect/index",
                icon: "icon-star",
            },
            {
                title: "实名认证",
                url: "/pages/realname-certification/index",
                icon: "icon-shield-user-line",
            },
            {
                title: "收货地址",
                url: "/pages/address/index",
                icon: "icon-map-pin-line",
            },
            {
                title: "发票中心",
                url: "/pages/invoice/center/index",
                icon: "icon-file-list-3-line",
            },
            {
                title: "账号设置",
                url: "/pages/my/setting/index",
                icon: "icon-settings-line",
            },
            {
                title: "品牌授权书",
                url: "/pages/attorney/index",
                icon: "icon-file-shield-fill",
            },
            {
                title: "我的邀请码",
                url: "",
                icon: "icon-file-shield-fill",
            },
            {
                title: "在线客服",
                openType: "contact",
                icon: "icon-customer-service-fill",
            },
            {
                title: "修改密码",
                url: "/pages/login/reset-psw",
                icon: "icon-lock-line",
            },
        ];
    }, []);
    const otherRoleMenuList = useMemo(() => {
        return [
            {
                title: "优惠券",
                url: "/pages/coupon/index",
                icon: "icon-coupon-2-line",
            },
            {
                title: "发票中心",
                url: "/pages/invoice/center/index",
                icon: "icon-file-list-3-line",
            },
            {
                title: "品牌授权书",
                url: "/pages/attorney/index",
                icon: "icon-file-ppt-line",
            },
            {
                title: "消息中心",
                url: "/pages/notification/index",
                icon: "icon-message-line",
            },
            {
                title: "在线客服",
                openType: "contact",
                icon: "icon-customer-service-line",
            },
            {
                title: "协议条款",
                url: "/pages/privacy-policy/index",
                icon: "icon-draft-line",
            },
            {
                title: "实名认证",
                url: "/pages/realname-certification/index",
                icon: "icon-shield-user-line",
            },
            {
                title: "对账单",
                url: "/pages/withdrawal/account-statement/index",
                icon: "icon-file-list-3-line",
            },
            {
                title: "门店信息",
                url: "/pages/store-list/shop-info/index",
                icon: "icon-store-line",
            },
            {
                title: "提现银行卡",
                url: "/pages/my/bank-list/index",
                icon: "icon-yinhangka_huaban",
                dot: dot,
            },
            {
                title: "陈列打卡",
                url: "/pages/my/shop-clock/index",
                icon: "icon-calendar-2-line",
            },
        ].filter(item => {
            return !(
                ((item.title === "对账单" || item.title === "门店信息" || item.title === "提现银行卡") &&
                    !identityStore.isSubStore()) ||
                (item.title === "在线客服" && isAlipay()) ||
                (item.title === "陈列打卡" && !identityStore.isGuider() && !identityStore.isSubStore())
            );
        });
    }, [identityStore.identity, dot]);

    useDidShow(() => {
        if (identityStore.isSubStore()) {
            queryInfo({
                go: false,
                callback(data) {
                    console.log("info-data:", data);
                    if (
                        data.accountInfo.signContractStatus !== 2 ||
                        !data.accountInfo.stepBindAuth ||
                        !data.accountInfo.stepBindFirstCard ||
                        !data.accountInfo.stepBindPhone
                    ) {
                        setDot(true);
                    } else {
                        setDot(false);
                    }
                },
            });
        }
        // console.log("infoStore:", toJS(infoStore))
    });
    const info = toJS(infoStore.newUserProfile);
    return (
        <View className="menu-list">
            <CellGroup inset>
                {/*消费者菜单*/}
                {identityStore.isBuyer() &&
                    buyerMenuList.map(item => {
                        let render: CellProps = {};
                        if (item.title === "在线客服") {
                            render.renderTitle = (
                                <View className="custome-cell-title">
                                    在线客服
                                    <Button className="open-button" openType="contact">
                                        在线客服
                                    </Button>
                                </View>
                            );
                            if (isAlipay()) {
                                return null;
                            }
                        } else if (item.title === "我的邀请码") {
                            return (
                                <Cell
                                    isLink
                                    icon={"friends-o"}
                                    title={item.title}
                                    value={infoStore.newUserProfile?.inviteCode}
                                    onClick={() => {
                                        Taro.setClipboardData({
                                            data: infoStore.newUserProfile?.inviteContent || "邀请码获取失败",
                                            success: function (res) {
                                                DDYToast.info("复制邀请码成功");
                                            },
                                        });
                                    }}
                                />
                            );
                        }
                        return (
                            <Cell
                                isLink
                                renderIcon={<Icon name={` ${item.icon} text-icon-list`} classPrefix={"iconfont"} />}
                                title={!item.openType ? item.title : ""}
                                {...(item.openType && render)}
                                onClick={() => {
                                    if (!getSessionId() && item.url !== "/pages/attorney/index") {
                                        jumpLogin();
                                        return;
                                    }
                                    if (item.title === "修改密码") {
                                        DDYNavigateTo({
                                            url: item.url + "?phone=" + info.phone + "&title=修改密码",
                                        });
                                        return;
                                    }
                                    item.url && DDYNavigateTo({ url: item.url });
                                }}
                            />
                        );
                    })}
                {/* 门店、导购、服务商菜单*/}
                {!identityStore.isBuyer() && (
                    <View>
                        <Grid columnNum="4" border={false}>
                            {otherRoleMenuList.map((item, index) => (
                                <GridItem
                                    className={"info-menu"}
                                    onClick={() => {
                                        DDYNavigateTo({ url: item.url });
                                    }}
                                    key={index}
                                    iconPrefix={"iconfont"}
                                    icon={` ${item.icon} text-icon`}
                                    text={item.title}
                                    dot={item.dot}
                                >
                                    {item.openType && (
                                        <>
                                            <View className={"van-grid-item__icon icon-class"}>
                                                <Icon name={` ${item.icon} text-icon`} classPrefix={"iconfont"} />
                                            </View>
                                            <View className={"van-grid-item__text text-class"}>
                                                <Text>{item.title}</Text>
                                                <Button className="open-button" openType="contact">
                                                    在线客服
                                                </Button>
                                            </View>
                                        </>
                                    )}
                                </GridItem>
                            ))}
                        </Grid>
                    </View>
                )}
            </CellGroup>
            <View style={{ height: pxTransform(24) }} />
        </View>
    );
});
export default MenuList;
