import { View } from "@tarojs/components";
import { CellGroup, Icon } from "@antmjs/vantui";
import { observer } from "mobx-react-lite";
import "./index.less";
import identityStore from "../../../store/identity-store";
import infoStore from "../../../store/info-store";
import { DDYNavigateTo } from "@/utils/route";

const Income = observer(() => {
    return (
        <CellGroup inset className={"income-box"}>
            <View className="income-top">
                <View className={"income-title"}>
                    {identityStore.isSubStore()
                        ? "门店收益"
                        : identityStore.isServiceProvider()
                        ? "服务商收益"
                        : "我的收益"}
                </View>
                <View
                    className={"income-icon"}
                    onClick={() => {
                        DDYNavigateTo({ url: "/pages/store-revenue-detail/index" });
                    }}
                >
                    查看数据 <Icon name={"arrow"} />
                </View>
            </View>
            <View className="income-bottom">
                <View className="bottom-box">
                    <View className="income-num">{infoStore.profitData.todayProfitAmount || 0.0}</View>
                    <View className="income-detail">{identityStore.isServiceProvider() ? "今日奖励" : "今日推广"}</View>
                </View>
                <View className="bottom-box">
                    <View className="income-num">
                        {identityStore.isSubStore()
                            ? infoStore.profitData.todayTotalGuiderProfitAmount || 0
                            : infoStore.profitData.currentMonthProfitAmount || 0}
                    </View>
                    <View className="income-detail">
                        {identityStore.isSubStore() ? "今日店长奖励" : "本月预估收入"}
                    </View>
                </View>
            </View>
        </CellGroup>
    );
});
export default Income;
