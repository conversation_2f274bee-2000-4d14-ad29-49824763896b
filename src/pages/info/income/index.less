.income-box {
    overflow: hidden;
    border-radius: 16px;
    margin-top: 24px;
    background: #ffffff;
    .income-top {
        display: flex;
        margin-top: 24px;
        position: relative;
        .income-title {
            margin-left: 24px;
            font-size: 28px;
            line-height: 28px;
        }
        .income-icon {
            position: absolute;
            display: flex;
            right: 24px;
            font-size: 24px;
            font-weight: 400;
            color: #666666;
            line-height: 24px;
        }
    }
    .income-bottom {
        display: flex;
        .bottom-box {
            flex: 1;
            margin-top: 24px;
            .income-num {
                font-size: 40px;
                font-weight: bold;
                line-height: 80px;
                margin-left: 24px;
            }
            .income-detail {
                margin-left: 24px;
                font-size: 24px;
                font-weight: 400;
                line-height: 24px;
                margin-bottom: 40px;
            }
        }
    }
}
