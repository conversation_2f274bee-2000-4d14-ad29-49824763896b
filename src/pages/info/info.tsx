import { View } from "@tarojs/components";
import Taro, { useDidShow, useLoad } from "@tarojs/taro";
import { observer } from "mobx-react-lite";
import Header from "./header";
import MenuList from "./menu-list";
import Income from "./income";
import Balance from "./balance";
import UserList from "./user-list";
import Achievement from "./achievement";
import MyOrder from "./my-order";
import "./index.less";
import infoStore from "../../store/info-store";
import identityStore, { IdentityResponse } from "../../store/identity-store";
import { DDYObject } from "../../type/common";
import api from "@/utils/api";
import { getStorage, removeStorage, setStorage } from "@/utils/storage";
import Toast from "@/utils/toast";
import { useState } from "react";
import InviteAddGuider from "./invite-add-guider";
import { scene_decode } from "@/utils/common";
import { localLoginMask, reLogin } from "@/utils/ylogin";
import { useInfoDisplay } from "./hook/useInfoDisplay";
import { mall_event, mall_event_key, reportEvent } from "@/utils/track-report";

const Info = observer(() => {
    const [isShowAddGuider, setIsShowAddGuider] = useState(false);
    const [isLogin, setIsLogin] = useState(false);
    useLoad((param: DDYObject) => {
        const { scene } = param;
        // @ts-ignore
        let { inviteSubStoreId } = scene_decode(scene, "&", "=");
        if (!inviteSubStoreId) {
            //支付宝扫码需要从启动函数拿
            const query = Taro.getLaunchOptionsSync().query;
            if (query && query.inviteSubStoreId) {
                inviteSubStoreId = query.inviteSubStoreId;
            }
        }
        console.log("inviteSubStoreId", inviteSubStoreId);
        reportEvent(
            mall_event.MALL_EVENT_SCAN_GUIDER_MINI_QR,
            new Map()
                .set(mall_event_key.MALL_KEY_INVITE_SUBSTORE_ID, inviteSubStoreId)
                .set(mall_event_key.MALL_KEY_INVITE_SCENE, scene),
        );
        if (inviteSubStoreId) {
            api.getOrSetStoreInfo({
                data: {
                    sourceId: getStorage("storeId"),
                    id: inviteSubStoreId,
                },
                filterCheck: true,
            }).then(res => {
                setStorage("inviteSubStoreName", res.name);
                setStorage("inviteSubStoreId", inviteSubStoreId);
            });
        }
    });

    function handleDisplayName(userProfile: any, identityInfo: IdentityResponse | void) {
        let nickName = "";
        if (identityStore.isBuyer()) {
            if (userProfile) {
                if (!userProfile.realName || userProfile.realName == "微信用户") {
                    nickName = userProfile.username;
                } else {
                    nickName = userProfile.realName;
                }
            }
        } else {
            if (identityStore.isSubStore()) {
                nickName = identityInfo?.subStoreOwner?.name || "微信用户";
            } else if (identityStore.isServiceProvider()) {
                nickName = identityInfo?.serviceProviderInfo?.name || "微信用户";
            } else if (identityStore.isGuider()) {
                nickName = identityInfo?.guider?.storeGuiderNickname || "微信用户";
            }
        }
        infoStore.nickName = nickName;
    }

    useDidShow(async () => {
        let hasLogin = localLoginMask();
        let hold = await api.loginHold({ data: {}, showError: true, filterCheck: true });
        //如果信息过期,且以前登录过，则自动登录，以保持登录
        if (!hold && hasLogin) {
            try {
                await reLogin();
            } catch (e) {
                hasLogin = false;
            }
        }
        setIsLogin(hasLogin);
        if (hasLogin) {
            try {
                let getUserProfile = await infoStore.getUserProfile();
                let getIdentityInfo = await identityStore.getIdentityInfo();
                handleDisplayName(getUserProfile, getIdentityInfo);

                if (getStorage("inviteSubStoreId")) {
                    if (identityStore.isBuyer()) {
                        setIsShowAddGuider(true);
                    } else {
                        removeStorage("inviteSubStoreId");
                        Toast.info("您不属于消费者身份，无法成为导购");
                    }
                }
            } catch (e) {
                console.log("getUserInfo error:", e);
            }
            infoStore.getBalances();
            infoStore.getShopInfo();
            infoStore.getStoreInfo(false);
            infoStore.getProfitStatistics();
        }
    });
    const { showBalance, showGuiderList, showStoreList } = useInfoDisplay();
    return (
        <View
            className={"person-info"}
            style={{
                backgroundImage: `url(${
                    identityStore.isBuyer()
                        ? "https://dante-img.oss-cn-hangzhou.aliyuncs.com/55497726278.png"
                        : "https://dante-img.oss-cn-hangzhou.aliyuncs.com/26530320390.png"
                })`,
            }}
        >
            <Header isLogin={isLogin} />
            {/*我的订单*/ identityStore.isBuyer() && <MyOrder />}
            {/*收益*/ !identityStore.isBuyer() && <Income />}
            <View className={"balance-and-user"}>
                {/*我的余额*/ showBalance && <Balance />}
                {
                    /*导购列表、门店列表*/
                    (showStoreList || showGuiderList) && <UserList newIdentity={identityStore.identity} />
                }
            </View>
            {/*业绩： 门店业绩、服务商团队业绩、导购我的业绩*/ !identityStore.isBuyer() && <Achievement />}
            {/*菜单导航*/}
            <MenuList />
            <InviteAddGuider
                show={isShowAddGuider}
                onClose={() => {
                    setIsShowAddGuider(false);
                }}
            />
        </View>
    );
});

export default Info;
