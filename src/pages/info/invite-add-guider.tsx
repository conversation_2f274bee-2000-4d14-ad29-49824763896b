import { Input, View } from "@tarojs/components";
import { Button, Form, FormItem, Popup } from "@antmjs/vantui";
import { getStorage, removeStorage } from "@/utils/storage";
import api from "@/utils/api";
import infoStore from "@/store/info-store";
import { mall_event, mall_event_key, reportEvent } from "@/utils/track-report";

export default function InviteAddGuider({ show, onClose }) {
    const submit = values => {
        reportEvent(
            mall_event.MALL_EVENT_SCAN_GUIDER_MINI_QR_SUBMIT,
            new Map()
                .set(mall_event_key.MALL_KEY_INVITE_SUBSTORE_ID, getStorage("inviteSubStoreId"))
                .set(mall_event_key.MALL_KEY_INVITE_ADD_GUIDER_NAME, values?.name)
                .set(mall_event_key.MALL_KEY_INVITE_ADD_GUIDER_MOBILE, values?.myMobile)
                .set(mall_event_key.MALL_KEY_INVITE_ADD_GUIDER_FROM, "beforeSubmit"),
        );
        api.getJoinSubStore({
            method: "POST",
            data: {
                subStoreId: getStorage("inviteSubStoreId"),
                guiderName: values.name,
                guiderMobile: values.myMobile,
            },
        })
            .then(() => {
                reportEvent(
                    mall_event.MALL_EVENT_SCAN_GUIDER_MINI_QR_SUBMIT,
                    new Map()
                        .set(mall_event_key.MALL_KEY_INVITE_SUBSTORE_ID, getStorage("inviteSubStoreId"))
                        .set(mall_event_key.MALL_KEY_INVITE_ADD_GUIDER_NAME, values?.name)
                        .set(mall_event_key.MALL_KEY_INVITE_ADD_GUIDER_MOBILE, values?.myMobile)
                        .set(mall_event_key.MALL_KEY_INVITE_ADD_GUIDER_FROM, "submitSuccess"),
                );
                removeStorage("inviteSubStoreId");
                removeStorage("inviteSubStoreName");
            })
            .catch(() => {
                reportEvent(
                    mall_event.MALL_EVENT_SCAN_GUIDER_MINI_QR_SUBMIT,
                    new Map()
                        .set(mall_event_key.MALL_KEY_INVITE_SUBSTORE_ID, getStorage("inviteSubStoreId"))
                        .set(mall_event_key.MALL_KEY_INVITE_ADD_GUIDER_NAME, values?.name)
                        .set(mall_event_key.MALL_KEY_INVITE_ADD_GUIDER_MOBILE, values?.myMobile)
                        .set(mall_event_key.MALL_KEY_INVITE_ADD_GUIDER_FROM, "fail"),
                );
                removeStorage("inviteSubStoreId");
                removeStorage("inviteSubStoreName");
            })
            .finally(() => {
                onClose();
            });
    };

    return (
        <Popup round show={show} onClose={() => onClose()} closeable>
            <View className="guiderForm_box">
                <View className="guiderForm_title">成为导购员</View>
                <Form
                    onFinish={(errs, res) => submit(res)}
                    initialValues={{
                        subStoreName: getStorage("inviteSubStoreName"),
                        myMobile: infoStore.userProfile?.mobile,
                    }}
                >
                    <FormItem name={"subStoreName"} label={"邀请门店"} borderBottom>
                        <Input type="text" disabled />
                    </FormItem>
                    <FormItem
                        name={"name"}
                        label={"导购名称"}
                        required
                        borderBottom
                        trigger="onInput"
                        valueFormat={e => e.detail.value}
                    >
                        <Input type="text" name="name" placeholder={"请输入导购名称"} maxlength={15} />
                    </FormItem>
                    <FormItem name={"myMobile"} label={"手机号"} borderBottom>
                        <Input type="text" name="name" disabled />
                    </FormItem>
                    <View className={"btn_box"}>
                        <Button round type={"primary"} className={"from_btn submit_btn"} formType={"submit"}>
                            确定
                        </Button>
                    </View>
                </Form>
            </View>
        </Popup>
    );
}
