.my-order {
    margin-top: 40px;

    .title {
        position: relative;
        padding: 0 32px;
        height: 76px;
        align-items: center;
        display: flex;
        justify-content: space-between;
    }
    .title::after {
        position: absolute;
        box-sizing: border-box;
        -webkit-transform-origin: center;
        transform-origin: center;
        content: " ";
        pointer-events: none;
        right: 32px;
        bottom: 0;
        left: 32px;
        border-bottom: 1px solid #ebedf0;
        transform: scaleY(0.5);
        border-bottom-color: rgb(235, 237, 240);
    }
    &-title {
        flex: 1;
        font-size: 28px;
    }

    &-action {
        font-size: 24px;
        font-weight: 400;
        color: #666666;
        line-height: 24px;
    }
    .van-tabs__wrap--scrollable .van-tab::last-child {
        width: 200px !important;
    }
}
