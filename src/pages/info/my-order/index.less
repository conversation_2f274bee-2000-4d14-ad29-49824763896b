.my-order {
    margin-top: -21px;

    .title {
        position: relative;
        padding: 0 42px;
        height: 70px;
        align-items: center;
        display: flex;
        justify-content: space-between;
    }
    .title::after {
        position: absolute;
        box-sizing: border-box;
        -webkit-transform-origin: center;
        transform-origin: center;
        content: " ";
        pointer-events: none;
        right: 19px;
        bottom: 0;
        left: 19px;
        border-bottom: 1px solid #f5f5f5;
    }
    &-title {
        flex: 1;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 24px;
        color: #333333;
        line-height: 30px;
        font-style: normal;
    }

    &-action {
        font-size: 24px;
        font-weight: 400;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 18px;
        color: #999999;
        line-height: 30px;
        font-style: normal;
        &-icon {
            font-size: 18px;
        }
    }
    .van-tabs__wrap--scrollable .van-tab::last-child {
        width: 200px !important;
    }
    .grid {
        height: 152px;
        .van-grid-item__text {
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            font-size: 22px;
            color: #3b3b3b;
            line-height: 25px;
            font-style: normal;
        }
    }
}
