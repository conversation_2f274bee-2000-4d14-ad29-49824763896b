import { Image, View } from "@tarojs/components";
import daifa from "@/images/daifa.png";
import daifu from "@/images/daifu.png";
import daishou from "@/images/daishou.png";
import wancheng from "@/images/wancheng.png";
import rufund from "@/images/rufund.png";
import "./index.less";
import { CellGroup, Grid, GridItem, Icon } from "@antmjs/vantui";
import { DDYNavigateTo, DDYRedirectTo } from "@/utils/route";
import { useEffect, useState } from "react";
import { useDidShow } from "@tarojs/taro";
import { getStorage } from "@/utils/storage";
import api from "@/utils/api";
import { STORE_ID, USER_INFO } from "@/utils/constant";

export default function MyOrder() {
    const [orderType, setOrderType] = useState([
        {
            type: 1,
            name: "待付款",
            icon: daifu,
            badge: "",
        },
        {
            type: 2,
            name: "待发货",
            icon: daifa,
            badge: "",
        },
        {
            type: 3,
            name: "待收货",
            icon: daishou,
            badge: "",
        },
        {
            type: 5,
            name: "已完成",
            icon: wancheng,
        },
        {
            type: 4,
            name: "退款/售后",
            icon: rufund,
        },
    ]);
    const goOrderList = item => {
        // if (item.type !== -1) {
        DDYNavigateTo({
            url: "/pages/order/order/index?tab=" + item.type,
        });
        // } else {
        //     DDYNavigateTo({
        //         url: "/pages/order/refund-order/index",
        //     });
        // }
    };

    useDidShow(() => {
        let userInfo = getStorage(USER_INFO) || {};
        if (userInfo.userId) {
            let queryData = {
                buyerId: userInfo.userId,
                way: 0,
                outFrom: "subStore",
                shopId: getStorage(STORE_ID),
                type: 1, //1是正常订单 3积分订单
            };
            api.countByStatusAll({
                data: queryData,
                filterCheck: true,
            }).then(res => {
                orderType[0].badge = res.tobeshipped;
                orderType[1].badge = res.shipped;
                orderType[2].badge = res.toBeReceived;
                setOrderType([...orderType]);
            });
        }
    });
    return (
        <CellGroup inset className={"my-order"}>
            <View className={"title"}>
                <View className={"my-order-title"}>我的订单</View>
                <View
                    className={"my-order-action"}
                    onClick={() => {
                        goOrderList(0);
                    }}
                >
                    查看数据
                    <Icon name={"arrow"} />
                </View>
            </View>
            <View>
                <Grid columnNum={5} border={false}>
                    {orderType.map(item => {
                        return (
                            <GridItem
                                icon={item.icon}
                                text={item.name}
                                onClick={() => {
                                    goOrderList(item);
                                }}
                                badge={item.badge}
                            />
                        );
                    })}
                </Grid>
            </View>
        </CellGroup>
    );
}
