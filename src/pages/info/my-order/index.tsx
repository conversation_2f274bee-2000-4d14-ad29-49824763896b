import { Image, View } from "@tarojs/components";
import "./index.less";
import { CellGroup, Grid, GridItem, Icon } from "@antmjs/vantui";
import { DDYNavigateTo, DDYRedirectTo } from "@/utils/route";
import { useEffect, useState } from "react";
import { useDidShow } from "@tarojs/taro";
import { getStorage } from "@/utils/storage";
import api from "@/utils/api";
import { STORE_ID, USER_INFO } from "@/utils/constant";

export default function MyOrder() {
    const [orderType, setOrderType] = useState([
        {
            type: 1,
            name: "待付款",
            icon: "https://dante-img.oss-cn-hangzhou.aliyuncs.com/43891556560.png",
            badge: "",
        },
        {
            type: 2,
            name: "待发货",
            icon: "https://dante-img.oss-cn-hangzhou.aliyuncs.com/43891560667.png",
            badge: "",
        },
        {
            type: 3,
            name: "待收货",
            icon: "https://dante-img.oss-cn-hangzhou.aliyuncs.com/43891560667.png",
            badge: "",
        },
        {
            type: 5,
            name: "已完成",
            icon: "https://dante-img.oss-cn-hangzhou.aliyuncs.com/43891524629.png",
        },
        {
            type: 4,
            name: "退款/售后",
            icon: "https://dante-img.oss-cn-hangzhou.aliyuncs.com/438915239.png",
        },
    ]);
    const goOrderList = item => {
        // if (item.type !== -1) {
        DDYNavigateTo({
            url: "/pages/order/order/index?tab=" + item.type,
        });
        // } else {
        //     DDYNavigateTo({
        //         url: "/pages/order/refund-order/index",
        //     });
        // }
    };

    useDidShow(() => {
        let userInfo = getStorage(USER_INFO) || {};
        if (userInfo.userId) {
            let queryData = {
                buyerId: userInfo.userId,
                way: 0,
                outFrom: "communityOperation",
                shopId: getStorage(STORE_ID),
                type: 1, //1是正常订单 3积分订单
            };
            api.countByStatusAll({
                data: queryData,
                filterCheck: true,
            }).then(res => {
                orderType[0].badge = res.tobeshipped;
                orderType[1].badge = res.shipped;
                orderType[2].badge = res.toBeReceived;
                setOrderType([...orderType]);
            });
        }
    });
    return (
        <CellGroup inset className={"my-order"}>
            <View className={"title"}>
                <View className={"my-order-title"}>我的订单</View>
                <View
                    className={"my-order-action"}
                    onClick={() => {
                        goOrderList({ type: 0 });
                    }}
                >
                    查看数据
                    <Icon name={"arrow"} className="my-order-action-icon" />
                </View>
            </View>
            <View>
                <Grid columnNum={5} border={false} className="grid" iconSize={45}>
                    {orderType.map(item => {
                        return (
                            <GridItem
                                icon={item.icon}
                                text={item.name}
                                onClick={() => {
                                    goOrderList(item);
                                }}
                                badge={item.badge}
                            />
                        );
                    })}
                </Grid>
            </View>
        </CellGroup>
    );
}
