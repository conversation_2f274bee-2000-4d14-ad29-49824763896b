import { View, Text } from "@tarojs/components";
import { observer } from "mobx-react-lite";
import "./index.less";
import infoStore from "../../../store/info-store";
import { DDYNavigateTo } from "@/utils/route";
import { Icon, Image } from "@antmjs/vantui";

const Balance = observer(() => {
    return (
        <View
            className="balance-section"
            onClick={() => {
                DDYNavigateTo({ url: "/pages/account-balance/index" });
            }}
        >
            <View className="balance-info">
                <Icon
                    className="balance-info-icon"
                    name={"https://dante-img.oss-cn-hangzhou.aliyuncs.com/***********.png"}
                />
                <View>
                    <View className="balance-label">可用余额</View>
                    <View className="balance-amount">
                        {infoStore.newUserProfile?.balance || 0.0}
                        <Text className="balance-amount-fuhao">元</Text>
                    </View>
                </View>
            </View>
            <View className="balance-detail">
                查询明细
                <Icon name={"arrow"} />
            </View>
        </View>
    );
});
export default Balance;
