.header {
    display: flex;
    width: 100%;
    padding-top: 180px;

    &-avatar {
        position: relative;
        margin-right: 20px;
        margin-left: 40px;
        &-img {
            border: 3px solid #fff;
        }
        .getUser {
            position: absolute;
            top: 0px;
            left: 0px;
            width: 128px;
            height: 128px;
            z-index: 20;
            opacity: 0;
        }
    }
    .nickName {
        margin-top: 15px;
        width: 335px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        font-size: 40px;
        line-height: 40px;
        color: #333333;
        font-weight: 600;
    }
    .toLogin {
        font-size: 40px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: #333333;
        line-height: 56px;
    }
    .refresh {
        width: 31px;
        height: 31px;
        border-radius: 50%;
        background: #ffffff;
        position: absolute;
        top: 100px;
        right: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        .refresh_img {
            width: 22px;
            height: 22px;
        }
    }
    .shop-audit {
        display: inline-flex;
        margin-top: 20px;
        background: #555c7e;
        border-radius: 8rpx;

        .useTag {
            font-size: 20rpx;
            padding: 0 12rpx;
            color: #ffffff;
            height: 36rpx;
            line-height: 36rpx;
        }
    }
}
