import { Button, View } from "@tarojs/components";
import { Icon, Image } from "@antmjs/vantui";
import Taro, { pxTransform } from "@tarojs/taro";
import { observer } from "mobx-react-lite";
import "./index.less";
import identityStore from "../../../store/identity-store";
import infoStore from "../../../store/info-store";
import { PROJECT_CONFIG } from "@/utils/env";
import DDYToast from "@/utils/toast";
import api from "@/utils/api";
import { jumpLogin } from "@/utils/PageUtils";
import { DDYNavigateTo } from "@/utils/route";
import newAPi from "@/utils/newApi";
import { getStorage } from "@/utils/storage";
import { IS_LOGIN } from "@/utils/constant";

export interface IHeaderProps {
    isLogin: boolean;
    whetherGroup: boolean;
}
const Header = observer((props: IHeaderProps) => {
    const inName = {
        subStore: "门店",
        serviceProvider: "服务商",
        guider: "导购",
        buyer: "消费端",
    };
    const onChooseAvatar = evt => {
        DDYToast.showLoading("图片上传中");
        Taro.uploadFile({
            url: `${PROJECT_CONFIG.API_MALL}/api/user/files/upload?folderId=0`,
            filePath: evt.detail.avatarUrl,
            name: "file",
            success: function (res) {
                DDYToast.hideLoading();
                try {
                    const json = JSON.parse(res.data);
                    submitInfo(json.image);
                } catch (error) {
                    DDYToast.error("图片上传失败");
                }
            },
            fail: function () {
                DDYToast.error("图片上传失败");
                DDYToast.hideLoading();
            },
        });
    };
    const submitInfo = avatarUrl => {
        newAPi
            .syncWeChatUserInfo({
                method: "POST",
                data: {
                    headImg: avatarUrl,
                },
            })
            .then(() => {
                infoStore.getNewUserProfile();
            });
    };
    console.log("infoStore.newUserProfile.whetherGroup:", infoStore.newUserProfile.whetherGroup);
    return (
        <View className="header">
            <view className="header-avatar">
                <Button className="getUser" openType={"chooseAvatar"} onChooseAvatar={onChooseAvatar}></Button>
                <Image
                    width={pxTransform(128)}
                    height={pxTransform(128)}
                    className="header-avatar-img"
                    round={true}
                    src={infoStore.newUserProfile.avatarUrl}
                />
                {props.isLogin ? (
                    <view className="refresh">
                        <Image
                            className="refresh_img"
                            src="https://dante-img.oss-cn-hangzhou.aliyuncs.com/76317459890.svg"
                        />
                    </view>
                ) : null}
            </view>
            {(getStorage(IS_LOGIN) && !infoStore.newUserProfile.whetherGroup) ||
            (getStorage(IS_LOGIN) && infoStore.newUserProfile.whetherGroup) ? (
                <View
                    className="wool-herdd-btn"
                    onClick={() => {
                        DDYNavigateTo({
                            url: `/pages/wool-herd/index?join=${infoStore.newUserProfile.whetherGroup}`,
                        });
                    }}
                >
                    {infoStore.newUserProfile.groupName || "我的福利群"}
                    <Icon name={"arrow"} size={20} style={{ color: "#ff3b61" }} />
                </View>
            ) : null}
            {props.isLogin ? (
                <View onClick={() => DDYNavigateTo({ url: "/pages/my/setting/index" })}>
                    <View className="nickName">{infoStore.newUserProfile?.nickName || "微信用户"}</View>
                    <View className="phone">{infoStore.newUserProfile?.phone}</View>
                    <View className="userId">{`ID:${infoStore.newUserProfile?.userId}`}</View>
                    {!identityStore.isBuyer() && (
                        <View className="shop-audit">
                            <View className="useTag">{inName[identityStore.identity]}</View>
                            {identityStore.isSubStore() && (
                                <>
                                    {identityStore.isPending() && (
                                        <View
                                            className="useTag"
                                            onClick={() =>
                                                DDYNavigateTo({ url: "/pages/store-list/invite-open-status/index" })
                                            }
                                        >
                                            待审核
                                            <Icon name={"arrow"} size={20} />
                                        </View>
                                    )}
                                    {identityStore.isReject() && (
                                        <View
                                            className="useTag"
                                            onClick={() =>
                                                DDYNavigateTo({ url: "/pages/store-list/invite-open-status/index" })
                                            }
                                        >
                                            审核拒绝&gt;
                                        </View>
                                    )}
                                </>
                            )}
                        </View>
                    )}
                </View>
            ) : (
                <View className={"flex_row v_center"}>
                    <View className={"toLogin"} onClick={() => jumpLogin()}>
                        立即登录
                    </View>
                </View>
            )}
        </View>
    );
});
export default Header;
