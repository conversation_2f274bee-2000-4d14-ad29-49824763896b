import { Button, View } from "@tarojs/components";
import { Icon, Image } from "@antmjs/vantui";
import Taro, { pxTransform } from "@tarojs/taro";
import { observer } from "mobx-react-lite";
import "./index.less";
import identityStore from "../../../store/identity-store";
import infoStore from "../../../store/info-store";
import { PROJECT_CONFIG } from "@/utils/env";
import DDYToast from "@/utils/toast";
import api from "@/utils/api";
import { jumpLogin } from "@/utils/PageUtils";
import { DDYNavigateTo } from "@/utils/route";

export interface IHeaderProps {
    isLogin: boolean;
}
const Header = observer((props: IHeaderProps) => {
    const inName = {
        subStore: "门店",
        serviceProvider: "服务商",
        guider: "导购",
        buyer: "消费端",
    };
    const onChooseAvatar = evt => {
        DDYToast.showLoading("图片上传中");
        Taro.uploadFile({
            url: `${PROJECT_CONFIG.API_MALL}/api/user/files/upload?folderId=0`,
            filePath: evt.detail.avatarUrl,
            name: "file",
            success: function (res) {
                DDYToast.hideLoading();
                try {
                    const json = JSON.parse(res.data);
                    submitInfo(json.image);
                } catch (error) {
                    DDYToast.error("图片上传失败");
                }
            },
            fail: function () {
                DDYToast.error("图片上传失败");
                DDYToast.hideLoading();
            },
        });
    };
    const submitInfo = avatarUrl => {
        api.saveUserInfo({
            method: "PUT",
            data: {
                avatar: avatarUrl,
            },
            filterCheck: true,
        }).then(() => {
            infoStore.getUserProfile();
        });
    };
    return (
        <View className="header">
            <view className="header-avatar">
                <Button className="getUser" openType={"chooseAvatar"} onChooseAvatar={onChooseAvatar}></Button>
                <Image
                    width={pxTransform(128)}
                    height={pxTransform(128)}
                    className="header-avatar-img"
                    round={true}
                    src={infoStore.avatarUrl}
                />
                <view className="refresh">
                    <Image
                        className="refresh_img"
                        src="https://dante-img.oss-cn-hangzhou.aliyuncs.com/76317459890.svg"
                    />
                </view>
            </view>
            {props.isLogin ? (
                <View>
                    <View className="nickName">{infoStore.nickName || "微信用户"}</View>

                    <View className="shop-audit">
                        <View className="useTag">{inName[identityStore.identity]}</View>
                        {identityStore.isSubStore() && (
                            <>
                                {identityStore.isPending() && (
                                    <View
                                        className="useTag"
                                        onClick={() =>
                                            DDYNavigateTo({ url: "/pages/store-list/invite-open-status/index" })
                                        }
                                    >
                                        待审核
                                        <Icon name={"arrow"} size={20} />
                                    </View>
                                )}
                                {identityStore.isReject() && (
                                    <View
                                        className="useTag"
                                        onClick={() =>
                                            DDYNavigateTo({ url: "/pages/store-list/invite-open-status/index" })
                                        }
                                    >
                                        审核拒绝&gt;
                                    </View>
                                )}
                            </>
                        )}
                    </View>
                </View>
            ) : (
                <View className={"flex_row v_center"}>
                    <View className={"toLogin"} onClick={() => jumpLogin()}>
                        立即登录
                    </View>
                </View>
            )}
        </View>
    );
});
export default Header;
