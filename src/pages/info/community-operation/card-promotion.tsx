import { Icon, Image } from "@antmjs/vantui";
import { View, Text } from "@tarojs/components";
import { DDYNavigateTo } from "@/utils/route";
import Taro from "@tarojs/taro";
import DDYToast from "@/utils/toast";
import { observer } from "mobx-react-lite";
import infoStore from "@/store/info-store";

const CardPromotion = observer(() => {
    return (
        <View className={"card-promotion"}>
            {/*任务中心*/}
            <View className="task-center" onClick={() => DDYNavigateTo({ url: "/pages/task/task-center/index" })}>
                <View className="task-title">
                    <View className={"task-title-text"}>任务中心</View>
                    <Icon name={"arrow"} color={"#89613C"} className="task-title-icon" />
                </View>
                <View className={"task-subtitle"}>参加活动赚余额</View>
                <Image
                    className="task-center-img"
                    src="https://dante-img.oss-cn-hangzhou.aliyuncs.com/43876065297.png"
                ></Image>
            </View>

            {(infoStore.personalCenterActivity?.whetherGoingPromotionActivity > 0 ||
                infoStore.personalCenterActivity?.whetherGoingInviteActivity) && (
                <View className="coupon-showcase">
                    {infoStore.personalCenterActivity?.whetherGoingPromotionActivity > 0 ? (
                        <View
                            className={`task-coupon-card ${
                                infoStore.personalCenterActivity?.whetherGoingInviteActivity
                                    ? ""
                                    : "task-coupon-card-single"
                            }`}
                            onClick={() =>
                                DDYNavigateTo({
                                    url:
                                        "/pages/task/task-sign-up/index?id=" +
                                        infoStore.personalCenterActivity?.promotionActivityId,
                                })
                            }
                        >
                            <View className="coupon-title">
                                <View className="coupon-title-content">
                                    {infoStore.personalCenterActivity?.promotionActivityName}
                                </View>
                                <Icon name={"arrow"} color={"#84C4FF"} className="coupon-title-icon" />
                            </View>
                            <View className="coupon-desc">
                                {infoStore.personalCenterActivity?.promotionActivityChannel}晒单可得
                            </View>
                            <View className="task-amount">
                                <text className="symbol">¥</text>
                                <text className="number">
                                    {infoStore.personalCenterActivity?.promotionActivityRewardsPrice}
                                </text>
                            </View>
                            <Image
                                className="task-coupon-card-img"
                                src="https://dante-img.oss-cn-hangzhou.aliyuncs.com/43876062148.png"
                                renderLoading={null}
                            ></Image>
                        </View>
                    ) : null}
                    {infoStore.personalCenterActivity?.whetherGoingInviteActivity ? (
                        <View
                            className={`coupon-card ${
                                infoStore.personalCenterActivity?.whetherGoingPromotionActivity > 0
                                    ? ""
                                    : "coupon-card-single"
                            }`}
                        >
                            <View className="coupon-desc">邀请好友领取红包</View>
                            <View className="coupon-amount">
                                <text className="symbol">¥</text>
                                <text className="number">
                                    {infoStore.personalCenterActivity?.inviteActivityRewardsPrice}
                                </text>
                            </View>
                            <View className="action-button-wrap">
                                <View
                                    className="action-button"
                                    onClick={() => {
                                        Taro.setClipboardData({
                                            data: infoStore.newUserProfile?.inviteContent || "邀请码获取失败",
                                            success: function (res) {
                                                DDYToast.info("复制邀请码成功");
                                            },
                                        });
                                    }}
                                >
                                    复制邀请码
                                </View>
                            </View>
                            <Image
                                className="coupon-card-img"
                                src="https://dante-img.oss-cn-hangzhou.aliyuncs.com/43876068427.png"
                            ></Image>
                        </View>
                    ) : null}
                </View>
            )}
        </View>
    );
});
export default CardPromotion;
