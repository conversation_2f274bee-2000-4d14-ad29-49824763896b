import { DDYNavigateTo, DDYSwitchTab } from "@/utils/route";
import { Icon } from "@antmjs/vantui";
import { ScrollView, View } from "@tarojs/components";
import { observer } from "mobx-react-lite";
import infoStore from "@/store/info-store";
import { optimizeCoupons } from "../../wallet-card/fn/optimizeCoupons";
import { formatEndTime } from "@/utils/DateTimeUtils";
import { toJS } from "mobx";
import Taro from "@tarojs/taro";

const CardPromotion = observer(() => {
    console.log(toJS(infoStore.newUserProfile));
    return (
        <View className="coupon-list">
            <View
                className="list-header"
                onClick={() => {
                    DDYNavigateTo({
                        url: "/pages/coupon/index",
                    });
                }}
            >
                <View className={"coupon-title"}>
                    <Icon name={"https://dante-img.oss-cn-hangzhou.aliyuncs.com/70146095824.svg"} />
                    <View className={"coupon-title-text"}>我的优惠券</View>
                </View>
                <View className="total-count">
                    共{infoStore?.newUserProfile?.couponNum || 0}张
                    <Icon name="arrow" color="#999999" />
                </View>
            </View>
            <ScrollView scrollX={true} style={{ marginTop: Taro.pxTransform(24) }}>
                <View className="coupon-items">
                    {optimizeCoupons(infoStore.newUserProfile?.coupons || []).map((coupon, index) => {
                        return (
                            <View className="coupon-item">
                                <View className="item-left">
                                    <View className="price">
                                        <View className="number">{coupon.amountDesc}</View>
                                    </View>
                                    <View className="condition">{coupon.methodDesc}</View>
                                </View>
                                <View className="item-right">
                                    <View className="validity">{coupon.usageTimeDesc}</View>
                                    <button
                                        className="use-button"
                                        onClick={() => {
                                            if (coupon.scopeType === "PRODUCT") {
                                                DDYNavigateTo({ url: "/pages/goods_detail?id=" + coupon.scopeValue });
                                            } else {
                                                DDYSwitchTab({ url: "/pages/home/<USER>" });
                                            }
                                        }}
                                    >
                                        使用
                                    </button>
                                </View>
                            </View>
                        );
                    })}
                </View>
            </ScrollView>
        </View>
    );
});
export default CardPromotion;
