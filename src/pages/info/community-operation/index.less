.community-operation {
    margin-top: 20px;

    .card-promotion {
        height: 352px;
        background: #ffffff;
        border-radius: 16px;
        box-sizing: border-box;
        padding: 26px 33px 41px 30px;
        display: flex;
        justify-content: space-between;

        .task-center {
            display: flex;
            flex-direction: column;
            padding: 0 17px 0 27px;
            flex: 1 1 298px;
            height: 285px;
            background: #feecd7;
            border-radius: 37px;
            box-sizing: border-box;
            position: relative;

            &-img {
                width: 176px;
                height: 176px;
                position: absolute;
                bottom: 0;
                right: 0;
            }

            .task-title {
                display: flex;
                font-family: PingFangSC, PingFang SC;
                font-weight: 600;
                font-size: 32px;
                color: #89613c;
                line-height: 33px;
                font-style: normal;
                margin-right: 11px;
                margin: 27px 0 9px;

                .task-title-text {
                    flex-grow: 1;
                }

                &-icon {
                    font-size: 17px;
                }
            }

            .task-subtitle {
                font-family: PingFangSC, PingFang SC;
                font-weight: 300;
                font-size: 26px;
                color: #888888;
                line-height: 25px;
                font-style: normal;
            }
        }

        .coupon-showcase {
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            flex: 1 1 320px;

            .task-coupon-card {
                margin-left: 21px;
                height: 132px;
                background: #def1ff;
                border-radius: 28px;
                padding: 14px 0 18px 24px;
                position: relative;
                flex-grow: 1;
                box-sizing: border-box;
                margin-bottom: 17px;

                &-img {
                    width: 116px;
                    height: 116px;
                    position: absolute;
                    bottom: 0;
                    right: 7px;
                }

                .coupon-title {
                    display: flex;
                    align-items: center;
                    margin-bottom: 6px;

                    &-content {
                        font-family: PingFangSC, PingFang SC;
                        font-weight: 600;
                        font-size: 24px;
                        color: #47a0fb;
                        line-height: 28px;
                        text-align: left;
                        font-style: normal;
                        width: 150px;
                        white-space: nowrap;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        margin-right: 5px;
                        display: inline-block;
                    }

                    &-icon {
                        font-size: 17px;
                    }
                }

                .coupon-desc {
                    font-family: PingFangSC, PingFang SC;
                    font-weight: 300;
                    font-size: 22px;
                    color: #888888;
                    line-height: 22px;
                    text-align: left;
                    font-style: normal;
                    margin-bottom: 16px;
                }

                .task-amount {
                    font-family: PingFangSC, PingFang SC;
                    font-weight: 500;
                    font-size: 28px;
                    color: #68b6fc;
                    line-height: 28px;
                    text-align: left;
                    font-style: normal;

                    .symbol {
                        font-size: 20px;
                    }
                }
            }

            .coupon-card {
                margin-left: 21px;
                height: 132px;
                background: #ffdee2;
                border-radius: 28px;
                box-sizing: border-box;
                padding: 18px 0 10px 24px;
                flex-grow: 1;
                position: relative;
                display: flex;
                flex-direction: column;

                &-img {
                    width: 108px;
                    height: 108px;
                    position: absolute;
                    bottom: 0;
                    right: 7px;
                }

                .coupon-desc {
                    font-family: PingFangSC, PingFang SC;
                    font-weight: 400;
                    font-size: 24px;
                    color: #888888;
                    line-height: 22px;
                    text-align: left;
                    font-style: normal;
                    margin-bottom: 7px;
                }

                .coupon-amount {
                    font-family: PingFangSC, PingFang SC;
                    font-weight: 600;
                    font-size: 28px;
                    color: #ff2424;
                    line-height: 28px;
                    text-align: left;
                    font-style: normal;
                    margin-bottom: 11px;

                    .symbol {
                        font-size: 20px;
                    }
                }

                .action-button-wrap {
                    .action-button {
                        width: 133px;
                        height: 36px;
                        line-height: 36px;
                        text-align: center;
                        background: #ffdee2;
                        border-radius: 18px;
                        border: 1px solid #ff0940;
                        font-family: PingFangSC, PingFang SC;
                        font-weight: 300;
                        font-size: 18px;
                        color: #ff0940;
                        line-height: 34px;
                        font-style: normal;
                        box-sizing: border-box;
                    }
                }
            }

            .coupon-card-single {
                padding-top: 27px;

                .coupon-desc {
                    margin-bottom: 20px;
                }

                .coupon-amount {
                    margin-bottom: 15px;
                }

                .coupon-card-img {
                    width: 176px;
                    height: 176px;
                    right: 0;
                }
            }

            .task-coupon-card-single {
                margin-bottom: 0;
                padding-top: 27px;

                .coupon-title {
                    margin-bottom: 20px;
                }

                .coupon-desc {
                    margin-bottom: 15px;
                }

                .task-coupon-card-img {
                    width: 176px;
                    height: 176px;
                    right: 0;
                }
            }
        }
    }

    .coupon-list {
        margin-top: 22px;
        background-color: #ffffff;
        border-radius: 16px;
        padding: 24px;
    }

    .list-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-size: 16px;
        color: #333333;

        .coupon-title {
            display: flex;
            align-items: center;
            font-size: 28px;
            color: #333;

            &-text {
                margin-left: 15px;
            }
        }

        .coupon-task-subtitle {
            font-size: 24px;
            color: #999999;
            margin-left: 20px;
        }
    }

    .total-count {
        display: flex;
        align-items: center;
        color: #666;
        font-weight: 400;
        font-size: 24px;
        line-height: 24px;
    }

    .coupon-items {
        display: flex;
        flex-direction: row;
        gap: 24px;
    }

    .coupon-item {
        min-width: 42%;
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 24px;
        background-color: #f8f8f8;
        border-radius: 12px;
    }

    .item-left {
        display: flex;
        flex-direction: column;
    }

    .price {
        display: flex;
        align-items: baseline;
        color: #ff4444;
    }

    .price .symbol {
        font-size: 28px;
    }

    .price .number {
        font-size: 36px;
        font-weight: 600;
    }

    .condition {
        font-size: 24px;
        line-height: 28px;
        color: #ff0940;
        margin-top: 8px;
    }

    .item-right {
        display: flex;
        flex-direction: column;
        align-items: flex-end;
        gap: 12px;
    }

    .validity {
        font-size: 20px;
        font-weight: 300;
        color: #888888;
        line-height: 28px;
    }

    .use-button {
        font-size: 18px;
        line-height: 28px;
        color: #ffffff;
        background-color: #e50a1a;
        border: none;
        border-radius: 24px;
        padding: 5px 25px;
    }
}
