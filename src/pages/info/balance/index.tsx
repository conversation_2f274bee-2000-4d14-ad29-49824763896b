import { View } from "@tarojs/components";
import { observer } from "mobx-react-lite";
import "./index.less";
import infoStore from "../../../store/info-store";
import { DDYNavigateTo } from "@/utils/route";
import identityStore from "@/store/identity-store";

const Balance = observer(() => {
    return (
        <View
            className="balance-box"
            onClick={() =>
                DDYNavigateTo({
                    url: identityStore.isSubStore()
                        ? "/pages/withdrawal/account-statement/index"
                        : "/pages/balance/index",
                })
            }
        >
            {/*@tap.stop="redirectToPage" data-pagename="{{newIdentity==='subStore' ? 'toWithdraw' : 'balance'}}"*/}
            <View className="balance-left">
                <View className="balance-title">我的余额</View>
                <View className="balance-num">
                    {infoStore.balance}
                    {/*{{newIdentity==='subStore' ? validProfit : balance / 100}}*/}
                </View>
            </View>
            <View className="balance-right">去提现</View>
        </View>
    );
});
export default Balance;
