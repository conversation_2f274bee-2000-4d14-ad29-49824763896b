import { View } from "@tarojs/components";
import { CellGroup, Icon } from "@antmjs/vantui";
import { observer } from "mobx-react-lite";
import identityStore from "../../store/identity-store";
import infoStore from "../../store/info-store";
import { DDYNavigateTo } from "@/utils/route";

/**
 * 业绩
 */
const Achievement = observer(() => {
    return (
        <CellGroup inset className={"income-box"}>
            <View className="income-top">
                <View className="income-title">
                    {identityStore.isSubStore()
                        ? "门店业绩"
                        : identityStore.isServiceProvider()
                        ? "团队业绩"
                        : "我的业绩"}
                </View>
                <View
                    className="income-icon"
                    onClick={() => {
                        DDYNavigateTo({
                            url: "/pages/order/my-order/index?tab=" + 0,
                        });
                    }}
                >
                    <View>
                        查看数据
                        <Icon name={"arrow"} />
                    </View>
                </View>
            </View>
            <View className="income-bottom">
                <View className="bottom-box">
                    <View className="income-num">{infoStore.profitData?.todayOrderCount || 0}</View>
                    <View className="income-detail">今日订单量</View>
                </View>
                <View className="bottom-box">
                    <View className="income-num">{infoStore.profitData?.todayOrderTotalAmount || 0}</View>
                    <View className="income-detail">今日总金额</View>
                </View>
            </View>
        </CellGroup>
    );
});
export default Achievement;
