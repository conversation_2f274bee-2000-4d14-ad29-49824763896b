.good-commit-page {
    background-color: rgba(240, 240, 240, 1);
    min-height: 100vh;
    .good-commit-header {
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: space-around;
        margin: 16px 19px;
        background-color: #fff;
        border-radius: 38px;
        height: 127px;
        .good-commit-head-item {
            position: relative;
            .good-commit-head-title {
                font-weight: 400;
                font-size: 28px;
                color: #444444;
                line-height: 30px;
                text-align: center;
                margin-bottom: 12px;
            }
            .good-commit-head-value {
                height: 25px;
                font-weight: 300;
                font-size: 24px;
                color: #999999;
                line-height: 36px;
                margin-top: 10px;
                text-align: center;
            }
            .good-commit-head-item-line {
                width: 45px;
                height: 1px;
                box-shadow: 0px 3px 4px 0px #ff320a;
                background-color: #ff320a;
                border: 4px solid #ff320a;
                position: absolute;
                left: 50%;
                top: 50%;
                transform: translate(-50%, -50%);
            }
        }
    }
    .good-commit-container {
        background: #ffffff;
        border-radius: 19px;
        padding: 0 38px;
        margin: 19px;
        .good-commit-title {
            height: 82px;
            border-bottom: 1px solid rgba(255, 255, 255, 1);
            font-weight: 400;
            font-size: 24px;
            line-height: 82px;
            color: #444444;
            // line-height: 30px;
        }
    }
}
