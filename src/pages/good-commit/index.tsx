import { View } from "@tarojs/components";
import { useEffect, useRef, useState } from "react";
import "./index.less";
import { useRouter } from "@tarojs/taro";
import {
    InfiniteScroll,
    InfiniteScrollInstance,
    Empty,
    InfiniteScrollProps,
    IPullToRefreshProps,
    PullToRefresh,
} from "@antmjs/vantui";
import newAPi from "@/utils/newApi";
import GoodsCommitItem from "@/components/goods-commit-item";
definePageConfig({
    navigationBarTitleText: "全部评价",
});
export default () => {
    const route = useRouter();
    const [current, setCurrent] = useState<number | null>(null);
    const [countObj, setCountObj] = useState({
        totalCountDesc: "",
        goodCountDesc: "",
        badCountDesc: "",
        pictureCountDesc: "",
    });

    let evaluationQueryType = useRef<number | null>(null);

    const pageNo = useRef(1);
    const infiniteScrollInstance = useRef<InfiniteScrollInstance>();

    const [dataList, setDataList] = useState<any[]>([]);

    useEffect(() => {
        pageNo.current = 1;
        infiniteScrollInstance.current?.reset(true);
        getCount();
    }, []);

    const getCount = () => {
        newAPi
            .getDetailCommentAllData({
                method: "POST",
                data: {
                    itemId: route.params.id || 6914,
                },
            })
            .then(res => {
                setCountObj(res);
            });
    };

    async function getList() {
        let data = {
            evaluationQueryType: evaluationQueryType.current,
            itemId: route.params.id || 6914,
            currentPage: pageNo.current,
            pageSize: 20,
        };
        return await newAPi.getDetailComments({ data, method: "POST", filterCheck: true });
    }

    // 下拉刷新
    const onRefresh: IPullToRefreshProps["onRefresh"] = () => {
        return new Promise(resolve => {
            pageNo.current = 1;
            infiniteScrollInstance.current?.reset(true);
            resolve(undefined);
        });
    };

    // 加载更多（下拉时也会走此方法）
    const loadMore: InfiniteScrollProps["loadMore"] = async () => {
        return new Promise(async resolve => {
            getList()
                .then(res => {
                    let newData: any[] = [];
                    if (pageNo.current === 1) {
                        newData = [...res.data.dataList];
                    } else {
                        newData = [...dataList, ...res.data.dataList];
                    }
                    pageNo.current++;
                    setDataList(newData);
                    resolve(
                        res.data.dataList.length === 0 && res.data.page.currentPage >= pageNo.current
                            ? "loading"
                            : "complete",
                    );
                })
                .catch(e => {
                    resolve("error");
                });
        });
    };
    return (
        <View className="good-commit-page">
            <View className="good-commit-header">
                <View
                    className="good-commit-head-item"
                    onClick={() => {
                        evaluationQueryType.current = null;
                        setCurrent(null);
                        pageNo.current = 1;
                        infiniteScrollInstance.current?.reset(true);
                    }}
                >
                    <View className="good-commit-head-title">全部</View>
                    <View className="good-commit-head-value">（{countObj.totalCountDesc || 0}）</View>
                    {current === null ? <View className="good-commit-head-item-line"></View> : null}
                </View>
                <View
                    className="good-commit-head-item"
                    onClick={() => {
                        evaluationQueryType.current = 1;
                        setCurrent(1);
                        pageNo.current = 1;
                        infiniteScrollInstance.current?.reset(true);
                    }}
                >
                    <View className="good-commit-head-title">好评</View>
                    <View className="good-commit-head-value">{countObj.goodCountDesc || 0}</View>
                    {current === 1 ? <View className="good-commit-head-item-line"></View> : null}
                </View>
                <View
                    className="good-commit-head-item"
                    onClick={() => {
                        evaluationQueryType.current = 2;
                        setCurrent(2);
                        pageNo.current = 1;
                        infiniteScrollInstance.current?.reset(true);
                    }}
                >
                    <View className="good-commit-head-title">中差评</View>
                    <View className="good-commit-head-value">{countObj.badCountDesc || 0}</View>
                    {current === 2 ? <View className="good-commit-head-item-line"></View> : null}
                </View>
                <View
                    className="good-commit-head-item"
                    onClick={() => {
                        evaluationQueryType.current = 3;
                        setCurrent(3);
                        pageNo.current = 1;
                        infiniteScrollInstance.current?.reset(true);
                    }}
                >
                    <View className="good-commit-head-title">有图</View>
                    <View className="good-commit-head-value">{countObj.pictureCountDesc || 0}</View>
                    {current === 3 ? <View className="good-commit-head-item-line"></View> : null}
                </View>
            </View>
            <PullToRefresh onRefresh={onRefresh}>
                {dataList.length ? (
                    <View className="good-commit-container">
                        <View className="good-commit-title">用户评价（{countObj.totalCountDesc || 0}）</View>
                        {dataList?.map((item: any, index) => {
                            return <GoodsCommitItem {...item} key={index} />;
                        })}
                    </View>
                ) : null}
                <InfiniteScroll
                    completeText={
                        <>
                            {dataList.length == 0 ? (
                                <Empty
                                    description="暂无数据！"
                                    image="https://dante-img.oss-cn-hangzhou.aliyuncs.com/55439458101.png"
                                />
                            ) : (
                                "没有更多了"
                            )}
                        </>
                    }
                    loadMore={loadMore}
                    ref={infiniteScrollInstance}
                />
            </PullToRefresh>
        </View>
    );
};
