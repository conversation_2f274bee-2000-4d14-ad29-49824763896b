page {
    height: 100%;
    background-color: #f1f2f3;
}
.store-list {
    .top {
        display: flex;
        margin-top: 24px;
        margin-left: 15px;
        margin-right: 15px;
        .top-item {
            display: flex;
            flex: 1;
            height: 132px;
            margin-left: 9px;
            margin-right: 9px;
            border-radius: 16px;
            background: #ffffff;
            align-items: center;
            justify-content: center;
            flex-direction: column;
            .text {
                margin-top: 21px;
                font-size: 28px;
                font-weight: 400;
                line-height: 28px;
                color: #333;
            }
            .icon {
                text-align: center;
                color: #f5001d;
                font-size: 40px;
            }
        }
    }
    .list {
        margin-left: 24px;
        margin-right: 24px;

        .title {
            padding-left: 28px;
            position: relative;
            margin-top: 48px;
            font-size: 32px;
            font-weight: 500;
            color: #333333;
            z-index: 99;
            line-height: 32px;
        }

        .title:before {
            content: "";
            border-radius: 28px;
            width: 28px;
            height: 28px;
            position: absolute;
            left: 0;
            top: 0;
            z-index: -1;
            display: inline-block;
            transform: translateX(10px);
            background: linear-gradient(90deg, #f5001d 0%, #ffeaef 100%);
        }
        .items {
            margin-top: 24px;
            background: #fff;
            border-radius: 16px;
            padding: 24px 32px;

            .item {
                position: relative;
                display: flex;
                flex-direction: column;
                justify-content: center;
                padding: 20px;

                .name {
                    font-size: 28px;
                    font-weight: 500;
                    color: #333333;
                    line-height: 28px;
                }
                .mobile {
                    margin-top: 16px;
                    font-size: 28px;
                    font-weight: 400;
                    color: #bbbbbb;
                    line-height: 28px;
                }
                &:last-child:after {
                    border: none;
                }
                &:after {
                    position: absolute;
                    top: 0;
                    left: 0;
                    box-sizing: border-box;
                    width: 200%;
                    height: 200%;
                    border-bottom: 1px solid #ddd;
                    border-radius: inherit;
                    content: " ";
                    transform: scale(0.5);
                    transform-origin: 0 0;
                    pointer-events: none;
                }
            }
        }
        .menbox {
            display: flex;
            flex-direction: row;
            justify-content: space-between;
        }
    }
    .close {
        position: absolute;
        right: 0px;
        z-index: 999;
        top: -5px;
        .icon {
            font-size: 60px;
            color: #fff;
        }
    }
}
.share-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-top: 57px;
    .share-image {
        width: 600px;
        border-radius: 16px;
    }

    .save-btn {
        margin-top: 50px;
        width: 600px;
        height: 88px;
        background: #04c060;
        border-radius: 44px;
        font-size: 32px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: #ffffff;
        line-height: 88px;
    }
}
