.invite-open-status {
    padding: 24px;

    .head {
        margin-top: 20rpx;
        display: flex;
        flex-direction: column;
        padding: 32rpx;
        background-color: #fff;
        border-radius: 16rpx;
        .status {
            display: flex;
            align-items: center;

            .text {
                font-size: 36rpx;
                font-family: PingFangSC-Semibold, PingFang SC;
                font-weight: 600;
                color: #333333;
                padding-left: 8rpx;
            }
        }
        .desc {
            margin-top: 16rpx;
            font-size: 28rpx;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #999999;
            line-height: 38rpx;
        }
    }
    .card {
        margin-top: 24px;
        background-color: #fff;
        border-radius: 16rpx;

        .vant-form-formItem-wrapper {
            background: transparent;
        }
        .vant-form-formItem-controll {
            margin-left: 0;
        }
        .van-uploader__upload,
        .van-uploader__preview-image {
            width: 307rpx !important;
            height: 180rpx !important;
        }

        .image-uploader-controll {
            margin-left: 0;
            margin-top: 0;
        }

        .image-uploader-form-item {
            margin: 0;
            padding: 0;
        }

        .image-uploader-form-item-not-padding {
            padding: 10px 0;
        }

        .list_block {
            margin-top: 20rpx;
            border-radius: 16rpx;
            background: #fff;
            margin-left: 24rpx;
            margin-right: 24rpx;
        }

        textarea {
            height: 90px;
            width: 450px;
            line-height: 28px;
            font-size: 28px;
            padding: 8px 0;
        }
    }
    .submit-btn {
        margin: 40rpx 75rpx env(safe-area-inset-bottom);
    }
}
