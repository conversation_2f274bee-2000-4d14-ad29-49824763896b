import { Form, FormItem, Icon, Uploader } from "@antmjs/vantui";
import { View, Textarea, Input, Button, ITouchEvent, Text } from "@tarojs/components";
import "./index.less";
import { FormPicker } from "@/components/form-picker";
import DDYToast from "@/utils/toast";
import Taro, { useDidShow } from "@tarojs/taro";
import api from "@/utils/api";
import { getStorage } from "@/utils/storage";
import { PROJECT_CONFIG } from "@/utils/env";
import { useState } from "react";
import { STORE_ID } from "@/utils/constant";
import { isString } from "@tarojs/shared";
import { DDYRedirectTo, DDYSwitchTab } from "@/utils/route";

export default () => {
    const [shopStatusInfo, setShopStatusInfo] = useState();
    const [disabled, setDisabled] = useState(false);
    const form = Form.useForm();
    const deleteFile = (event: ITouchEvent, name: string | string[]) => {
        const { index, fileList } = event.detail;
        fileList.splice(index, 1);
        form?.setFieldsValue(name, fileList);
    };
    const valueFormatUpload = event => {
        DDYToast.showLoading("上传中...");

        // 异步更新
        return new Promise(resolve => {
            const { file } = event.detail;
            Taro.uploadFile({
                url: `${PROJECT_CONFIG.API_MALL}/api/user/files/upload`, //仅为示例，非真实的接口地址
                name: "file",
                filePath: file.url,
                success(res) {
                    DDYToast.hideLoading();
                    file.url = JSON.parse(res.data).image;
                    resolve([file]);
                },
            });
        });
    };

    const saveStore = formata => {
        api.seviceRegsiterStore({
            method: "POST",
            data: {
                name: formata.name,
                address: formata.address,
                shopId: getStorage(STORE_ID),
                province: formata.province,
                city: formata.city,
                county: formata.county,
                businessImg: formata.businessImg,
                frontImg: formata.frontImg,
                backImg: formata.backImg,
                foodSellAllowImg: formata.foodSellAllowImg,
                bankNo: formata.bankNo,
                bankName: formata.bankName,
                nameInLaw: formata.nameInLaw,
            },
        }).then(res => {
            DDYSwitchTab({ url: "/pages/info" });
            DDYToast.success("重新提交成功");
        });
    };

    const submit = (e: any) => {
        form.validateFields((errorMessage, fieldValues) => {
            // customValid();

            if (errorMessage && errorMessage.length) {
                return;
            }
            const params = { ...fieldValues };
            params.frontImg = params.userInfo.frontImg?.[0].url;
            params.backImg = params.userInfo.backImg?.[0].url;
            params.province = params.region ? params.region.value[0] : "";
            params.city = params.region ? params.region.value[1] : "";
            params.county = params.region ? params.region.value[2] : "";
            params.businessImg = params.businessImg[0].url;
            params.foodSellAllowImg = params.foodSellAllowImg?.[0].url;
            delete params.region;
            delete params.userInfo;
            delete params.userInfoCopy;
            saveStore(params);
        });
    };

    const customValid = () => {
        const value = form.getFieldsValue();
        // console.log("value:", value);
        setTimeout(() => {
            if (!value.userInfo) {
                form.setErrorMessage("userInfoCopy", "请上传身份证人像面");
            } else {
                if (!value.userInfo.frontImg) {
                    form.setErrorMessage("userInfoCopy", "请上传身份证人像面");
                } else if (!value.userInfo.backImg) {
                    form.setErrorMessage("userInfoCopy", "请上传身份证国徽面");
                } else {
                    form.setErrorMessage("userInfoCopy", "");
                }
            }
        }, 100);
    };
    const valueTrim = name => {
        let fieldValue = form.getFieldValue(name);
        if (isString(fieldValue)) {
            form.setFieldsValue(name, fieldValue.trim());
        }
    };
    useDidShow(() => {
        api.getSubStoreId({
            data: {
                shopId: getStorage("storeId"),
            },
        }).then(res => {
            setShopStatusInfo(res);
            setDisabled(res.authStatus !== "审核已拒绝");
            let obj = {
                ...res,
                region: {
                    value: [res.province, res.city, res.county],
                },
                foodSellAllowImg: [{ url: res.foodSellAllowImg, name: "foodSellAllowImg" }],
                businessImg: [{ url: res.businessImg, name: "businessImg" }],
                userInfo: {
                    frontImg: [{ url: res.frontImg, name: "frontImg" }],
                    backImg: [{ url: res.backImg, name: "backImg" }],
                },
            };
            form.setFields(obj);
        });
    });
    return (
        <View className="invite-open-status">
            <View className="head">
                <View className="status">
                    {shopStatusInfo?.authStatus == "审核已拒绝" && (
                        <Icon name={"warning"} size={40} color={"#f5001D"} />
                    )}
                    {shopStatusInfo?.authStatus == "待审核" && <Icon name={"clock"} size={40} color={"#E1C88C"} />}
                    {shopStatusInfo?.authStatus == "审核已通过" && (
                        <Icon name={"checked"} size={40} color={"#04C060"} />
                    )}
                    <View className="text">{shopStatusInfo?.authStatus}</View>
                </View>
                {shopStatusInfo?.reason && (
                    <Text maxLines={2} className="desc">
                        拒绝原因：{shopStatusInfo.reason}
                    </Text>
                )}
            </View>
            <Form
                form={form}
                // 校验占位代码
                initialValues={{ userInfoCopy: "111" }}
            >
                <View className="card">
                    <FormItem
                        label="门店名称"
                        name="name"
                        required
                        borderBottom
                        trigger="onInput"
                        valueFormat={e => e.detail.value}
                    >
                        <Input
                            placeholder="请输入门店名称"
                            maxlength={20}
                            disabled={disabled}
                            onBlur={() => valueTrim("name")}
                        />
                    </FormItem>
                    <FormItem
                        label="所在地区"
                        name="region"
                        mutiLevel
                        required
                        borderBottom
                        valueFormat={e => e.detail}
                        renderRight={<Icon name="arrow" />}
                    >
                        <FormPicker mode={"region"} disabled={disabled} />
                    </FormItem>
                    <FormItem
                        label="详细地址"
                        name="address"
                        required
                        borderBottom
                        trigger="onInput"
                        valueFormat={e => e.detail.value}
                    >
                        <Textarea
                            placeholder="请输入详细地址信息"
                            maxlength={200}
                            disabled={disabled}
                            onBlur={() => valueTrim("address")}
                        />
                    </FormItem>
                    {/* <FormItem
                        label="法人姓名"
                        name="nameInLaw"
                        required
                        borderBottom
                        trigger="onInput"
                        valueFormat={e => e.detail.value}
                    >
                        <Input
                            placeholder="请输入法人姓名"
                            maxlength={15}
                            disabled={disabled}
                            onBlur={() => valueTrim("nameInLaw")}
                        />
                    </FormItem>
                    <FormItem
                        label="银行账号"
                        name="bankNo"
                        required
                        borderBottom
                        trigger="onInput"
                        valueFormat={e => e.detail.value}
                    >
                        <Input
                            placeholder="请输入银行账号"
                            maxlength={50}
                            type={"numberpad"}
                            disabled={disabled}
                            onBlur={() => valueTrim("bankNo")}
                        />
                    </FormItem>
                    <FormItem label="开户机构" name="bankName" trigger="onInput" valueFormat={e => e.detail.value}>
                        <Input
                            placeholder="请输入开户机构"
                            maxlength={50}
                            disabled={disabled}
                            onBlur={() => valueTrim("bankName")}
                        />
                    </FormItem> */}
                </View>
                <View className="card">
                    <FormItem
                        name="businessImg"
                        required
                        mutiLevel
                        layout="vertical"
                        label="营业执照"
                        valueKey="fileList"
                        valueFormat={valueFormatUpload}
                        trigger="onAfterRead"
                        validateTrigger="onAfterRead"
                        className="image-uploader-form-item-not-padding"
                    >
                        <Uploader
                            maxCount={1}
                            className="uploader-img"
                            imageFit="widthFix"
                            onDelete={event => deleteFile(event, "businessImg")}
                            uploadText="上传营业执照"
                            deletable={!disabled}
                            disabled={disabled}
                        ></Uploader>
                    </FormItem>
                    <FormItem
                        name={"userInfoCopy"}
                        // required
                        layout="vertical"
                        label="法人身份证"
                        className="image-uploader-form-item-group image-uploader-form-item-not-padding"
                    >
                        <FormItem
                            name={["userInfo", "frontImg"]}
                            layout="vertical"
                            mutiLevel
                            label=""
                            valueKey="fileList"
                            valueFormat={valueFormatUpload}
                            trigger="onAfterRead"
                            validateTrigger="onAfterRead"
                            className="image-uploader-form-item"
                            controllClassName="image-uploader-controll"
                        >
                            <Uploader
                                maxCount={1}
                                className="uploader-img"
                                onDelete={event => deleteFile(event, ["userInfo", "frontImg"])}
                                uploadText="上传人像面"
                                deletable={!disabled}
                                disabled={disabled}
                            ></Uploader>
                        </FormItem>
                        <FormItem
                            name={["userInfo", "backImg"]}
                            label=""
                            mutiLevel
                            layout="vertical"
                            valueKey="fileList"
                            valueFormat={valueFormatUpload}
                            trigger="onAfterRead"
                            validateTrigger="onAfterRead"
                            className="image-uploader-form-item"
                            controllClassName="image-uploader-controll"
                        >
                            <Uploader
                                maxCount={1}
                                className="uploader-img"
                                onDelete={event => deleteFile(event, ["userInfo", "backImg"])}
                                uploadText="上传国徽面"
                                imageFit="widthFix"
                                deletable={!disabled}
                                disabled={disabled}
                            ></Uploader>
                        </FormItem>
                    </FormItem>

                    <FormItem
                        name="foodSellAllowImg"
                        // required
                        mutiLevel
                        layout="vertical"
                        label="食品流通许可证"
                        valueKey="fileList"
                        valueFormat={valueFormatUpload}
                        trigger="onAfterRead"
                        validateTrigger="onAfterRead"
                        className="image-uploader-form-item-not-padding"
                    >
                        <Uploader
                            maxCount={1}
                            className="uploader-img"
                            onDelete={event => deleteFile(event, "foodSellAllowImg")}
                            uploadText="上传营许可证"
                            imageFit="widthFix"
                            deletable={!disabled}
                            disabled={disabled}
                        ></Uploader>
                    </FormItem>
                </View>
                {shopStatusInfo?.authStatus == "审核已拒绝" && (
                    <Button type="primary" className="submit-btn" onClick={submit}>
                        重新提交
                    </Button>
                )}
            </Form>
        </View>
    );
};
