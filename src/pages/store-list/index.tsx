import React, { useMemo, useRef, useState } from "react";
import { View } from "@tarojs/components";
import {
    Empty,
    InfiniteScroll,
    InfiniteScrollProps,
    InfiniteScrollInstance,
    IPullToRefreshProps,
    PullToRefresh,
    Popup,
    Image,
    Button,
} from "@antmjs/vantui";
import api from "@/utils/api";
import { getStorage } from "@/utils/storage";

import usePoster from "@/components/createPoster/usePoster";
import "./index.less";
import { buildServerProviderPost } from "@/components/createPoster/createPoster";
import { DDYNavigateTo } from "@/utils/route";
import { useDidShow } from "@tarojs/taro";
import { PROJECT_CONFIG } from "@/utils/env";
import { mall_event, mall_event_key, reportEvent } from "@/utils/track-report";

const StoreList: React.FC = () => {
    const [record, setRecord] = useState<any[]>([]);
    const [serviceInfo, setServiceInfo] = useState();
    const [show, setShow] = useState(false);
    const pageNo = useRef(1);
    const infiniteScrollInstance = useRef<InfiniteScrollInstance>();
    useDidShow(() => {
        pageNo.current = 1;
        infiniteScrollInstance.current?.reset(true);
    });
    const { poster, sharePath, handleSavePhoto } = usePoster({
        posterInfo: serviceInfo,
        createPalette: () => {
            return buildServerProviderPost({
                postBgUrl: PROJECT_CONFIG.INVITE_BG,
                miniQr: serviceInfo?.img,
                serverAvatarUrl: serviceInfo?.avatar,
                serverShopName: serviceInfo?.name,
            });
        },
        onImgOK: () => {
            setShow(true);
        },
    });

    const onRefresh: IPullToRefreshProps["onRefresh"] = () => {
        return new Promise(resolve => {
            pageNo.current = 1;
            infiniteScrollInstance.current?.reset(true);
            resolve(undefined);
        });
    };
    const loadMore: InfiniteScrollProps["loadMore"] = () => {
        return new Promise(resolve => {
            getInvitationRecord()
                .then(res => {
                    let newData: any[];
                    if (pageNo.current === 1) {
                        newData = res.data;
                    } else {
                        newData = record.concat(res.data);
                    }
                    pageNo.current++;
                    setRecord(newData);
                    if (newData.length == 0) {
                        resolve("complete");
                    } else {
                        resolve(newData.length < res.total ? "loading" : "complete");
                    }
                })
                .catch(e => {
                    resolve("error");
                });
        });
    };

    const getInvitationRecord = () => {
        return api.getStoreList({ data: { shopId: getStorage("storeId"), size: 10, page: pageNo.current } });
    };
    const addGuider = () => {
        DDYNavigateTo({ url: "/pages/store-list/add-store/index" });
    };
    const inviteClick = () => {
        reportEvent(
            mall_event.MALL_EVENT_SHARE_INVITE_GUIDER_MINI_QR,
            new Map().set(mall_event_key.MALL_KEY_INVITE_ADD_GUIDER_FROM, "beforeGet"),
        );
        api.serviceInviteImg({
            data: {
                shopId: getStorage("storeId"),
                projectId: PROJECT_CONFIG.PROJECT_ID,
                page: "pages/invite-open-shop",
            },
            showLoad: true,
            filterCheck: true,
        }).then(res => {
            setServiceInfo(res);
            reportEvent(
                mall_event.MALL_EVENT_SHARE_INVITE_GUIDER_MINI_QR,
                new Map()
                    .set(mall_event_key.MALL_KEY_INVITE_ADD_STORE_SERVICE_PROVIDER_QR, res?.img ?? "")
                    .set(mall_event_key.MALL_KEY_INVITE_ADD_STORE_SERVICE_PROVIDER_AVATAR, res?.avatar ?? "")
                    .set(mall_event_key.MALL_KEY_INVITE_ADD_STORE_SERVICE_PROVIDER_NAME, res?.name ?? "")
                    .set(mall_event_key.MALL_KEY_INVITE_ADD_GUIDER_FROM, "getSuccess"),
            );
        });
    };

    return (
        <PullToRefresh onRefresh={onRefresh}>
            <View className="store-list">
                <View className="top">
                    <View className="top-item" onClick={addGuider}>
                        <View className="dt-mini  icon-mini-add-box-fill icon" />
                        <View className="text">添加门店</View>
                    </View>
                    <View className="top-item" onClick={inviteClick}>
                        <View className="dt-mini icon-mini-reply-fill icon" />
                        <View className="text">邀请门店</View>
                    </View>
                </View>
                <View className="list">
                    <View className="title">门店列表</View>
                    <View className="items">
                        {record?.map((item, index) => {
                            return (
                                <View className="item" key={item.id}>
                                    <View className="menbox">
                                        <View className="name">{item.name}</View>
                                        <View>{item.authStatus}</View>
                                    </View>
                                    <View className="mobile">{item.mobile}</View>
                                    <View className="mobile">
                                        {item.province}
                                        {item.city}
                                        {item.county}
                                        {item.address}
                                    </View>
                                </View>
                            );
                        })}
                    </View>
                    <InfiniteScroll
                        loadMore={loadMore}
                        ref={infiniteScrollInstance}
                        completeText={
                            <>
                                {record.length == 0 ? (
                                    <Empty
                                        description="暂无数据！"
                                        image="https://dante-img.oss-cn-hangzhou.aliyuncs.com/55439458101.png"
                                    />
                                ) : (
                                    "没有更多了"
                                )}
                            </>
                        }
                    />
                </View>
                {poster}
                <Popup show={show} style={{ background: "#00000000" }}>
                    <View className="close" onClick={() => setShow(false)}>
                        <View className="iconfont iconfont_2n icon-close-circle-line icon"></View>
                    </View>
                    <View className="share-container">
                        <Image fit="widthFix" src={sharePath} className="share-image" />
                        <Button
                            className="save-btn"
                            onClick={() => {
                                handleSavePhoto().then(() => {
                                    setShow(false);
                                });
                            }}
                        >
                            保存图片
                        </Button>
                    </View>
                </Popup>
            </View>
        </PullToRefresh>
    );
};
export default StoreList;
