import { DDYRedirectTo, DDYSwitchTab } from "@/utils/route";
import { Form, FormItem, Icon, Uploader } from "@antmjs/vantui";
import { View, Image, Textarea, Input, Button, ITouchEvent } from "@tarojs/components";
import "./index.less";
import { FormPicker } from "@/components/form-picker";
import DDYToast from "@/utils/toast";
import Taro, { useDidShow, useLoad } from "@tarojs/taro";
import api from "@/utils/api";
import { getStorage } from "@/utils/storage";
import { PROJECT_CONFIG } from "@/utils/env";
import { useRef, useState } from "react";
import { scene_decode } from "@/utils/common";
import { STORE_ID } from "@/utils/constant";
import PageHeader from "./components/page-header";
import { DDYObject } from "../../../type/common";
import { isString } from "@tarojs/shared";
import { mall_event, mall_event_key, reportEvent } from "@/utils/track-report";
import { SuccessModal } from "./components/success-modal";

export default () => {
    const form = Form.useForm();
    const wxCode = useRef("");
    const inviteCode = useRef("");
    // const inviteCode = useRef(route.scene)
    //@ts-ignore

    const [serviceAvatar, setServiceAvatar] = useState("");
    const [serviceName, setServiceName] = useState("");
    const [show, setShow] = useState(false);
    const deleteFile = (event: ITouchEvent, name: string | string[]) => {
        const { index, fileList } = event.detail;
        fileList.splice(index, 1);
        form?.setFieldsValue(name, fileList);
    };
    const valueFormatUpload = event => {
        DDYToast.showLoading("上传中...");

        // 异步更新
        return new Promise(resolve => {
            const { file } = event.detail;
            console.log("file", file);
            Taro.uploadFile({
                url: `${PROJECT_CONFIG.API_MALL}/api/user/files/upload`, //仅为示例，非真实的接口地址
                name: "file",
                filePath: file.url,
                success(res) {
                    DDYToast.hideLoading();
                    file.url = JSON.parse(res.data).image;
                    resolve([file]);
                },
            });
        });
    };

    const addStore = (code, encryptedData, iv, formata) => {
        const data = {
            name: formata.name,
            province: formata.province,
            city: formata.city,
            county: formata.county,
            address: formata.address,
            inviteCode: inviteCode.current,
            code: code,
            encryptedData: encryptedData,
            iv: iv,
            shopId: getStorage("storeId"),
            projectId: Number(PROJECT_CONFIG.PROJECT_ID),
            frontImg: formata.frontImg,
            backImg: formata.backImg,
            businessImg: formata.businessImg,
            foodSellAllowImg: formata.foodSellAllowImg,
            bankNo: formata.bankNo,
            bankName: formata.bankName,
            nameInLaw: formata.nameInLaw,
        };
        api.seviceRegsiterStore({
            method: "POST",
            data: data,
        })
            .then(_ => {
                reportEvent(
                    mall_event.MALL_EVENT_SCAN_SERVICE_PROVIDER_MINI_QR_SUBMIT,
                    new Map()
                        .set(mall_event_key.MALL_KEY_INVITE_CODE, inviteCode.current)
                        .set(mall_event_key.MALL_KEY_LOGIN_CALL_BACK, wxCode.current)
                        .set(mall_event_key.MALL_KEY_SUBMIT, "success")
                        .set(mall_event_key.MALL_KEY_SUBMIT_INFO, JSON.stringify(data)),
                );
                setShow(true);
                DDYToast.success("保存成功");
            })
            .catch(res => {
                reportEvent(
                    mall_event.MALL_EVENT_SCAN_SERVICE_PROVIDER_MINI_QR_SUBMIT,
                    new Map()
                        .set(mall_event_key.MALL_KEY_INVITE_CODE, inviteCode.current)
                        .set(mall_event_key.MALL_KEY_LOGIN_CALL_BACK, wxCode.current)
                        .set(mall_event_key.MALL_KEY_SUBMIT, "fail")
                        .set(mall_event_key.MALL_KEY_SUBMIT_INFO, JSON.stringify(res)),
                );
                getWxLoginCode();
            });
    };
    const checkSession = () => {
        if (process.env.TARO_ENV === "weapp") {
            return Taro.checkSession();
        } else {
            return Promise.resolve();
        }
    };
    const getPhoneNumber = (e: any) => {
        console.log(e.detail);
        let { encryptedData, iv } = e.detail;
        submit(encryptedData, iv);
    };
    function submit(encryptedData: any, iv: any) {
        form.validateFields((errorMessage, fieldValues) => {
            console.log("fieldValues:", fieldValues);
            // form.setErrorMessage("userInfoCopy", "")
            // customValid();

            if (errorMessage && errorMessage.length) {
                return;
            }
            const params = { ...fieldValues };
            params.frontImg = params.userInfo.frontImg?.[0].url;
            params.backImg = params.userInfo.backImg?.[0].url;
            params.province = params.region ? params.region.value[0] : "";
            params.city = params.region ? params.region.value[1] : "";
            params.county = params.region ? params.region.value[2] : "";
            params.businessImg = params.businessImg[0].url;
            params.foodSellAllowImg = params.foodSellAllowImg?.[0].url;
            delete params.region;
            delete params.userInfo;
            delete params.userInfoCopy;

            checkSession()
                .then(() => {
                    addStore(wxCode.current, encryptedData, iv, params);
                })
                .catch(_ => {
                    Taro.login({
                        success: async function (res) {
                            let code = res.code;
                            if (code) {
                                wxCode.current = code;
                                addStore(code, encryptedData, iv, params);
                            } else {
                                // tip.error("微信授权登录失败");
                            }
                        },
                    });
                });
        });
    }
    const getInviterInfo = (inviteCodeStr: string) => {
        // let that = this;
        api.getInviterInfo({
            data: {
                code: inviteCodeStr,
            },
        }).then(res => {
            let { name, avatar } = res;
            setServiceName(name);
            setServiceAvatar(avatar);
        });
    };
    const getCheckStatus = () => {
        api.authcurrent({
            data: {
                shopId: getStorage(STORE_ID),
            },
            isDefaultGoLogin: false,
        }).then(data => {
            let { role, auth } = data;
            switch (role) {
                //门店
                case "subStore":
                    if (auth == 2) {
                        //审核通过
                        DDYToast.showModal({
                            title: "提示",
                            content: "您的身份已经是门店啦~",
                            showCancel: false,
                            success(res) {
                                if (res.confirm) {
                                    DDYSwitchTab({ url: "/pages/info" });
                                }
                            },
                        });
                    } else {
                        DDYRedirectTo({
                            url: "/pages/store-list/invite-open-status/index",
                        });
                    }
                    break;
                //服务商
                case "serviceProvider":
                    DDYToast.showModal({
                        title: "提示",
                        content: "您的身份为服务商，无法成为门店",
                        showCancel: false,
                        success(res) {
                            if (res.confirm) {
                                DDYSwitchTab({ url: "/pages/info" });
                            }
                        },
                    });
                    break;
                case "guider":
                    DDYToast.showModal({
                        title: "提示",
                        content: "您的身份为导购，无法成为门店",
                        showCancel: false,
                        success(res) {
                            if (res.confirm) {
                                DDYSwitchTab({ url: "/pages/info" });
                            }
                        },
                    });
                    break;
                default:
                    break;
            }
        });
    };

    // @ts-ignore
    const customValid = () => {
        const value = form.getFieldsValue();
        // console.log("value:", value);
        setTimeout(() => {
            if (!value.userInfo) {
                form.setErrorMessage("userInfoCopy", "请上传身份证人像面");
            } else {
                if (!value.userInfo.frontImg) {
                    form.setErrorMessage("userInfoCopy", "请上传身份证人像面");
                } else if (!value.userInfo.backImg) {
                    form.setErrorMessage("userInfoCopy", "请上传身份证国徽面");
                } else {
                    form.setErrorMessage("userInfoCopy", "");
                }
            }
        }, 100);
    };
    useLoad((param: DDYObject) => {
        const { scene } = param;
        // @ts-ignore
        const c = scene_decode(scene).c;
        const query = Taro.getLaunchOptionsSync().query;
        if (!c) {
            inviteCode.current = query.code;
        } else {
            inviteCode.current = c;
        }
        reportEvent(
            mall_event.MALL_EVENT_SCAN_SERVICE_PROVIDER_MINI_QR,
            new Map().set(mall_event_key.MALL_KEY_INVITE_CODE, c),
        );
        getInviterInfo(inviteCode.current);
    });
    // @ts-ignore
    useDidShow((options: any) => {
        //登录状态检查状态
        getCheckStatus();
        //未登录下获取wxCode
        getWxLoginCode();
    });
    function getWxLoginCode() {
        Taro.login({
            success: async function (res) {
                let code = res.code;
                if (code) {
                    wxCode.current = code;
                    let map = new Map();
                    map.set(mall_event_key.MALL_KEY_INVITE_CODE, inviteCode.current);
                    map.set(mall_event_key.MALL_KEY_LOGIN_CALL_BACK, wxCode.current);
                    reportEvent(mall_event.MALL_EVENT_SCAN_SERVICE_PROVIDER_MINI_QR, map);
                } else {
                    let map = new Map();
                    map.set(mall_event_key.MALL_KEY_INVITE_CODE, inviteCode.current);
                    map.set(mall_event_key.MALL_KEY_LOGIN_CALL_BACK, "fail,微信授权登录失败");
                    reportEvent(mall_event.MALL_EVENT_SCAN_SERVICE_PROVIDER_MINI_QR, map);
                    // tip.error("微信授权登录失败");
                }
            },
        });
    }
    const valueTrim = name => {
        let fieldValue = form.getFieldValue(name);
        if (isString(fieldValue)) {
            form.setFieldsValue(name, fieldValue.trim());
        }
    };
    return (
        <View className="invite-open-shop">
            <PageHeader />
            <View className="page-head">
                <Image className="head-bg" src={"https://dante-img.oss-cn-hangzhou.aliyuncs.com/55354601569.png"} />
                <View className="service-info">
                    <Image className="service-img" src={serviceAvatar} />
                    <View className="service-name">{serviceName}</View>
                </View>
            </View>
            <Form
                form={form}
                // 占位代码
                initialValues={{ userInfoCopy: "111" }}
                style={{
                    position: "relative",
                    top: "-24rpx",
                    // paddingBottom: '80rpx'
                }}
            >
                <View className="card">
                    <FormItem
                        label="门店名称"
                        name="name"
                        required
                        borderBottom
                        trigger="onInput"
                        valueFormat={e => e.detail.value}
                    >
                        <Input placeholder="请输入门店名称" maxlength={20} onBlur={() => valueTrim("name")} />
                    </FormItem>
                    <FormItem
                        label="所在地区"
                        name="region"
                        required
                        borderBottom
                        valueFormat={e => e.detail}
                        renderRight={<Icon name="arrow" />}
                    >
                        <FormPicker mode={"region"} />
                    </FormItem>
                    <FormItem
                        label="详细地址"
                        name="address"
                        required
                        borderBottom
                        trigger="onInput"
                        valueFormat={e => e.detail.value}
                    >
                        <Textarea
                            placeholder="请输入详细地址信息"
                            maxlength={200}
                            showCount
                            onBlur={() => valueTrim("address")}
                        />
                    </FormItem>
                    {/* <FormItem
                        label="法人姓名"
                        name="nameInLaw"
                        required
                        borderBottom
                        trigger="onInput"
                        valueFormat={e => e.detail.value}
                    >
                        <Input placeholder="请输入法人姓名" maxlength={15} onBlur={() => valueTrim("nameInLaw")} />
                    </FormItem> */}
                    {/* <FormItem
                        label="银行账号"
                        name="bankNo"
                        required
                        borderBottom
                        trigger="onInput"
                        valueFormat={e => e.detail.value}
                    >
                        <Input
                            placeholder="请输入银行账号"
                            maxlength={50}
                            type={"numberpad"}
                            onBlur={() => valueTrim("bankNo")}
                        />
                    </FormItem>
                    <FormItem label="开户机构" name="bankName" trigger="onInput" valueFormat={e => e.detail.value}>
                        <Input placeholder="请输入开户机构" maxlength={50} onBlur={() => valueTrim("bankName")} />
                    </FormItem> */}
                </View>
                <View className="card">
                    <FormItem
                        name="businessImg"
                        required
                        layout="vertical"
                        label="营业执照"
                        valueKey="fileList"
                        valueFormat={valueFormatUpload}
                        trigger="onAfterRead"
                        validateTrigger="onAfterRead"
                        className="image-uploader-form-item-not-padding"
                    >
                        <Uploader
                            maxCount={1}
                            className="uploader-img"
                            onDelete={event => deleteFile(event, "businessImg")}
                            uploadText="上传营业执照"
                        ></Uploader>
                    </FormItem>
                    <FormItem
                        name={"userInfoCopy"}
                        // required
                        layout="vertical"
                        label="法人身份证"
                        className="image-uploader-form-item-group image-uploader-form-item-not-padding"
                    >
                        <FormItem
                            name={["userInfo", "frontImg"]}
                            layout="vertical"
                            label=""
                            valueKey="fileList"
                            valueFormat={valueFormatUpload}
                            trigger="onAfterRead"
                            validateTrigger="onAfterRead"
                            className="image-uploader-form-item"
                            controllClassName="image-uploader-controll"
                        >
                            <Uploader
                                maxCount={1}
                                className="uploader-img"
                                onDelete={event => deleteFile(event, ["userInfo", "frontImg"])}
                                uploadText="上传人像面"
                            ></Uploader>
                        </FormItem>
                        <FormItem
                            name={["userInfo", "backImg"]}
                            label=""
                            layout="vertical"
                            valueKey="fileList"
                            valueFormat={valueFormatUpload}
                            trigger="onAfterRead"
                            validateTrigger="onAfterRead"
                            className="image-uploader-form-item"
                            controllClassName="image-uploader-controll"
                        >
                            <Uploader
                                maxCount={1}
                                className="uploader-img"
                                onDelete={event => deleteFile(event, ["userInfo", "backImg"])}
                                uploadText="上传国徽面"
                                imageFit="widthFix"
                            ></Uploader>
                        </FormItem>
                    </FormItem>

                    <FormItem
                        name="foodSellAllowImg"
                        // required
                        layout="vertical"
                        label="食品流通许可证"
                        valueKey="fileList"
                        valueFormat={valueFormatUpload}
                        trigger="onAfterRead"
                        validateTrigger="onAfterRead"
                        className="image-uploader-form-item-not-padding"
                    >
                        <Uploader
                            maxCount={1}
                            className="uploader-img"
                            onDelete={event => deleteFile(event, "foodSellAllowImg")}
                            uploadText="上传营许可证"
                        ></Uploader>
                    </FormItem>
                </View>
                {process.env.TARO_ENV === "weapp" && (
                    <Button
                        type="primary"
                        className="submit-btn"
                        open-type="getPhoneNumber"
                        onGetPhoneNumber={getPhoneNumber}
                    >
                        提交
                    </Button>
                )}
                {process.env.TARO_ENV === "alipay" && (
                    <Button
                        type="primary"
                        className="submit-btn"
                        onClick={() => {
                            // @ts-ignore
                            Taro.getPhoneNumber({
                                success: res => {
                                    let encryptedData = res.response;
                                    submit(encryptedData, "");
                                },
                                fail: res => {
                                    console.log(res);
                                },
                            });
                        }}
                    >
                        提交
                    </Button>
                )}
                <View style={{ height: "1rpx" }} />
            </Form>
            <SuccessModal show={show} onClose={() => DDYSwitchTab({ url: "/pages/info" })} />
        </View>
    );
};
