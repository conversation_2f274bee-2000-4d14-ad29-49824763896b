import { Icon, Popup } from "@antmjs/vantui";
import { View } from "@tarojs/components";
import "./success-modal.less";

export function SuccessModal({ show, onClose }) {
    return (
        <Popup show={show} round onClose={onClose}>
            <View className={"invite-open-shop-success-modal"}>
                <View className="content">
                    <Icon className="icon" name={"clock"} color={"#E1C88C"} size={100} />
                    <View className="title">申请已提交，等待管理员审核！</View>
                    <View className="sub-title">审核结果会在小程序【我的】页面中显示</View>
                </View>
                <View className="footer">
                    <View className="close" onClick={onClose}>
                        我知道了
                    </View>
                </View>
            </View>
        </Popup>
    );
}
