import { isAlipay } from "@/utils/common";
import { DDYBack, DDYSwitchTab } from "@/utils/route";
import { NavBar } from "@antmjs/vantui";
import { usePageScroll } from "@tarojs/taro";
import { useState } from "react";

export default () => {
    const [top, setTop] = useState(false);
    usePageScroll(payload => {
        if (payload.scrollTop > 100) {
            setTop(true);
        } else {
            setTop(false);
        }
    });

    return (
        <NavBar
            title="邀请开店"
            leftText=""
            className={`page-title ${top ? "white" : "transparent"}`}
            fixed
            leftArrow={!isAlipay()}
            border={false}
            onClickLeft={() => {
                DDYBack({ delta: 1 }).catch(() => {
                    DDYSwitchTab({ url: "/pages/index/index" });
                });
            }}
        />
    );
};
