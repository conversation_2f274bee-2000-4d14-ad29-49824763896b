import React, { useEffect } from "react";
import { View, Input, Textarea } from "@tarojs/components";
import { Button, Form, FormItem, Icon, Uploader } from "@antmjs/vantui";
import Toast from "@/utils/toast";
import Taro from "@tarojs/taro";
import { FormPicker } from "@/components/form-picker";
import "./index.less";
import api from "@/utils/api";
import { getStorage } from "@/utils/storage";
import { STORE_ID } from "@/utils/constant";
import { DDYBack } from "@/utils/route";
import { PROJECT_CONFIG } from "@/utils/env";
import { isString } from "@tarojs/shared";

const StoreList: React.FC = () => {
    const formIt = Form.useForm(undefined, { userInfoCopy: "无效占位值" });
    const deleteFile = (event, name) => {
        const { index, fileList } = event.detail;
        fileList.splice(index, 1);

        formIt?.setFieldsValue(name, fileList);
    };
    const valueFormatUpload = event => {
        Toast.showLoading("上传中...");

        // 异步更新
        return new Promise(resolve => {
            const { file } = event.detail;
            console.log("file", file);
            Taro.uploadFile({
                url: `${PROJECT_CONFIG.API_MALL}/api/user/files/upload`, //仅为示例，非真实的接口地址
                name: "file",
                filePath: file.url,
                success(res) {
                    console.log("res", JSON.parse(res.data).image);
                    Toast.hideLoading();
                    file.url = JSON.parse(res.data).image;
                    console.log("file", file);
                    resolve([file]);
                    // customValid();
                },
            });
        });
    };
    const addStore = (value: Record<string, any>) => {
        if (value.region) {
            value.province = value.region.value[0];
            value.city = value.region.value[1];
            value.county = value.region.value[2];
            delete value.region;
        }
        if (value.useInfo) {
            value.frontImg = value.useInfo.frontImg?.[0].url;
            value.backImg = value.useInfo.backImg?.[0].url;
            delete value.useInfo;
        }
        value.businessImg = value.businessImg ? value.businessImg[0].url : "";
        value.foodSellAllowImg = value.foodSellAllowImg ? value.foodSellAllowImg?.[0].url : "";
        api.addStore({
            method: "POST",
            data: {
                ...value,
                shopId: getStorage(STORE_ID),
            },
        }).then(res => {
            DDYBack();
            Toast.success("保存成功");
        });
    };

    const customValid = () => {
        const value = formIt.getFieldsValue();
        setTimeout(() => {
            if (!value.userInfo) {
                formIt.setErrorMessage("userInfoCopy", "请上传身份证人像面");
            } else {
                if (!value.userInfo.frontImg) {
                    formIt.setErrorMessage("userInfoCopy", "请上传身份证人像面");
                } else if (!value.userInfo.backImg) {
                    formIt.setErrorMessage("userInfoCopy", "请上传身份证国徽面");
                } else {
                    formIt.setErrorMessage("userInfoCopy", "");
                }
            }
        }, 100);
    };
    const valueTrim = name => {
        let fieldValue = formIt.getFieldValue(name);
        if (isString(fieldValue)) {
            formIt.setFieldsValue(name, fieldValue.trim());
        }
    };
    return (
        <View className="add-store">
            <Form
                form={formIt}
                onFinish={(errs, res) => {
                    addStore(res);
                }}
            >
                <View className="list_block">
                    <FormItem
                        label="门店名称"
                        name="name"
                        required
                        borderBottom
                        trigger="onInput"
                        valueFormat={e => e.detail.value}
                    >
                        <Input placeholder="请输入门店名称" maxlength={20} onBlur={() => valueTrim("name")} />
                    </FormItem>
                    <FormItem
                        label="手机号码"
                        name="mobile"
                        required
                        borderBottom
                        trigger="onInput"
                        valueFormat={e => e.detail.value}
                    >
                        <Input
                            placeholder="请输入手机号码"
                            maxlength={11}
                            type="number"
                            onBlur={() => valueTrim("mobile")}
                        />
                    </FormItem>
                    <FormItem
                        label="所在地区"
                        name="region"
                        required
                        borderBottom
                        valueFormat={e => e.detail}
                        renderRight={<Icon name="arrow" />}
                    >
                        <FormPicker mode={"region"} />
                    </FormItem>
                    <FormItem
                        label="详细地址"
                        name="address"
                        required
                        borderBottom
                        trigger="onInput"
                        valueFormat={e => e.detail.value}
                    >
                        <Textarea
                            placeholder="请输入详细地址信息"
                            maxlength={200}
                            onBlur={() => valueTrim("address")}
                        />
                    </FormItem>
                    {/* <FormItem
                        label="法人姓名"
                        name="nameInLaw"
                        required
                        borderBottom
                        trigger="onInput"
                        valueFormat={e => e.detail.value}
                    >
                        <Input placeholder="请输入法人姓名" maxlength={15} onBlur={() => valueTrim("nameInLaw")} />
                    </FormItem>
                    <FormItem
                        label="银行账号"
                        name="bankNo"
                        required
                        borderBottom
                        trigger="onInput"
                        valueFormat={e => e.detail.value}
                    >
                        <Input
                            placeholder="请输入银行账号"
                            maxlength={50}
                            type={"numberpad"}
                            onBlur={() => valueTrim("bankNo")}
                        />
                    </FormItem>
                    <FormItem label="开户机构" name="bankName" trigger="onInput" valueFormat={e => e.detail.value}>
                        <Input placeholder="请输入开户机构" maxlength={50} onBlur={() => valueTrim("bankName")} />
                    </FormItem> */}
                </View>
                <View className="list_block">
                    <FormItem
                        name="businessImg"
                        required
                        layout="vertical"
                        label="营业执照"
                        valueKey="fileList"
                        valueFormat={valueFormatUpload}
                        trigger="onAfterRead"
                        validateTrigger="onAfterRead"
                        className="image-uploader-form-item-not-padding"
                    >
                        <Uploader
                            maxCount={1}
                            onDelete={event => deleteFile(event, "businessImg")}
                            uploadText="上传营业执照"
                        ></Uploader>
                    </FormItem>
                    <FormItem
                        name="userInfoCopy"
                        // required
                        layout="vertical"
                        label="法人身份证"
                        className="image-uploader-form-item-group"
                    >
                        <FormItem
                            name={["useInfo", "frontImg"]}
                            layout="vertical"
                            label=""
                            valueKey="fileList"
                            valueFormat={valueFormatUpload}
                            trigger="onAfterRead"
                            validateTrigger="onAfterRead"
                            className="image-uploader-form-item"
                            controllClassName="image-uploader-controll"
                        >
                            <Uploader
                                maxCount={1}
                                onDelete={event => deleteFile(event, ["useInfo", "frontImg"])}
                                uploadText="上传人像面"
                            ></Uploader>
                        </FormItem>
                        <FormItem
                            name={["useInfo", "backImg"]}
                            label=""
                            layout="vertical"
                            valueKey="fileList"
                            valueFormat={valueFormatUpload}
                            trigger="onAfterRead"
                            validateTrigger="onAfterRead"
                            className="image-uploader-form-item"
                            controllClassName="image-uploader-controll"
                        >
                            <Uploader
                                maxCount={1}
                                onDelete={event => deleteFile(event, ["useInfo", "backImg"])}
                                uploadText="上传国徽面"
                                imageFit="widthFix"
                            ></Uploader>
                        </FormItem>
                    </FormItem>

                    <FormItem
                        name="foodSellAllowImg"
                        // required
                        layout="vertical"
                        label="食品流通许可证"
                        valueKey="fileList"
                        valueFormat={valueFormatUpload}
                        trigger="onAfterRead"
                        validateTrigger="onAfterRead"
                        className="image-uploader-form-item-not-padding"
                    >
                        <Uploader
                            maxCount={1}
                            onDelete={event => deleteFile(event, "foodSellAllowImg")}
                            uploadText="上传营许可证"
                        ></Uploader>
                    </FormItem>
                </View>
            </Form>
            <Button
                type="primary"
                className="submit-btn"
                round
                onClick={() => {
                    formIt.submit();
                    // customValid();
                }}
            >
                提交
            </Button>
        </View>
    );
};
export default StoreList;
