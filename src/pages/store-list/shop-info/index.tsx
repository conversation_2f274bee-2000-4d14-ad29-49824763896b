import { Input, Textarea, View } from "@tarojs/components";
import { Form, FormItem, Icon } from "@antmjs/vantui";
import { FormPicker } from "@/components/form-picker";
import "./index.less";
import { useLoad } from "@tarojs/taro";
import api from "@/utils/api";
import { getStorage } from "@/utils/storage";
import { STORE_ID } from "@/utils/constant";

definePageConfig({
    navigationBarTitleText: "门店信息",
});

export default () => {
    const formIt = Form.useForm();
    useLoad(() => {
        getStoreInfo();
    });
    const getStoreInfo = () => {
        api.getOrSetStoreInfo({
            data: { sourceId: getStorage(STORE_ID) },
            filterCheck: true,
        }).then(res => {
            formIt.setFields({
                ...res,
                region: {
                    value: [res.province, res.city, res.county],
                },
            });
        });
    };
    return (
        <View className={"shop-info"}>
            <Form form={formIt}>
                <FormItem label="门店名称" name="name" borderBottom trigger="onInput" valueFormat={e => e.detail.value}>
                    <Input disabled />
                </FormItem>
                <FormItem
                    label="联系方式"
                    name="mobile"
                    borderBottom
                    trigger="onInput"
                    valueFormat={e => e.detail.value}
                >
                    <Input disabled />
                </FormItem>
                <FormItem
                    label="所在地区"
                    name="region"
                    mutiLevel
                    borderBottom
                    valueFormat={e => e.detail}
                    renderRight={<Icon name="arrow" />}
                >
                    <FormPicker mode={"region"} disabled={true} />
                </FormItem>
                <FormItem
                    label="详细地址"
                    name="address"
                    borderBottom
                    trigger="onInput"
                    valueFormat={e => e.detail.value}
                >
                    <Textarea maxlength={200} disabled />
                </FormItem>
            </Form>
        </View>
    );
};
