import { View, Text, ScrollView } from "@tarojs/components";
import { Button } from "@antmjs/vantui";
import "./index.less";
import { useEffect, useState } from "react";
import newAPi from "@/utils/newApi";
import { useRouter } from "@tarojs/taro";
import { DDYNavigateTo, DDYSwitchTab } from "@/utils/route";

definePageConfig({
    navigationBarTitleText: "我的优惠券",
});
const Coupon = () => {
    const [coupons, setCoupons] = useState<any>([]);
    const router = useRouter();
    // 字段WAIT_USE:待使用 USED:已使用 TIME_OUT
    let tabs = [
        {
            key: 1,
            name: "未使用",
            color: "red",
            status: "WAIT_USE",
        },
        {
            key: 2,
            name: "已使用",
            color: "gray",
            status: "USED",
        },
        {
            key: 3,
            name: "已过期",
            color: "gray",
            status: "TIME_OUT",
        },
    ];

    const [active, setActive] = useState("WAIT_USE");

    function optimizeCoupons(coupons) {
        return coupons.map(item => {
            let amountDesc = "";
            let methodDesc = "";

            switch (item.method) {
                case "DISCOUNT":
                    amountDesc = `${item.discount}折`;
                    methodDesc = "折扣";
                    break;
                case "DIRECT_REDUCTION":
                    amountDesc = `￥${item.deductionAmount}`;
                    methodDesc = "立减";
                    break;
                case "FULL_REDUCTION":
                    amountDesc = `￥${item.thresholdDeductionAmount}`;
                    methodDesc = `满${item.thresholdAmount}减`;
                    break;
                default:
                    break;
            }

            return {
                ...item,
                amountDesc,
                methodDesc,
            };
        });
    }

    const getUsedCoupon = status => {
        newAPi
            .queryCoupons({
                method: "GET",
                data: {
                    unGet: false,
                    status: status || "WAIT_USE",
                    // goodsCode: 6914
                },
            })
            .then(res => {
                const optimizedCoupons = optimizeCoupons(res);
                setCoupons(optimizedCoupons);
            });
    };

    useEffect(() => {
        getUsedCoupon("WAIT_USE");
    }, []);

    return (
        <View className="coupon-list-container">
            <ScrollView className="scroll-rect" scrollY scrollTop={0}>
                {coupons?.map((item, key) => {
                    const { amountDesc, methodDesc } = item;
                    return (
                        <View key={key} className={`coupon-list-item ${active !== "WAIT_USE" ? "gray" : ""}`}>
                            <View className="coupon-left">
                                <View className="price">
                                    <View className="amount">{amountDesc}</View>
                                    <View className="condition">{methodDesc}</View>
                                </View>
                                <View className="up-border"></View>
                                <View className="down-border"></View>
                            </View>
                            <View className="coupon-right">
                                {item.scopeType === "SHOP" && (
                                    <View className="info">
                                        <View className="title">店铺券</View>
                                        <View className="expiry">{item.usageTimeDesc}</View>
                                        <View className="store">全店铺可用</View>
                                    </View>
                                )}
                                {item.scopeType === "PRODUCT" && (
                                    <View className="info">
                                        <View className="title">商品券</View>
                                        <View className="expiry">{item.usageTimeDesc}</View>
                                        <View className="store">店铺指定商品可用</View>
                                    </View>
                                )}
                                {active === "WAIT_USE" && (
                                    <Button
                                        className={!item.timeAvailable ? "use-btn gray" : "use-btn red"}
                                        size="small"
                                        disabled={!item.timeAvailable}
                                        onClick={() => {
                                            if (item.scopeType === "PRODUCT") {
                                                DDYNavigateTo({ url: "/pages/goods_detail?id=" + item.scopeValue });
                                            } else {
                                                DDYSwitchTab({ url: "/pages/home/<USER>" });
                                            }
                                        }}
                                    >
                                        {" "}
                                        去使用
                                    </Button>
                                )}
                            </View>
                        </View>
                    );
                })}
            </ScrollView>
            <View className="marin-base-bottom">
                {tabs.map((item, index) => {
                    return (
                        <Button
                            key={index}
                            size="small"
                            className={`use-btn marin-base-right ${active === item.status ? "red" : "gray"}`}
                            onClick={() => {
                                setActive(item.status);
                                getUsedCoupon(item.status);
                            }}
                        >
                            {item.name}
                        </Button>
                    );
                })}
            </View>
        </View>
    );
};

export default Coupon;
