/**
 * @description: 优惠券item
 * @author: wangchunting
 */
import { DDYNavigateTo, DDYSwitchTab } from "@/utils/route";
import { Button } from "@antmjs/vantui";
import { View } from "@tarojs/components";
import { useEffect, useState } from "react";
import "../index.less";

const Coupon = ({ data, active }) => {
    const [item, setItem] = useState<any>({});
    function optimizeCoupons(data) {
        let amountDesc = "";
        let methodDesc = "";
        switch (data.method) {
            case "DISCOUNT":
                amountDesc = `${data.discount}折`;
                methodDesc = "折扣";
                break;
            case "DIRECT_REDUCTION":
                amountDesc = `￥${data.deductionAmount}`;
                methodDesc = "立减";
                break;
            case "FULL_REDUCTION":
                amountDesc = `￥${data.thresholdDeductionAmount}`;
                methodDesc = `满${data.thresholdAmount}减`;
                break;
            default:
                break;
        }

        return {
            ...data,
            amountDesc,
            methodDesc,
        };
    }

    useEffect(() => {
        const optimizedCoupons = optimizeCoupons(data);
        setItem(optimizedCoupons);
    }, []);

    return (
        <View className={`coupon-list-item ${active !== "WAIT_USE" ? "gray" : ""}`}>
            <View className="coupon-left">
                <View className="price">
                    <View className="amount">{item.amountDesc}</View>
                    <View className="condition">{item.methodDesc}</View>
                </View>
                <View className="up-border"></View>
                <View className="down-border"></View>
            </View>
            <View className="coupon-right">
                {item.scopeType === "SHOP" && (
                    <View className="info">
                        <View className="title">店铺券</View>
                        <View className="expiry">{item.usageTimeDesc}</View>
                        <View className="store">全店铺可用</View>
                    </View>
                )}
                {item.scopeType === "PRODUCT" && (
                    <View className="info">
                        <View className="title">商品券</View>
                        <View className="expiry">{item.usageTimeDesc}</View>
                        <View className="store">店铺指定商品可用</View>
                    </View>
                )}
                {active === "WAIT_USE" && (
                    <Button
                        className={!item.timeAvailable ? "use-btn gray" : "use-btn red"}
                        size="small"
                        disabled={!item.timeAvailable}
                        onClick={() => {
                            if (item.scopeType === "PRODUCT") {
                                DDYNavigateTo({ url: "/pages/goods_detail?id=" + item.scopeValue });
                            } else {
                                DDYSwitchTab({ url: "/pages/home/<USER>" });
                            }
                        }}
                    >
                        {" "}
                        去使用
                    </Button>
                )}
            </View>
        </View>
    );
};

export default Coupon;
