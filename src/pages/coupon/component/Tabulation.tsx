import newAPi from "@/utils/newApi";
import {
    Empty,
    IPullToRefreshProps,
    InfiniteScroll,
    InfiniteScrollInstance,
    InfiniteScrollProps,
    PullToRefresh,
} from "@antmjs/vantui";
import { View } from "@tarojs/components";
import React, { useEffect, useRef, useState } from "react";
import CouponListItem from "./couponListItem";

interface TabulationType {
    status: "WAIT_USE" | "USED" | "TIME_OUT";
}

const Tabulation: React.FC<TabulationType> = props => {
    const { status } = props;

    const pageNo = useRef(1);
    const infiniteScrollInstance = useRef<InfiniteScrollInstance>();

    const [dataList, setDataList] = useState<any[]>([]);

    useEffect(() => {
        pageNo.current = 1;
        infiniteScrollInstance.current?.reset(true);
    }, [status]);

    async function getList() {
        let data = {
            unGet: false,
            status: status,
            pageNo: pageNo.current,
            pageSize: 10,
        };
        // pageNo.current = page;
        return await newAPi.queryCoupons({ data, filterCheck: true });
    }

    // 下拉刷新
    const onRefresh: IPullToRefreshProps["onRefresh"] = () => {
        console.log("onRefresh");
        return new Promise(resolve => {
            pageNo.current = 1;
            // getList().then(res => {
            //     setDataList(res.data);
            infiniteScrollInstance.current?.reset(true);
            resolve(undefined);
            // })
        });
    };

    // 加载更多（下拉时也会走此方法）
    const loadMore: InfiniteScrollProps["loadMore"] = async () => {
        console.log("loadMore");
        return new Promise(async resolve => {
            getList()
                .then(res => {
                    res.total = 1;
                    let newData = [];
                    if (pageNo.current === 1) {
                        newData = [...res.data];
                    } else {
                        newData = [...dataList, ...res.data];
                    }
                    pageNo.current++;
                    setDataList(newData);
                    // resolve(newData.length < res.total ? "loading" : "complete");
                    resolve("complete");
                })
                .catch(() => {
                    resolve("error");
                });
        });
    };

    return (
        <PullToRefresh onRefresh={onRefresh}>
            {dataList.length ? (
                <View className="coupon-list-container">
                    {dataList?.map((item: any, index) => {
                        return <CouponListItem data={item} active={status} key={index} />;
                    })}
                </View>
            ) : null}
            <InfiniteScroll
                completeText={
                    <>
                        {dataList.length == 0 ? (
                            <Empty
                                description="暂无数据！"
                                image="https://dante-img.oss-cn-hangzhou.aliyuncs.com/55439458101.png"
                            />
                        ) : (
                            "没有更多了"
                        )}
                    </>
                }
                loadMore={loadMore}
                ref={infiniteScrollInstance}
            />
        </PullToRefresh>
    );
};
export default Tabulation;
