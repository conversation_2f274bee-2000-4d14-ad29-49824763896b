import api from "@/utils/api";
import { STORE_ID } from "@/utils/constant";
import { getStorage } from "@/utils/storage";
import {
    IPullToRefreshProps,
    InfiniteScroll,
    InfiniteScrollInstance,
    InfiniteScrollProps,
    PullToRefresh,
    Empty,
} from "@antmjs/vantui";
import { View } from "@tarojs/components";
import React, { useEffect, useRef, useState } from "react";
import CouponListItem from "@/components/CouponListItem";

interface TabulationType {
    param: Record<string, any>;
    status: "unUse" | "used" | "expired";
}

const Tabulation: React.FC<TabulationType> = props => {
    const { param, status } = props;

    const pageNo = useRef(1);
    const infiniteScrollInstance = useRef<InfiniteScrollInstance>();

    const [dataList, setDataList] = useState<any[]>([]);

    useEffect(() => {
        getList(1).then(res => {
            setDataList(res.data);
        });
    }, [param]);

    async function getList(page) {
        let data = {
            shopId: getStorage(STORE_ID),
            pageNo: page,
            pageSize: 10,
            ...param,
        };
        pageNo.current = page;
        return await api.getMyCoupons({ data, filterCheck: true });
    }

    const onRefresh: IPullToRefreshProps["onRefresh"] = () => {
        return new Promise(resolve => {
            getList(1).then(res => {
                setDataList(res.data);
                infiniteScrollInstance.current?.reset(true);
            });
            resolve(undefined);
        });
    };

    const loadMore: InfiniteScrollProps["loadMore"] = async () => {
        return new Promise(async resolve => {
            getList(++pageNo.current)
                .then(res => {
                    setDataList(prevData => {
                        let newData = [...prevData, ...res.data];
                        resolve(newData.length < res.total ? "loading" : "complete");
                        return newData;
                    });
                })
                .catch(() => {
                    resolve("error");
                });
        });
    };

    return (
        <PullToRefresh onRefresh={onRefresh}>
            {dataList.length ? (
                <View>
                    {dataList?.map((item: any) => {
                        return <CouponListItem item={item} status={status} key={item.promotion.id} />;
                    })}
                    <InfiniteScroll loadMore={loadMore} ref={infiniteScrollInstance} />
                </View>
            ) : (
                <Empty
                    image="https://dante-img.oss-cn-hangzhou.aliyuncs.com/53286339194.png"
                    description="暂无相关信息"
                />
            )}
        </PullToRefresh>
    );
};
export default Tabulation;
