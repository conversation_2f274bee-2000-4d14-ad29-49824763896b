.coupon-list-item {
    background-image: url("../../../images/bg_img.png");
    background-size: 100% 100%;
    margin: 24px;
    display: flex;
    min-height: 210px;
    padding-left: 32px;
    overflow: hidden;
    position: relative;

    &-left {
        width: 496px;

        .coupon-name {
            margin-top: 52px;
            font-size: 26px;
            font-weight: 500;
            color: #333333;
            line-height: 30px;
        }

        .coupon-endAt {
            margin-top: 16px;
            font-size: 20px;
            font-weight: 400;
            color: #666666;
            line-height: 23px;
        }

        .coupon-use-rule {
            display: flex;
            margin-top: 24px;

            &-label {
                flex: 1;
                font-size: 20px;
                font-weight: 400;
                color: #999999;
                line-height: 24px;
            }

            &-collapse {
                margin-right: 32px;

                &-icon {
                    width: 24px;
                    height: 24px;
                    border-radius: 50%;
                    background-color: #dddddd;
                }
            }
        }

        .limit-rule-content {
            font-size: 20px;
            font-weight: 400;
            color: #999999;
            line-height: 24px;
            margin-bottom: 42px;
        }
    }

    &-right {
        width: 206px;
        color: #f5001e;
        display: flex;
        justify-content: center;
        flex-direction: column;
        align-items: center;

        .fee {
            margin-top: 34px;
            display: flex;
            align-items: flex-end;
            justify-content: center;

            .amount {
                font-size: 60px;
                font-weight: 500;
                line-height: 60px;
            }
        }

        .title {
            font-size: 33px;
            font-weight: 500;
            line-height: 48px;
        }

        .desc {
            margin: 8px 16px 0;
            font-size: 20px;
            font-weight: 500;
            line-height: 20px;
        }

        .use {
            width: 206rpx;
            height: 89rpx;

            .use_img {
                width: 120rpx;
                height: 120rpx;
                position: absolute;
                bottom: -30px;
                right: -10px;
                z-index: 1;
                transform: rotate(-18deg);
            }
        }
    }
    .gray {
        color: #999999;
    }
}
