export interface CouponResponse {
    promotion: CouponResponsePromotion;
    userPromotion: CouponResponseUserPromotion;
}
export interface CouponResponsePromotionUserScopeParams {
    activityServiceProviderId: string;
}
export interface CouponResponsePromotionSkuScopeParams {
    type: string;
    skuIds: string;
    itemIds: string;
    itemName: string;
}
export interface CouponResponsePromotionConditionParams {
    activityConditionItemNum: string;
    limitedItemMinBoughtNum: string;
}
export interface CouponResponsePromotionBehaviorParams {
    activityItemNumAccumulate: string;
}

export interface CouponResponsePromotion {
    id: number;
    shopId: number;
    name: string;
    description: string;
    promotionDefId: number;
    type: number;
    status: number;
    startAt: number;
    endAt: number;
    userScopeParams: CouponResponsePromotionUserScopeParams;
    skuScopeParams: CouponResponsePromotionSkuScopeParams;
    conditionParams: CouponResponsePromotionConditionParams;
    behaviorParams: CouponResponsePromotionBehaviorParams;
    extra: Record<string, any>;
    createdAt: number;
    updatedAt: number;
    issueStatus: number;
    reduceFee: string;
    conditionDesc: string;
    typeDesc: string;
    expired: boolean;
}
export interface CouponResponseUserPromotion {
    id: number;
    userId: number;
    shopId: number;
    promotionId: number;
    type: number;
    name: string;
    availableQuantity: number;
    frozenQuantity: number;
    receiveQuantity: number;
    status: number;
    startAt: number;
    endAt: number;
    createdAt: number;
    updatedAt: number;
}
