import { Tab, Tabs } from "@antmjs/vantui";
import { View } from "@tarojs/components";
import Tabulation from "./component/Tabulation";
import "./index.less";

definePageConfig({
    navigationBarTitleText: "优惠券",
});

interface ITabData {
    title: string;
    key: "unUse" | "used" | "expired";
    param: { usedStatus?: number; status: number };
}

/**
 * @
 * @constructor
 */
const Index = () => {
    const tabData: ITabData[] = [
        {
            title: "未使用",
            key: "unUse",
            param: {
                status: 2,
                usedStatus: 1,
            },
        },
        {
            title: "已使用",
            key: "used",
            param: {
                status: 2,
                usedStatus: 0,
            },
        },
        {
            title: "已过期",
            key: "expired",
            param: {
                status: -1,
            },
        },
    ];

    return (
        <View className="coupon-list">
            <Tabs sticky>
                {tabData.map(tabItem => {
                    return (
                        <Tab key={tabItem.key} title={tabItem.title}>
                            <Tabulation param={tabItem.param} status={tabItem.key} />
                        </Tab>
                    );
                })}
            </Tabs>
        </View>
    );
};

export default Index;
