import { Tab, Tabs } from "@antmjs/vantui";
import { View } from "@tarojs/components";
import Tabulation from "./component/Tabulation";
import "./index.less";

definePageConfig({
    navigationBarTitleText: "优惠券",
});

interface ITabData {
    title: string;
    key: "WAIT_USE" | "USED" | "TIME_OUT";
}

const Index = () => {
    const tabData: ITabData[] = [
        {
            title: "未使用",
            key: "WAIT_USE",
        },
        {
            title: "已使用",
            key: "USED",
        },
        {
            title: "已过期",
            key: "TIME_OUT",
        },
    ];

    return (
        <View className="coupon-list">
            <Tabs sticky>
                {tabData.map(tabItem => {
                    return (
                        <Tab key={tabItem.key} title={tabItem.title}>
                            <Tabulation status={tabItem.key} />
                        </Tab>
                    );
                })}
            </Tabs>
        </View>
    );
};

export default Index;
