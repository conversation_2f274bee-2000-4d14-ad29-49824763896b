.coupon-list-container {
    width: 100%;
    min-height: 100%;
    padding: 24px;
    box-sizing: border-box;
    background: #ffffff;
    position: relative;
    border-radius: 24px 24px 0px 0px;
    .scroll-rect {
        padding: 24px;
        box-sizing: border-box;
        margin-top: 100px;
    }
    .marin-base-right {
        margin-right: 24px;
    }

    .marin-base-bottom {
        // margin-bottom: 24px;
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        background-color: #fff;
        // padding-bottom: 20px;
        // box-shadow: 0 0 5px #ccc;
        z-index: 400px;
        padding: 20px 20px 20px 20px;
        // height: 40px;
        box-sizing: border-box;
    }

    .use-btn {
        // padding: 12x 24px;
        background: #ffffff;
        border-radius: 80px;
        font-weight: 400;
        font-size: 24px;
        padding: 0 24px;

        &.red {
            color: #ff0940;
            border: 1px solid #ff0940;
        }

        &.gray {
            background: rgba(102, 102, 102, 0.06);
            color: #888;
            border: 1px solid #888;
        }
    }

    .coupon-list-item {
        width: 100%;
        padding: 0 24px;
        box-sizing: border-box;
        background: rgba(255, 124, 154, 0.09);
        overflow: hidden;
        border-radius: 16px;
        display: flex;
        height: 188px;
        margin-bottom: 24px;

        .down-border,
        .up-border {
            padding: 14px 15px;
            background-color: #fff;
            border-radius: 50%;
            position: absolute;
        }

        .coupon-left {
            color: #ff0940;
            font-weight: bold;

            .price {
                width: 100%;
                height: 100%;
                display: flex;
                align-items: center;
                flex-direction: column;
                justify-content: center; /* 垂直居中 */
                .amount {
                    display: block;
                    font-size: 20px;
                    font-weight: 500;
                    font-size: 50px;
                    font-family: PingFangSC-Medium;
                    font-weight: 500;
                }
            }

            // .condition {
            //     font-size: 20px;
            //     font-weight: 500;
            // }

            width: 30%;
            position: relative;
            margin-right: 20px;

            .up-border {
                top: -8px;
                right: -35px;
            }

            .down-border {
                bottom: -13px;
                right: -35px;
            }
        }

        .coupon-right {
            width: 70%;
            border-left: 1px dashed #ffbfcd;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: space-between;

            .info {
                // flex: 1;
                margin-left: 24px;

                .title {
                    font-weight: 500;
                    font-size: 28px;
                    color: #ff0940;
                    display: block;
                    margin-bottom: 8px;
                }

                .expiry {
                    font-weight: 400;
                    font-size: 20px;
                    color: #010101;
                    margin-bottom: 12px;
                }

                .store {
                    color: #888888;
                    font-size: 20px;
                }
            }
        }
    }

    .gray {
        opacity: 0.5;
    }
}
