import { Tab, Tabs } from "@antmjs/vantui";
import { View } from "@tarojs/components";
import "./index.less";
import EachDate from "./component/EatchDate";
definePageConfig({
    navigationBarTitleText: "全部数据",
});

export interface Idata {
    date: string;
    count: number;
    paymentSum: number;
    unsettledProfit: number;
}

const Index = () => {
    const dateList = [
        { name: "今日", id: "0" },
        { name: "昨日", id: "1" },
        { name: "近7日", id: "7" },
        { name: "近30日", id: "30" },
    ];

    return (
        <View className="store-revenue-detail">
            <Tabs sticky={true} lazyRender={true}>
                {dateList.map((item, index) => {
                    return (
                        <Tab title={item.name} key={index}>
                            <EachDate day={item.id} />
                        </Tab>
                    );
                })}
            </Tabs>
        </View>
    );
};

export default Index;
