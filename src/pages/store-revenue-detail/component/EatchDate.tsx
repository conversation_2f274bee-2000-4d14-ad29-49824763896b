import api from "@/utils/api";
import { STORE_ID } from "@/utils/constant";
import { getStorage } from "@/utils/storage";
import { View } from "@tarojs/components";
import { useEffect, useState } from "react";
import { Idata } from "..";

type IEatchDate = { day: string };

type Imap = [key: string, val: Idata];

const EachDate: React.FC<IEatchDate> = props => {
    const { day } = props;

    const [data, setData] = useState<Idata[]>([]);

    async function getData(day) {
        let data = { sourceId: getStorage(STORE_ID), day };
        return await api.getStoreProfitDetail({ data, filterCheck: true });
    }

    useEffect(() => {
        getData(day).then(res => {
            const data = Object.entries(res).map(([key, val]: Imap) => {
                val["date"] = key;
                return val;
            });
            setData(data);
        });
    }, []);

    return (
        <View className={`container`}>
            <View className={`tabulation`}>
                <View className="header">
                    <View>日期</View>
                    <View>有效订单</View>
                    <View>有效订单金额</View>
                    <View>预估收入</View>
                </View>
                {data.map(item => {
                    return (
                        <View className="item">
                            <View>{item.date}</View>
                            <View>{item.count}</View>
                            <View>¥{item.paymentSum / 100}</View>
                            <View>¥{item.unsettledProfit / 100}</View>
                        </View>
                    );
                })}
            </View>
        </View>
    );
};
export default EachDate;
