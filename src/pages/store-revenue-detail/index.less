.store-revenue-detail {
    --tabs-nav-background-color: #f1f2f3;
    .container {
        display: inline-block;
        width: 100%;
        font-size: 25px;
    }
    .tabulation {
        background-color: #fff;
        border-radius: 25px;
        margin: 24px 24px 90px 24px;
        padding: 10px 20px;
        .header,
        .item {
            height: 100px;
            line-height: 100px;
            display: flex;
        }
        .item {
            border-top: 1px solid #f1f2f3;
        }
        .header > view,
        .item > view {
            flex: 1;
            text-align: center;
        }
    }
}
