import { DDYSwitchTab } from "@/utils/route";
import { getStorage } from "@/utils/storage";
import { View, Image } from "@tarojs/components";

const arrow_right = require("../../../images/rightArrow.png");

export default ({ detail }) => {
    const storeAvatar = getStorage("subStoreInfo").imageUrl;
    const storeName = getStorage("subStoreInfo").name;
    return (
        <View
            className="info_block"
            onClick={() => {
                detail?.item?.brandId &&
                    DDYSwitchTab({
                        url: `/pages/home/<USER>
                    });
            }}
        >
            <View>
                <View className="item_content">
                    <Image className="item_img" src={storeAvatar} />
                    <View className="text">{storeName}</View>
                </View>
            </View>
            <Image className="right-icon" src={arrow_right} />
        </View>
    );
};
