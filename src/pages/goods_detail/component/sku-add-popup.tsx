import newAPi from "@/utils/newApi";
import { ActionSheet, Button, Dialog, Field, Popup, Stepper } from "@antmjs/vantui";
import { View, Text, Image, ScrollView } from "@tarojs/components";
import { useEffect, useRef, useState } from "react";
import "./sku-add-popup.less";
import { DDYNavigateTo, DDYReLaunch, DDYSwitchTab } from "@/utils/route";
import Taro, { reportEvent } from "@tarojs/taro";
import api from "@/utils/api";
import { mall_event, mall_event_key } from "@/utils/track-report";
import DDYToast from "@/utils/toast";
import { getStorage, removeStorage } from "@/utils/storage";
import { IS_LOGIN, SCAN_PAGE, STORE_ID } from "@/utils/constant";
import { handleGoodsLink, jumpLogin } from "@/utils/PageUtils";
import { DDYObject } from "src/type/common";
const Dialog_ = Dialog.createOnlyDialog();

// 两个数组是否有交集
function hasIntersection(arr1, arr2) {
    return arr1.some(item => arr2.includes(item));
}

const isSubset = (a, b) => {
    // console.log(a, b);
    const setA = new Set(a);
    return b.every(item => setA.has(item)) && a.length - b.length <= 1;
};

function hasAtLeastNMinusOneCommonElements(A, B) {
    const n = A.length;
    const bLength = B.length;

    // 特殊情况处理
    if (n === 0) return true; // 空数组视为满足条件
    if (bLength < n - 1) return false; // B元素太少，不可能满足

    // 将数组A转换为集合以便快速查找
    const setA = new Set(A);
    let commonCount = 0;

    // 计算B中有多少元素在A中出现
    for (const elem of B) {
        if (setA.has(elem)) {
            commonCount++;
            // 如果已经满足条件，可以提前返回
            if (commonCount >= n - 1) {
                return true;
            }
        }
    }

    return commonCount >= n - 1;
}

export default ({
    show,
    closeFn,
    hasGbActivity,
    // 0: 加入购物车 ； 1: 立即购买
    openType,
    goodsId,
    skuId,
    cartItemId,
    skuNum,
    skuNote,
    skuLoad,
    groupAction,
    groupId,
    activityId,
}) => {
    const [mainPic, setMainPic] = useState("");
    const [price, setPrice] = useState<number | 0>(0);
    const [discountPrice, setDiscouponPrice] = useState(0.0);
    const [stockQuantity, setStockQuantity] = useState(0);
    const [limitQuantity, setLimitQuantity] = useState(0);
    const [orderNum, setOrderNum] = useState(1);
    const [isSelectdFinished, setIsSelectdFinished] = useState(false);
    const [skusAttr, setSkusAttr] = useState<any[]>([]);
    const [selectStr, setSelectStr] = useState("");
    const [note, setNote] = useState("");
    const selectSkuId = useRef<number | null>();
    // const [selectSku]
    const selectedSkuInfo = useRef<DDYObject>();
    const [sku, setSku] = useState([]);
    const [goodsItem, setGoodsItem] = useState<DDYObject>({});
    const currentNote = useRef(skuNote);

    const autoGetCoupons = () => {
        newAPi
            .autoCoupons({
                method: "GET",
                data: {
                    goodsCodeList: goodsId,
                },
                showError: false,
            })
            .then(res => {});
    };

    const skuChange = (attrs, skus, clickIndex) => {
        let bol = true;
        let strArr: any[] = [];
        let specIdArr: any[] = [];
        const selectAttrObj = {};
        attrs.map((skusItem, index) => {
            if (goodsItem.type === 2) {
                selectSkuId.current = skusItem.id;
            }
            if (!skusItem.selected) {
                bol = false;
            } else {
                selectAttrObj[index] = skusItem.frontId;
                const item: DDYObject = skusItem.itemSpecificationDetailParams.filter(
                    item => item.frontId === skusItem.selected,
                )[0];
                strArr.push(item.name);
                specIdArr.push(item.frontId);
            }
        });

        // 根据选中的规格反向推算未选中的规则是否还有库存，对状态做实时更新

        // 获取与当前选择的规格有交集的sku中没有库存的sku；
        const skuNoStock = skus.filter((item: any) => {
            const arr = specIdArr.filter(item => item !== selectAttrObj[clickIndex]);
            if (!arr.length) return false;
            return hasAtLeastNMinusOneCommonElements(item.specDetails || [], arr) && item.stockQuantity === 0;
        });

        // 下架商品
        const skuNoActiveStock = skus.filter((item: any) => {
            const arr = specIdArr.filter(item => item !== selectAttrObj[clickIndex]);
            if (!arr.length) return false;
            return (
                hasAtLeastNMinusOneCommonElements(item.specDetails || [], arr) &&
                (item.status === -1 || (!item.activityName && hasGbActivity))
            );
        });
        // 重置库存售罄状态
        attrs.map((attrsItem, attrsIndex) => {
            attrsItem.itemSpecificationDetailParams.map(specItem => {
                // 当前点击的属性组不重置状态
                if (clickIndex !== null && attrsIndex !== clickIndex) {
                    specItem.status = 1;
                    specItem.isHide = false;
                }
            });
        });
        if (skuNoActiveStock.length) {
            skuNoActiveStock.map(skuNoActiveStockItem => {
                const { specDetails = [] } = skuNoActiveStockItem;
                attrs.map(attrsItem => {
                    // 遍历它的属性组，找出在当前specDetails的规格属性，并将其标记成售罄
                    attrsItem.itemSpecificationDetailParams.map(specItem => {
                        if ((specDetails || []).includes(specItem.frontId) && attrsItem.selected !== specItem.frontId) {
                            specItem.isHide = true;
                        }
                    });
                });
            });
        }
        if (skuNoStock.length) {
            skuNoStock.map(skuNoStockItem => {
                const { specDetails = [] } = skuNoStockItem;
                attrs.map((attrsItem, attrsIndex) => {
                    // 遍历它的属性组，找出在当前specDetails的规格属性，并将其标记成售罄
                    attrsItem.itemSpecificationDetailParams.map(specItem => {
                        if ((specDetails || []).includes(specItem.frontId)) {
                            if (attrsItem.selected !== specItem.frontId) {
                                specItem.status = -1;
                            }
                        }
                    });
                });
            });

            setSkusAttr([...attrs]);
        }

        // 获取sku信息
        if (goodsItem.type === 2) {
            selectSkuId.current = null;
            //组合商品
            attrs.map(item => {
                item.itemSpecificationDetailParams.map(specItem => {
                    if (item.selected === specItem.frontId) {
                        selectSkuId.current = specItem.id;
                        selectedSkuInfo.current = specItem;
                        if (specItem.extraMap && specItem.extraMap.extraActivityValid) {
                            setPrice(+specItem.extraMap.activitySalesPrice);
                            setDiscouponPrice(+specItem.extraMap.activitySalesPrice);
                        } else {
                            setPrice(specItem.skuPrice);
                            setDiscouponPrice(specItem.skuPrice);
                        }
                        console.log("goodsItem:", goodsItem);
                        if (goodsItem && goodsItem.extra && goodsItem.extraActivityValid) {
                            console.log(specItem.image);
                            setMainPic(specItem.image || goodsItem.extra.activityMainImage || goodsItem.mainImage);
                        } else {
                            setMainPic(specItem.image || goodsItem.mainImage);
                        }
                    }
                });
            });
        } else {
            selectSkuId.current = null;
            skus.map(item => {
                if ((item.specDetails || []).join("+") === specIdArr.join("+")) {
                    selectSkuId.current = item.id;
                    if (item.extraMap && item.extraMap.extraActivityValid) {
                        setPrice(+item.extraMap.activitySalesPrice);
                        setDiscouponPrice(+item.extraMap.activitySalesPrice);
                    } else {
                        setPrice(item.skuPrice);
                        setDiscouponPrice(item.skuPrice);
                    }
                    console.log(goodsItem.extra, goodsItem.extraActivityValid);
                    if (goodsItem && goodsItem.extra && goodsItem.extraActivityValid) {
                        setMainPic(item.image || goodsItem.extra.activityMainImage || goodsItem.mainImage);
                    } else {
                        setMainPic(item.image || goodsItem.mainImage);
                    }
                    selectedSkuInfo.current = item;
                }
            });
        }
        // 完整选中规格时
        if (bol) {
            setSelectStr(strArr.join("/"));
            querySkuPriceInfo(skuNum || 1);
            if (!skuNum) {
                setOrderNum(1);
            }
        } else {
            if (goodsItem && goodsItem.extra && goodsItem.extraActivityValid) {
                setMainPic(goodsItem.extra.activityMainImage || goodsItem.mainImage);
            } else {
                setMainPic(goodsItem.mainImage);
            }
            setPrice(goodsItem.itemLowPrice || goodsItem.lowPrice);
            setDiscouponPrice(goodsItem.itemLowPrice || goodsItem.lowPrice);
        }
        setIsSelectdFinished(bol);
    };

    const querySkuPriceInfo = num => {
        if (!selectSkuId.current) return;
        newAPi
            .getSkuInfo({
                method: "GET",
                data: {
                    id: selectSkuId.current,
                    hasGbActivity: hasGbActivity,
                    count: num,
                },
            })
            .then(res => {
                setStockQuantity(res.stockQuantity);
                setDiscouponPrice(res.discountPrice);
                setLimitQuantity(res.purchaseLimit);
            });
    };

    const getDetailInfo = () => {
        newAPi
            .getSkus({
                data: {
                    itemId: goodsId,
                    hasGbActivity: !!hasGbActivity,
                },
            })
            .then((res: any) => {
                // 组合品
                setGoodsItem(res.item);
                selectSkuId.current = skuId;
                if (res.item.type === 2) {
                    let selected = null;
                    if (skuId) {
                        selected = skuId;
                    }
                    const arr = [
                        {
                            name: "套餐名称",
                            itemSpecificationDetailParams: res.skus.map(item => {
                                return {
                                    ...item,
                                    frontId: item.id,
                                    isHide: item.status === -1,
                                    status: item.stockQuantity === 0 ? -1 : 1,
                                };
                            }),
                            selected: selected,
                        },
                    ];
                    setSkusAttr(arr);
                    // skuChange(arr, res.skus, null);
                } else {
                    let arr = res.itemSpecificationParams || [];
                    if (skuId) {
                        let specDetails = (res.skus || []).filter(item => item.id === skuId)[0].specDetails;

                        arr.map((item, index) => {
                            item.selected = specDetails[index];
                        });
                        setIsSelectdFinished(true);
                    }
                    if (arr.length === 1) {
                        arr = arr.map(specParam => {
                            specParam.itemSpecificationDetailParams = specParam.itemSpecificationDetailParams.map(
                                detail => {
                                    // 查找匹配的SKU
                                    const matchingSku = res.skus.find(
                                        sku => !!sku.specDetails && sku.specDetails[0] === detail.frontId,
                                    );
                                    return {
                                        ...detail,
                                        status: !!matchingSku && matchingSku.stockQuantity === 0 ? -1 : 1,
                                        isHide: !!matchingSku && matchingSku.status === -1,
                                    };
                                },
                            );
                            return specParam;
                        });
                    }
                    skuChange(arr, res.skus, null);
                    setSkusAttr(arr);
                    if (arr.length === 0) {
                        setIsSelectdFinished(true);
                        selectSkuId.current = res.skus[0].id;
                    }
                }
                skuLoad && skuLoad(res.skus);
                if (skuId) {
                    const arr = res.skus.filter(item => item.id === skuId)[0];
                    // if(res.ite)
                    if (arr.length) {
                        if (res.item.extra && res.item.extraActivityValid) {
                            setMainPic(arr[0].image || res.item.extra.activityMainImage || res.item.mainImage);
                        } else {
                            setMainPic(arr[0].image || res.item.mainImage);
                        }
                    } else {
                        if (res.item.extra && res.item.extraActivityValid) {
                            setMainPic(res.item.extra.activityMainImage || res.item.mainImage);
                        } else {
                            setMainPic(res.item.mainImage);
                        }
                    }
                    setIsSelectdFinished(true);
                } else {
                    if (res.item.extra && res.item.extraActivityValid) {
                        setMainPic(res.item.extra.activityMainImage || res.item.mainImage);
                    } else {
                        setMainPic(res.item.mainImage);
                    }
                    const validSkus = res.skus.filter(item => item.status !== -1);
                    console.log("validSkus:", validSkus);
                    // 特殊逻辑，当未售罄的sku只有一个时，默认选择当前sku对应的属性规格;
                    if (validSkus.length === 1) {
                        selectSkuId.current = validSkus[0].id;
                        setIsSelectdFinished(true);
                        selectedSkuInfo.current = validSkus[0];
                        if (res.item.type === 2) {
                            // 组合品默认选中逻辑
                            const arr = [
                                {
                                    name: "套餐名称",
                                    itemSpecificationDetailParams: res.skus.map(item => {
                                        return {
                                            ...item,
                                            frontId: item.id,
                                            isHide: item.status === -1,
                                            status: item.stockQuantity === 0 ? -1 : 1,
                                        };
                                    }),
                                    selected: selectSkuId.current,
                                },
                            ];
                            setSkusAttr(arr);
                        } else {
                            let arr = res.itemSpecificationParams || [];
                            let specDetails = validSkus[0].specDetails;
                            arr.map((item, index) => {
                                item.selected = specDetails[index];
                            });
                            setSkusAttr(arr);
                        }

                        setIsSelectdFinished(true);
                    }
                }

                setSku(res.skus);
                setPrice(res.item.itemLowPrice || res.item.lowPrice);
                setDiscouponPrice(res.item.itemLowPrice || res.item.lowPrice);
                if (selectSkuId.current) {
                    querySkuPriceInfo(skuNum || 1);
                }
            })
            .catch(err => {
                console.log(err);
            });
    };

    const takeCart = type => {
        // e 0:加购；1:下单
        const goodsName = goodsItem.name;

        for (let index = 0; index < skusAttr.length; index++) {
            const item = skusAttr[index];
            if (!item.selected) {
                return DDYToast.info(`请选择 ${item.name}`);
            }
        }

        if (goodsItem.status === -1) {
            DDYToast.error("商品已被下架, 无法购买！");
            let map = new Map();
            map.set(mall_event_key.MALL_KEY_GOOD_ID, goodsItem.id);
            map.set(mall_event_key.MALL_KEY_GOOD_NUM, orderNum);
            map.set(mall_event_key.MALL_KEY_ITEM_NAME, goodsName);
            map.set(mall_event_key.MALL_KEY_BUY_FAIL_REASON, "商品已被下架, 无法购买！");
            reportEvent(mall_event.MALL_EVENT_GOOD_IMMEDIATELY_BUY_FAIL, map);
            return;
        }

        if ((getStorage(IS_LOGIN) || "") != "1") {
            jumpLogin();
            let map = new Map();
            map.set(mall_event_key.MALL_KEY_GOOD_ID, goodsItem.id);
            map.set(mall_event_key.MALL_KEY_GOOD_NUM, orderNum);
            map.set(mall_event_key.MALL_KEY_ITEM_NAME, goodsName);
            map.set(mall_event_key.MALL_KEY_BUY_FAIL_REASON, "未登录，跳转到登录！");
            reportEvent(mall_event.MALL_EVENT_GOOD_IMMEDIATELY_BUY_FAIL, map);
            return false;
        } else {
            removeStorage(SCAN_PAGE);
        }

        confirmTake(type);
    };

    const confirmTake = type => {
        if (type === 0) {
            doTakeCart();
        } else {
            doTakeOrder();
        }
    };

    const doTakeOrder = () => {
        const type = goodsItem.type;
        const goodsId = goodsItem.id;
        const goodsName = goodsItem.name;
        const sourceType = goodsItem.sourceType;
        const specId = getSpecId();

        let max = 0;

        let map = new Map();
        map.set(mall_event_key.MALL_KEY_GOOD_ID, goodsId);
        map.set(mall_event_key.MALL_KEY_GOOD_NUM, orderNum);
        map.set(mall_event_key.MALL_KEY_ITEM_NAME, goodsName);
        map.set(mall_event_key.MALL_KEY_GOOD_SPEC_ID, specId);
        reportEvent(mall_event.MALL_EVENT_GOOD_IMMEDIATELY_BUY, map);

        const { activityName, marketingToolId, activityId } = selectedSkuInfo.current;
        const data = [
            {
                skuId: selectSkuId.current,
                quantity: orderNum,
                sourceType: sourceType,
                note: note,
                activityName: activityName || "",
                marketingToolId: marketingToolId || "",
                activityId: activityId,
            },
        ];
        api.preCheck({
            data: {
                data: JSON.stringify(data),
            },
        }).then(res => {
            if (type == "3" || type == "4" || type == "5") {
                DDYNavigateTo({
                    url:
                        "/pages/order/comfire-order/index?goodsId=" +
                        goodsId +
                        "&orderNum=" +
                        orderNum +
                        "&specId=" +
                        specId +
                        "&exchange=integral" +
                        "&maxDiscount=" +
                        max +
                        "&sourceType=" +
                        sourceType +
                        "&groupAction=" +
                        (groupAction || "") +
                        "&activityName=" +
                        (activityName || "") +
                        "&marketingToolId=" +
                        (marketingToolId || "") +
                        "&note=" +
                        (note || "") +
                        "&groupId=" +
                        (groupId || "") +
                        "&activityId=" +
                        (activityId || ""),
                });
            } else {
                DDYNavigateTo({
                    url:
                        "/pages/order/comfire-order/index?goodsId=" +
                        goodsId +
                        "&orderNum=" +
                        orderNum +
                        "&specId=" +
                        specId +
                        "&maxDiscount=" +
                        max +
                        "&sourceType=" +
                        sourceType +
                        "&groupAction=" +
                        (groupAction || "") +
                        "&activityName=" +
                        (activityName || "") +
                        "&marketingToolId=" +
                        (marketingToolId || "") +
                        "&note=" +
                        (note || "") +
                        "&groupId=" +
                        (groupId || "") +
                        "&activityId=" +
                        (activityId || ""),
                });
            }
        });
    };

    const getSpecId = () => {
        // todp
        return selectSkuId.current;
    };
    const doTakeCart = () => {
        const specId = getSpecId();
        if (!specId) {
            return DDYToast.info("请选择规格");
        }
        let map = new Map();
        map.set(mall_event_key.MALL_KEY_GOOD_ID, specId);
        map.set(mall_event_key.MALL_KEY_GOOD_NUM, orderNum);
        reportEvent(mall_event.MALL_EVENT_ADD_CART, map);
        if (!skuId) {
            api.addCart({
                method: "POST",
                contentType: "application/x-www-form-urlencoded",
                data: {
                    skuId: String(specId),
                    quantity: String(orderNum),
                    buyerNote: note,
                },
                filterCheck: true,
            })
                .then(res => {
                    if (res > 0) {
                        DDYToast.success("成功加入购物车");
                        closeFn(true);
                        getCartNumber();
                    } else {
                        DDYToast.info("加购失败");
                    }
                })
                .catch(err => {
                    DDYToast.info("加入购物车失败" + err.message);
                });
        } else {
            // 修改购物车skuid
            newAPi
                .updateCartsSku({
                    method: "POST",
                    data: {
                        id: cartItemId,
                        skuId: String(specId),
                        quantity: String(orderNum),
                        buyerNote: note,
                    },
                })
                .then(res => {
                    DDYToast.success("修改成功");
                    closeFn(true);
                    getCartNumber();
                });
        }
    };

    /**
     * 更新首页购物车 的数量标识
     */

    const getCartNumber = () => {
        newAPi
            .getCartCount({
                data: {
                    shopId: getStorage(STORE_ID),
                },
                method: "POST",
                // filterCheck: true,
            })
            .then(res => {
                const cart = res.data;
                if (cart > 0) {
                    Taro.setTabBarBadge({
                        index: 2,
                        text: cart,
                    });
                } else {
                    Taro.removeTabBarBadge({
                        index: 2,
                    });
                }
                DDYSwitchTab({
                    url: "/pages/shop_cart/index",
                });
            })
            .catch();
    };

    useEffect(() => {
        if (goodsItem) {
            setPrice(goodsItem.itemLowPrice || goodsItem.lowPrice);
        }
    }, [goodsItem]);

    useEffect(() => {
        if (skuLoad) {
            getDetailInfo();
        } else {
            if (!skuLoad && show) {
                getDetailInfo();
                selectSkuId.current = skuId;
            }
        }
    }, [show]);

    useEffect(() => {
        if (skuNum) {
            setOrderNum(skuNum);
        }
    }, [skuNum]);

    return (
        <>
            <ActionSheet
                show={show}
                style={{ zIndex: 3000 }}
                title={<View style={{ height: "49px" }}>规格属性</View>}
                onClose={() => {
                    closeFn();
                }}
            >
                <View className="sku-add-popup">
                    <View className="actionSheet-goods-item">
                        <Image
                            className="actionSheet-goods-item-img"
                            src={mainPic}
                            mode="scaleToFill"
                            onClick={() => {
                                Taro.previewImage({
                                    urls: sku.map(item => {
                                        return item.image || mainPic;
                                    }),
                                    current: mainPic,
                                });
                            }}
                        />
                        <View className="actionSheet-goods-item-info">
                            <View className="actionSheet-goods-item-name">{goodsItem?.name}</View>
                            {sku.length > 1 ? (
                                <View className="actionSheet-goods-item-skus-attr">
                                    {!isSelectdFinished ? "请选择" : <Text>已选择：{selectStr}</Text>}
                                </View>
                            ) : null}

                            <View className="cart-good-price">
                                <Text className="cart-good-origin">
                                    ¥{price}
                                    {sku.length > 1 && !isSelectdFinished ? "起" : null}
                                </Text>
                                {price !== discountPrice ? (
                                    <View className="cart-good-discount">
                                        <Text className="discount-text1">折后</Text>
                                        <Text className="discount-text2">
                                            ¥{discountPrice}
                                            {sku.length > 1 && !isSelectdFinished ? "起" : null}
                                        </Text>
                                    </View>
                                ) : null}
                            </View>
                        </View>
                    </View>
                    <ScrollView scrollY style={{ height: "300px" }} className="sku-scroll" showScrollbar={false}>
                        <View className="sku-rect">
                            {isSelectdFinished ? (
                                <View className="popup-item">
                                    <Text className="item-title">可用库存：{stockQuantity}</Text>
                                </View>
                            ) : null}
                            {/* 规格选择区域 */}
                            <View>
                                {skusAttr.map((items, skusIndex) => {
                                    return (
                                        <View className="sku-attr-item">
                                            <View className="sku-attr-item-title">{items.name}</View>
                                            <View className="sku-attr-item-selects">
                                                {items.itemSpecificationDetailParams.map(item => {
                                                    if (item.isHide) return null;
                                                    return (
                                                        <View
                                                            onClick={() => {
                                                                if (item.status === -1 || item.isHide) {
                                                                    return;
                                                                }
                                                                if (item.frontId === items.selected) {
                                                                    items.selected = null;
                                                                } else {
                                                                    items.selected = item.frontId;
                                                                }
                                                                setSkusAttr([...skusAttr]);
                                                                skuChange(skusAttr, sku, skusIndex);
                                                            }}
                                                            className={`sku-attr-item-selects-item
                                                                 ${items.selected === item.frontId ? "selected" : ""}
                                            ${item.status === -1 || item.isHide ? "expire" : ""}
                                                `}
                                                        >
                                                            {item.name}
                                                            {/* {item.price ? ` ¥${item.price / 100}` : null} */}
                                                            {item.status === -1 ? (
                                                                <View className="expire-tag">库存不足</View>
                                                            ) : null}
                                                        </View>
                                                    );
                                                })}
                                            </View>
                                        </View>
                                    );
                                })}
                            </View>
                            {isSelectdFinished ? (
                                <View className="popup-item">
                                    <Text className="item-title">数量</Text>
                                    <View className="item-right">
                                        {limitQuantity > 0 ? (
                                            <Text className="item-limit">限购{limitQuantity || 0} 件</Text>
                                        ) : null}

                                        <Stepper
                                            value={orderNum}
                                            max={
                                                limitQuantity
                                                    ? stockQuantity > limitQuantity
                                                        ? limitQuantity
                                                        : stockQuantity
                                                    : stockQuantity
                                            }
                                            onChange={val => {
                                                setOrderNum(val.detail as number);
                                                querySkuPriceInfo(val.detail);
                                            }}
                                        />
                                    </View>
                                </View>
                            ) : null}

                            <View className="popup-item">
                                <Text className="item-title">备注</Text>
                                <View
                                    className="item-right-icon"
                                    onClick={() => {
                                        Dialog_.confirm({
                                            title: "备注",
                                            zIndex: 4100,
                                            message: (
                                                <Field
                                                    type="textarea"
                                                    value={note}
                                                    style="width:100%"
                                                    placeholder-style="color:#CFCFCF"
                                                    placeholder="点此给商家备注"
                                                    onChange={e => {
                                                        //@ts-ignore
                                                        const { value } = e.target;
                                                        setNote(value);
                                                        currentNote.current = value;
                                                    }}
                                                />
                                            ),
                                        }).then(value => {
                                            if (value === "confirm") {
                                                setNote(currentNote.current);
                                            } else {
                                                setNote(note);
                                            }
                                        });
                                    }}
                                >
                                    {note || "无备注"}
                                </View>
                            </View>
                        </View>
                    </ScrollView>
                    <Button
                        type="primary"
                        color="#f51214"
                        round
                        block
                        onClick={() => {
                            takeCart(openType);
                            autoGetCoupons();
                        }}
                    >
                        {!isSelectdFinished ? "请选择规格" : openType == 0 ? "加入购物车" : "立即购买"}
                    </Button>
                </View>
            </ActionSheet>
            <Dialog_ />
        </>
    );
};
