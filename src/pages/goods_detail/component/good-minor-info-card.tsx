import { DDYNavigateTo } from "@/utils/route";
import { ActionSheet, Icon } from "@antmjs/vantui";
import { View, Label, Text, ScrollView, Image } from "@tarojs/components";
import { useEffect, useState } from "react";
import { getStorage, setStorage } from "@/utils/storage";
import { ADDRESS_ID, IS_LOGIN } from "@/utils/constant";
import { useDidShow } from "@tarojs/taro";
import api from "@/utils/api";
import DDYToast from "@/utils/toast";
import { jumpLogin } from "@/utils/PageUtils";
export default ({ onLoad }) => {
    const [modal, setModal] = useState(false);
    const [load, setLoad] = useState(false);
    const [address, setAddress] = useState({});
    const openTip = () => {
        setModal(true);
    };
    const selectAddress = () => {
        if (!getStorage(IS_LOGIN)) return jumpLogin();
        DDYNavigateTo({
            url: "/pages/address/index?type=order",
        });
    };

    // setStorage(ADDRESS_ID, id);
    useEffect(() => {
        getDefaultAddress();
    }, []);
    const getDefaultAddress = () => {
        api.getDefaultAddress({
            data: {},
            filterCheck: true,
        }).then(res => {
            if (res && res.id) {
                setStorage(ADDRESS_ID, res.id);
                getAddressDetail(res.id);
            }
            console.log("res:", res);
        });
    };

    const getAddressDetail = addressId => {
        api.getAddress({
            data: {
                id: addressId,
            },
            showError: false,
            filterCheck: true,
        }).then(res => {
            if (res.id) {
                setAddress(res);
                console.log("res:", res);
                setLoad(true);
                onLoad(res);
            } else {
                DDYToast.info("获取地址失败");
            }
        });
    };

    useDidShow(() => {
        console.log("useDidShowADDRESS_ID", getStorage(ADDRESS_ID));
        const addressId = getStorage(ADDRESS_ID);
        addressId && getAddressDetail(addressId);
    });
    return (
        <>
            <View className="card card3" style={{ paddingTop: "15px" }}>
                <View className="tile" onClick={openTip}>
                    <View className="label">说明</View>
                    <View>重要提示</View>
                    <View className="text">跨境产品不支持七天无理由退换货</View>
                    <View>税费说明</View>
                    {/* <Image className="right-icon" src={arrow_right} /> */}
                </View>
                {/* 京东云商品显示 */}
                <View className="tile" onClick={selectAddress}>
                    <View className="label">收货</View>
                    <View
                        className="tile-content"
                        style={{
                            color: load ? "#222" : "#f51214",
                        }}
                    >
                        {load
                            ? `${address.province}
                            ${address.city}
                            ${address.region}
                        ${address.detail}`
                            : "请添加收货地址"}
                    </View>
                </View>
                <View className="line">
                    <View className="left">
                        <Icon
                            classPrefix={"iconfont-fx"}
                            style={{ fontSize: "30rpx", marginRight: "8rpx" }}
                            name={"icon-zheng"}
                            size={32}
                            color={"#989898"}
                        />
                        正品保证
                    </View>
                    <View className="center">
                        <Icon
                            classPrefix={"iconfont-fx"}
                            style={{ fontSize: "30rpx", marginRight: "8rpx" }}
                            name={"icon-haiwai"}
                            size={32}
                            color={"#989898"}
                        />
                        海外货源
                    </View>
                    <View className="right">
                        <Icon
                            classPrefix={"iconfont-fx"}
                            style={{ fontSize: "30rpx", marginRight: "8rpx" }}
                            name={"icon-shou"}
                            size={32}
                            color={"#989898"}
                        />
                        售后保证
                    </View>
                </View>
            </View>
            <ActionSheet show={modal} title="说明" onClose={() => setModal(false)}>
                <View className="minor-modal-content">
                    <Label>1、重要提示</Label>
                    <View>如需取消订单，请在下单后的30分钟内联系在线客服处理。</View>
                    <View style={{ color: "red" }}>30分钟后，订单推送海关成功，将不支持取消订单。</View>
                    <Label>2、跨境产品不支持七天无理由退换货</Label>
                    <View>
                        请下单前仔细核对订单信息。且不接受拒收，如您强行拒收，
                        <Text style={{ color: "red" }}>因此造成的损失由您自行承担</Text>。
                    </View>
                    <Label>3、划线价</Label>
                    <View>本店所有划线价格为厂商指导价或正品零售价，并非原价，仅供参考</View>
                    <Label>4、正品保障</Label>
                    <View>商城承诺该商品为100%海外正品，假一赔十，正品保障，无忧购物</View>
                    <Label>5、需要实名认证</Label>
                    <View>
                        根据中国海关总署要求，需要您提供真实的收货人姓名、身份证信息配合进行个人物品入境申报。我们将严格的保密用户信息。
                    </View>
                    <Label>6、保税产品上为什么有不同的标记</Label>
                    <View>
                        不同保税仓仓库会根据自身要求给仓库产品贴上标签，类似一个身份的象征（就像每公民都有身份证一样），作为产品出入库的标志
                    </View>
                    <Label>7、购买产品是否有发票？</Label>
                    <View>
                        由于购买的是进口保税的商品，货物未出保税区前，性质为境外货物，商家为海外商家，无法按照国内税法规定提供购物发票。
                    </View>
                    <Label>8、税费说明</Label>
                    <View>
                        根据国家政策规定，本商品适用于“跨境电商综合税”，实际结算税费请以提交订单时的应付总额明细为准。
                    </View>
                    <View>
                        依据《关于完善跨境电子商务零售进口税收政策的通知》，跨境电子商务零售进口商品的单次交易限值为人民币5000元，个人年度交易限值为人民币26000元。
                    </View>
                    <View className="safe-bottom-padding">
                        在限值以内进口的跨境电子商务零售进口商品，关税税率暂设为0%；进口环节增值税、消费税取消免征税额，暂按法定应纳税额的70%征收，平台代征代缴。
                    </View>
                </View>
            </ActionSheet>
        </>
    );
};
