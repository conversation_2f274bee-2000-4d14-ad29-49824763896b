import usePoster from "@/components/createPoster/usePoster";
import { GUIDER_INFO, STORE_ID, SUB_STORE_ID } from "@/utils/constant";
import { getStorage } from "@/utils/storage";
import Taro, { useShareAppMessage } from "@tarojs/taro";
import { useEffect, useRef, useState } from "react";
import configsFn from "./config";
import { ShareSheet } from "@antmjs/vantui";
import api from "@/utils/api";
import { IS_TEST } from "@/utils/env";
import { mall_event, mall_event_key, reportEvent } from "@/utils/track-report";
import infoStore from "@/store/info-store";

const options = [
    {
        name: "分享商品链接",
        icon: "link",
        openType: "share",
    },
    {
        name: "分享商品海报",
        icon: "https://dante-img.oss-cn-hangzhou.aliyuncs.com/79269798188.png",
    },
];

export default ({ detail, detailInfo, show, onClose }) => {
    const [create, setCreate] = useState(false);
    const qrCodeUrl = useRef("");

    const getCode = () => {
        const storeid = getStorage(STORE_ID);
        api.getGoodEwm({ data: {}, filterCheck: true }).then(res => {
            console.log("getGoodEwm:", res);
            if (res) {
                qrCodeUrl.current = IS_TEST ? "https://mtest.mall.yang800.cn/" : res;
                qrCodeUrl.current =
                    qrCodeUrl.current +
                    "store-distribution/detail_xh?storeId=" +
                    storeid +
                    "&skuSn=" +
                    detail?.item?.id +
                    "&subStoreId=" +
                    getStorage(SUB_STORE_ID);
                if (getStorage(GUIDER_INFO) && getStorage(GUIDER_INFO) != 0) {
                    qrCodeUrl.current += "&guiderId=" + getStorage(GUIDER_INFO);
                }
                console.log("qrCodeUrl:", qrCodeUrl.current);
            }
        });
    };

    const { poster, sharePath } = usePoster({
        posterInfo: create,
        createPalette: () => {
            const goodName = detail?.item?.name;
            let goodsSrc =
                detailInfo.itemDetail.images.length === 0 ? detail.item.mainImage : detailInfo.itemDetail.images[0].url;
            // let goodsSrc = "https://dante-img.oss-cn-hangzhou.aliyuncs.com/38524941351.jpg"
            const priceNow =
                detail.item.lowPrice == detail.item.highPrice
                    ? detail.item.lowPrice / 100
                    : detail.item.lowPrice / 100 + "-" + detail.item.highPrice / 10;
            let posterName = getStorage("posterName");
            // const qrCodeUrl = "https://dante-img.oss-cn-hangzhou.aliyuncs.com/38524941351.jpg";
            console.log("qrCodeUrl:", qrCodeUrl.current);
            return configsFn(
                goodsSrc.replace("http:", "https:"),
                goodName,
                "¥" + priceNow,
                posterName,
                qrCodeUrl.current,
            );
        },
        onImgOK: res => {
            console.log("onImgOK", res.detail?.path);
            setCreate(false);
            onClose();
        },
    });

    useShareAppMessage(callback => {
        const { from } = callback;
        const subStoreId = getStorage(SUB_STORE_ID);
        const guiderId = getStorage(GUIDER_INFO);
        let path = `/pages/goods_detail?shareStoreId=${subStoreId}&id=${detail.item.id}&inviteCode=${infoStore.newUserProfile.inviteCode}`;
        if (getStorage("identity") == "guider") {
            path += `&shareGuiderId=${guiderId}`;
        }
        const itemName = detail.item.name;
        const itemId = detail.item.id;
        reportEvent(
            mall_event.MALL_EVENT_GOODS_SHARE,
            new Map()
                .set(mall_event_key.MALL_KEY_ITEM_NAME, itemName)
                .set(mall_event_key.MALL_KEY_ITEM_ID, itemId)
                .set(mall_event_key.MALL_KEY_SOURCE_FROM, from)
                .set(mall_event_key.MALL_KEY_PATH, path),
        );
        return {
            title: itemName,
            path,
        };
    });

    useEffect(() => {
        sharePath &&
            Taro.previewImage({
                urls: [sharePath as string],
                enablesavephoto: true,
                enableShowPhotoDownload: true,
            });
    }, [sharePath]);

    useEffect(() => {
        if (show) {
            getCode();
        }
    }, [show]);

    return (
        <>
            {poster}
            <ShareSheet
                show={show}
                title="分享"
                options={options}
                onSelect={e => {
                    if (e.detail.name === "分享商品海报") {
                        setCreate(true);
                    }
                }}
                onClose={onClose}
            />
        </>
    );
};
