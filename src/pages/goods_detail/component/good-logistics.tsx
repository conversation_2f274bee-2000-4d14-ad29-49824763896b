import { View, Image } from "@tarojs/components";
import { useEffect, useState } from "react";

export default ({ origin }) => {
    const [arriveDate, setArriveDate] = useState("");

    useEffect(() => {
        let date = new Date();
        date.setDate(date.getDate() + 3);
        let y: string | number = date.getFullYear();
        let m: string | number = date.getMonth() + 1;
        m = m < 10 ? "0" + m : m;
        let d: string | number = date.getDate();
        d = d < 10 ? "0" + d : d;
        setArriveDate(y + "-" + m + "-" + d);
    }, []);

    return (
        <View className="card card2" style={{ paddingTop: "20rpx" }}>
            <View className="express">
                <View className="line">
                    <View className="label">物流</View>
                    <View className="text" style={{ textAlign: "center" }}>
                        16:00前付款，预计{arriveDate}送达
                    </View>
                </View>
                <View className="steps">
                    <Image src="https://dante-img.oss-cn-hangzhou.aliyuncs.com/3584882914.png" mode="aspectFit" />
                    <View className="step1">{origin ? origin : ""}进口 </View>
                    <View className="step3">目的地</View>
                </View>
            </View>
        </View>
    );
};
