const configsFn = (
    goodsSrc: string,
    goodsName: string,
    goodsPrice: string | number,
    userName: string,
    qrUrl: string,
) => {
    return {
        width: "650rpx",
        height: "900rpx",
        background: "#fff",
        views: [
            {
                type: "image",
                url: goodsSrc,
                css: {
                    width: "650rpx",
                    height: "650rpx",
                    borderColor: "#000000",
                    mode: "scaleToFill",
                    top: "0rpx",
                    right: "0rpx",
                },
            },
            {
                type: "text",
                text: goodsName,
                css: {
                    color: "#333",
                    background: "rgba(0,0,0,0)",
                    width: "400rpx",
                    top: "670rpx",
                    left: "20rpx",
                    fontSize: "28rpx",
                    maxLines: "2",
                    textAlign: "left",
                    lineHeight: "32rpx",
                },
            },
            {
                type: "qrcode",
                content: qrUrl,
                css: {
                    color: "#000000",
                    background: "#ffffff",
                    width: "163rpx",
                    height: "163rpx",
                    top: "695rpx",
                    left: "440rpx",
                    rotate: "0",
                },
            },
            {
                type: "text",
                text: userName,
                css: {
                    color: "#333333",
                    background: "rgba(0,0,0,0)",
                    width: "400rpx",
                    left: "20rpx",
                    top: "820rpx",
                    borderColor: "#000000",
                    fontSize: "28rpx",
                    maxLines: "1",
                    textAlign: "left",
                    lineHeight: "32rpx",
                },
            },
            {
                type: "text",
                text: goodsPrice,
                css: {
                    width: "400rpx",
                    top: "770rpx",
                    left: "20rpx",
                    color: "#f42121",
                    borderColor: "#000000",
                    fontSize: "40rpx",
                    maxLines: "1",
                    textAlign: "left",
                    lineHeight: "48rpx",
                },
            },
        ],
    };
};
export default configsFn;
