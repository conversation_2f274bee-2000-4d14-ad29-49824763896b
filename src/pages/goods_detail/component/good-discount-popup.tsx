import react, { useEffect, useState } from "react";
import { View, Text } from "@tarojs/components";
import { ActionSheet, Button } from "@antmjs/vantui";
import "./good-discount-popup.less";
import newAPi from "@/utils/newApi";
import { useRouter } from "@tarojs/taro";
import DDYToast from "@/utils/toast";
import { DDYObject } from "src/type/common";
import { getStorage } from "@/utils/storage";
import { IS_LOGIN } from "@/utils/constant";
export default function Demo({ show, setShow, viewCouponInfos, price, discountPrice }) {
    const router = useRouter();

    const [currentPrice, setCurrentPrice] = useState();

    const [list, setList] = useState([]);

    const getUsedCoupon = () => {
        newAPi
            .queryCoupons({
                method: "GET",
                data: {
                    goodsCode: router.params.id || 6914,
                    unGet: true,
                },
            })
            .then(res => {
                // setList(res || []);
                let result: DDYObject[] = res;
                const arr = viewCouponInfos.filter(item => item.couponType === "福豆");
                // result = [...result]
                // console.log(arr);
                if (arr.length > 0) {
                    const str = +arr[0].couponScope.replace("可抵", "");
                    const price = str.toFixed(2);
                    result.push({
                        scopeType: "GIFT",
                        price: price,
                    });
                }
                // console.log(result);
                setList(result);
            });
    };

    const userGetCoupons = (couponActivityId, couponScopeId) => {
        newAPi
            .userGetCoupons({
                method: "GET",
                data: {
                    couponActivityId,
                    couponScopeId,
                },
            })
            .then(res => {
                DDYToast.info("领取成功");
                getUsedCoupon();
            });
    };

    useEffect(() => {
        if (!getStorage(IS_LOGIN) || getStorage(IS_LOGIN) === "0") return;
        getUsedCoupon();
    }, []);

    return (
        <>
            <ActionSheet show={show} title="优惠明细" onClose={() => setShow(false)} zIndex={1000} disableScroll>
                <View className="good-discount-popup">
                    <View className="discount-rect">
                        <View className="discount-main">
                            <Text className="text-sm">券后价</Text>
                            <Text className="text-4xl">¥{discountPrice}</Text>
                        </View>
                        <View className="line-rect">
                            <View className="line-w"></View>
                            <View className="peak"></View>
                        </View>
                        <View className="price-content">
                            <View className="price-text3">{price}</View>
                            {viewCouponInfos.map(item => (
                                <View className="price-item">
                                    <View className="price-text2">-</View>
                                    <View className="price-text3">{item.amount}</View>
                                </View>
                            ))}
                        </View>
                        <View className="dicount-contents">
                            <View className="goods-price">当前售价</View>
                            {viewCouponInfos.map(item => (
                                <View className="discount-price">
                                    <View className="discount-price-title">{item.couponType}</View>
                                    <View className="discount-price-info">{item.couponScope}</View>
                                </View>
                            ))}
                        </View>
                    </View>
                    <View className="coupon-list">
                        {list.map((item, index) => {
                            return (
                                <View key={index} className={item.alreadyGet ? "coupon-item-2" : "coupon-item-1"}>
                                    <View className="coupon-left">
                                        {item.method === "DIRECT_REDUCTION" ? (
                                            <Text className="coupon-text1">
                                                <Text className="coupon-text2">{item.deductionAmount}</Text>
                                                <Text className="coupon-text6">元</Text>
                                            </Text>
                                        ) : null}
                                        {item.method === "FULL_REDUCTION" ? (
                                            <Text className="coupon-text1">
                                                <Text className="coupon-text2">{item.thresholdDeductionAmount}</Text>
                                                <Text className="coupon-text6">元</Text>
                                            </Text>
                                        ) : null}
                                        {item.method === "DISCOUNT" ? (
                                            <Text className="coupon-text1">
                                                <Text className="coupon-text2">{item.discount}</Text>
                                                <Text className="coupon-text6">折</Text>
                                            </Text>
                                        ) : null}
                                        {item.scopeType === "GIFT" ? (
                                            <Text className="coupon-text1">
                                                <Text className="coupon-text2">{item.price}</Text>
                                                <Text className="coupon-text6">元</Text>
                                            </Text>
                                        ) : null}

                                        {item.scopeType !== "GIFT" ? (
                                            <View className="coupon-text3">
                                                {!item.thresholdAmount ? "无门槛使用" : `满${item.thresholdAmount}可用`}
                                            </View>
                                        ) : (
                                            <View className="coupon-text3">该商品可用</View>
                                        )}
                                    </View>
                                    <View className="coupon-right">
                                        <View style={{ width: "120px" }}>
                                            {/* <Text className="coupon-text4">{}</Text> */}
                                            {item.scopeType === "SHOP" && <Text className="coupon-text4">店铺券</Text>}
                                            {item.scopeType === "PRODUCT" && (
                                                <Text className="coupon-text4">商品券</Text>
                                            )}
                                            {item.scopeType === "GIFT" && <Text className="coupon-text4">福豆</Text>}
                                            <View />
                                            <Text className="coupon-text5">{item.usageTimeDesc}</Text>
                                        </View>
                                        <View style={{ width: "133rpx" }}>
                                            {item.scopeType !== "GIFT" ? (
                                                <>
                                                    {item.alreadyGet ? (
                                                        <View className="coupon-btn2">已领取</View>
                                                    ) : (
                                                        <View
                                                            className="coupon-btn1"
                                                            onClick={() => {
                                                                userGetCoupons(
                                                                    item.couponActivityId,
                                                                    item.couponScopeId,
                                                                );
                                                            }}
                                                        >
                                                            领取
                                                        </View>
                                                    )}
                                                </>
                                            ) : (
                                                <View />
                                            )}
                                        </View>
                                    </View>
                                </View>
                            );
                        })}
                    </View>
                </View>
            </ActionSheet>
        </>
    );
}
