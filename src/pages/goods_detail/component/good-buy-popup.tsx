import { ActionSheet } from "@antmjs/vantui";
import { View, Image } from "@tarojs/components";

export default ({ show, closeFn, list }) => {
    return (
        <ActionSheet
            show={show}
            style={{ zIndex: 4000, padding: "0 24px" }}
            title={<View style={{ height: "49px" }}>最近购买</View>}
            onClose={() => {
                // setShow(false);
                closeFn();
            }}
        >
            <View className="buy-log-list">
                {list.map((item, index) => {
                    return (
                        <View className="buyer-item">
                            <Image className="buyer-item-avator" src={item.headImg} />
                            <View className="buyer-item-name">{item.buyerName}</View>
                            <View className="buyer-item-goodName">{item.itemName}</View>
                            <View className="buyer-item-time">{item.byDesc}</View>
                        </View>
                    );
                })}
                <View className="buy-log-bottom">仅展示最近30条购买记录</View>
                <View style={{ height: "40px" }} />
            </View>
        </ActionSheet>
    );
};
