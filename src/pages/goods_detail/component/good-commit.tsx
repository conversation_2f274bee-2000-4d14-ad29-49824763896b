import newAPi from "@/utils/newApi";
import { View } from "@tarojs/components";
import { useDidShow, useRouter } from "@tarojs/taro";
import { useEffect, useState } from "react";
import "./good-commit.less";
import GoodsCommitItem from "@/components/goods-commit-item";
import { DDYNavigateTo } from "@/utils/route";
import { IS_LOGIN } from "@/utils/constant";
import { getStorage } from "@/utils/storage";

export default () => {
    const route = useRouter();
    const [list, setList] = useState([]);
    const [countObj, setCountObj] = useState({
        totalCountDesc: "",
        goodCountDesc: "",
        badCountDesc: "",
        pictureCountDesc: "",
    });
    const [praise, setPraise] = useState(0);
    const getevaluation = () => {
        newAPi
            .getDetailCommentInfo({
                method: "POST",
                data: {
                    itemId: route.params.id,
                },
            })
            .then(res => {
                setList(res);
                if (res[0]) {
                    setPraise(res[0].praise);
                }
            });
    };

    const getCount = () => {
        newAPi
            .getDetailCommentAllData({
                method: "POST",
                data: {
                    itemId: route.params.id || 6914,
                },
            })
            .then(res => {
                setCountObj(res);
            });
    };

    useDidShow(() => {
        if (!getStorage(IS_LOGIN) || getStorage(IS_LOGIN) === "0") {
            return;
        }
        getevaluation();
        getCount();
    });
    return (
        <View className="goods-commit-rect">
            <View
                className="goods-commit-header"
                onClick={() => {
                    DDYNavigateTo({
                        url: `/pages/good-commit/index?id=${route.params.id}`,
                    });
                }}
            >
                <View className="goods-commit-title">用户评价（{countObj.totalCountDesc || 0}）</View>
                <View className="goods-commit-right">{praise < 90 ? "查看全部" : `好评度${praise}%`}</View>
            </View>
            <View className="goods-commit-content">
                {list.map((item: any) => {
                    return <GoodsCommitItem {...item} isEllipsisImg={true} />;
                })}
            </View>
        </View>
    );
};
