.good-discount-popup {
    height: 60vh;
    overflow-y: scroll;
    .discount-rect {
        border: 1px dashed #c2c2c2;
        margin: 20px;
        padding-bottom: 76px;
        .discount-main {
            // text-align: ;
            display: flex;
            flex-direction: row;
            align-items: flex-end;
            justify-content: center;
            margin-top: 40px;
            .text-sm {
                font-weight: 500;
                font-size: 28px;
                color: #ff0940;
                line-height: 28px;
                text-align: left;
                font-style: normal;
            }
            .text-4xl {
                font-weight: 500;
                margin-left: 8px;
                font-size: 50px;
                color: #ff0940;
                line-height: 50px;
                text-align: left;
                font-style: normal;
            }
        }
        .line-rect {
            position: relative;
            height: 48px; /* 容器高度 */
            width: 618px; /* 容器宽度 */
            margin-top: 50px;
            margin: 50px auto 0 auto;
            .line-w {
                position: absolute;
                top: 50%;
                left: 0;
                height: 2px; /* 线的高度 */
                width: 618px;
                background-color: rgba(255, 9, 64, 1); /* 线的颜色 */
                transform: translateY(-50%);
            }
            .peak {
                position: absolute;
                top: -24px; /* 尖角相对于线的位置 */
                left: 50%;
                height: 0;
                width: 0;
                border-left: 24px solid transparent;
                border-right: 24px solid transparent;
                border-bottom: 48px solid rgba(255, 9, 64, 1); /* 尖角的颜色 */
            }
            .peak::after {
                position: absolute;
                top: 9px; /* 尖角相对于线的位置 */
                left: -24px;
                height: 0;
                width: 0;
                content: "";
                border-left: 24px solid transparent;
                border-right: 24px solid transparent;
                border-bottom: 48px solid #fff; /* 尖角的颜色 */
            }
        }
        .price-content,
        .dicount-contents {
            display: flex;
            flex-direction: row;
            align-items: center;
            justify-content: flex-start;
        }
        .price-content {
            margin-left: 38px;
            .price-text3 {
                width: 140px;
                font-weight: 500;
                height: 50px;
                font-size: 40px;
                color: #ff0940;
                line-height: 50px;
                text-align: center;
                font-style: normal;
            }
            .price-text2 {
                width: 28px;
                height: 50px;
                font-weight: 500;
                font-size: 40px;
                color: #ff0940;
                line-height: 50px;
                text-align: center;
                font-style: normal;
            }
            .price-item {
                display: flex;
                flex-direction: row;
                align-items: center;
            }
        }
        .dicount-contents {
            margin-left: 31px;
            margin-top: 50px;
            .goods-price {
                width: 127px;
                height: 40px;
                margin-bottom: 40px;
                border: 1px solid #ff0940;
                text-align: center;
                font-size: 20px;
                color: #ff0940;
                line-height: 40px;
                text-align: left;
                font-style: normal;
                text-align: center;
                border-radius: 8px;
                box-sizing: border-box;
                background-color: rgba(255, 9, 64, 0.09);
            }
            .discount-price {
                width: 127px;
                height: 80px;
                border: 1px solid #ff0940;
                margin-left: 47px;
                box-sizing: border-box;
                border-radius: 8px;
                .discount-price-title {
                    height: 40px;
                    border-bottom: 1px solid #ff0940;
                    text-align: center;
                    font-size: 20px;
                    color: #ff0940;
                    box-sizing: border-box;
                    line-height: 40px;
                    text-align: center;
                    font-style: normal;
                    background-color: rgba(255, 9, 64, 0.09);
                }
                .discount-price-info {
                    height: 40px;
                    text-align: center;
                    font-size: 20px;
                    color: #ff0940;
                    line-height: 40px;
                    box-sizing: border-box;
                    text-align: center;
                    font-style: normal;
                }
            }
        }
    }
    .coupon-list {
        .coupon-item-2 {
            opacity: 0.8;
        }
        .coupon-item-1,
        .coupon-item-2 {
            background-image: url(https://dante-img.oss-cn-hangzhou.aliyuncs.com/21692716456.png);
            background-repeat: no-repeat;
            background-size: 692px 203px;
            background-position: center;
            width: 692px;
            height: 203px;
            display: flex;
            flex-direction: row;
            align-items: center;
            margin-top: 49px;
            margin-left: 30px;
            .coupon-left {
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                width: 172px;
                .coupon-text1 {
                    font-size: 32px;
                    line-height: 72px;
                    color: #e50a1a;
                }
                .coupon-text2 {
                    font-weight: 600;
                    font-size: 60px;
                    line-height: 72px;
                    color: #e50a1a;
                }
                .coupon-text6 {
                    font-weight: 600;
                    font-size: 20px;
                    line-height: 72px;
                    color: #e50a1a;
                }
                .coupon-text3 {
                    font-weight: 200;
                    font-size: 18px;
                    color: #e50a1a;
                    line-height: 25px;
                    text-align: center;
                    font-style: normal;
                }
            }
            .coupon-right {
                width: 493px;
                display: flex;
                flex-direction: row;
                align-items: center;
                justify-content: center;
                box-sizing: border-box;
                padding-left: 30px;
                .coupon-text4 {
                    font-weight: 500;
                    font-size: 32px;
                    color: #e50a1a;
                    line-height: 45px;
                    text-align: center;
                    font-style: normal;
                }
                .coupon-text5 {
                    font-weight: 400;
                    font-size: 20px;
                    color: #ff0940;
                    line-height: 28px;
                    text-align: left;
                    font-style: normal;
                }
                .coupon-btn1 {
                    width: 133px;
                    height: 58px;
                    background: #ffffff;
                    border-radius: 32px;
                    border: 1px solid #ff0940;
                    font-weight: 400;
                    font-size: 24px;
                    color: #ff0940;
                    line-height: 58px;
                    text-align: center;
                    font-style: normal;
                    margin-left: 44px;
                }
                .coupon-btn2 {
                    width: 133px;
                    height: 58px;
                    border-radius: 32px;
                    font-weight: 400;
                    font-size: 24px;
                    color: #ff7c99;
                    line-height: 58px;
                    text-align: center;
                    font-style: normal;
                    margin-left: 44px;
                }
            }
        }
    }
}
