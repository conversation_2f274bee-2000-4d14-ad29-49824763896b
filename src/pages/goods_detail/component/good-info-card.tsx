import { View, Text, Image } from "@tarojs/components";
import GoodDiscountPopup from "./good-discount-popup";
import { useState } from "react";
import { Icon } from "@antmjs/vantui";
import newApi from "@/utils/newApi";
import DDYToast from "@/utils/toast";

export default ({ detail, skus, spec, detailInfo, showDiscountPop, showSkuPop, onCollectChange }) => {
    const { flagList, promotionList } = detail;
    const { type, isBonded, name, stockQuantity, discount } = detail.item;
    const priceNow = detailInfo?.price;
    const maxPriceNow = detailInfo?.maxPrice;
    // detail.item.lowPrice == detail.item.highPrice
    //     ? detail.item.lowPrice / 100
    //     : detail.item.lowPrice / 100 + "-" + detail.item.highPrice / 10;
    const discountPrice = detailInfo?.discountPrice;
    const crossedPrice = skus?.[0]?.crossedPrice || 0;
    const originUrl = spec.defaultOriginUrl; // 国家图片
    const customTaxHolder = spec.customTaxHolder; // 税金承担方(1:买家承担，2:卖家承担)
    const tax = (spec.tax / 100).toFixed(2);
    const origin = spec.defaultOrigin;
    const detailInfoItem = detailInfo.item;
    // const [show, setShow] = useState(false);

    const isJdGoods = flagList?.includes("jdCloudItem");

    const toggleStar = item => {
        if (item.collect) {
            // 调用取消收藏的接口
            getCollect(item, 10);
        } else {
            // 调用收藏的接口
            getCollect(item, 1);
        }
    };

    function getCollect(item, status) {
        newApi
            .getCancelCollect({
                method: "POST",
                data: {
                    itemId: item.id,
                    status: status,
                },
            })
            .then(res => {
                onCollectChange();
                DDYToast.info(`${status === 10 ? "取消收藏成功" : "收藏成功"}`);
            });
    }

    const validSkus = (skus || []).filter(item => item.status !== -1);

    return (
        <View className="card card1">
            <View className="couponItem-box">
                {flagList?.map((flagItem, index) => (
                    <View className="label-box" key="flagItem">
                        {flagItem == "promotionActivityItem" && <View className="label-item">活动</View>}
                        {flagItem == "promotionLimitedItem" && <View className="label-item-limit">限定</View>}
                    </View>
                ))}
                {promotionList?.map((item, index) => (
                    <View className="coupon-box" key={index}>
                        {item.couponType == "CASH_REBATE" && (
                            <View className="cash-rebate">
                                满{item.conditionFee}减{item.reduceFee}
                            </View>
                        )}
                        {item.couponType == "LADDER" && <View className="cash-rebate">满减</View>}
                    </View>
                ))}
            </View>
            {skus.length > 1 ? (
                <View
                    className="sku-rect"
                    onClick={() => {
                        showSkuPop(1, false);
                    }}
                >
                    <View className="sku-items-rect">
                        {validSkus.map((item, index) => {
                            // if (item.status === -1) return null;
                            return (
                                <View className={`sku-items-rect-item ${index === 0 ? "sku-selected" : ""}`}>
                                    {item.specification || item.name}
                                </View>
                            );
                        })}
                    </View>

                    <View className="sku-items-ellipsis">共{validSkus.length}款可选</View>
                </View>
            ) : null}
            <View
                className="line1"
                onClick={() => {
                    if (discountPrice !== priceNow) {
                        showDiscountPop(true);
                    }
                }}
            >
                <Text className="price_lbl"></Text>
                <Text className="price">{priceNow}</Text>
                {maxPriceNow > priceNow ? <Text style={{ fontSize: "10px", color: "#f51214" }}>起</Text> : null}
                {crossedPrice ? <Text className="original-price">¥{crossedPrice}</Text> : null}
                {discountPrice !== priceNow ? (
                    <View className="discount-price">
                        <Text className="discount-text1">折后</Text>
                        <Text className="discount-text2">¥{discountPrice}</Text>
                        {validSkus.length > 1 ? <Text className="discount-text1"> 起</Text> : null}
                    </View>
                ) : null}

                {customTaxHolder === 2 ? <Text className="tag">含税</Text> : null}
                {isBonded == 1 ? (
                    <View className="origin">
                        <Image className="icon" src={originUrl} mode="aspectFit" />
                        <View className="name">{origin}</View>
                    </View>
                ) : null}
            </View>
            <View className="title">{name}</View>
            <View className={`item-info ${customTaxHolder !== 1 ? "flex-end" : ""}`}>
                {/* <View className="line2"> */}
                {customTaxHolder === 1 && <View className="rate">预估税费：{tax}元</View>}
                {/* {!isJdGoods ? <View className="type">库存：{stockQuantity}</View> : null} */}
                {/* </View> */}
                {/* 添加收藏 */}
                <View className="collect" onClick={() => toggleStar(detailInfoItem)}>
                    <Icon
                        name={detailInfoItem?.collect ? "star" : "star-o"}
                        size={42}
                        color={detailInfoItem?.collect ? "#ff0940" : "#333"}
                    />
                    <Text className="collect-text">收藏</Text>
                </View>
            </View>
        </View>
    );
};
