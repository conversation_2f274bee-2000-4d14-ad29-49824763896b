import { View, Text, Image } from "@tarojs/components";

export default ({ detail, spec }) => {
    const { flagList, promotionList } = detail;
    const { type, isBonded, name, stockQuantity } = detail.item;
    const priceNow =
        detail.item.lowPrice == detail.item.highPrice
            ? detail.item.lowPrice / 100
            : detail.item.lowPrice / 100 + "-" + detail.item.highPrice / 10;

    const crossedPrice = spec.skus?.[0].extraPrice.crossedPrice || 0;
    const originUrl = spec.defaultOriginUrl; // 国家图片
    const customTaxHolder = spec.customTaxHolder; // 税金承担方(1:买家承担，2:卖家承担)
    const tax = spec.tax;
    const origin = spec.defaultOrigin;

    const isJdGoods = flagList?.includes("jdCloudItem");
    return (
        <View className="card card1">
            <View className="couponItem-box">
                {flagList?.map((flagItem, index) => (
                    <View className="label-box" key="flagItem">
                        {flagItem == "promotionActivityItem" && <View className="label-item">活动</View>}
                        {flagItem == "promotionLimitedItem" && <View className="label-item-limit">限定</View>}
                    </View>
                ))}
                {promotionList?.map((item, index) => (
                    <View className="coupon-box" key={index}>
                        {item.couponType == "CASH_REBATE" && (
                            <View className="cash-rebate">
                                满{item.conditionFee / 100}减{item.reduceFee / 100}
                            </View>
                        )}
                        {item.couponType == "LADDER" && <View className="cash-rebate">满减</View>}
                    </View>
                ))}
            </View>
            <View className="line1">
                <Text className="price_lbl"></Text>
                {type == "1" ? (
                    <Text className="price">{priceNow}</Text>
                ) : (
                    <Text className="point">积分{priceNow}</Text>
                )}
                {crossedPrice ? <Text className="original-price">¥{crossedPrice / 100}</Text> : null}
                {customTaxHolder === 2 ? <Text className="tag">含税</Text> : null}
                {isBonded == 1 ? (
                    <View className="origin">
                        <Image className="icon" src={originUrl} mode="aspectFit" />
                        <View className="name">{origin}</View>
                    </View>
                ) : null}
            </View>
            <View className="title">{name}</View>
            <View className="line2">
                {customTaxHolder === 1 && <View className="rate">预估税费：{tax / 100}元</View>}
                {!isJdGoods ? <View className="type">库存：{stockQuantity}</View> : null}
            </View>
        </View>
    );
};
