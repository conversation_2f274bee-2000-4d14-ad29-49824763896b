import { Image, View } from "@tarojs/components";
import { useEffect, useState } from "react";
import { Swiper, SwiperItem } from "@antmjs/vantui";
import Taro from "@tarojs/taro";
export default (images: any[] = [], defaultImage) => {
    const [currentImg, setCurrentImg] = useState(0);
    const [kg, setkg] = useState(Taro.getSystemInfoSync().windowWidth || 375);

    return (
        <View style={{ position: "relative" }}>
            <Swiper
                autoPlay={3000}
                height={kg}
                paginationVisible
                width={kg}
                isCenter
                onChange={index => {
                    setCurrentImg(index);
                }}
            >
                {images?.map((item, index) => (
                    <SwiperItem key={`swiper#demo1${index}`}>
                        <Image src={item.url} mode="aspectFill" className="slide-image" />
                    </SwiperItem>
                ))}
                {Array.isArray(images) && images.length === 0 ? (
                    <SwiperItem>
                        <Image src={defaultImage} mode="aspectFill" className="slide-image" />
                    </SwiperItem>
                ) : null}
            </Swiper>
            {images.length == 0 ? (
                <View className="dots">
                    {currentImg + 1}/{1}
                </View>
            ) : (
                <View className="dots">
                    {currentImg + 1}/{images.length}
                </View>
            )}
        </View>
    );
};
