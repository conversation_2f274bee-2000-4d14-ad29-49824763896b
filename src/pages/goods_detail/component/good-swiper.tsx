import { Image, Video, View } from "@tarojs/components";
import { useEffect, useState } from "react";
import { Swiper, SwiperItem } from "@antmjs/vantui";
import Taro from "@tarojs/taro";
import { isVideoFile } from "@/utils/common";
export default ({ images = [], defaultImage, videoUrl }) => {
    const [currentImg, setCurrentImg] = useState(0);
    const [kg, setkg] = useState(Taro.getSystemInfoSync().windowWidth || 375);
    const [list, setList] = useState<{ url: string }[]>([]);

    useEffect(() => {
        if (images.length && defaultImage) {
            const url = defaultImage;
            images.unshift({ url: url });
            console.log("images:", images);
            setList([...images]);
        }
        if (images.length && videoUrl) {
            const url = videoUrl;
            images.unshift({ url: url });
            console.log("images:", images);
            setList([...images]);
        }
    }, [images, videoUrl]);
    // const [list,]
    return (
        <View style={{ position: "relative" }}>
            {list.length ? (
                <>
                    <Swiper
                        autoPlay={videoUrl ? 0 : 3000}
                        height={kg}
                        paginationVisible
                        width={kg}
                        touchable={images.length === 0 ? false : true}
                        isCenter
                        onChange={index => {
                            setCurrentImg(index);
                        }}
                    >
                        {list?.map((item, index) => {
                            if (isVideoFile(item.url)) {
                                return (
                                    <SwiperItem key={`swiper#demo1${index}`}>
                                        <Video
                                            src={item.url}
                                            className="slide-image"
                                            showFullscreenBtn={true}
                                            autoplay={false}
                                            showPlayBtn={true}
                                            showCenterPlayBtn={true}
                                            controls={true}
                                        />
                                    </SwiperItem>
                                );
                            }
                            return (
                                <SwiperItem key={`swiper#demo1${index}`}>
                                    <Image src={item.url} mode="aspectFill" className="slide-image" />
                                </SwiperItem>
                            );
                        })}
                    </Swiper>
                </>
            ) : (
                <>
                    {videoUrl ? (
                        <Video
                            src={videoUrl}
                            className="slide-image"
                            showFullscreenBtn={true}
                            autoplay={false}
                            showPlayBtn={true}
                            showCenterPlayBtn={true}
                            controls={true}
                        />
                    ) : null}
                    {defaultImage ? <Image src={defaultImage} mode="aspectFill" className="slide-image" /> : null}
                </>
            )}
            {list.length == 0 ? (
                <View className="dots">
                    {currentImg + 1}/{videoUrl ? 2 : 1}
                </View>
            ) : (
                <View className="dots">
                    {currentImg + 1}/{list.length}
                </View>
            )}
        </View>
    );
};
