import { ActionSheet, Button, Card, GoodsAction, GoodsActionButton, GoodsActionIcon, Stepper } from "@antmjs/vantui";
import Taro, { useRouter } from "@tarojs/taro";
import { useEffect, useRef, useState } from "react";
import { View, Text } from "@tarojs/components";
import { IDENTITY, IS_LOGIN, SCAN_PAGE, STORE_ID } from "@/utils/constant";
import { getStorage, removeStorage } from "@/utils/storage";
import GoodShare from "./good-share";
import { DDYNavigateTo, DDYReLaunch, DDYSwitchTab } from "@/utils/route";

import infoStore from "@/store/info-store";
import useValid from "../hooks/useValid";
import DDYToast from "@/utils/toast";
import api from "@/utils/api";
import { jumpLogin } from "@/utils/PageUtils";
import { mall_event, mall_event_key, reportEvent } from "@/utils/track-report";

const btnName = {
    1: "立即购买",
    2: "申请补货",
};

const jdBtnName = {
    1: "立即购买",
    2: "该地区无库存",
    3: "库存查询中...",
};

export default ({ detail, detailInfo, spec, jdQStatus }) => {
    const route = useRouter();
    const [purchasetype, setPurchasetype] = useState<1 | 2>(1);
    const [shareVisible, setShareVisible] = useState(false);
    const [addCartShow, setAddCartShow] = useState(false);
    const openType = useRef(1);
    const [validIntegral] = useValid();
    // const specId
    const [orderNum, setOrderNum] = useState(1);
    const isJDGoods = detail?.flagList?.includes("jdCloudItem");
    useEffect(() => {
        console.log(infoStore.subStore);
        //@ts-ignore
        setPurchasetype(route.params.purchasetype);
    }, []);

    useEffect(() => {
        console.log("spec:", spec);
    }, [spec]);

    const getSpecId = () => {
        return spec.skus[0].id;
    };

    /**
     *
     * @returns 处理价格显示
     */
    const getPrice = () => {
        if (!detail || !detail.item) {
            return 0;
        }
        const priceNow =
            detail.item.lowPrice == detail.item.highPrice
                ? detail.item.lowPrice / 100
                : detail.item.lowPrice / 100 + "-" + detail.item.highPrice / 100;
        return priceNow;
    };

    const takeCart = e => {
        // e 0:加购；1:下单
        const goodsName = detail.item.name;
        if (detail.item.status === -1) {
            DDYToast.error("商品已被下架, 无法购买！");
            let map = new Map();
            map.set(mall_event_key.MALL_KEY_GOOD_ID, detail.item.id);
            map.set(mall_event_key.MALL_KEY_GOOD_NUM, orderNum);
            map.set(mall_event_key.MALL_KEY_ITEM_NAME, goodsName);
            map.set(mall_event_key.MALL_KEY_BUY_FAIL_REASON, "商品已被下架, 无法购买！");
            reportEvent(mall_event.MALL_EVENT_GOOD_IMMEDIATELY_BUY_FAIL, map);
            return;
        }

        if ((getStorage(IS_LOGIN) || "") != "1") {
            jumpLogin();
            let map = new Map();
            map.set(mall_event_key.MALL_KEY_GOOD_ID, detail.item.id);
            map.set(mall_event_key.MALL_KEY_GOOD_NUM, orderNum);
            map.set(mall_event_key.MALL_KEY_ITEM_NAME, goodsName);
            map.set(mall_event_key.MALL_KEY_BUY_FAIL_REASON, "未登录，跳转到登录！");
            reportEvent(mall_event.MALL_EVENT_GOOD_IMMEDIATELY_BUY_FAIL, map);
            return false;
        } else {
            removeStorage(SCAN_PAGE);
        }

        if (getStorage("IsNewUser") === 1) {
            DDYReLaunch({ url: "/pages/my/bind-mobile/index?type=register" });
            let map = new Map();
            map.set(mall_event_key.MALL_KEY_GOOD_ID, detail.item.id);
            map.set(mall_event_key.MALL_KEY_GOOD_NUM, orderNum);
            map.set(mall_event_key.MALL_KEY_ITEM_NAME, goodsName);
            map.set(mall_event_key.MALL_KEY_BUY_FAIL_REASON, "未绑定手机号！");
            reportEvent(mall_event.MALL_EVENT_GOOD_IMMEDIATELY_BUY_FAIL, map);
            return;
        }
        //@ts-ignore
        // console.log(infoStore.subStore.authed)
        // 当门店正在审核中且不是消费者时，提示门店正在审核中，分享、购买商品时无服务费提成
        //@ts-ignore
        // if (infoStore.subStore && infoStore.subStore.authed === false) {
        //     // infoStore.getStoreInfo().then
        //

        confirmTake(e);
    };

    const confirmTake = e => {
        // 积分商品
        try {
            if (detail.item.type !== 1) {
                const price = getPrice();
                if (orderNum * (price as number) > validIntegral) {
                    DDYToast.info("购买总积分不能超过可用积分");
                    return;
                }
            }

            if (detail.skus[0].extra && detail.skus[0].extra.orderQuantityLimit) {
                const orderQuantityLimit = detail.skus[0].extra.orderQuantityLimit;
                if (orderQuantityLimit && orderQuantityLimit != 0 && orderNum > orderQuantityLimit) {
                    DDYToast.info("购买数量不能超过限购数量");
                    return;
                }
            }
        } catch (e) {
            const goodsName = detail.item.name;
            let map = new Map();
            map.set(mall_event_key.MALL_KEY_GOOD_ID, detail.item.id);
            map.set(mall_event_key.MALL_KEY_GOOD_NUM, orderNum);
            map.set(mall_event_key.MALL_KEY_ITEM_NAME, goodsName);
            map.set(mall_event_key.MALL_KEY_BUY_FAIL_REASON, e.toString());
            reportEvent(mall_event.MALL_EVENT_GOOD_IMMEDIATELY_BUY_FAIL, map);
        }
        setAddCartShow(false);
        // const specChose = 0;
        if (e === 0) {
            doTakeCart();
        } else {
            doTakeOrder();
        }
    };

    const doTakeOrder = () => {
        const type = detail.item.type;
        const goodsId = detail.item.id;
        const goodsName = detail.item.name;
        const sourceType = detail.item.sourceType;
        const specId = getSpecId();

        let max = 0;
        detail?.promotionList?.map(item => {
            if (item.maxDiscount) {
                if (item.maxDiscount > max) {
                    max = item.maxDiscount;
                }
            } else {
                if (item.reduceFee > max) {
                    max = item.reduceFee;
                }
            }
        });

        let map = new Map();
        map.set(mall_event_key.MALL_KEY_GOOD_ID, goodsId);
        map.set(mall_event_key.MALL_KEY_GOOD_NUM, orderNum);
        map.set(mall_event_key.MALL_KEY_ITEM_NAME, goodsName);
        map.set(mall_event_key.MALL_KEY_GOOD_SPEC_ID, specId);
        reportEvent(mall_event.MALL_EVENT_GOOD_IMMEDIATELY_BUY, map);
        const data = [
            {
                skuId: specId,
                quantity: orderNum,
                sourceType: sourceType,
            },
        ];
        api.preCheck({
            data: {
                data: JSON.stringify(data),
            },
            // filterCheck: true,
            // method: 'POST',
        }).then(res => {
            if (type == "3" || type == "4" || type == "5") {
                DDYNavigateTo({
                    url:
                        "/pages/order/comfire-order/index?goodsId=" +
                        goodsId +
                        "&orderNum=" +
                        orderNum +
                        "&specId=" +
                        specId +
                        "&exchange=integral" +
                        "&maxDiscount=" +
                        max +
                        "&sourceType=" +
                        sourceType,
                });
            } else {
                DDYNavigateTo({
                    url:
                        "/pages/order/comfire-order/index?goodsId=" +
                        goodsId +
                        "&orderNum=" +
                        orderNum +
                        "&specId=" +
                        specId +
                        "&maxDiscount=" +
                        max +
                        "&sourceType=" +
                        sourceType,
                });
            }
        });
    };

    const doTakeCart = () => {
        const specId = getSpecId();
        let map = new Map();
        map.set(mall_event_key.MALL_KEY_GOOD_ID, specId);
        map.set(mall_event_key.MALL_KEY_GOOD_NUM, orderNum);
        reportEvent(mall_event.MALL_EVENT_ADD_CART, map);
        api.addCart({
            method: "POST",
            contentType: "application/x-www-form-urlencoded",
            data: {
                skuId: String(specId),
                quantity: String(orderNum),
            },
            filterCheck: true,
        })
            .then(res => {
                DDYToast.success("成功加入购物车");
                getCartNumber();
            })
            .catch(err => {
                DDYToast.info("加入购物车失败" + err.message);
            });
    };

    /**
     * 更新首页购物车 的数量标识
     */

    const getCartNumber = () => {
        api.getCartNumber({
            data: {
                shopId: getStorage(STORE_ID),
            },
            filterCheck: true,
        })
            .then(res => {
                console.log("getCartNumber:", res);
                const cart = res.data;
                if (cart > 0) {
                    Taro.setTabBarBadge({
                        index: 2,
                        text: cart,
                    });
                } else {
                    Taro.removeTabBarBadge({
                        index: 2,
                    });
                }
                DDYSwitchTab({
                    url: "/pages/shop_cart/index",
                });
            })
            .catch();
    };
    const share = async () => {
        // // 判断是否登录，没登录跳到登录页面
        let hold = await api.loginHold({ data: {}, showError: false, filterCheck: true });
        if (!hold) {
            DDYNavigateTo({
                url: "/pages/switch-login/index",
            });
            return;
        }
        // 当门店正在审核中，提示门店正在审核中，分享、购买商品时无服务费提成
        if (getStorage("subStoreInfo") && getStorage("subStoreInfo").authed == false) {
            DDYToast.showModal({
                title: "提示信息",
                content: "门店正在审核中，分享、购买商品时无服务费提成",
                success(res) {
                    if (res.confirm) {
                        // that.getGoodEwm();
                        console.log("用户点击确定");
                        setShareVisible(true);
                    } else if (res.cancel) {
                        console.log("用户点击取消");
                    }
                },
            });
        } else {
            setShareVisible(true);
        }
    };
    const disabled = detail.item.status === -1 || detail.item.sellOutStatus == 2;
    return (
        <>
            {detail.item.status === -1 && <View className="off-shelf">已下架</View>}
            {detail.item.sellOutStatus == 2 && <View className="off-shelf">已售罄</View>}
            <GoodsAction>
                <GoodsActionIcon
                    icon="wap-home-o"
                    text="首页"
                    onClick={() => {
                        DDYSwitchTab({ url: "/pages/index/index" });
                    }}
                />
                {detail && detail.item && detail.item.type === 1 ? (
                    <GoodsActionIcon
                        icon="shopping-cart-o"
                        text="购物袋"
                        // disabled=
                        onClick={() => {
                            DDYSwitchTab({ url: "/pages/shop_cart/index" });
                        }}
                    />
                ) : null}

                <GoodsActionIcon
                    icon="share-o"
                    text="分享"
                    disabled={disabled}
                    onClick={() => {
                        share();
                    }}
                />
                {detail && detail.item && detail.item.type === 1 ? (
                    <GoodsActionButton
                        color="#ffa900"
                        text="加入购物袋"
                        disabled={disabled}
                        type="warning"
                        onClick={() => {
                            openType.current = 0;
                            setAddCartShow(true);
                        }}
                    />
                ) : null}
                {isJDGoods ? (
                    <GoodsActionButton
                        color={[2, 3].includes(jdQStatus) ? "#ccc" : "#f30"}
                        disabled={[2, 3].includes(jdQStatus)}
                        text={jdBtnName[jdQStatus]}
                        onClick={() => {
                            if (jdQStatus === 2) {
                                DDYToast.info("当前区域没有可用库存");
                                return;
                            }
                            openType.current = 1;

                            setAddCartShow(true);
                        }}
                    />
                ) : (
                    <GoodsActionButton
                        color="#f30"
                        disabled={disabled}
                        text={btnName[purchasetype || 1]}
                        onClick={() => {
                            openType.current = 1;
                            setAddCartShow(true);
                        }}
                    />
                )}
            </GoodsAction>

            {/** 弹框部分 */}

            {/** 分享弹框 */}
            <GoodShare
                detail={detail}
                detailInfo={detailInfo}
                show={shareVisible}
                onClose={() => setShareVisible(false)}
            />

            {/** 选择数量弹框 */}
            <ActionSheet
                show={addCartShow}
                title={openType.current === 0 ? "加入购物袋" : "选择数量"}
                onClose={() => {
                    setAddCartShow(false);
                    setOrderNum(1);
                }}
            >
                <Card
                    thumbMode="scaleToFill"
                    price={getPrice() as string}
                    title={detail?.item?.name}
                    thumb={detail?.item?.mainImage}
                />
                <View
                    style={{
                        height: "200px",
                        padding: "10px",
                        borderTop: "1px solid #ccc",
                        margin: "0 10px",
                        position: "relative",
                    }}
                >
                    <View style={{ display: "flex", alignItems: "center", justifyContent: "space-between" }}>
                        <View>
                            <Text>购买数量</Text>
                            <Text>库存：{detail?.item?.stockQuantity || 0} 件</Text>
                        </View>
                        <View>
                            <Stepper
                                value={orderNum}
                                max={detail?.item?.stockQuantity || 0}
                                onChange={val => {
                                    setOrderNum(val.detail as number);
                                }}
                            />
                        </View>
                    </View>
                    <Button
                        type="primary"
                        color="#f51214"
                        round
                        block
                        style={{ position: "absolute", bottom: "20px", left: 0, right: 0 }}
                        onClick={() => {
                            takeCart(openType.current);
                        }}
                    >
                        确定
                    </Button>
                </View>
            </ActionSheet>
        </>
    );
};
