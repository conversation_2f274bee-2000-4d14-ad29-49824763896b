import {
    ActionSheet,
    Button,
    Card,
    GoodsAction,
    GoodsActionButton,
    GoodsActionIcon,
    Icon,
    Stepper,
} from "@antmjs/vantui";
import Taro, { useRouter } from "@tarojs/taro";
import { Fragment, useEffect, useRef, useState } from "react";
import { View, Text, Image } from "@tarojs/components";
import { IDENTITY, IS_LOGIN, SCAN_PAGE, STORE_ID } from "@/utils/constant";
import { getStorage, removeStorage } from "@/utils/storage";
import GoodShare from "./good-share";
import { DDYNavigateTo, DDYReLaunch, DDYSwitchTab } from "@/utils/route";

import infoStore from "@/store/info-store";
import useValid from "../hooks/useValid";
import DDYToast from "@/utils/toast";
import api from "@/utils/api";
import { jumpLogin } from "@/utils/PageUtils";
import { mall_event, mall_event_key, reportEvent } from "@/utils/track-report";
import newAPi from "@/utils/newApi";
import discountPrice from "@/components/discount-price";

const btnName = {
    1: "立即购买",
    2: "申请补货",
};

const jdBtnName = {
    1: "立即购买",
    2: "该地区无库存",
    3: "库存查询中...",
};

const CouponBtnName = {
    0: "立即购买",
    1: "领券购买",
    2: "用券购买",
};

export default ({ detail, detailInfo, skus, spec, jdQStatus, showSkuPop }) => {
    const route = useRouter();
    const [purchasetype, setPurchasetype] = useState<1 | 2>(1);
    const [shareVisible, setShareVisible] = useState(false);
    // const [addCartShow, setAddCartShow] = useState(false);
    const openType = useRef(1);
    const [validIntegral] = useValid();
    const [newDiscount, setNewDiscount] = useState();
    // const specId
    const [orderNum, setOrderNum] = useState(1);
    const isJDGoods = detail?.flagList?.includes("jdCloudItem");
    // 组团商品
    const { hasGbActivity } = detailInfo;
    const [userCouponStatus, setUserCouponStatus] = useState(0); // 0: 无须领券；1:有需要领的优惠券；2: 已经领完了所有可用优惠券；
    // const updatePrice = num => {
    //     newAPi
    //         .getSkuDiscount({
    //             method: "POST",
    //             data: {
    //                 // itemId: detail.item.id,
    //                 id: skus[0].id,
    //                 count: num,
    //             },
    //         })
    //         .then(res => {
    //             setNewDiscount(res);
    //         });
    // };

    useEffect(() => {
        // if (getStorage(IS_LOGIN) && skus.length > 0) {
        //     updatePrice(1);
        // }
    }, [skus]);

    useEffect(() => {
        //@ts-ignore
        setPurchasetype(route.params.purchasetype);
    }, []);

    useEffect(() => {
        // if()
        const arr = detailInfo?.viewCouponInfos || [];
        // arr.map((item)=>{})
        if (arr.length === 0) {
            setUserCouponStatus(0);
        } else {
            let bol = true;
            if (arr.filter(item => !item.alreadyGet).length) {
                bol = false;
            }
            setUserCouponStatus(bol ? 2 : 1);
        }
    }, [detailInfo?.viewCouponInfos]);

    const getSpecId = () => {
        return spec.skus[0].id;
    };

    /**
     *
     * @returns 处理价格显示
     */
    const getPrice = () => {
        if (!detail || !detail.item) {
            return 0;
        }
        const priceNow =
            detail.item.lowPrice == detail.item.highPrice
                ? detail.item.lowPrice / 100
                : detail.item.lowPrice / 100 + "-" + detail.item.highPrice / 100;
        return priceNow;
    };

    useEffect(() => {
        if (detailInfo) {
            detailInfo.price && setNewDiscount(detailInfo.discountPrice);
        }
    }, [detailInfo]);

    const disabled = detail.item.status === -1 || detail.item.sellOutStatus == 2;
    return (
        <>
            {detail.item.status === -1 && <View className="off-shelf">已下架</View>}
            {detail.item.sellOutStatus == 2 && !hasGbActivity && <View className="off-shelf">已售罄</View>}
            <GoodsAction>
                <GoodsActionIcon
                    icon="wap-home-o"
                    text="首页"
                    onClick={() => {
                        DDYSwitchTab({ url: "/pages/index/index" });
                    }}
                />
                {
                    <GoodsActionIcon
                        icon="shopping-cart-o"
                        text="购物袋"
                        // disabled=
                        onClick={() => {
                            DDYSwitchTab({ url: "/pages/shop_cart/index" });
                        }}
                    />
                }
                {getStorage(IS_LOGIN) === 1 && (
                    <Button openType="share" style={{ padding: 0 }}>
                        {/* <GoodsActionIcon
                            icon="share-o"
                            text="分享"
                            disabled={disabled}
                            // type=""
                            onClick={() => { }}
                        /> */}
                        <View style={{ width: "60px" }}>
                            <Icon name="share-o" size="18px" />
                            <View className="share-btn-text">分享</View>
                        </View>
                    </Button>
                )}

                {!hasGbActivity ? (
                    <GoodsActionButton
                        color="#ffa900"
                        text="加入购物袋"
                        disabled={disabled}
                        type="warning"
                        onClick={() => {
                            openType.current = 0;
                            // updatePrice(1);
                            // setAddCartShow(true);
                            showSkuPop(0, false);
                        }}
                    />
                ) : null}
                {isJDGoods ? (
                    <GoodsActionButton
                        color={[2, 3].includes(jdQStatus) ? "#ccc" : "#f30"}
                        disabled={[2, 3].includes(jdQStatus)}
                        // text={jdBtnName[jdQStatus]}
                        text={
                            (purchasetype || 1) === 1 ? (
                                <View style={{ fontSize: "12px" }}>
                                    {userCouponStatus === 0 ? CouponBtnName[userCouponStatus] : null}
                                    {userCouponStatus !== 1 ? (
                                        <Fragment>
                                            <Text>¥{newDiscount}</Text>
                                            <View>{CouponBtnName[userCouponStatus]}</View>
                                        </Fragment>
                                    ) : null}
                                </View>
                            ) : (
                                <Text style={{ fontSize: "14px" }}>{btnName[purchasetype]}</Text>
                            )
                        }
                        onClick={() => {
                            if (jdQStatus === 2) {
                                DDYToast.info("当前区域没有可用库存");
                                return;
                            }
                            openType.current = 1;
                            // updatePrice(1);
                            // setAddCartShow(true);
                            showSkuPop(1, false);
                        }}
                    />
                ) : null}
                {hasGbActivity ? (
                    <>
                        <GoodsActionButton
                            // color="#f30"
                            disabled={disabled}
                            color="#ffa900"
                            text="原价购买"
                            type="warning"
                            onClick={() => {
                                openType.current = 1;
                                // updatePrice(1);
                                showSkuPop(1, false);
                            }}
                        />
                        <GoodsActionButton
                            color="#f30"
                            // disabled={disabled}
                            text={"组团购买"}
                            onClick={() => {
                                openType.current = 1;
                                // updatePrice(1);

                                showSkuPop(1, true);
                            }}
                        />
                    </>
                ) : null}
                {!isJDGoods && !hasGbActivity ? (
                    <GoodsActionButton
                        color="#f30"
                        disabled={disabled}
                        text={
                            (purchasetype || 1) === 1 ? (
                                <View style={{ fontSize: "12px" }}>
                                    {userCouponStatus === 0 ? CouponBtnName[userCouponStatus] : null}
                                    {userCouponStatus !== 0 ? (
                                        <Fragment>
                                            <Text>
                                                ¥{newDiscount}
                                                {skus.length > 1 ? "起" : null}
                                            </Text>
                                            <View>{CouponBtnName[userCouponStatus]}</View>
                                        </Fragment>
                                    ) : null}
                                </View>
                            ) : (
                                <Text style={{ fontSize: "14px" }}>{btnName[purchasetype]}</Text>
                            )
                        }
                        onClick={() => {
                            openType.current = 1;
                            // updatePrice(1);
                            showSkuPop(1, false);
                        }}
                    />
                ) : null}
            </GoodsAction>

            {/** 弹框部分 */}

            {/** 分享弹框 */}
            <GoodShare
                detail={detail}
                detailInfo={detailInfo}
                show={shareVisible}
                onClose={() => setShareVisible(false)}
            />
        </>
    );
};
