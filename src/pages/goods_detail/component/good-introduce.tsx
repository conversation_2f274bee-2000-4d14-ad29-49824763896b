import { View, Image, RichText, Text } from "@tarojs/components";

export default ({ detail, detailInfo }) => {
    return (
        <View style="margin-top:20rpx;margin-bottom:calc(120rpx + env(safe-area-inset-bottom))">
            <View className="swiper-tab-pd" style="border-bottom:1rpx solid #ededed;padding-top:15px;">
                商品介绍
            </View>
            <View>
                <View className="doc">
                    <View className="wxParse-p">
                        {detailInfo && (
                            <View className="introd-detail">
                                {detailInfo?.groupedOtherAttributes?.map((item, index) => (
                                    <View style="overflow: hidden;">
                                        {item.otherAttributes.map((attrItem, attrIndex) => {
                                            return (
                                                <View className="introd-detail-list" key={attrIndex}>
                                                    {attrItem.attrKey == "birthday" ||
                                                    attrItem.attrKey == "weight" ||
                                                    attrItem.attrKey == "origin" ? (
                                                        <View>
                                                            {attrItem.attrKey == "birthday" && <Text>上市时间：</Text>}
                                                            {attrItem.attrKey == "weight" && <Text>重量：</Text>}
                                                            {attrItem.attrKey == "origin" && <Text>产地：</Text>}
                                                            <Text>{attrItem.attrVal}</Text>
                                                        </View>
                                                    ) : (
                                                        <View>
                                                            <Text>{attrItem.attrKey}：</Text>
                                                            <Text>{attrItem.attrVal}</Text>
                                                        </View>
                                                    )}
                                                </View>
                                            );
                                        })}
                                    </View>
                                ))}
                            </View>
                        )}
                        {detailInfo?.itemDetail?.detail ? (
                            <RichText nodes={packageRichTextContent(detailInfo?.itemDetail?.detail)} />
                        ) : null}
                        {/* <RichText nodes={packageRichTextContent(TEXT)} /> */}

                        <View>
                            {detail?.item?.type && detail?.item?.type == "1" && (
                                <Image
                                    src="https://dante-img.oss-cn-hangzhou.aliyuncs.com/online/778df484d00d4eb9d64790dd6920d98b.jpg"
                                    className="slide-image"
                                    mode="widthFix"
                                />
                            )}
                        </View>
                    </View>
                </View>
            </View>
        </View>
    );
};

/**
 * 因后台富文本无法处理样式
 * 手动添加处理最外层边距
 */
const packageRichTextContent = text => {
    const result = text
        .replaceAll("<img ", '<img style="width:100%"')
        .replaceAll("<p>", '<p style="padding:0 12px;display:inline-block;">')
        .replaceAll("<h1>", '<h1 style="padding:0 12px;display:inline-block;">')
        .replaceAll("<h2>", '<h2 style="padding:0 12px;display:inline-block;">');
    return `<div style="width:100%;background-color:#fff;">${result}</div>`;
};
