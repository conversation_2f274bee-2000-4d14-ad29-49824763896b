import { ScrollView, View } from "@tarojs/components";
import { pxTransform, useDidShow, useRouter } from "@tarojs/taro";
import { useEffect, useState } from "react";
import "./index.less";
import GoodSwiper from "./component/good-swiper";
import useDetail from "./hooks/useDetail";
import useDetailInfo from "./hooks/useDetailInfo";
import GoodInfoCard from "./component/good-info-card";
import useSpec from "./hooks/useSpec";
import GoodMinorInfoCard from "./component/good-minor-info-card";
import useLogistics from "./hooks/useLogistics";
import GoodLogistics from "./component/good-logistics";
import GoodShopCard from "./component/good-shop-card";
import GoodIntroduce from "./component/good-introduce";
import GoodBottom from "./component/good-bottom";
import { getStorage } from "@/utils/storage";
import { ADDRESS_ID, IDENTITY, IS_LOGIN } from "@/utils/constant";
import api from "@/utils/api";
export default () => {
    const [winheight, setWinheight] = useState(750);
    const [detail, getDetail] = useDetail();
    const [detailInfo, getDetailInfo] = useDetailInfo();
    const [spec, getSpec] = useSpec();
    const [logistics, getLogistics] = useLogistics();
    const scrollTopFun = e => {};
    const [jdQStatus, setjdQStatus] = useState(1); // 1:立即购买,2:该地区无库存,3:库存查询中...
    const identity = getStorage(IDENTITY);
    const isLogin = getStorage(IS_LOGIN);

    const getJdStatus = address => {
        if (detail.stockCheck && Object.keys(address).length && detail.skus.length) {
            setjdQStatus(3);
            api.queryJDYSkuStatus({
                method: "POST",
                data: {
                    skuQuantityList: detail.skus.map(item => {
                        return { outerSkuId: item.outerSkuId, quantity: 1 };
                    }),
                    address,
                },
                showError: false,
            })
                .then(res => {
                    setTimeout(() => {
                        setjdQStatus(res ? 1 : 2);
                    }, 500);
                })
                .catch(err => {
                    setjdQStatus(1);
                });
        }
    };

    return (
        <>
            <ScrollView className="scroll-container" scroll-y="true" scrollTop={0} onScroll={scrollTopFun}>
                {/** 轮播模块 */}
                <View className="swiper-rect">
                    <View className="broadcast">
                        {
                            //@ts-ignore
                            detail?.item?.status == -1 && <View className="sellStatus_bck" />
                        }

                        {
                            //@ts-ignore
                            GoodSwiper(detailInfo?.itemDetail?.images || [], detail?.item?.mainImage)
                        }
                    </View>
                </View>

                {/** 主体信息模块（商品标签，活动标签，价格，库存...） */}
                {detail && spec && <GoodInfoCard detail={detail} spec={spec} />}

                {/** 次级信息展示模块（商品标签，电商标签） */}
                {!!detail && detail?.item?.type === 1 && (
                    <GoodMinorInfoCard
                        onLoad={address => {
                            // toJS(comfireStore.address)
                            // console.log(toJS(comfireStore.address));
                            getJdStatus(address);
                        }}
                    />
                )}

                {/** 物流信息模块 */}
                {!!detail && detail?.item?.type === 1 && spec && logistics.length > 0 && (
                    //@ts-ignore
                    <GoodLogistics origin={spec?.defaultOrigin} />
                )}

                {/** 店铺信息模块 */}
                {!!detail && detail?.item?.type === 1 && <GoodShopCard detail={detail} />}

                {/** 商品介绍 */}
                {!!detail && <GoodIntroduce detail={detail} detailInfo={detailInfo} />}
                <View style={{ height: "1rpx" }} />
            </ScrollView>
            {!!detail && (isLogin != "1" || identity == "buyer" || identity == "guider" || identity == "subStore") && (
                <GoodBottom detail={detail} detailInfo={detailInfo} spec={spec} jdQStatus={jdQStatus} />
            )}
        </>
    );
};
