import { ScrollView, View } from "@tarojs/components";
import { pxTransform, reportEvent, useDidShow, useRouter, useShareAppMessage } from "@tarojs/taro";
import { useEffect, useState } from "react";
import "./index.less";
import GoodSwiper from "./component/good-swiper";
import useDetail from "./hooks/useDetail";
import useDetailInfo from "./hooks/useDetailInfo";
import GoodInfoCard from "./component/good-info-card";
import useSpec from "./hooks/useSpec";
import GoodMinorInfoCard from "./component/good-minor-info-card";
import useLogistics from "./hooks/useLogistics";
import GoodLogistics from "./component/good-logistics";
import GoodShopCard from "./component/good-shop-card";
import GoodIntroduce from "./component/good-introduce";
import GoodBottom from "./component/good-bottom";
import { getStorage } from "@/utils/storage";
import { ADDRESS_ID, IDENTITY, IS_LOGIN } from "@/utils/constant";
import api from "@/utils/api";
import Coupon from "../wallet-card/coupon";
import newAPi from "@/utils/newApi";
import { optimizeCoupons } from "../wallet-card/fn/optimizeCoupons";
import GoodDiscountPopup from "./component/good-discount-popup";
import SkuAddPopup from "./component/sku-add-popup";
import GoodCommit from "./component/good-commit";
import GoodBuyLog from "./component/good-buy-log";
import GoodBuyPopup from "./component/good-buy-popup";
export default () => {
    const router = useRouter();
    const [detail, getDetail] = useDetail();
    const [detailInfo, getDetailInfo] = useDetailInfo();
    const [spec, getSpec] = useSpec();
    const [logistics, getLogistics] = useLogistics();
    const [skuPop, setSkuPop] = useState(false);
    const [openType, setOpenType] = useState(0); // 0:加购；1:下单

    const [skus, setSkus] = useState([]);

    const scrollTopFun = e => {};
    const [jdQStatus, setjdQStatus] = useState(1); // 1:立即购买,2:该地区无库存,3:库存查询中...
    const identity = getStorage(IDENTITY);
    const isLogin = getStorage(IS_LOGIN);

    const [showPopup, setShowPopup] = useState(false);
    const [coupons, setCoupons] = useState([]);
    const [buys, setBuys] = useState([]);
    const [buyOpen, setBuyOpen] = useState(false);

    const [show, setShow] = useState(false);
    const [hasGbActivity, setHasGbActivity] = useState(false);

    // 领取优惠券
    const getCouponsSend = () => {
        console.log("getCouponsSend", isLogin);
        if (isLogin === "0" || !isLogin) return;
        newAPi
            .getAutoGetCoupons({
                method: "GET",
                data: {
                    goodsCode: router.params.id,
                },
            })
            .then(res => {
                if (res) {
                    const optimizedCoupons = optimizeCoupons(res, "popup");
                    setCoupons(optimizedCoupons);
                    if (optimizedCoupons?.length) {
                        setShowPopup(true);
                    }
                }
            });
    };

    const getJdStatus = address => {
        if (detail.stockCheck && Object.keys(address).length && detail.skus.length) {
            setjdQStatus(3);
            api.queryJDYSkuStatus({
                method: "POST",
                data: {
                    skuQuantityList: detail.skus.map(item => {
                        return { outerSkuId: item.outerSkuId, quantity: 1 };
                    }),
                    address,
                },
                showError: false,
            })
                .then(res => {
                    setTimeout(() => {
                        setjdQStatus(res ? 1 : 2);
                    }, 500);
                })
                .catch(err => {
                    setjdQStatus(1);
                });
        }
    };

    useEffect(() => {
        getCouponsSend();
    }, []);

    return (
        <>
            <ScrollView className="scroll-container" scroll-y="true" scrollTop={0} onScroll={scrollTopFun}>
                {/** 轮播模块 */}
                <View className="swiper-rect">
                    <View className="broadcast">
                        {
                            //@ts-ignore
                            detail?.item?.status == -1 && <View className="sellStatus_bck" />
                        }

                        {
                            //@ts-ignore
                            // GoodSwiper(detailInfo?.itemDetail?.images || [], detail?.item?.mainImage)
                            <GoodSwiper
                                images={detailInfo?.itemDetail?.images || []}
                                defaultImage={
                                    detailInfo?.item?.extraActivityValid
                                        ? detailInfo?.item?.extra?.activityMainImage
                                        : detailInfo?.item?.mainImage
                                }
                                videoUrl={detailInfo?.itemDetail?.videoUrl}
                            />
                        }
                    </View>
                </View>

                {/** 主体信息模块（商品标签，活动标签，价格，库存...） */}
                {detail && detailInfo && spec && (
                    <GoodInfoCard
                        detail={detail}
                        spec={spec}
                        skus={skus}
                        detailInfo={detailInfo}
                        showDiscountPop={() => {
                            setShow(true);
                        }}
                        showSkuPop={(type, bol) => {
                            setSkuPop(true);
                            setOpenType(type);
                            console.log("setHasGbActivity:", bol);
                            setHasGbActivity(bol);
                        }}
                        onCollectChange={() => {
                            getDetailInfo && getDetailInfo();
                        }}
                    />
                )}

                {/** 次级信息展示模块（商品标签，电商标签） */}
                {!!detail && detail?.item?.type === 1 && (
                    <GoodMinorInfoCard
                        onLoad={address => {
                            // toJS(comfireStore.address)
                            // console.log(toJS(comfireStore.address));
                            getJdStatus(address);
                        }}
                    />
                )}

                {/** 物流信息模块 */}
                {!!detail && detail?.item?.type === 1 && spec && logistics.length > 0 && (
                    //@ts-ignore
                    <GoodLogistics origin={spec?.defaultOrigin} />
                )}
                {!!detail && (
                    <GoodBuyLog
                        showBuyModal={data => {
                            setBuys(data);
                            setBuyOpen(true);
                        }}
                    />
                )}
                {/** 店铺信息模块 */}
                {/* {!!detail && detail?.item?.type === 1 && <GoodShopCard detail={detail} />} */}
                {/** 商品评论 **/}
                <GoodCommit />
                {/** 商品介绍 */}
                {!!detail && <GoodIntroduce detail={detail} detailInfo={detailInfo} />}

                <View style={{ height: "1rpx", marginTop: "calc(120rpx + env(safe-area-inset-bottom))" }} />
            </ScrollView>
            {!!detail && !!detailInfo && (
                <GoodBottom
                    detail={detail}
                    detailInfo={detailInfo}
                    spec={spec}
                    skus={skus}
                    jdQStatus={jdQStatus}
                    showSkuPop={(type, bol) => {
                        setSkuPop(true);
                        setOpenType(type);
                        console.log("setHasGbActivity:", bol);
                        setHasGbActivity(bol);
                    }}
                />
            )}

            {/* 优惠券 */}
            {showPopup && coupons?.length > 0 && (
                <Coupon visible={showPopup} dataList={coupons} onClose={() => setShowPopup(false)} />
            )}

            <GoodDiscountPopup
                setShow={setShow}
                show={show}
                viewCouponInfos={detailInfo?.viewCouponInfos || []}
                price={detailInfo?.price}
                discountPrice={detailInfo?.discountPrice}
            />

            <SkuAddPopup
                show={skuPop}
                closeFn={load => {
                    setSkuPop(false);
                }}
                skuLoad={datas => {
                    setSkus([...datas]);
                }}
                openType={openType}
                goodsId={router.params.id}
                hasGbActivity={hasGbActivity}
            />

            <GoodBuyPopup
                show={buyOpen}
                closeFn={() => {
                    setBuyOpen(false);
                }}
                list={buys}
            />
        </>
    );
};
