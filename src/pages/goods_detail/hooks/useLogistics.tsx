import { handleGoodsLink } from "@/utils/PageUtils";
import api from "@/utils/api";
import { useRouter } from "@tarojs/taro";
import { useEffect, useState } from "react";

export default () => {
    const [logistics, setLogistics] = useState([]);
    const route = useRouter();

    const getQuery = () => {
        api.getQuery({
            data: {
                itemId: handleGoodsLink(route.params).id || 6914,
            },
            filterCheck: true,
        }).then(res => {
            setLogistics(res);
        });
    };

    useEffect(() => {
        getQuery();
    }, []);

    return [logistics, getQuery];
};
