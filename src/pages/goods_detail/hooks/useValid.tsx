import api from "@/utils/api";
import { IS_LOGIN, STORE_ID, USER_INFO } from "@/utils/constant";
import { getStorage } from "@/utils/storage";
import { useEffect, useState } from "react";

export default () => {
    const [validIntegral, setValidIntegral] = useState<number>(0);

    const getUserIntegralNum = () => {
        if (getStorage(IS_LOGIN) == "1") {
            api.getUserIntegralNum({
                data: {
                    userId: getStorage(USER_INFO).userId,
                    shopId: getStorage(STORE_ID),
                },
                filterCheck: true,
            })
                .then(res => {
                    setValidIntegral(res);
                })
                .catch(err => {
                    console.log(err);
                });
        }
    };
    useEffect(() => {
        getUserIntegralNum();
    }, []);

    return [validIntegral];
};
