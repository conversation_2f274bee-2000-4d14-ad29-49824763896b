import { handleGoodsLink } from "@/utils/PageUtils";
import api from "@/utils/api";
import { useDidShow, useRouter } from "@tarojs/taro";
import { useState } from "react";

export default () => {
    const [detail, setDetail] = useState<any>();
    const route = useRouter();

    useDidShow(() => {
        getDetail();
    });

    const getDetail = () => {
        api.goodsDetail({
            data: {
                ids: handleGoodsLink(route.params).id,
            },
            filterCheck: true,
        })
            .then(res => {
                setDetail(res);
            })
            .catch(err => {
                console.log(err);
            });
    };
    return [detail, getDetail];
};
