import { handleGoodsLink } from "@/utils/PageUtils";
import api from "@/utils/api";
import { useRouter } from "@tarojs/taro";
import { useEffect, useState } from "react";

export default () => {
    const [spec, setSpec] = useState();
    const route = useRouter();

    useEffect(() => {
        console.log(route);
        getSpec();
    }, []);

    const getSpec = () => {
        api.getSpec({
            data: {
                itemId: handleGoodsLink(route.params).id,
            },
            filterCheck: true,
        })
            .then(res => {
                setSpec(res);
            })
            .catch(err => {
                console.log(err);
            });
    };
    return [spec, getSpec];
};
