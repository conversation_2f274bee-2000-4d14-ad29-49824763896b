import { handleGoodsLink } from "@/utils/PageUtils";
import api from "@/utils/api";
import newAPi from "@/utils/newApi";
import { useRouter } from "@tarojs/taro";
import { useEffect, useState } from "react";

export default () => {
    const [detailInfo, setDetailInfo] = useState();
    const route = useRouter();

    useEffect(() => {
        getDetailInfo();
    }, []);

    const getDetailInfo = () => {
        newAPi
            .getGoodsDetail({
                data: {
                    itemId: handleGoodsLink(route.params).id || 6914,
                },
                // filterCheck: true,
            })
            .then(res => {
                setDetailInfo(res);
            })
            .catch(err => {
                console.log(err);
            });
    };
    return [detailInfo, getDetailInfo];
};
