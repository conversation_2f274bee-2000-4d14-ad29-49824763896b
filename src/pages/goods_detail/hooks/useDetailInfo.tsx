import { handleGoodsLink } from "@/utils/PageUtils";
import api from "@/utils/api";
import { useRouter } from "@tarojs/taro";
import { useEffect, useState } from "react";

export default () => {
    const [detailInfo, setDetailInfo] = useState();
    const route = useRouter();

    useEffect(() => {
        getDetailInfo();
    }, []);

    const getDetailInfo = () => {
        api.goodsDetailInfo({
            data: {
                itemId: handleGoodsLink(route.params).id,
            },
            filterCheck: true,
        })
            .then(res => {
                setDetailInfo(res);
            })
            .catch(err => {
                console.log(err);
            });
    };
    return [detailInfo, getDetailInfo];
};
