page {
    height: 100%;
    min-height: 100%;
    background: #efefef;
    --color-primary: #ff5500;
    box-sizing: border-box;
}
.scroll-container {
    height: 100vh;
    // padding-bottom: 120px;
    // padding-bottom: calc(120px + env(safe-area-inset-bottom))
    .swiper-rect {
        height: 750px;
        overflow: hidden;
        background-color: #f0f0f0;
    }
}
.wxParse-img {
    width: 100%;
}
.slide-image {
    width: 100%;
    height: 750px;
}
/*数字下标*/
.broadcast {
    background: #ffffff;
    position: relative;
}
.sellStatus {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 2;
    width: 220px;
    height: 220px;
}
.sellStatus_bck {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 1;
    width: 100%;
    height: 100%;
    background: rgba(52, 52, 52, 0.05);
}
.dots {
    bottom: 30rpx;
    right: 30px;
    position: absolute;
    width: 88px;
    height: 33px;
    line-height: 33px;
    margin: auto;
    background: #e3e3e3;
    text-align: center;
    border-radius: 100px;
    color: #3a3a3a;
    font-size: 24px;
    z-index: 10;
}
.big_images {
    height: 100%;
    display: block;
    margin-bottom: 90px;
    image {
        width: 100%;
    }
}
.depot {
    font-size: 24px;
    color: #8f8f8f;
    padding: 23px 0;
    margin: 0 20px;
    background: #ffffff;
    display: flex;
    align-items: center;
    border-bottom: 1px solid #cccccc;
    .country {
        width: 38px;
        height: 38px;
        border-radius: 50%;
    }
    text {
        line-height: 45px;
    }
    .brand {
        padding-left: 30px;
    }
}
.details-msg {
    margin-bottom: 20px;
    padding: 40px 20px;
    background: #ffffff;
    .details-title {
        overflow: hidden;
        width: 100%;
        box-sizing: border-box;
        position: relative;
        font-size: 30px;
        color: #3a3a3a;
        padding: 28px 0 21px;
        font-weight: semibold;
    }
    .details-introduction {
        color: #999;
        font-size: 28px;
        line-height: 40px;
        margin-top: 20px;
    }
    .coupon {
        display: flex;
        justify-content: space-between;
        height: 63px;
        line-height: 63px;
        background: #f0f3f7;
        .coupon_amount {
            padding: 0 10px;
            border: 1px solid #f51214;
            color: #f51214;
            margin: 0 10px 0 18px;
            font-size: 24px;
        }
        .coupon_reduce {
            font-size: 24px;
            color: #3a3a3a;
        }
        .coupon_receive {
            padding: 0 20px;
            background: #e0e5ec;
            width: 121px;
            text-align: center;
            color: #3a3a3a;
            .coupon_more {
                font-size: 32px;
                padding-left: 10px;
            }
        }
    }
    .weight {
        height: 68px;
        line-height: 68px;
        font-size: 24px;
        color: #8f8f8f;
        display: flex;
        justify-content: space-between;
    }
}
.order_num {
    background: #ffffff;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 108px;
    border-bottom: 1px solid #efefef;
    padding: 0 30px;
    .doc {
        color: #808080;
        .num {
            color: #f73c3c;
        }
    }
}
.block {
    padding: 27px 0px;
    background: #ffffff;
    .block_title {
        color: #000;
        height: 30px;
        line-height: 30px;
        border-left: 6px solid #f73c3c;
        padding-left: 20px;
    }
    .block_content {
        padding: 38px 22px;
        .process {
            font-size: 25px;
            margin: 0 auto;
            border: 1px solid #999999;
            padding: 10px;
            border-radius: 200px;
            text-align: center;
            margin-bottom: 25px;
            color: #808080;
        }
        .doc {
            color: #808080;
            font-size: 26px;
            line-height: 30px;
        }
    }
    .table {
        margin: 0 auto;
        margin-top: -24px;
        .th {
            display: flex;
            justify-content: space-between;
            margin-top: 24px;
        }
        .tr {
            font-size: 26px;
            color: #808080;
            text-align: left;
            flex: 1;
        }
    }
}
.detail-bottom {
    width: 100%;
    position: fixed;
    bottom: 0;
    background: #ffffff;
    z-index: 200;
    .bottom-box {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        position: relative;
        .contact {
            width: 100px;
            height: 80px;
            margin: 0 auto;
            position: absolute;
            text-align: center;
            line-height: 80px;
            left: 0;
            opacity: 0;
        }
    }
    .sy-bottom {
        padding: 0px 60px;
        height: 90px;
        line-height: 90px;
        font-size: 28px;
        background: #f5a623;
        color: #fff;
    }
    .already_down {
        width: 498px;
        text-align: center;
        height: 90px;
        line-height: 90px;
        font-size: 28px;
        background: #555555;
        color: #fff;
    }
    .btn_order {
        background: #f51214;
        color: #fff;
    }
    .btn_cart {
        color: #fff;
        background: #ff6e30;
    }
    .order_color {
        background: #ee4856;
        color: #fff;
    }
    .cart_color {
        color: #fff;
        background: #a9a9a9;
    }
    .item {
        flex: 1;
        text-align: center;
        .bottom_img {
            width: 35px;
            padding: 15px 0 9px;
        }
        .doc {
            font-size: 22px;
            color: #8f8f8f;
        }
    }
    .selec_active {
        .doc {
            color: #ff4856;
        }
        .iconfont {
            color: #ff4856;
        }
    }
}
.over_model {
    position: fixed;
    overflow: hidden;
    background: rgba(0, 0, 0, 0.5);
    z-index: 999;
    width: 100%;
    height: 100%;
    top: 0;
}
.head_box {
    display: flex;
    align-items: center;
    border-bottom: 1px solid #efefef;
    padding-bottom: 26px;
    .img_wrap {
        height: 200px;
        width: 200px;
        background: #000;
    }
    .goods_img {
        height: 200px;
        width: 200px;
        background: #000;
    }
    .product_wrap {
        padding: 20px;
    }
    .product_name {
        width: 400px;
        height: 90px;
        font-size: 30px;
        line-height: 45px;
        color: #333;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        max-height: 100px;
        overflow: hidden;
    }
    .price {
        color: #f30;
        font-size: 36px;
        padding-top: 32px;
    }
}
.rule_box {
    border-bottom: 1px solid #efefef;
    padding-bottom: 26px;
    .title {
        color: #4c4c4c;
        font-size: 32px;
        margin-top: 10px;
        white-space: normal;
        overflow: hidden;
        text-overflow: ellipsis;
        -webkit-line-clamp: 3;
    }
    .items {
        display: flex;
        flex-wrap: wrap;
        margin-top: 5px;
        margin-left: -20px;
    }
    .item {
        padding: 15px 28px;
        background: #e6e6e6;
        color: #000;
        margin-left: 20px;
        margin-top: 10px;
        border-radius: 10px;
    }
    .active {
        background: #ed394a;
        color: #fff;
    }
}
.num_box {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 50px 0px;
    .title-stock {
        display: flex;
        align-items: center;
        .title {
            font-size: 28px;
            color: #333;
            height: 50px;
            line-height: 50px;
        }
        .stock {
            margin: 0 15px;
            color: #999;
            font-size: 24px;
        }
    }

    .buy-num {
        width: 170px;
        height: 60px;
        line-height: 50px;
        display: flex;
        font-size: 28px;
        text-align: center;
        .jian-btn {
            color: #333;
            width: 60px;
            height: 100%;
            border-left: 1px solid #d7d7d7;
            border-bottom: 1px solid #d7d7d7;
            border-top: 1px solid #d7d7d7;
            border-bottom-left-radius: 6px;
            border-top-left-radius: 6px;
        }
        .jian-btn.disabled {
            background-color: #f5f5f9;
            border-left: 1px solid #eee;
            border-bottom: 1px solid #eee;
            border-top: 1px solid #eee;
            color: #ccc;
        }
        .jia-btn {
            color: #333;
            width: 60px;
            height: 100%;
            border-right: 1px solid #d7d7d7;
            border-bottom: 1px solid #d7d7d7;
            border-top: 1px solid #d7d7d7;
            border-bottom-right-radius: 6px;
            border-top-right-radius: 6px;
        }
        .jia-btn.disabled {
            background-color: #f5f5f9;
            border-right: 1px solid #eee;
            border-bottom: 1px solid #eee;
            border-top: 1px solid #eee;
            color: #ccc;
        }
        input {
            width: 68px;
            height: 60px;
            min-height: 50px;
            text-align: center;
            font-size: 28px;
            border: 2px solid #ccc;
        }
    }
}

.panle_model {
    position: fixed;
    width: 100%;
    z-index: 1002;
    background: #fff;
    bottom: 0;
}
.model_content {
    padding: 20px;
    position: relative;
}
.colse_model {
    position: absolute;
    right: 10px;
    top: 10px;
    .icon-close {
        color: #e11500;
        font-size: 32px;
    }
}
.comfire_btn {
    font-size: 32px;
    height: 80px;
    line-height: 80px;
    width: 600px;
    border-radius: 80px;
    background: #f51214;
    text-align: center;
    color: #fff;
    position: absolute;
    bottom: 20px;
    left: 75px;
}
.drawer {
    line-height: 40px;
}

.card {
    border-radius: 20px;
    margin: 20px;
    background: #fff;
    padding: 0 20px;
    color: #333;
    font-size: 26px;
    line-height: 40px;
    position: relative;
    .drawer {
        .show {
            .mask {
                display: block;
            }
            .content {
                transform: translate(0, 0);
                border-radius: 20px 20px 0 0;
            }
        }
        .mask {
            position: fixed;
            left: 0;
            right: 0;
            top: 0;
            bottom: 0;
            background: #000;
            opacity: 0.4;
            z-index: 102;
            display: none;
        }
        .content {
            position: fixed;
            width: 750px;
            background: #fff;
            overflow: hidden;
            z-index: 200;
            left: 0;
            bottom: 0;
            transform: translate(0, 1200px);
            transition: transform 0.3s;
            box-sizing: border-box;
            padding: 20px;

            .close {
                font-family: "iconfont";
                position: absolute;
                top: 20px;
                width: 60px;
                text-align: center;
                right: 20px;
                font-size: 40px;
                line-height: 60px;
                height: 60px;
                z-index: 2;
                :before {
                    font-family: "iconfont";
                    content: "\e6d2";
                    color: #888;
                }
            }
        }
        .content_share {
            .share {
                display: flex;
                height: 340px;
                .share_box {
                    width: 375px;
                    text-align: center;
                    position: relative;
                    .share_img {
                        width: 104px;
                        height: 104px;
                        display: block;
                        margin: 94px auto 24px;
                    }
                    .share_button {
                        position: absolute;
                        width: 150px;
                        height: 160px;
                        top: 94px;
                        left: 100px;
                        opacity: 0;
                    }
                }
            }
            .share_close {
                height: 102px;
                height: calc(102px + env(safe-area-inset-bottom));
                line-height: 102px;
                line-height: calc(102px + env(safe-area-inset-bottom));
                width: 100%;
                font-size: 32px;
                text-align: center;
                border-top: 1px solid #f1f2f3;
            }
        }
    }
    .right-icon {
        position: absolute;
        right: 20px;
        width: 12px;
        height: 24px;
        top: 50%;
        transform: translateY(-50%);
    }
    .tile {
        view {
            display: inline-block;
        }
        &-content {
            border-radius: 8px;
            background-color: #f5f5f5;
            padding: 4px 20px;
        }
    }
    .tile,
    .line {
        // height: 88px;
        line-height: 60px;
        overflow: hidden;
        padding: 0 100px;
        position: relative;
        .label {
            color: #999;
            position: absolute;
            left: 0;
            top: 0;
        }
        .text {
            overflow: hidden;
            height: 60px;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 1;
            -webkit-box-orient: vertical;
            view {
                margin-right: 20px;
            }
        }
    }
}
.card1 {
    padding: 30px 20px;
}

.card1 .line1 {
    position: relative;
    height: 56px;
    line-height: 56px;
}
.card1 .couponItem-box {
    display: flex;
    margin-bottom: 30px;
    align-items: center;
    .coupon-box {
        // height: 36px;
        font-size: 24px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #de1600;
        margin-left: 5px;
        // line-height: 36px;
        .cash-rebate {
            border-radius: 4px;
            border: 2px solid #f50e0c;
            padding: 0 4px;
        }
    }
}

.card1 .line1 .price,
.point {
    color: #f51214;
    font-size: 56px;
    display: inline-block;
}

.card1 .line1 .price:before {
    content: "￥";
    font-size: 40px;
    display: inline-block;
    margin-right: 4px;
}

.card1 .line1 .tag {
    display: inline-block;
    width: 64px;
    height: 40px;
    line-height: 40px;
    background: #fff1f1;
    color: #f51214;
    font-size: 24px;
    margin-left: 20px;
    text-align: center;
    transform: translateY(-8px);
    border-radius: 6px;
}
.card1 .line1 .original-price {
    font-size: 24px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #888888;
    line-height: 33px;
    margin-left: 10px;
    text-decoration: line-through;
}

.card1 .line1 .origin {
    position: absolute;
    right: 0;
    top: 8px;
}

.card1 .line1 .origin .icon {
    height: 32px;
    width: 32px;
    margin-right: 10px;
    transform: translateY(6px);
}

.card1 .line1 .origin .name {
    display: inline-block;
}

.card1 .title {
    margin-top: 20px;
    font-size: 32px;
    font-weight: bold;
    line-height: 50px;
}

.card1 .sub-title {
    color: #999;
    margin-top: 20px;
}

.card1 .line2 {
    margin-top: 20px;
    position: relative;
    height: 40px;
    color: #999;
}

.card1 .line2 .sold,
.card1 .line2 .type {
    position: absolute;
    top: 0;
}

.card1 .line2 .type {
    right: 0;
}

.card1 .rate {
    text-align: center;
}
.card2 {
    .express .steps {
        position: relative;
        height: 150px;
        image {
            width: 500px;
            height: 40px;
            margin: 20px auto;
            display: block;
        }
        view {
            position: absolute;
            width: 200px;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 1;
            -webkit-box-orient: vertical;
            height: 40px;
            overflow: hidden;
            text-align: center;
        }
        .step1 {
            left: 10px;
        }
        .step2 {
            left: 255px;
        }
        .step3 {
            left: 470px;
        }
    }
}

.card2 .drawer .bd .btn {
    width: 640px;
    margin: 100px auto 20px;
    background: #f51214;
    color: #fff !important;
    font-size: 32px;
    text-align: center;
    line-height: 80px;
    height: 80px;
    border-radius: 80px;
    text-indent: 0;
}

.card3 {
    .line {
        position: relative;
        height: 88px;
        color: #999;
        view {
            display: flex;
            flex-direction: row;
            align-items: center;
            justify-content: center;
        }
        // .center:before {
        //     content: "\e6c7";
        // }
        // .you:before {
        //     content: "\e6c8";
        // }
        // .right:before {
        //     content: "\e6ca";
        // }
        .left,
        .right {
            position: absolute;
            top: 0;
        }
        .left {
            left: 0;
        }
        .right {
            right: 0;
        }
        .center {
            text-align: center;
        }
    }
    .tile {
        .text {
            &:after {
                position: absolute;
                right: 0px;
                content: "\e678";
                font-family: "iconfont-fx";
            }
        }
    }
}

.info_block {
    padding: 24px;
    background: #fff;
    margin-bottom: 20px;
    background: #fff;
    color: #333;
    font-size: 26px;
    line-height: 40px;
    position: relative;
    .right-icon {
        position: absolute;
        right: 40px;
        width: 12px;
        height: 24px;
        top: 50%;
        transform: translateY(-50%);
    }
    .item {
        padding: 30px 0;
        border-bottom: 1px solid #e7e7e7;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }
    .item:last-child {
    }
    .item_content {
        display: flex;
        align-items: center;
        .text {
            font-size: 26px;
            margin-left: 20px;
        }
        .strong {
            color: #999;
        }
        .image-title {
            margin-left: 20px;
            height: 50px;
            width: 50px;
        }
    }
    .item-right {
        margin-right: 20px;
    }
    .item_img {
        width: 70px;
        height: 70px;
    }
    .arrow {
        color: #3a3a3a;
        font-size: 38px;
    }
    .tip {
        color: #999;
        font-size: 24px;
        margin-top: 20px;
        margin-left: 60px;
    }
}
.swiper-tab-pd {
    line-height: 70px;
    padding: 0 30px;
    background: #fff;
    font-size: 30px;
    color: #3a3a3a;
}
.swiper-tab-order.active {
    color: #333;
    border-bottom: 5px solid #333;
}
.wxParse-p {
    /*margin-top: -10px;*/
}
.wxParse-img {
    max-width: 100%;
    display: block;
}
.introd-detail {
    overflow: hidden;
    background: #fff;
    margin-top: -1px;
    .introd-detail-list {
        padding: 10px 0;
        float: left;
        width: 50%;
        font-size: 26px;
        padding-left: 30px;
        box-sizing: border-box;
    }
}
.top {
    position: fixed;
    bottom: 108px;
    right: 34px;
    image {
        width: 60px;
        height: 60px;
    }
}
/*自定义弹框开始*/
.drawer {
    line-height: 40px;
    .mask {
        position: fixed;
        left: 0;
        right: 0;
        top: 0;
        bottom: 0;
        background: #000;
        opacity: 0.4;
        z-index: 102;
        display: none;
    }
    .content {
        position: fixed;
        width: 750px;
        background: #fff;
        overflow: hidden;
        z-index: 200;
        left: 0;
        bottom: 0;
        transform: translate(0, 1200px);
        transition: transform 0.3s;
        box-sizing: border-box;
        padding: 20px;
        .close {
            font-family: "iconfont-fx";
            position: absolute;
            top: 20px;
            width: 60px;
            text-align: center;
            right: 20px;
            font-size: 40px;
            line-height: 60px;
            height: 60px;
            z-index: 2;
        }
        .close:before {
            font-family: "iconfont-fx";
            content: "\e6fd";
            color: #888;
        }
        .hd {
            height: 60px;
            line-height: 60px;
            font-size: 36px;
            color: #333;
            font-weight: bold;
            text-align: center;
            border-bottom: solid 1px #eee;
            padding-bottom: 30px;
            display: block;
        }
        .bd {
            padding: 20px;
            display: block;
            // height: 1000px;
            -webkit-overflow-scrolling: touch;
            label {
                color: #333;
                font-size: 30px;
                line-height: 50px;
                margin-top: 30px;
                display: block;
                font-weight: bold;
            }
            view {
                color: #555;
                font-size: 26px;
                line-height: 40px;
                // margin-top: 20px;
                // text-indent: 56px;
            }
        }
    }
    &.show {
        .content {
            transform: translate(0, 0);
            border-radius: 20px 20px 0 0;
        }
        .mask {
            display: block;
        }
    }
}

button::after {
    border: none;
    border-radius: 0px;
}
.row {
    display: flex;
    flex-direction: column;
}

.cell {
    display: flex;
    flex-direction: row;
    align-items: center;
}
.disabled .ft .buy,
.disabled .ft .add-shopping-cart {
    background: #ccc;
    color: #666;
}

.ft {
    position: fixed;
    bottom: 0;
    z-index: 100;
    width: 750px;
    height: 100px;
    left: 0;
    background: #fff;
    border-top: solid 1px #e7e7e7;
    height: calc(104px + env(safe-area-inset-bottom));
    .add-shopping-cart,
    .buy {
        width: 212px;
        height: 76px;
        line-height: 76px;
        color: #fff;
        background: #f30;
        text-align: center;
        font-size: 28px;
        color: #fff;
        position: absolute;
        right: 10px;
        top: 12px;
        border-radius: 100px;
    }
    .add-shopping-cart {
        right: 232px;
        background: rgb(255, 169, 0);
    }
    .index,
    .share,
    .shopping-cart,
    .on-shelf {
        /* position: absolute; */
        font-size: 20px;
        width: 100px;
        text-align: center;
        /* left: 20px; */
        color: #666;
        /* top: 12px; */
    }
    .index:before,
    .share:before,
    .shopping-cart:before,
    .on-shelf:before {
        content: "\e63c";
        display: block;
        width: 56px;
        height: 56px;
        text-align: center;
        line-height: 56px;
        margin: 0 auto;
        font-family: "iconfont-fx";
        font-size: 45px;
        color: #333;
    }
    .bottomBox .share {
        margin: 0px !important;
        padding: 0px !important;
    }
    .shopping-cart {
        left: 210px;
    }
    .on-shelf {
        left: 210px;
    }
    .share:before {
        content: "\e65c";
    }

    .shopping-cart:before {
        content: "\e6b7";
    }

    .on-shelf:before {
        font-family: "iconfont-fx";
        content: "\e65d";
    }
}
.bottomBox {
    width: 100%;
    display: flex;
    flex-direction: row;
    align-items: center;
    /* justify-content: space-around; */
}
.icon-close:before {
    color: #888;
}
.label-box {
    margin-left: 10px;
    .label-item {
        border: 2rpx solid #f50e0c;
        border-radius: 6px;
        padding: 0 8px;
        // height: 36px;
        font-size: 24px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #de1600;
        display: inline-block;
    }
    .label-item-limit {
        border: 2rpx solid #f50e0c;
        background: #de1600;
        color: #fff;
        padding: 0 8px;
        border-radius: 6px;
        // height: 36px;
        font-size: 24px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        display: inline-block;
    }
}
.safe-bottom-padding {
    padding-bottom: 50px;
    padding-bottom: calc(50px + env(safe-area-inset-bottom));
}
.van-share-sheet__options {
    justify-content: space-around;
}

.minor-modal-content {
    height: 700px;
    overflow: hidden;
    overflow-y: scroll;
    width: 750rpx;
    box-sizing: border-box;
    padding: 30rpx;
}

.off-shelf {
    position: fixed;
    bottom: calc(var(--goods-action-height, 100rpx) + env(safe-area-inset-bottom));
    height: 60px;
    background: rgba(50, 50, 50, 0.8);
    color: #ddd;
    text-align: center;
    line-height: 60px;
    font-size: 32px;
    width: 100%;
}
