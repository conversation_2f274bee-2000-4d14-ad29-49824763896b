import { useEffect, useState } from "react";
import "./view.less";
import { View, Text, Image as TaroImage } from "@tarojs/components";
import { NavBar, Icon, Image } from "@antmjs/vantui";
import { DDYBack, DDYSwitchTab } from "@/utils/route";
import <PERSON><PERSON> from "./danmu";
import newAPi from "@/utils/newApi";
import Taro, { useRouter } from "@tarojs/taro";
definePageConfig({
    navigationStyle: "custom",
    navigationBarTitleText: "我的福利群",
});

const datas = [
    {
        avator: "https://ai-public.mastergo.com/ai/img_res/e939af0876595b2ff8ecd90970b3d779.jpg",
        name: "张三1",
        msg: "宝宝用品很实惠，谢谢推荐🎁",
    },
    {
        avator: "https://ai-public.mastergo.com/ai/img_res/e939af0876595b2ff8ecd90970b3d779.jpg",
        name: "张三2",
        msg: "宝宝用品很实惠，谢谢推荐🎁",
    },
    {
        avator: "https://ai-public.mastergo.com/ai/img_res/e939af0876595b2ff8ecd90970b3d779.jpg",
        name: "张三3",
        msg: "宝宝用品很实惠，谢谢推荐🎁",
    },
];
export default () => {
    const route = useRouter();
    const { join } = route.params;
    const [data, setData] = useState([]);
    const [qrcode, setQrCode] = useState("");
    const [contents, setContents] = useState([]);
    const [imgPath, setImgPath] = useState("");
    const getData = () => {
        newAPi
            .getBullets({
                method: "POST",
                data: {},
            })
            .then(res => {
                setData(res.bulletScreenList);
                setQrCode(res.qrCode);
                const arr = (res.groupDecorationDetail.content?.split(";") || []).filter(item => !!item);
                setContents(arr);
                setImgPath(res.groupDecorationDetail.imgPath);
            });
    };

    useEffect(() => {
        getData();
    }, []);
    let leftArrow = Taro.getCurrentPages().length > 1;
    return (
        <View className="wool-herd">
            <NavBar
                title="我的福利群"
                leftArrow={leftArrow}
                renderLeft={!leftArrow ? <Icon name="wap-home-o" size={Taro.pxTransform(32)} /> : null}
                onClickLeft={() => {
                    if (leftArrow) {
                        DDYBack();
                    } else {
                        DDYSwitchTab({
                            url: "/pages/index/index",
                        });
                    }
                }}
                style={"background-color:rgba(245, 221, 221, 1)"}
                border={false}
            />
            <View className="page-bg" />
            <View className="page-main">
                <View className="page-head">
                    <View className="main-title">
                        <Text className="title-text">反馈信息</Text>
                        <View className="title-text1">实时更新</View>
                    </View>
                    <View className="barrage-rect">
                        <View className="barrage-content">
                            <Danmu message={data || datas} />
                        </View>
                        <View className="barrage-left-icon"></View>
                        <View className="barrage-right-icon"></View>
                    </View>
                    {!(join === "true") ? (
                        <View className="qrcode-rect">
                            <Image
                                src={qrcode}
                                showMenuByLongpress
                                fit={"contain"}
                                width={Taro.pxTransform(300)}
                                height={Taro.pxTransform(300)}
                            />
                            {/* <QRCode text={qrcode} size={160} scale={4} errorCorrectLevel="M" typeNumber={2} /> */}
                        </View>
                    ) : null}
                    {!(join === "true") ? (
                        <View className="info-rect">
                            <View className="info-head">
                                <Icon name="wechat" size="32px" style={{ color: "#ccc", marginRight: "10px" }} />
                                <Text className="wx-title">添加小助手微信</Text>
                            </View>
                            {contents.map((item, index) => {
                                return (
                                    <View key={index} className="info-text1">
                                        {item}
                                    </View>
                                );
                            })}
                        </View>
                    ) : null}
                </View>

                {imgPath ? (
                    <View>
                        <TaroImage mode="widthFix" src={imgPath} style={{ width: "100%", margin: "20px auto" }} />
                    </View>
                ) : null}
            </View>
        </View>
    );
};
