.scroll-container {
    position: relative;
    width: 100%;
    // height: 30px;
    overflow: hidden;
    .scroll-content {
        display: inline-block;
        white-space: nowrap;
        display: flex;
        flex-direction: row;
        align-items: center;
        // animation: scroll 20s linear infinite; // 滚动动画
        margin-bottom: 18px;
        width: max-content;
    }
    .scroll-item {
        display: inline-block;
        margin-right: 20px; // 信息之间的间距
        font-size: 14px;
        color: #333;
    }
}
@keyframes scroll0 {
    0% {
        transform: translateX(0);
    }
    100% {
        transform: translateX(-100%); // 动态计算滚动距离
    }
}
@keyframes scroll1 {
    0% {
        transform: translateX(20%);
    }
    100% {
        transform: translateX(-80%); // 动态计算滚动距离
    }
}
@keyframes scroll2 {
    0% {
        transform: translateX(10%);
    }
    100% {
        transform: translateX(-90%); // 动态计算滚动距离
    }
}

.bubble-item {
    display: flex;
    flex-direction: row;
    align-items: center;
    margin-right: 41px;
    .bubble-avator {
        height: 44px;
        width: 44px;
        border-radius: 22px;
    }
    .bubble-info {
        display: inline-block;
        margin-right: 20px; // 气泡之间的间距
        padding: 12px;
        background-color: #fff;
        border-radius: 10px;
        position: relative;
        height: 59px;
        background: #ffffff;
        box-shadow: 0px 2px 4px 0px rgba(223, 223, 223, 0.5);
        border-radius: 18px;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        margin-left: 10px;
        box-sizing: border-box;
    }
    .bubble-info::before {
        content: "";
        position: absolute;
        left: -10px; // 三角形位于气泡左侧
        top: 50%;
        transform: translateY(-50%);
        width: 0;
        height: 0;
        border-top: 10px solid transparent;
        border-bottom: 10px solid transparent;
        border-right: 10px solid #fff; // 三角形颜色与气泡背景一致
    }
    .bubble-text {
        font-size: 14px;
        color: #333;
        font-weight: bold;
    }

    .bubble-subtext {
        font-size: 12px;
        color: #666;
    }
}
