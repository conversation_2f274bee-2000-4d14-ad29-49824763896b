import React, { useEffect, useRef, useState } from "react";
import Taro from "@tarojs/taro";
import { View, Text, Image } from "@tarojs/components";
import "./danmu.less";
import DanmuItem from "./danmu-item";

// 每行的滚动速率（单位：秒）
const lineSpeeds = [10, 11, 12]; // 3 行，每行的速率不同

const lefts = [2, 1.75, 1.5];

const Danmu = ({ message }) => {
    const [groupData, setGroupData] = useState([
        {
            list: [],
            containerWidth: 0,
            ref: null,
        },
        {
            list: [],
            containerWidth: 0,
            ref: null,
        },
        {
            list: [],
            containerWidth: 0,
            ref: null,
        },
    ]);
    const [animationData, setAnimationData] = useState({}); // 动画数据
    const [isAnimating, setIsAnimating] = useState(true); // 动画状态
    const animation = Taro.createAnimation({
        duration: 10000, // 动画持续时间
        timingFunction: "linear", // 动画效果
        delay: 0, // 动画延迟
    });

    // 启动动画
    const startAnimation = () => {
        setIsAnimating(true);
        animate();
    };

    // 停止动画
    const stopAnimation = () => {
        setIsAnimating(false);
    };
    // 动画逻辑
    const animate = () => {
        // if (!isAnimating) return; // 如果动画已停止，则退出递归

        // 平移动画：水平方向平移 200px
        animation.translateX(-400).step();

        // 更新动画数据
        setAnimationData(animation.export());

        // 重置动画
        // setTimeout(() => {
        //     console.log('sss')
        //     animation.translateX(0).step({
        //         duration: 0,
        //     });
        //     setAnimationData(animation.export());
        //     // 递归调用，实现无限循环
        //     animate();
        // }, 10000); // 1秒后重置
    };

    const reAnimation = () => {
        console.log("reAnimation:");
        animation.translateX(0).step({ duration: 0 }); // 重置动画
        // this.animationData = this.animation.export()
        setAnimationData({ ...animation.export() });
        setTimeout(() => {
            animate();
        }, 0);
    };

    useEffect(() => {
        if (message.length) {
            const num = Math.floor(message.length / 3) || 1;
            console.log("num:", num);
            setGroupData([
                {
                    list: message.slice(0, num),
                    containerWidth: 0,
                    ref: null,
                }, // 第一组
                // message.slice(num, num * 2), // 第二组
                {
                    list: message.slice(num, num * 2),
                    containerWidth: 0,
                    ref: null,
                }, // 第一组
                // message.slice(num * 2, message.length), // 第三组
                {
                    list: message.slice(num * 2, message.length),
                    containerWidth: 0,
                    ref: null,
                }, // 第一组
            ]);
            setTimeout(() => {
                console.log("开始");
                startAnimation();
            }, 100);
        }
    }, [message]);

    return (
        <View className="scroll-container">
            {groupData.map((groupItem, index) => {
                return <DanmuItem lists={groupItem.list} index={index} />;
            })}
        </View>
    );
};

export default Danmu;
