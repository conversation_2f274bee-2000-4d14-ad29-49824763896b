import { View, Image } from "@tarojs/components";
import Taro from "@tarojs/taro";
import { useEffect, useRef, useState } from "react";

export default ({ lists, index }) => {
    const [animationData, setAnimationData] = useState({}); // 动画数据
    const [isAnimating, setIsAnimating] = useState(true); // 动画状态
    const animation = useRef<Animation>(null);
    const width = useRef(0);
    // 启动动画
    const startAnimation = () => {
        setIsAnimating(true);
        animate();
    };

    // 停止动画
    const stopAnimation = () => {
        setIsAnimating(false);
    };
    // 动画逻辑
    const animate = () => {
        if (!isAnimating) return; // 如果动画已停止，则退出递归
        // console.log("width.current: 滚动距离", width.current);
        // 平移动画：水平方向平移 -width.current / 2
        animation.current.translateX(-width.current / 2).step();

        // 更新动画数据
        setAnimationData(animation.current.export());
    };

    const reAnimation = () => {
        animation.current?.translateX(0).step({ duration: 0 }); // 重置动画
        // this.animationData = this.animation.export()
        // 动画属性在动画执行阶段是可以自动更新的。
        // 但是走新的动画状态的时候，需要更改animationData引用地址，不然无法更新
        setAnimationData({ ...animation.current.export() });
        setTimeout(() => {
            // 重置动画可能会被覆盖
            animate();
        }, 50);
    };
    useEffect(() => {
        if (lists.length) {
            setTimeout(() => {
                const query = Taro.createSelectorQuery();
                query
                    .selectAll(".scroll-content")
                    .boundingClientRect(rects => {
                        width.current = rects[index].width;
                        animation.current = Taro.createAnimation({
                            duration: (width.current / 40) * 500, // 动画持续时间
                            timingFunction: "linear", // 动画效果
                            delay: 0, // 动画延迟
                        });
                        startAnimation();
                    })
                    .exec();
            }, 100);
        }
        return () => {
            setIsAnimating(false);
        };
    }, [lists]);

    return (
        <View className="scroll-content" animation={animationData} onTransitionEnd={reAnimation}>
            {lists.map((item, index) => (
                <View className="bubble-item" key={index}>
                    <Image src={item.avatarPath} mode="aspectFit" className="bubble-avator" />
                    <View className="bubble-info">
                        <View className="bubble-text">
                            {item.groupName}-{item.userName}
                        </View>
                        <View className="bubble-subtext">{item.content}</View>
                    </View>
                </View>
            ))}
            {lists.map((item, index) => (
                <View className="bubble-item" key={index + "k"}>
                    <Image src={item.avatarPath} mode="aspectFit" className="bubble-avator" />
                    <View className="bubble-info">
                        <View className="bubble-text">
                            {item.groupName}-{item.userName}
                        </View>
                        <View className="bubble-subtext">{item.content}</View>
                    </View>
                </View>
            ))}
        </View>
    );
};
