// https://dante-img.oss-cn-hangzhou.aliyuncs.com/97354462137.png
.wool-herd {
    min-height: 100vh;
    --primary-color: #333 !important;
    background-color: rgba(242, 242, 242, 1);
    position: relative;

    .page-bg {
        background-image: url(https://dante-img.oss-cn-hangzhou.aliyuncs.com/97354462137.png);
        background-repeat: no-repeat;
        background-size: 750px 211px;
        // padding-top: calc(env());
        padding-top: 110px;
        box-sizing: content-box;
        height: 211px;
        width: 750px;
        // background-color: ;
    }
    .page-main {
        position: absolute;
        top: 276px;
        left: 24px;
        right: 24px;

        min-height: 200px;

        .page-head {
            padding-top: 37px;
            background-color: #fff;
            border-radius: 16px;
            padding-bottom: 54px;
            .main-title {
                display: flex;
                flex-direction: row;
                align-items: center;
                // margin-top: 37px;
                margin-left: 26px;
                .title-text {
                    font-weight: 500;
                    font-size: 30px;
                    color: #333333;
                    line-height: 30px;
                    text-align: left;
                    font-style: normal;
                }
                .title-text1 {
                    background-image: url(https://dante-img.oss-cn-hangzhou.aliyuncs.com/98805290820.png);
                    background-repeat: no-repeat;
                    background-size: 20px 20px;
                    background-position: 16px 13px;
                    width: 150px;
                    margin-left: 10px;
                    // height: 30px;
                    border-radius: 30px;
                    background-color: #ff0940;
                    box-shadow: 0px 0px 8px 0px rgba(255, 22, 73, 0.4);
                    padding-left: 33px;
                    font-weight: 400;
                    font-size: 24px;
                    color: #ffffff;
                    line-height: 24px;
                    text-align: left;
                    font-style: normal;
                    padding: 8px 8px 8px 40px;
                    box-sizing: border-box;
                }
            }
            .barrage-rect {
                padding: 48px 51px;
                position: relative;
                .barrage-content {
                    width: 597px;
                    height: 267px;
                    background: rgba(233, 36, 36, 0.05);
                    border-radius: 37px;
                    box-sizing: border-box;
                    padding: 24px;
                }
                .barrage-left-icon {
                    position: absolute;
                    left: 0;
                    top: 20px;
                    background-image: url(https://dante-img.oss-cn-hangzhou.aliyuncs.com/98819237197.png);
                    background-size: 50px 50px;
                    background-position: 20px 10px;
                    background-repeat: no-repeat;
                    width: 100px;
                    height: 100px;
                }
                .barrage-right-icon {
                    position: absolute;
                    right: 16px;
                    bottom: 30px;
                    background-image: url(https://dante-img.oss-cn-hangzhou.aliyuncs.com/98823976923.png);
                    background-size: 50px 50px;
                    background-position: 20px 10px;
                    background-repeat: no-repeat;
                    width: 100px;
                    height: 100px;
                }
            }
            .qrcode-rect {
                width: 367px;
                height: 367px;
                border-radius: 33px;
                border: 5px solid rgba(255, 9, 64, 0.12);
                margin: auto;
                padding: 24px;
                box-sizing: border-box;
            }
            .info-rect {
                padding: 10px 75px;
                text-align: center;
                .info-head {
                    display: flex;
                    flex-direction: row;
                    align-items: center;
                    justify-content: center;
                }
                .info-title {
                    font-weight: 500;
                    font-size: 36px;
                    color: #333333;
                    line-height: 32px;
                    text-align: left;
                    font-style: normal;
                    text-transform: uppercase;
                }
                .info-text1 {
                    font-weight: 400;
                    font-size: 24px;
                    color: rgba(153, 153, 153, 0.65);
                    margin-top: 14px;
                    line-height: 32px;
                    text-align: left;
                    font-style: normal;
                }
            }
        }
    }
}
