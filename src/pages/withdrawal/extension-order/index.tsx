import React, { useRef, useState } from "react";
import { View } from "@tarojs/components";
import {
    InfiniteScroll,
    InfiniteScrollProps,
    InfiniteScrollInstance,
    IPullToRefreshProps,
    PullToRefresh,
    CellGroup,
    Empty,
    Image,
} from "@antmjs/vantui";
import api from "@/utils/api";
import { useDidShow, useRouter } from "@tarojs/taro";
import "./index.less";
import { AmountView } from "@/components/amount-view";

export interface IExtensionOrderItem {
    accountStatementId: number;
    orderId: number;
    orderType: number;
    declareId: string;
    orderAt: string;
    orderAmount: number;
    commissionAmount: number;
    itemName: string;
    itemUrl: string;
    itemQuantity: number;
}

const ExtensionOrder: React.FC = () => {
    const [record, setRecord] = useState<IExtensionOrderItem[]>([]);
    const pageNo = useRef(1);
    const infiniteScrollInstance = useRef<InfiniteScrollInstance>();
    const { accountStatementId } = useRouter().params;
    useDidShow(() => {
        pageNo.current = 1;
        infiniteScrollInstance.current?.reset(true);
    });

    const onRefresh: IPullToRefreshProps["onRefresh"] = () => {
        return new Promise(resolve => {
            pageNo.current = 1;
            infiniteScrollInstance.current?.reset(true);
            resolve(undefined);
        });
    };
    const loadMore: InfiniteScrollProps["loadMore"] = () => {
        return new Promise(resolve => {
            getRecord()
                .then(res => {
                    let newData: any[];
                    if (pageNo.current === 1) {
                        newData = res.data;
                    } else {
                        newData = record.concat(res.data);
                    }
                    setRecord(newData);
                    pageNo.current++;
                    if (newData.length == 0) {
                        resolve("complete");
                    } else {
                        resolve(newData.length < res.total ? "loading" : "complete");
                    }
                })
                .catch(e => {
                    resolve("error");
                });
        });
    };

    const getRecord = () => {
        return api.getFindPromoteDetail({
            method: "POST",
            data: {
                accountStatementId: accountStatementId,
                pageSize: 10,
                currentPage: pageNo.current,
            },
        });
    };

    return (
        <PullToRefresh onRefresh={onRefresh}>
            <View className="extension-order-list">
                <CellGroup style={{ background: "transparent" }}>
                    {record?.map((item, index) => {
                        return (
                            <View className={"extension-order-list-item"} border={false} key={item.declareId}>
                                <View className="orderId">订单号：{item.declareId}</View>
                                <View className="orderId">下单时间：{item.orderAt}</View>
                                <View className="order-detail">
                                    <Image className="order-img" src={item.itemUrl} />
                                    <View className="order-name">{item.itemName}</View>
                                </View>
                                <View className="box-bottom">
                                    <View className="box">
                                        <View className="bottom-title">购买数量</View>
                                        <View className="bottom-num">{item.itemQuantity}</View>
                                    </View>
                                    <View className="box">
                                        <View className="bottom-title">记服务费额</View>
                                        <View className="bottom-num">
                                            <AmountView amount={item.orderAmount / 100} />
                                        </View>
                                    </View>
                                    <View className="box">
                                        <View className="bottom-title">预估服务费</View>
                                        <View className="bottom-num">
                                            <AmountView amount={item.commissionAmount / 100} />
                                        </View>
                                    </View>
                                </View>
                            </View>
                        );
                    })}
                </CellGroup>
                <InfiniteScroll
                    loadMore={loadMore}
                    ref={infiniteScrollInstance}
                    completeText={
                        <>
                            {record.length == 0 ? (
                                <Empty
                                    description="亲，暂时还没有记录哦！"
                                    image="https://dante-img.oss-cn-hangzhou.aliyuncs.com/73070296290.png"
                                />
                            ) : (
                                "没有更多了"
                            )}
                        </>
                    }
                />
            </View>
        </PullToRefresh>
    );
};
export default ExtensionOrder;
