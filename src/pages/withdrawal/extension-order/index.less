.extension-order-list {
    margin-top: 24px;

    &-item {
        background: #ffffff;
        border-radius: 16px;
        margin: 0 24px 20px;
        overflow: hidden;
        font-size: 24px;
        font-family: PingFangSC-Regular, PingFang SC;
        .orderId {
            font-size: 26px;
            font-weight: 400;
            line-height: 26px;
            margin-top: 20px;
            margin-left: 24px;
        }
        .orderId:nth-child(1) {
            margin-top: 32px;
        }
        .order-detail {
            margin: 32px 24px 0;
            display: flex;
            .order-img {
                width: 140px;
                height: 140px;
                display: inline-block;
            }
            .order-name {
                font-size: 26px;
                font-weight: 400;
                line-height: 38px;
                margin-left: 44px;
                max-width: 470px;
                max-height: 76px;
            }
        }
        .box-bottom {
            display: flex;
            margin: 27px 24px 0;
            text-align: center;
            overflow: hidden;
            .box {
                flex: 1;
                .bottom-title {
                    font-weight: 400;
                    color: #999999;
                    line-height: 24px;
                    margin-top: 24px;
                }
                .bottom-num {
                    margin-top: 16px;
                    margin-bottom: 32px;
                    font-size: 36px;
                    font-family: D-DIN-Bold, D-DIN;
                    font-weight: bold;
                    line-height: 36px;
                }
            }
        }
    }
}
