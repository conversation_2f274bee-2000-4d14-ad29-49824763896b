import React, { useRef, useState } from "react";
import { Text, View } from "@tarojs/components";
import {
    InfiniteScroll,
    InfiniteScrollProps,
    InfiniteScrollInstance,
    IPullToRefreshProps,
    PullToRefresh,
    CellGroup,
    Cell,
    Empty,
    Icon,
} from "@antmjs/vantui";
import api from "@/utils/api";
import { getStorage } from "@/utils/storage";
import { useDidShow } from "@tarojs/taro";
import "./index.less";
import identityStore from "@/store/identity-store";
import { DDYNavigateTo } from "@/utils/route";

export interface IAccountStatementItem {
    accountStatementId: number;
    accountStatementTitle: string;
    promoteAmount: number;
    rewardAmount: number;
    totalAmount: number;
}

const WithdrawalHistory: React.FC = () => {
    const [record, setRecord] = useState<IAccountStatementItem[]>([]);
    const pageNo = useRef(1);
    const infiniteScrollInstance = useRef<InfiniteScrollInstance>();
    useDidShow(() => {
        pageNo.current = 1;
        infiniteScrollInstance.current?.reset(true);
    });

    const onRefresh: IPullToRefreshProps["onRefresh"] = () => {
        return new Promise(resolve => {
            pageNo.current = 1;
            infiniteScrollInstance.current?.reset(true);
            resolve(undefined);
        });
    };
    const loadMore: InfiniteScrollProps["loadMore"] = () => {
        return new Promise(resolve => {
            getRecord(false)
                .then(res => {
                    let newData: any[];
                    if (pageNo.current === 1) {
                        newData = res.data;
                    } else {
                        newData = record.concat(res.data);
                    }
                    setRecord(newData);
                    pageNo.current++;
                    if (newData.length == 0) {
                        resolve("complete");
                    } else {
                        resolve(newData.length < res.total ? "loading" : "complete");
                    }
                })
                .catch(e => {
                    resolve("error");
                });
        });
    };

    const getRecord = isRefresh => {
        return api.getAccountStatement({
            method: "POST",
            data: {
                shopId: getStorage("storeId"),
                pageSize: 10,
                currentPage: pageNo.current,
            },
        });
    };

    return (
        <PullToRefresh onRefresh={onRefresh}>
            <View className="withdrawal-order-list">
                <CellGroup style={{ background: "transparent" }}>
                    {record?.map((item, index) => {
                        return (
                            <Cell
                                className={"withdrawal-order-list-item"}
                                border={false}
                                key={item.accountStatementId}
                                renderTitle={
                                    <View className={"flex_row"}>
                                        <View className="van-cell__title">{item.accountStatementTitle}</View>
                                        <View
                                            className={"van-cell__value"}
                                            onClick={() => {
                                                DDYNavigateTo({
                                                    url: `/pages/withdrawal/extension-order/index?accountStatementId=${item.accountStatementId}`,
                                                });
                                            }}
                                        >
                                            账单详情
                                            <Icon name={"arrow"} />
                                        </View>
                                    </View>
                                }
                                label={
                                    <View style={{ width: "100%" }}>
                                        <View className="sum-profit">总收益</View>
                                        <View className="sum-price">{item.totalAmount / 100}</View>
                                        <View className="box-bottom">
                                            <View className="extension">
                                                推广收益
                                                <Text className="price">{item.promoteAmount / 100}</Text>
                                            </View>
                                            {!identityStore.isGuider() && (
                                                <View className="extension">
                                                    奖励收益
                                                    <Text className="price">{item.promoteAmount / 100}</Text>
                                                </View>
                                            )}
                                        </View>
                                    </View>
                                }
                            ></Cell>
                        );
                    })}
                </CellGroup>
                <InfiniteScroll
                    loadMore={loadMore}
                    ref={infiniteScrollInstance}
                    completeText={
                        <>
                            {record.length == 0 ? (
                                <Empty
                                    description="亲，暂时还没有结算记录哦！"
                                    image="https://dante-img.oss-cn-hangzhou.aliyuncs.com/73070296290.png"
                                />
                            ) : (
                                "没有更多了"
                            )}
                        </>
                    }
                />
            </View>
        </PullToRefresh>
    );
};
export default WithdrawalHistory;
