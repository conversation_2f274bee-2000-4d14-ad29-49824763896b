import { Picker, Text, View } from "@tarojs/components";
import VirtualList from "@tarojs/components/virtual-list";
import { Empty, Icon, Image } from "@antmjs/vantui";
import { icon_calendar_line } from "@/images";
import React, { useMemo, useRef, useState } from "react";
import dayjs from "dayjs";
import "./index.less";
import { pxTransform, useDidShow, useLoad } from "@tarojs/taro";
import api from "@/utils/api";
import { getStorage } from "@/utils/storage";
import { DDYNavigateTo } from "@/utils/route";
import { AccountStatementListItem } from "./list-item";
import {
    AccountStatementBottomLayout,
    AccountStatementBottomLayoutForwardedRef,
} from "./account-statement-bottom-layout";
import { DDYObject } from "../../../type/common";
import DDYToast from "@/utils/toast";

interface RecordItem {
    date: string;
    fee: number;
    from: string;
    guider: string;
    id: number;
    profit: string;
    service: string;
    status: string;
    isChecked: boolean;
}

export default function () {
    const [record, setRecord] = useState<RecordItem[]>([]);
    const selectedRecord = useRef<RecordItem[]>([]);
    const pageNo = useRef(1);
    const checkArr = useRef<number[]>([]);
    let bottomLayoutRef = useRef<AccountStatementBottomLayoutForwardedRef>(null);
    useDidShow(() => {
        pageNo.current = 1;
        loadMore(null);
    });
    const [startDate, setStartDate] = useState("请选择");
    const [endDate, setEndDate] = useState("请选择");
    const [statusIndex, setStatusIndex] = useState(0);
    useLoad(() => {
        setStartDate(dayjs().startOf("month").format("YYYY-MM-DD"));
        setEndDate(dayjs().endOf("month").format("YYYY-MM-DD"));
    });
    const statusArray = [
        {
            id: 0,
            name: "待收益",
        },
        {
            id: 1,
            name: "待提现",
        },
        {
            id: 2,
            name: "已提现",
        },
        {
            id: 3,
            name: "审核中",
        },
    ];
    const bindStartDateChange = e => {
        let value = e.detail.value;
        setStartDate(value);
        loadMore({ from: value });
    };
    const bindEndDateChange = e => {
        let value = e.detail.value;
        setEndDate(value);
        loadMore({ to: value });
    };
    const bindStatusChange = e => {
        let index = Number(e.detail.value);
        setStatusIndex(index);
        loadMore({ status: statusArray[index].id });
    };
    const getIncomeDetail = (changeParams: DDYObject | null) => {
        console.log("statusIndex at getIncomeDetail ", statusIndex);
        const data = Object.assign(
            {
                status: statusArray[statusIndex].id,
                from: startDate,
                to: endDate,
                shopId: getStorage("storeId"),
            },
            changeParams,
        );
        return api.getQueryOrder({ data });
    };
    const loadMore = (changeParams: DDYObject | null) => {
        getIncomeDetail(changeParams).then(res => {
            let newData: any[] = [];
            if (pageNo.current === 1) {
                newData = res;
                selectedRecord.current = [];
                checkArr.current = [];
                bottomLayoutRef.current?.setTotalWithdrawalMoney("0.00");
                bottomLayoutRef.current?.setAllSelect(false);
            } else {
                newData = record.concat(res);
            }
            setRecord(newData);
        });
    };
    const selectAll = isChecked => {
        selectedRecord.current = [];
        if (isChecked) {
            selectedRecord.current = record;
        }
        setRecord([...record]); //强制更新
        caclAmount();
    };
    const selectOne = (selectItem: RecordItem, checked) => {
        if (checked) {
            selectedRecord.current.push(selectItem);
        } else {
            selectedRecord.current = selectedRecord.current.filter(item => selectItem.id !== item.id);
        }
        caclAmount();
    };
    const caclAmount = () => {
        checkArr.current = [];
        let totalMoney = 0;
        selectedRecord.current.map(item => {
            checkArr.current.push(item.id);
            totalMoney += Number(item.profit);
        });
        bottomLayoutRef.current?.setAllSelect(checkArr.current.length === record.length);
        bottomLayoutRef.current?.setTotalWithdrawalMoney(totalMoney.toFixed(2));
    };
    const toPayOrder = () => {
        if (checkArr.current.length === 0) {
            DDYToast.info("请勾选账单再提现！");
            return;
        }
        DDYNavigateTo({
            url:
                "/pages/withdrawal/index?formAccountStatement=true&checkArr=" +
                    JSON.stringify(checkArr.current) +
                    "&totalWithdrawalMoney=" +
                    bottomLayoutRef.current?.totalWithdrawalMoney ?? 0.0,
        });
    };

    const virtualListHeight = useMemo(() => {
        let height: string = "calc(100vh - 98px)";
        if (statusArray[statusIndex].id == 1) {
            //代提现
            height = "calc(100vh - 152px)";
        }
        return height;
    }, [statusIndex]);

    return (
        <View className={"account-statement"}>
            <View className="filter">
                <Picker mode="date" value={startDate} onChange={bindStartDateChange}>
                    <View className="date-content">
                        <Image src={icon_calendar_line} className={"data_icon"} />
                        <View className="picker">{startDate}</View>
                    </View>
                </Picker>
                <Picker mode={"date"} value={endDate} onChange={bindEndDateChange}>
                    <View className="date-content">
                        <Image src={icon_calendar_line} className={"data_icon"} />
                        <View className="picker">{endDate}</View>
                    </View>
                </Picker>
                <View className="filter-on"></View>
                <View className="refund_reason">
                    <Picker
                        mode="selector"
                        range={statusArray}
                        range-key="name"
                        value={statusIndex}
                        onChange={bindStatusChange}
                    >
                        <View className="list-msg">
                            <Text>{statusArray[statusIndex].name}</Text>
                            <Icon name={"arrow-down"} />
                        </View>
                    </Picker>
                </View>
            </View>
            <View className={"list"}>
                <View className="item head" style={{ height: "49px" }}>
                    <View style={{ width: pxTransform(230) }}>来源</View>
                    <View style={{ width: pxTransform(216) }}>商品含税总额</View>
                    <View style={{ flex: 1 }}>门店服务费</View>
                </View>
                {record.length == 0 ? (
                    <Empty
                        description="暂无数据！"
                        image="https://dante-img.oss-cn-hangzhou.aliyuncs.com/55439458101.png"
                    />
                ) : (
                    <VirtualList
                        width="100%"
                        height={virtualListHeight}
                        itemData={record}
                        itemCount={record.length}
                        placeholderCount={20}
                        itemSize={50}
                        enhanced
                        item={React.memo(({ id, index, data }) => {
                            let rowItem = data[index];
                            return (
                                <View id={id}>
                                    <AccountStatementListItem
                                        item={rowItem}
                                        initCheckedValue={checkArr.current.includes(rowItem.id)}
                                        showCheckbox={statusArray[statusIndex].name == "待提现"}
                                        onChange={(item, checked) => {
                                            selectOne(item, checked);
                                        }}
                                    />
                                </View>
                            );
                        })}
                    />
                )}
            </View>
            <AccountStatementBottomLayout
                ref={bottomLayoutRef}
                onSelectAll={selectAll}
                show={statusArray[statusIndex].name == "待提现"}
                toPayOrder={toPayOrder}
            />
        </View>
    );
}
