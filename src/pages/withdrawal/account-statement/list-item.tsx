import { View } from "@tarojs/components";
import { Checkbox } from "@antmjs/vantui";
import { pxTransform } from "@tarojs/taro";
import { useEffect, useState } from "react";

export function AccountStatementListItem({ showCheckbox, item, initCheckedValue, onChange }) {
    const [isChecked, setIsChecked] = useState(false);
    useEffect(() => {
        setIsChecked(initCheckedValue);
    }, [initCheckedValue]);

    return (
        <View
            style={{ height: "49px" }}
            className="item"
            key={item.id}
            onClick={() => {
                const checked = !isChecked;
                setIsChecked(checked);
                onChange(item, checked);
            }}
        >
            {showCheckbox && (
                <View className="select_bottom">
                    <Checkbox value={isChecked} />
                </View>
            )}
            <View style={{ width: pxTransform(180) }}>{item.from}</View>
            <View style={{ width: pxTransform(216) }}>{item.fee / 100}</View>
            <View style={{ flex: 1 }}>{item.profit}</View>
        </View>
    );
}
