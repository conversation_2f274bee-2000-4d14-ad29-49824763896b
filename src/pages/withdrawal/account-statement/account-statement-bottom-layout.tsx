import React, { forwardRef, useImperativeHandle, useState } from "react";
import { View } from "@tarojs/components";
import { Button, Checkbox } from "@antmjs/vantui";

export interface AccountStatementBottomLayoutForwardedRef {
    setAllSelect: React.Dispatch<React.SetStateAction<boolean>>;
    setTotalWithdrawalMoney: React.Dispatch<React.SetStateAction<String>>;
    totalWithdrawalMoney: string;
}
export type AccountStatementBottomLayoutRefProps = React.ForwardRefExoticComponent<
    React.RefAttributes<AccountStatementBottomLayoutForwardedRef> & {
        show: boolean;
        toPayOrder: () => void;
        onSelectAll: (isAllSelect) => void;
    }
>;
export const AccountStatementBottomLayout: AccountStatementBottomLayoutRefProps = forwardRef((props, ref) => {
    const [allSelect, setAllSelect] = useState(false);
    const [totalWithdrawalMoney, setTotalWithdrawalMoney] = useState("0.00");
    const { show, toPayOrder, onSelectAll } = props;
    const selectAll = () => {
        const isAllSelect = !allSelect;
        onSelectAll(isAllSelect);
        setAllSelect(isAllSelect);
    };
    useImperativeHandle(ref, () => {
        return {
            setAllSelect: setAllSelect,
            setTotalWithdrawalMoney: setTotalWithdrawalMoney,
            totalWithdrawalMoney: totalWithdrawalMoney,
        };
    });
    return (
        <>
            {show ? (
                <View className="bottom-box" style={{ height: "60px" }}>
                    <View className="left-price">
                        <View className="all_select_box" onTap={selectAll}>
                            <Checkbox value={allSelect} />
                            <View className="all_select">全选</View>
                        </View>
                    </View>
                    <View className={"total-amount"}>合计 : {totalWithdrawalMoney}元</View>
                    <Button className={"submit-order"} onClick={toPayOrder} round color={"#F50050"} size={"small"}>
                        去提现
                    </Button>
                </View>
            ) : null}
        </>
    );
});
