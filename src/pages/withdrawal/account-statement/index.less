.account-statement {
    .filter {
        padding: 0 24px;
        height: 88px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        background-color: #ffffff;
        font-size: 28px;
        font-family: PingFangSC-Regular, PingFang SC;
        color: #666666;
        line-height: 28px;

        .filter-on {
            width: 1px;
            height: 40px;
            margin-left: 60px;
            background: #cbcccd;
        }

        .date-content {
            display: flex;
            align-items: center;
        }

        .picker {
            margin-left: 14px;
            width: 180px;
            height: 28px;
            font-size: 28px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #666666;
            line-height: 28px;
        }
    }

    .data_icon {
        width: 30px;
        height: 30px;
    }

    .list {
        text-align: center;
    }

    .item {
        text-align: center;
        background: #fff;
        color: #666666;
        padding-left: 20px;
        padding-right: 20px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        flex: 1;
        font-size: 28px;
        border-bottom: 1px solid #cccccc;

        .see_all_order {
            display: flex;
            flex-direction: row;
            align-items: center;
            justify-content: flex-end;
        }

        view {
            text-align: center;
            // width: 136px;
        }
    }
    .head {
        border-bottom: none;
    }
    .bottom-box {
        display: flex;
        justify-content: space-between;
        width: 100%;
        position: fixed;
        bottom: 0;
        left: 0;
        border-bottom: 1px solid #eee;
        border-top: 1px solid #eee;
        background-color: #fff;
        z-index: 9999;
        align-items: center;
        .left-price {
            display: flex;
            justify-content: space-between;
            padding: 0 18px 0 26px;
            font-size: 28px;
            box-sizing: border-box;
            align-items: center;
        }
        .total-amount {
            flex: 1;
            text-align: right;
        }
        .total {
            color: #f51214;
            font-size: 30px;
        }
        .submit-order {
            margin: 0 24px;
        }
    }

    .all_select_box {
        width: 200px;
        height: 100%;
        display: flex;
        align-items: center;
    }

    .all_select {
        flex: 1;
        padding-left: 10px;
    }
}
