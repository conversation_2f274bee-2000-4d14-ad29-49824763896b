import React, { useRef, useState } from "react";
import { View } from "@tarojs/components";
import {
    InfiniteScroll,
    InfiniteScrollProps,
    InfiniteScrollInstance,
    IPullToRefreshProps,
    PullToRefresh,
    CellGroup,
    Cell,
    Empty,
    Image,
} from "@antmjs/vantui";
import api from "@/utils/api";
import { getStorage } from "@/utils/storage";
import { useDidShow } from "@tarojs/taro";
import classNames from "classnames";
import { AccountStatementWithdrawStatusEnum } from "../types";
import "./index.less";

export interface IWithdrawalItem {
    id: number;
    applyAt: string;
    withDrawAmount: number;
    status: number;
    statusString: string;
    statusStr: string;
    description: string;
}

const WithdrawalHistory: React.FC = () => {
    const [record, setRecord] = useState<IWithdrawalItem[]>([]);
    const pageNo = useRef(1);
    const infiniteScrollInstance = useRef<InfiniteScrollInstance>();

    useDidShow(() => {
        pageNo.current = 1;
        infiniteScrollInstance.current?.reset(true);
    });

    const onRefresh: IPullToRefreshProps["onRefresh"] = () => {
        return new Promise(resolve => {
            pageNo.current = 1;
            infiniteScrollInstance.current?.reset(true);
            resolve(undefined);
        });
    };
    const loadMore: InfiniteScrollProps["loadMore"] = () => {
        return new Promise(resolve => {
            getRecord()
                .then(res => {
                    let newData: any[];
                    if (pageNo.current === 1) {
                        newData = res.data;
                    } else {
                        newData = record.concat(res.data);
                    }
                    setRecord(newData);
                    pageNo.current++;
                    if (newData.length == 0) {
                        resolve("complete");
                    } else {
                        resolve(newData.length < res.total ? "loading" : "complete");
                    }
                })
                .catch(e => {
                    resolve("error");
                });
        });
    };

    const getRecord = () => {
        return api.getWithdrawProfitApply({
            method: "POST",
            data: {
                shopId: getStorage("storeId"),
                pageSize: 10,
                currentPage: pageNo.current,
            },
        });
    };

    return (
        <PullToRefresh onRefresh={onRefresh}>
            <View className="withdrawal-history-list">
                <CellGroup>
                    {record?.map((item, index) => {
                        return (
                            <View
                                className={"withdrawal-history-list-item"}
                                border={index !== record.length - 1}
                                key={item.id}
                            >
                                <View>
                                    <Image
                                        className={"left-icon"}
                                        src={"https://dante-img.oss-cn-hangzhou.aliyuncs.com/73477213940.png"}
                                    />
                                </View>
                                <View className={"right-box"}>
                                    <View className="flex_row description">
                                        <View className={"title"}>提现金额</View>
                                        <View className={"amount"}>{item.withDrawAmount / 100 || item.fee / 100}</View>
                                    </View>
                                    <View>{item.description}</View>
                                    <View className="flex_row description">
                                        <View className={"label"}>提交时间:</View>{" "}
                                        <View className={"value"}>{item.applyAt || item.createdAt}</View>
                                    </View>
                                    <View className="flex_row description">
                                        <View className={"label"}>提现状态:</View>
                                        <View
                                            className={classNames("value", {
                                                warn: [
                                                    AccountStatementWithdrawStatusEnum.WAITING_FOR_AUTH,
                                                    AccountStatementWithdrawStatusEnum.WAITING_FOR_PAY,
                                                    AccountStatementWithdrawStatusEnum.PAY_IN_PROGRESS,
                                                ].some(it => it === item.status),
                                                error: [
                                                    AccountStatementWithdrawStatusEnum.REJECTED,
                                                    AccountStatementWithdrawStatusEnum.FAILED_TO_PAY,
                                                ].some(it => it === item.status),
                                            })}
                                        >
                                            {item.statusString || item.statusStr}
                                        </View>
                                    </View>
                                    <View className="flex_row description">
                                        <View className={"label"}>预估转账时间:</View>{" "}
                                        <View className={"value"}>{item.foreseePaidAt}</View>
                                    </View>
                                    <View className="flex_row description">
                                        <View className={"label"}>实际转账时间:</View>{" "}
                                        <View className={"value"}>{item.paidAt}</View>
                                    </View>
                                </View>
                            </View>
                        );
                    })}
                </CellGroup>
                <InfiniteScroll
                    loadMore={loadMore}
                    ref={infiniteScrollInstance}
                    completeText={
                        <>
                            {record.length == 0 ? (
                                <Empty
                                    description="亲，暂时还没有提现记录哦！"
                                    image="https://dante-img.oss-cn-hangzhou.aliyuncs.com/73070296290.png"
                                />
                            ) : (
                                "没有更多了"
                            )}
                        </>
                    }
                />
            </View>
        </PullToRefresh>
    );
};
export default WithdrawalHistory;
