import { Input, Text, View } from "@tarojs/components";
import { FormItem, Icon, Image, Form, Button, Popup, Result } from "@antmjs/vantui";
import "./index.less";
import { pxTransform, useLoad, useRouter } from "@tarojs/taro";
import api from "@/utils/api";
import { getStorage, setStorage } from "@/utils/storage";
import { IDENTITY, STORE_ID } from "@/utils/constant";
import { useState } from "react";
import { IBankCard } from "./types";
import { DDYBack, DDYNavigateTo } from "@/utils/route";
import AgreeModal from "@/components/agree-modal";
import React from "react";
import { saveSignback } from "@/utils/common";

export default function () {
    const [bankCard, setBankCard] = useState<IBankCard>();
    const [showSuccess, setShowSuccess] = useState<boolean>(false);
    const { formAccountStatement, totalWithdrawalMoney, checkArr } = useRouter().params;
    const [defaultAccount, setDefaultAccount] = useState();
    const agreeModalRef = React.useRef();
    useLoad(() => {
        existCashPasswordFn();
        api.getBankCard({
            data: {
                shopId: getStorage(STORE_ID),
            },
        }).then((res: IBankCard) => {
            if (res.bankCardNo) {
                res.bankCardNoStr = res.bankCardNo.substring(res.bankCardNo.length - 4, res.bankCardNo.length);
            }
            setBankCard(res);
        });
    });
    const form = Form.useForm();
    const onFinish = (values: any) => {
        if (formAccountStatement) {
            subStoreProfitApply(values);
        } else {
            withdrawProfitApply();
        }
    };
    const existCashPasswordFn = () => {
        api.existCashPassword({
            data: { sourceId: getStorage("storeId") },
            filterCheck: true,
        }).then(res => {
            setDefaultAccount(res.defaultAccount);
        });
    };
    const subStoreProfitApply = values => {
        api.withdrawal({
            method: "POST",
            data: {
                fee: values.fee * 100, //提现金额
                sourceId: getStorage("storeId"), // 皇家sourceId都传，坦图sourceId传shopId
                //type: 1,      //提现方式：1.提现至微信零钱，2.提现至银行卡（暂时只支持到零钱）
                //applyRemark: "申请备注",
                list: checkArr ? JSON.parse(checkArr) : [],
                pw: "", //提现密码重构之前已经不需要了
                // @ts-ignore
                account: defaultAccount.id,
            },
            showLoad: true,
        }).then(() => {
            setShowSuccess(true);
        });
    };
    const withdrawProfitApply = () => {
        api.getWithdraw({
            method: "POST",
            data: {
                shopId: getStorage(STORE_ID),
                ...bankCard,
            },
            showLoad: true,
        }).then(() => {
            setShowSuccess(true);
        });
    };
    const withSuccess = () => {
        setShowSuccess(false);
        if (formAccountStatement) {
            toRecord();
        } else {
            DDYBack({
                delta: 2,
            });
        }
    };
    /**
     * 门店提现成功去提现记录
     */
    const toRecord = () => {
        DDYNavigateTo({ url: "/pages/withdrawal/history/index" });
    };

    return (
        <View className={"withdrawal"}>
            <View className={"withdrawal-top"}>
                <View
                    className={"withdrawal-account"}
                    onClick={() => {
                        if (getStorage(IDENTITY) === "subStore") {
                            DDYNavigateTo({
                                url: "/pages/my/bank-list/index",
                            });
                            // saveSignback()
                        }
                    }}
                >
                    <View className={"withdrawal-account-label"}>到账账户</View>
                    <Image
                        className="withdrawal-account-icon"
                        src="https://dante-img.oss-cn-hangzhou.aliyuncs.com/***********.png"
                    />
                    {bankCard && bankCard?.bankName ? (
                        <View className={"withdrawal-account-name"}>
                            {bankCard?.bankName}（{bankCard?.bankCardNoStr}）
                        </View>
                    ) : (
                        <View className={"withdrawal-account-name"}>暂无银行卡</View>
                    )}

                    <Icon name={"arrow"} color={"#CBCCCD"} />
                </View>
            </View>
            <View className={"withdrawal-submit-o"}>
                <View>
                    <Form
                        form={form}
                        initialValues={{ fee: totalWithdrawalMoney }}
                        onFinish={(error, values) => onFinish(values)}
                    >
                        <FormItem
                            name={"fee"}
                            label={"提现金额(元)"}
                            required
                            borderBottom
                            layout={"vertical"}
                            trigger="onInput"
                            valueFormat={e => e.detail.value}
                        >
                            <Input className={"withdrawal-amount"} type={"digit"} disabled maxlength={8} />
                        </FormItem>
                    </Form>
                </View>
                <View className={"withdrawal-submit-o-tip"}>
                    <Text className={"tip-text"}>仅支持全额提现</Text>
                </View>
                <Button
                    className={"submit-btn"}
                    onTap={() => {
                        //@ts-ignore
                        agreeModalRef.current.verify(() => {
                            form.submit();
                        });
                    }}
                >
                    申请提现
                </Button>
                <View className={"history-btn"} onTap={() => toRecord()}>
                    提现记录 &gt;
                </View>
            </View>
            <Popup closeOnClickOverlay={false} show={showSuccess} position={"bottom"} round>
                <View className={"white_success"}>
                    <Result type={"success"} title={"提现申请成功"} />
                    <Button className={"submit-btn"} onClick={() => withSuccess()}>
                        完成
                    </Button>
                </View>
            </Popup>
            <AgreeModal ref={agreeModalRef} />
        </View>
    );
}
