import { But<PERSON>, <PERSON>, Checkbox, Stepper, SwipeCell } from "@antmjs/vantui";
import { View, Image, Text } from "@tarojs/components";
import classNames from "classnames";

export default ({
    items,
    changeSelect,
    index,
    // getCartList,
    changeNumber,
    deleteGoods,
}) => {
    return (
        <View className="show-item">
            {items?.cartItems?.map((goodItem, goodIndex) => {
                const goodsDisabled = goodItem.itemStatus !== 1;
                return (
                    <SwipeCell
                        rightWidth={75}
                        leftWidth={0}
                        key={goodIndex}
                        renderRight={
                            <View
                                style={{
                                    width: "100%",
                                    backgroundColor: "#f50e0c",
                                    display: "flex",
                                    alignItems: "center",
                                    justifyContent: "center",
                                    height: "100%",
                                }}
                            >
                                <Button
                                    color="#f50e0c"
                                    onClick={() => {
                                        deleteGoods(index, goodIndex);
                                    }}
                                >
                                    删除
                                </Button>
                            </View>
                        }
                    >
                        <View style={{ display: "flex", alignItems: "center", flexDirection: "row", width: "100%" }}>
                            <Checkbox
                                disabled={goodsDisabled}
                                value={goodItem.checked}
                                onChange={e => {
                                    console.log(e);
                                    console.log("选中");
                                    //@ts-ignore
                                    changeSelect(index, goodIndex, e.detail);
                                }}
                            />

                            <Card
                                renderNum={
                                    goodsDisabled ? (
                                        <View className={"cart-disabled-tip"}>宝贝已失效,请重新选择</View>
                                    ) : (
                                        <View
                                            style={{
                                                width: "100%",
                                                display: "flex",
                                                flexDirection: "row",
                                                alignItems: "center",
                                                justifyContent: "space-between",
                                            }}
                                        >
                                            <View
                                                style={{ display: "flex", flexDirection: "row", alignItems: "center" }}
                                            >
                                                <Text
                                                    style={{
                                                        fontSize: "26rpx",
                                                        color: "#F51214",
                                                        lineHeight: "42rpx",
                                                    }}
                                                >
                                                    ¥{goodItem.sku.price / 100}
                                                </Text>
                                            </View>

                                            <Stepper
                                                value={goodItem?.cartItem?.quantity || 0}
                                                min={1}
                                                onChange={e => {
                                                    changeNumber(index, goodIndex, e.detail);
                                                }}
                                            />
                                        </View>
                                    )
                                }
                                price=""
                                desc=""
                                style={{ flex: 1, paddingLeft: 0, backgroundColor: "#fff" }}
                                // title={goodItem.itemName}
                                renderTitle={
                                    <View className={classNames("good-name", { disabled: goodsDisabled })}>
                                        <Text className="label-box">
                                            {goodItem?.flagList?.map(flagItem => {
                                                if (flagItem == "promotionActivityItem") {
                                                    return <Text className="label-item">活动</Text>;
                                                }
                                                if (flagItem == "promotionLimitedItem") {
                                                    return <Text className="label-item-limit">限定</Text>;
                                                }
                                            })}
                                        </Text>
                                        {goodItem.itemName}
                                    </View>
                                }
                                thumb={goodItem.itemImage}
                                thumbMode="scaleToFill"
                            />
                        </View>
                    </SwipeCell>
                );
            })}
        </View>
    );
};
