import { But<PERSON>, <PERSON>, Checkbox, Icon, <PERSON>per, SwipeCell } from "@antmjs/vantui";
import { View, Image, Text } from "@tarojs/components";
import classNames from "classnames";

export default ({
    items,
    changeSelect,
    index,
    // getCartList,
    changeNumber,
    deleteGoods,
    cartMode,
    showSkuModal,
    // changeSelectMethod,
}) => {
    console.log("items?.cartItems:", items?.cartItems);
    const isHide = cartMode === "delete";
    return (
        <View className="show-item">
            {items?.cartItems?.map((goodItem, goodIndex) => {
                const goodsDisabled = goodItem.itemStatus !== 1;
                return (
                    <SwipeCell
                        rightWidth={75}
                        leftWidth={0}
                        key={goodIndex}
                        renderRight={
                            <View
                                style={{
                                    width: "100%",
                                    backgroundColor: "#f50e0c",
                                    display: "flex",
                                    alignItems: "center",
                                    justifyContent: "center",
                                    height: "100%",
                                }}
                            >
                                <Button
                                    color="#f50e0c"
                                    onClick={() => {
                                        deleteGoods(index, goodIndex);
                                    }}
                                >
                                    删除
                                </Button>
                            </View>
                        }
                    >
                        <View style={{ display: "flex", alignItems: "center", flexDirection: "row", width: "100%" }}>
                            <Checkbox
                                disabled={cartMode === "cart" ? goodsDisabled : false}
                                value={goodItem.checked}
                                onChange={e => {
                                    //@ts-ignore
                                    changeSelect(goodItem.cartItemId, e.detail, goodIndex);
                                }}
                            />

                            <Card
                                renderNum={
                                    goodsDisabled ? (
                                        <View className={"cart-disabled-tip"}>宝贝已失效,请重新选择</View>
                                    ) : (
                                        <View
                                            style={{
                                                width: "100%",
                                                display: "flex",
                                                flexDirection: "row",
                                                alignItems: "center",
                                                justifyContent: "space-between",
                                            }}
                                        >
                                            <View
                                                style={{ display: "flex", flexDirection: "row", alignItems: "center" }}
                                            >
                                                {goodItem.discountPrice !== goodItem.sku.skuPrice ? (
                                                    <View>
                                                        <View
                                                            style={{
                                                                color: "#f50e0c",
                                                                fontSize: "14px",
                                                                lineHeight: "20px",
                                                            }}
                                                        >
                                                            ¥{goodItem.discountPrice}
                                                            <Text
                                                                style={{
                                                                    color: "#f50e0c",
                                                                    fontSize: "10px",
                                                                    marginLeft: "8px",
                                                                }}
                                                            >
                                                                折后价
                                                            </Text>
                                                        </View>
                                                        <Text style={{ fontSize: "10px", color: "#ccc" }}>
                                                            ¥{goodItem.sku.skuPrice}
                                                        </Text>
                                                    </View>
                                                ) : (
                                                    <Text
                                                        style={{
                                                            fontSize: "26rpx",
                                                            color: "#F51214",
                                                            lineHeight: "42rpx",
                                                        }}
                                                    >
                                                        ¥{goodItem.sku.skuPrice}
                                                    </Text>
                                                )}
                                            </View>
                                            {!isHide ? (
                                                <Stepper
                                                    value={goodItem?.cartItem?.quantity || 0}
                                                    min={1}
                                                    max={
                                                        goodItem?.orderQuantityLimit
                                                            ? Math.min(
                                                                  goodItem?.orderQuantityLimit,
                                                                  goodItem?.sku.stockQuantity,
                                                              )
                                                            : goodItem?.sku.stockQuantity
                                                    }
                                                    onChange={e => {
                                                        changeNumber(goodItem?.sku.id, e.detail);
                                                    }}
                                                />
                                            ) : null}
                                        </View>
                                    )
                                }
                                price=""
                                desc=""
                                style={{ flex: 1, paddingLeft: 0, backgroundColor: "#fff" }}
                                // title={goodItem.itemName}
                                renderTitle={
                                    <View className={classNames("good-name", { disabled: goodsDisabled })}>
                                        <View className="good-name-main">
                                            {goodItem?.flagList && (
                                                <Text className="label-box">
                                                    {goodItem?.flagList?.map(flagItem => {
                                                        if (flagItem == "promotionActivityItem") {
                                                            return <Text className="label-item">活动</Text>;
                                                        }
                                                        if (flagItem == "promotionLimitedItem") {
                                                            return <Text className="label-item-limit">限定</Text>;
                                                        }
                                                    })}
                                                </Text>
                                            )}
                                            {goodItem.itemName}
                                        </View>
                                        {goodItem.sku.type === 0 ? (
                                            <Image
                                                mode="aspectFit"
                                                style={{ width: "36.5px", height: "13pt" }}
                                                src="https://dante-img.oss-cn-hangzhou.aliyuncs.com/19198843262.png"
                                            />
                                        ) : null}
                                        {[1, 2].includes(goodItem.sku.type) ? (
                                            <Image
                                                mode="aspectFit"
                                                style={{ width: "36.5px", height: "13px" }}
                                                src="https://dante-img.oss-cn-hangzhou.aliyuncs.com/19194973267.png"
                                            />
                                        ) : null}
                                        {goodItem.sku.specification ? (
                                            <View
                                                onClick={() => {
                                                    // console.log("sku")
                                                    if (goodsDisabled) {
                                                        return;
                                                    }
                                                    // 点击管理后，不允许显示规格弹框
                                                    if (isHide) return;
                                                    showSkuModal(
                                                        goodItem.sku.itemId,
                                                        goodItem.sku.id,
                                                        goodItem?.cartItem?.quantity,
                                                        goodItem?.buyerNote,
                                                        goodItem?.cartItem?.id,
                                                    );
                                                }}
                                                className="sku-specs"
                                            >
                                                {goodItem.sku.specification}
                                                <Icon name="arrow-down" size="12px" className="sku-specs-icon" />
                                            </View>
                                        ) : null}
                                    </View>
                                }
                                // thumb={goodItem.sku.image || goodItem.itemImage}
                                renderThumb={
                                    <Image
                                        src={goodItem.sku.image || goodItem.itemImage}
                                        mode="scaleToFill"
                                        style={{ width: "80px", height: "80px", borderRadius: "8px" }}
                                        onClick={() => {
                                            if (goodsDisabled) {
                                                return;
                                            }
                                            changeSelect(goodItem.cartItemId, !goodItem.checked, goodIndex);
                                        }}
                                    />
                                }
                                thumbMode="scaleToFill"
                            />
                        </View>
                    </SwipeCell>
                );
            })}
        </View>
    );
};
