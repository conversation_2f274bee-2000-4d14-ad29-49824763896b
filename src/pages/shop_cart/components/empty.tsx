import { DDYSwitchTab } from "@/utils/route";
import { View, Image } from "@tarojs/components";

export default () => {
    // if()
    return (
        // {{list.length==0 && showEmpty&&(identity=='buyer'||identity=='sellerAudit'||identity=='guider')}}
        <View className="empty_cart">
            <Image
                src="https://dante-img.oss-cn-hangzhou.aliyuncs.com/53083388900.png"
                mode="widthFix"
                className="empty_cart_img"
            ></Image>
            <View className="empty_cart_text">您的购物车还是空的哦！</View>
            <View className="shop_btn">
                <View className="block btn_group">
                    <View
                        className="btn get_store"
                        onClick={() => {
                            DDYSwitchTab({ url: "/pages/index/index" });
                        }}
                    >
                        去逛逛
                    </View>
                </View>
            </View>
        </View>
    );
};
