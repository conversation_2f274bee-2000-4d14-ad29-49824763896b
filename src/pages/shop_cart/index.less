.shop-item {
    margin: 20rpx 24rpx;
    border-radius: 8rpx;
    background-color: #fff;
    padding: 0 24rpx;
    .shop_info {
        padding: 30rpx 0 14rpx 0;
        display: flex;
        flex-direction: row;
        align-items: center;
        .shop_img {
            width: 32rpx;
            height: 32rpx;
        }
        .shop_name {
            font-size: 32rpx;
            font-weight: 500;
            color: #333333;
            line-height: 32rpx;
            margin-left: 24rpx;
        }
    }
    .show-item {
    }
}

.empty_cart {
    margin-top: 338rpx;
    .shop_btn {
        display: flex;
        width: 100%;
    }
    .block {
        margin: 0 auto;
        text-align: center;
        display: flex;
        align-items: center;
    }
    .btn_group {
        // display: flex;
        // align-items: center;
        padding-top: 64rpx;
        .btn {
            font-size: 30rpx;
            line-height: 88rpx;
            text-align: center;
            margin: 0 auto;
            width: 300rpx;
            height: 88rpx;
            -moz-border-radius: 10rpx;
            /* Firefox */
            -webkit-border-radius: 10rpx;
            /* <PERSON>fari 和 Chrome */
            border-radius: 10rpx;
            /* Opera 10.5+, 以及使用了IE-CSS3的IE浏览器 */
        }
        .get_store {
            border: 1px solid #f51214;
            color: #fff;
            font-size: 32rpx;
            font-weight: 400;
            border-radius: 50rpx;
            // border-radius: 44rpx  100rpx  100rpx  44rpx !important;
            background: linear-gradient(#f50050, #f5001d);
            // margin-left: 1rpx;
        }
    }
}

.empty_cart_img {
    width: 320rpx;
    display: block;
    margin: 0 auto;
}

.empty_cart_text {
    font-size: 28rpx;
    color: #999999;
    font-weight: 400;
    margin-top: 32rpx;
    text-align: center;
}
.good-name {
    color: #333;
    font-size: 26px !important;
    font-weight: 400;
    overflow: hidden;
    text-overflow: ellipsis;
    width: 100%;
    line-height: 32rpx;
    display: -webkit-box;
    -webkit-line-clamp: 2; /*要显示的行数*/
    -webkit-box-orient: vertical;
    &.disabled {
        color: #999;
    }
}
.cart-disabled-tip {
    color: #999;
    font-size: 22px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    line-height: 22px;
}
.label-box {
    margin-right: 10rpx;
    .label-item {
        border: 2rpx solid #f50e0c;
        border-radius: 6rpx;
        padding: 2rpx 10rpx;
        // height: 36rpx;
        font-size: 24rpx;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #de1600;
        display: inline-block;
    }
    .label-item-limit {
        border: 2rpx solid #f50e0c;
        background: #de1600;
        color: #fff;
        padding: 2rpx 10rpx;
        border-radius: 6rpx;
        // height: 36rpx;
        font-size: 24rpx;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        display: inline-block;
    }
}
.right-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-direction: row;
    flex: 1;
    margin-right: 20rpx;
    .right-left {
        display: flex;
        align-items: center;
        // justify-content: space-between;
        flex-direction: row;
    }
    .total {
        // padding-left: 80rpx;
        .total-num {
            text-align: center;
            color: #4a4a4a;
            font-size: 24rpx;
            .total-num-red {
                color: #f51214;
                font-size: 30rpx;
            }
        }
        .total-note {
            text-align: center;
            color: #a0a0a0;
            font-size: 22rpx;
        }
    }
}
.shop_cart .van-submit-bar__button {
    margin-right: 0;
}
