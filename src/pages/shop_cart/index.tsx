import { View, Image, Text } from "@tarojs/components";
import Empty from "./components/empty";
import "./index.less";
import { useState } from "react";
import CartValid from "./components/cart-valid";
import api from "@/utils/api";
import { getStorage } from "@/utils/storage";
import { STORE_ID } from "@/utils/constant";
import Taro, { useDidShow } from "@tarojs/taro";
import { Checkbox, SubmitBar } from "@antmjs/vantui";
import { DDYNavigateTo } from "@/utils/route";
import DDYToast from "@/utils/toast";
import identityStore from "@/store/identity-store";
import { mall_event, mall_event_key, reportEvent } from "@/utils/track-report";

const Index = () => {
    const [allChecked, setAllChecked] = useState(false);
    const [totalCount, setTotalCount] = useState(0);
    const [list, setList] = useState<any[]>([]);
    const [num, setNum] = useState(0);
    const identity = identityStore.identity;
    // 更改店铺的选择
    const selectShopAll = (index: number) => {
        const bol = !list[index].allChecked;
        list[index].allChecked = bol;
        list[index].cartItems.map(item => {
            if (item.itemStatus === 1) {
                item.checked = bol;
            }
        });
        setList([...list]);
        statisticsStatus([...list]);
    };

    // 更改单个商品的选择
    const changeSelectMethod = (shopIndex, index, checked) => {
        list[shopIndex].cartItems[index].checked = checked;
        list[shopIndex].cartItems = [...list[shopIndex].cartItems];
        const result = list[shopIndex].cartItems.filter(item => item.checked);
        if (result.length === list[shopIndex].cartItems.length) {
            list[shopIndex].allChecked = true;
        }
        const result1 = list[shopIndex].cartItems.filter(item => !item.checked);
        if (result1.length === list[shopIndex].cartItems.length) {
            list[shopIndex].allChecked = false;
        }
        setList([...list]);
        statisticsStatus([...list]);
    };

    // 店铺选择或者商品勾选后，计算全选状态和合计金额
    const statisticsStatus = (arr: any) => {
        let count = 0;
        let goodsNum = 0;
        arr.map(shopItem => {
            shopItem.cartItems.map(goodItem => {
                if (goodItem.checked) {
                    console.log(goodItem);
                    count += goodItem.sku.price * goodItem.cartItem.quantity;
                    goodsNum += goodItem.cartItem.quantity;
                }
            });
        });
        const result = arr.filter(shopItem => {
            return shopItem.allChecked;
        });
        if (result.length === arr.length) {
            setAllChecked(true);
        } else {
            setAllChecked(false);
        }
        setTotalCount(count);
        setNum(goodsNum);
        if (arr.length === 0) {
            setAllChecked(false);
            setTotalCount(0);
        }
    };

    // 删除商品
    const deleteGoods = (shopIndex: number, index: number) => {
        console.log("删除：", index);
        const delId = list[shopIndex].cartItems[index].sku.id;
        // 删除单一项
        list[shopIndex].cartItems.splice(index, 1);
        // 如果该店铺下没有商品了，就删除选择店铺的所有数据
        if (list[shopIndex].cartItems.length == 0) {
            list.splice(shopIndex, 1);
        }
        setList([...list]);
        statisticsStatus([...list]);
        apiDeleteGoods([delId]);
    };

    // 批量删除商品
    const apiDeleteGoods = (ids: number[], reload?: boolean) => {
        if (ids.length == 0) {
            DDYToast.info("请选择删除的商品");
            return;
        }
        api.cartDel({
            method: "POST",
            contentType: "application/x-www-form-urlencoded",
            data: {
                skuIds: ids,
            },
            filterCheck: true,
        })
            .then(res => {
                if (res) {
                    DDYToast.info("删除成功");
                    if (reload) {
                        getCartList();
                        getTabCartNum();
                        setNum(0);
                        setTotalCount(0);
                        setAllChecked(false);
                    }
                } else {
                    DDYToast.info(res.errorMsg || "未知异常");
                }
            })
            .catch();
    };

    // 商品数量修改
    const changeNumber = (shopIndex: number, index: number, val: number) => {
        const old = list[shopIndex].cartItems[index].cartItem.quantity;
        const id = list[shopIndex].cartItems[index].sku.id;
        list[shopIndex].cartItems[index].cartItem.quantity = val;
        list[shopIndex].cartItems = [...list[shopIndex].cartItems];
        changeCartNumber(id, val - old);
        setList([...list]);
        statisticsStatus([...list]);
    };

    // 购物车数量变化
    const changeCartNumber = (id, count) => {
        api.cartUpdateNum({
            method: "POST",
            contentType: "application/x-www-form-urlencoded",
            data: {
                skuId: id,
                quantity: count,
            },
            // method:
            filterCheck: true,
            showError: false,
            showLoad: false,
        }).then(res => {
            console.log("res:", res, count);
            getTabCartNum();
        });
    };

    // 获取加购数据
    const getCartList = () => {
        api.cartList({
            data: {
                shopId: getStorage(STORE_ID),
            },
            showError: false,
            filterCheck: true,
        })
            .then(res => {
                for (let i = 0; i < res.length; i++) {
                    res[i].allChecked = false;
                    for (let j = 0; j < res[i].cartItems.length; j++) {
                        res[i].cartItems[j].checked = false;
                        res[i].cartItems[j].style = 0;
                    }
                }
                setList(Array.isArray(res) ? res : []);
            })
            .catch(() => {});
    };

    // 获取购物车数量
    const getTabCartNum = () => {
        api.getCartNumber({
            data: { shopId: getStorage(STORE_ID) },
            showError: false,
            filterCheck: true,
        }).then(res => {
            let cart = res + "";
            // 店主端或导购端没有购物车，去掉数量
            if (res > 0) {
                Taro.setTabBarBadge({ index: 2, text: cart });
            } else {
                Taro.removeTabBarBadge({ index: 2 });
            }
        });
    };

    // 全选设置
    const setAllCheckedChange = (checked: boolean) => {
        list.map(item => {
            item.allChecked = checked;
            item.cartItems?.map(goodsItem => {
                if (goodsItem.itemStatus === 1) {
                    goodsItem.checked = checked;
                }
            });
        });
        setList([...list]);
        setAllChecked(checked);
        statisticsStatus([...list]);
    };

    const batchDelete = () => {
        const ids = [];
        list.map(item => {
            item.cartItems?.map(goodsItem => {
                // goodsItem.checked = checked;
                if (goodsItem.checked) {
                    //@ts-ignore
                    ids.push(goodsItem.sku.id);
                }
            });
        });
        apiDeleteGoods(ids, true);
    };

    const toPayOrder = () => {
        let orderGoodsList = [];
        let payGoodsList = [];
        let orderGoods = {};
        let payGoods = {};
        for (let i = 0; i < list.length; i++) {
            for (let j = 0; j < list[i].cartItems.length; j++) {
                let cartItem = list[i].cartItems[j];
                if (cartItem.checked) {
                    orderGoods = {
                        skuId: cartItem.sku.id,
                        quantity: cartItem.cartItem.quantity,
                        sourceType: cartItem.sourceType,
                    };
                    payGoods = {
                        skuId: cartItem.sku.id,
                        quantity: cartItem.cartItem.quantity,
                        promotionId: null,
                    };

                    if (cartItem.cartItem.quantity > cartItem.sku.stockQuantity) {
                        DDYToast.info(cartItem.itemName + "购买数量不能超过商品库存！");
                        return;
                    }
                    if (
                        cartItem.sku.extra &&
                        cartItem.sku.extra.orderQuantityLimit &&
                        cartItem.cartItem.quantity > cartItem.sku.extra.orderQuantityLimit
                    ) {
                        DDYToast.info(cartItem.itemName + "购买数量不能超过商品限购数量！");
                        return;
                    }
                    //@ts-ignore
                    orderGoodsList.push(orderGoods);
                    //@ts-ignore
                    payGoodsList.push(payGoods);
                }
            }
        }
        if (orderGoodsList.length === 0) {
            DDYToast.info("请选择商品");
            return;
        }
        let map = new Map();
        map.set(mall_event_key.MALL_KEY_GOODS_ID, JSON.stringify(orderGoodsList));
        map.set(mall_event_key.MALL_KEY_GOODS_NUM, JSON.stringify(payGoodsList));
        // map.set(mall_event_key.MALL_KEY_GOOD_SPEC_ID, specId)
        reportEvent(mall_event.MALL_EVENT_CART_SETTLEMENT, map);
        api.preCheck({
            data: {
                data: JSON.stringify(orderGoodsList),
            },
            // filterCheck: true,
            // method: 'POST',
        }).then(res => {
            DDYNavigateTo({
                url:
                    "/pages/order/comfire-order/index?orderGoodsList=" +
                    JSON.stringify(orderGoodsList) +
                    "&payGoodsList=" +
                    JSON.stringify(payGoodsList) +
                    "&from=shop_cart",
                events: {
                    update: () => {
                        console.log(":update");
                        getCartList();
                        getTabCartNum();
                    },
                },
            });
        });
    };

    // useEffect(() => {
    //     getCartList();
    //     getTabCartNum();
    // }, []);

    useDidShow(() => {
        getCartList();
        getTabCartNum();
        setAllChecked(false);
        setNum(0);
        setTotalCount(0);
    });

    return (
        <View className="shop_cart">
            {identity != "buyer" && identity != "sellerAudit" && identity != "guider" && identity != "subStore" ? (
                <View className="empty_cart">
                    <Image
                        src="https://dante-img.oss-cn-hangzhou.aliyuncs.com/53083388900.png"
                        mode="widthFix"
                        className="empty_cart_img"
                    ></Image>
                    <View className="empty_cart_text">请切换至消费端查看购物车！</View>
                </View>
            ) : (
                <>
                    <View className="goodsList">
                        {list.map((item, index) => {
                            return (
                                <>
                                    <View className="shop-item">
                                        <View
                                            className="shop_info"
                                            onClick={() => {
                                                selectShopAll(index);
                                            }}
                                        >
                                            <Checkbox value={item.allChecked}>
                                                <Text className="shop_name">{item.shopName}</Text>
                                            </Checkbox>
                                        </View>
                                        <CartValid
                                            changeSelect={changeSelectMethod}
                                            // getCartList={getCartList}
                                            deleteGoods={deleteGoods}
                                            items={item}
                                            index={index}
                                            changeNumber={changeNumber}
                                        />
                                    </View>
                                </>
                            );
                        })}
                    </View>
                    {list.length > 0 ? (
                        <SubmitBar
                            // price={totalCount}
                            buttonText={`去结算(${num})`}
                            decimalLength={2}
                            onSubmit={toPayOrder}
                            safeAreaInsetBottom={false}
                        >
                            <View className="right-container">
                                <View className="right-left">
                                    <Checkbox value={allChecked} onChange={e => setAllCheckedChange(e.detail)}>
                                        全选
                                    </Checkbox>
                                    <Text
                                        style={{ fontSize: "24rpx", marginLeft: "10px" }}
                                        onClick={() => {
                                            batchDelete();
                                        }}
                                    >
                                        删除
                                    </Text>
                                </View>

                                <View className="total">
                                    <View className="total-num">
                                        合计:<Text className="total-num-red">¥{(totalCount / 100).toFixed(2)}</Text>
                                    </View>
                                    <View className="total-note">(不含运费和税费)</View>
                                </View>
                            </View>
                        </SubmitBar>
                    ) : null}

                    {list.length === 0 && <Empty />}
                </>
            )}
        </View>
    );
};

export default Index;
