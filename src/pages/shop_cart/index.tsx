import { View, Image, Text, ScrollView } from "@tarojs/components";
import Empty from "./components/empty";
import "./index.less";
import { useRef, useState } from "react";
import CartValid from "./components/cart-valid";
import api from "@/utils/api";
import { getStorage } from "@/utils/storage";
import { STORE_ID } from "@/utils/constant";
import Taro, { useDidShow } from "@tarojs/taro";
import { ActionSheet, Checkbox, NavBar, SubmitBar } from "@antmjs/vantui";
import { DDYNavigateTo } from "@/utils/route";
import DDYToast from "@/utils/toast";
import identityStore from "@/store/identity-store";
import { mall_event, mall_event_key, reportEvent } from "@/utils/track-report";
import newAPi from "@/utils/newApi";
import { DDYObject } from "src/type/common";
import SkuAddPopup from "../goods_detail/component/sku-add-popup";
import { debounce } from "@/utils/common";

// DUTY_PAID(0, "大贸（完税）"),
// BONDED(1, "全球购（保税）")

const Index = () => {
    const [allChecked, setAllChecked] = useState(false);
    const [totalCount, setTotalCount] = useState(0);

    const [cartMode, setCartMode] = useState<"cart" | "delete">("cart");

    const [list, setList] = useState<any[]>([]);
    const [totalDiscountPrice, settotalDiscountPrice] = useState(0);
    const [currentGoodsCode, setCurrentGoodsCode] = useState(null);
    const [currentSkuId, setCurrentSkuId] = useState(null);
    const [currentSkuNote, setCurrentSkuNote] = useState(null);
    const [currentSkuNum, setCurrentSkuNum] = useState(null);
    const [currentCartItemId, setCurrentCartItemId] = useState(null);
    const [skuModal, setSkuModal] = useState(false);
    const [num, setNum] = useState(0);

    // const [totalDiscountPrice,setCouponPrice] = useState(0);
    const [totalCashGift, settotalCashGift] = useState(0);
    const [totalDiscount, settotalDiscount] = useState(0);
    const [totalOriginalPrice, settotalOriginalPrice] = useState(0);
    const [totalCashGiftCount, settotalCashGiftCount] = useState(0);
    const [unget, setUnget] = useState(false);
    // 查看明细弹框
    const [popshow, setPopshow] = useState(false);
    const goodsCode = useRef([]);

    // const [to]
    const identity = identityStore.identity;

    const checkTranType = (arr, checked, shopChangedIndex, changedIndex) => {
        if (shopChangedIndex === null && changedIndex === null) {
            // 全选模式下，对选中做判断
            if (checked) {
                const types: number[] = [];
                for (let i = 0; i < arr.length; i++) {
                    const { cartItems } = arr[i];
                    for (let index = 0; index < cartItems.length; index++) {
                        const element = cartItems[index];
                        types.push(element.sku.type);
                    }
                }
                if (types.includes(0) && types.includes(1)) {
                    DDYToast.info("大贸和保税不能同时勾选");
                    return false;
                }
            }
        }

        // 单店铺勾选情况下
        if (shopChangedIndex !== null && changedIndex === null) {
            if (checked) {
                const types: number[] = [];
                for (let i = 0; i < arr.length; i++) {
                    const { cartItems } = arr[i];
                    for (let index = 0; index < cartItems.length; index++) {
                        const element = cartItems[index];
                        console.log(element);
                        types.push(element.sku.type);
                    }
                }
                if (types.includes(0) && types.includes(1)) {
                    DDYToast.info("大贸和保税不能同时勾选");
                    return false;
                }
            }
        }
        // 单个商品勾选切换
        if (shopChangedIndex !== null && changedIndex !== null) {
            if (checked) {
                const type = arr[shopChangedIndex].cartItems[changedIndex].sku.type;
                for (let i = 0; i < arr.length; i++) {
                    const { cartItems } = arr[i];
                    for (let index = 0; index < cartItems.length; index++) {
                        const element = cartItems[index];
                        if (element.checked && element.sku.type !== type) {
                            DDYToast.info("大贸和保税不能同时勾选");
                            return false;
                        }
                    }
                }
            }
        }
        return true;
    };

    // 更改店铺的选择
    const selectShopAll = (index: number) => {
        const bol = !list[index].allChecked;
        list[index].allChecked = bol;
        const arr = [];
        list[index].cartItems.map(item => {
            if (item.itemStatus === 1) {
                item.checked = bol;
            }
            arr.push(item.cartItemId);
        });
        setList([...list]);
        // statisticsStatus([...list]);
        // getCartList();
        updateCheckedCarts(arr, bol);
    };

    // 更改单个商品的选择
    const changeSelectMethod = (id, checked) => {
        updateCheckedCarts([id], checked);
    };

    // 删除商品
    const deleteGoods = (shopIndex: number, index: number) => {
        console.log("删除：", index);
        const delId = list[shopIndex].cartItems[index].sku.id;
        // 删除单一项
        list[shopIndex].cartItems.splice(index, 1);
        // 如果该店铺下没有商品了，就删除选择店铺的所有数据
        if (list[shopIndex].cartItems.length == 0) {
            list.splice(shopIndex, 1);
        }
        setList([...list]);
        // statisticsStatus([...list]);
        apiDeleteGoods([delId]);
    };

    // 批量删除商品
    const apiDeleteGoods = (ids: number[], reload?: boolean) => {
        if (ids.length == 0) {
            DDYToast.info("请选择删除的商品");
            return;
        }
        newAPi
            .deleteCarts({
                method: "POST",
                // contentType: "application/x-www-form-urlencoded",
                data: {
                    skuData: ids.join(","),
                },
            })
            .then(res => {
                DDYToast.info("删除成功");
                if (reload) {
                    getCartList();
                    getTabCartNum();
                    setNum(0);
                    setTotalCount(0);
                    setAllChecked(false);
                }
            })
            .catch();
    };

    // 商品数量修改
    const changeNumber = (cartItemId: number, val: number) => {
        changeCartNumber(cartItemId, val);
    };

    // 购物车数量变化
    const changeCartNumber = (id, count) => {
        newAPi
            .changeCartNum({
                method: "POST",
                // contentType: "application/x-www-form-urlencoded",
                data: {
                    skuId: id,
                    quantity: count,
                },
                showLoad: true,
            })
            .then(res => {
                console.log("res:", res, count);
                getTabCartNum();
                getCartList();
            });
    };

    // 获取加购数据
    const getCartList = () => {
        const arr = [];
        newAPi
            .getCarts({
                data: {
                    shopId: getStorage(STORE_ID),
                    // cartItemList: arr,
                },
                showError: false,
                showLoad: true,
                method: "POST",
                // filterCheck: true,
            })
            .then(res => {
                const {
                    richCarts,
                    totalDiscountPrice,
                    totalCashGift,
                    totalPrice,
                    totalDiscount,
                    totalOriginalPrice,
                    totalCashGiftCount,
                } = res;
                settotalDiscount(totalDiscount);
                settotalCashGift(totalCashGift);
                settotalCashGiftCount(totalCashGiftCount);
                setTotalCount(totalPrice);
                settotalOriginalPrice(totalOriginalPrice);
                // console.log("arr.length:", arr.length);
                goodsCode.current = [];
                let allChecked = true;
                let count = 0;
                for (let i = 0; i < richCarts.length; i++) {
                    // richCarts[i].allChecked = false;
                    let checked = true;
                    for (let j = 0; j < richCarts[i].cartItems.length; j++) {
                        const goodsDisabled = richCarts[i].cartItems[j].itemStatus !== 1;
                        if (!goodsDisabled && !richCarts[i].cartItems[j].checked) {
                            checked = false;
                        }
                        if (richCarts[i].cartItems[j].checked) {
                            count += 1;
                            goodsCode.current.push(richCarts[i].cartItems[j].sku.itemId);
                        }
                    }
                    richCarts[i].allChecked = checked;
                    if (!checked) {
                        allChecked = false;
                    }
                }
                setNum(count);
                setAllChecked(allChecked);
                setList(Array.isArray(richCarts) ? richCarts : []);
                if (!Array.isArray(richCarts) || richCarts.length === 0) {
                    setCartMode("cart");
                }
                settotalDiscountPrice(totalDiscountPrice);
                queryWaitGetCoupons(goodsCode.current);
            })
            .catch(() => {});
    };

    // 获取购物车数量
    const getTabCartNum = () => {
        newAPi
            .getCartCount({
                data: { shopId: getStorage(STORE_ID) },
                showError: false,
                method: "POST",
                // filterCheck: true,
            })
            .then(res => {
                let cart = res + "";
                // 店主端或导购端没有购物车，去掉数量
                if (res > 0) {
                    Taro.setTabBarBadge({ index: 2, text: cart });
                } else {
                    Taro.removeTabBarBadge({ index: 2 });
                }
            });
    };

    // 全选设置
    const setAllCheckedChange = (checked: boolean) => {
        const arr = [];
        list.map(item => {
            item.allChecked = checked;
            item.cartItems?.map(goodsItem => {
                if (cartMode === "cart") {
                    if (goodsItem.itemStatus === 1) {
                        goodsItem.checked = checked;
                    }
                } else {
                    goodsItem.checked = checked;
                }
                arr.push(goodsItem.cartItemId);
            });
        });

        if (cartMode === "cart") {
            changeSelectMethod(arr, checked);
        } else {
            // 管理模式下不请求后端数据，只做前端本地刷新
            setAllChecked(checked);
            setList([...list]);
        }

        // getCartList();
    };

    const batchDelete = () => {
        const ids = [];
        let count = 0;
        list.map(item => {
            item.cartItems?.map(goodsItem => {
                // goodsItem.checked = checked;
                if (goodsItem.checked) {
                    //@ts-ignore
                    ids.push(goodsItem.sku.id);
                    count += 1;
                }
            });
        });
        if (count === 0) {
            DDYToast.info("请选择删除的商品");
            return;
        }
        DDYToast.showModal({
            title: "确定",
            content: `是否确认将这${count}件宝贝删除？`,
            success(res) {
                if (res.confirm) {
                    apiDeleteGoods(ids, true);
                } else {
                }
            },
            confirmText: "确定删除",
            cancelText: "我再想想",
            cancelColor: "#f51214",
        });
    };

    const toPayOrder = () => {
        let orderGoodsList = [];
        let payGoodsList = [];
        let orderGoods = {};
        let payGoods = {};
        for (let i = 0; i < list.length; i++) {
            for (let j = 0; j < list[i].cartItems.length; j++) {
                let cartItem = list[i].cartItems[j];
                if (cartItem.checked) {
                    console.log("list[i]:", list[i].cartItems[j]);
                    let quantity = list[i].cartItems[j]?.orderQuantityLimit
                        ? Math.min(list[i].cartItems[j]?.orderQuantityLimit, list[i].cartItems[j]?.sku.stockQuantity)
                        : list[i].cartItems[j]?.sku.stockQuantity;
                    orderGoods = {
                        skuId: cartItem.sku.id,
                        quantity: Math.min(quantity, cartItem.cartItem.quantity),
                        sourceType: cartItem.sourceType,
                        buyerNote: cartItem.buyerNote,
                    };
                    payGoods = {
                        skuId: cartItem.sku.id,
                        quantity: Math.min(quantity, cartItem.cartItem.quantity),
                        promotionId: null,
                        buyerNote: cartItem.buyerNote,
                    };

                    //@ts-ignore
                    orderGoodsList.push(orderGoods);
                    //@ts-ignore
                    payGoodsList.push(payGoods);
                }
            }
        }
        if (orderGoodsList.length === 0) {
            DDYToast.info("请选择商品");
            return;
        }
        let map = new Map();
        map.set(mall_event_key.MALL_KEY_GOODS_ID, JSON.stringify(orderGoodsList));
        map.set(mall_event_key.MALL_KEY_GOODS_NUM, JSON.stringify(payGoodsList));
        // map.set(mall_event_key.MALL_KEY_GOOD_SPEC_ID, specId)
        reportEvent(mall_event.MALL_EVENT_CART_SETTLEMENT, map);
        api.preCheck({
            data: {
                data: JSON.stringify(orderGoodsList),
            },
            // filterCheck: true,
            // method: 'POST',
        }).then(res => {
            DDYNavigateTo({
                url:
                    "/pages/order/comfire-order/index?orderGoodsList=" +
                    JSON.stringify(orderGoodsList) +
                    "&payGoodsList=" +
                    JSON.stringify(payGoodsList) +
                    "&from=shop_cart",
                events: {
                    update: () => {
                        console.log(":update");
                        getCartList();
                        getTabCartNum();
                    },
                },
            });
        });
    };

    const updateCheckedCarts = (itemIds, checked) => {
        const cartCheckedInfoList: DDYObject[] = [];
        // console.log(itemId, checked)
        list.map(shopItem => {
            shopItem.cartItems.map(cartItem => {
                // cartItem.cartItemId === itemId
                if (itemIds.includes(cartItem.cartItemId)) {
                    cartItem.checked = checked;
                }
                // console.log("cartItem.cartItemId:", cartItem.cartItemId)
                cartCheckedInfoList.push({
                    cartItemId: cartItem.cartItemId,
                    checked: cartItem.checked,
                });
            });
        });
        newAPi
            .updateCheckedCarts({
                method: "POST",
                data: {
                    cartCheckedInfoList: cartCheckedInfoList,
                },
            })
            .then(res => {
                getCartList();
            });
    };

    // useEffect(() => {
    //     getCartList();
    //     getTabCartNum();
    // }, []);

    const queryWaitGetCoupons = goodsCode => {
        newAPi
            .queryWaitGetCoupons({
                method: "POST",
                data: {
                    goodsCodeList: goodsCode,
                },
            })
            .then(res => {
                console.log("res:", res);

                if (res.length != 0) {
                    setUnget(true);
                } else {
                    setUnget(false);
                }
            });
    };

    const getCouponsSend = () => {
        newAPi
            .getAutoGetCoupons({
                method: "GET",
                data: {
                    goodsCodeList: goodsCode.current.join(","),
                },
                // showError: false
            })
            .then(res => {
                toPayOrder();
            });
    };

    useDidShow(() => {
        getCartList();
        getTabCartNum();
        // setAllChecked(false);
        // setNum(0);
        // setTotalCount(0);
    });

    return (
        <View>
            <NavBar
                fixed
                // leftArrow={true}
                renderLeft={
                    <>
                        <Text
                            onClick={() => {
                                batchDelete();
                            }}
                        >
                            批量删除
                        </Text>
                    </>
                }
                title={"购物车"}
                // border={false}
                safeAreaInsetTop
            />
            <View className="shop_cart">
                {identity != "buyer" && identity != "sellerAudit" && identity != "guider" && identity != "subStore" ? (
                    <View className="empty_cart">
                        <Image
                            src="https://dante-img.oss-cn-hangzhou.aliyuncs.com/53083388900.png"
                            mode="widthFix"
                            className="empty_cart_img"
                        ></Image>
                        <View className="empty_cart_text">请切换至消费端查看购物车！</View>
                    </View>
                ) : (
                    <>
                        {list.length > 0 ? (
                            <>
                                {cartMode === "cart" ? (
                                    <Text
                                        style={{
                                            fontSize: "24rpx",
                                            marginLeft: "10px",
                                            position: "absolute",
                                            right: "40px",
                                            top: "20px",
                                            zIndex: 100,
                                        }}
                                        onClick={() => {
                                            // batchDelete();
                                            setCartMode("delete");
                                        }}
                                    >
                                        管理
                                    </Text>
                                ) : (
                                    <Text
                                        style={{
                                            fontSize: "24rpx",
                                            marginLeft: "10px",
                                            position: "absolute",
                                            right: "40px",
                                            top: "20px",
                                            zIndex: 100,
                                        }}
                                        onClick={() => {
                                            // batchDelete();
                                            setCartMode("cart");
                                        }}
                                    >
                                        退出管理
                                    </Text>
                                )}
                            </>
                        ) : null}

                        <View className="goodsList">
                            {list.map((item, index) => {
                                return (
                                    <>
                                        <View className="shop-item">
                                            <View
                                                className="shop_info"
                                                // onClick={() => {
                                                //     if (!checkTranType(list, !item.allChecked, index, null)) {
                                                //         return;
                                                //     }
                                                //     selectShopAll(index);
                                                // }}
                                            >
                                                {/* <Checkbox value={item.allChecked}> */}
                                                <Text className="shop_name">{item.shopName}</Text>
                                                {/* </Checkbox> */}
                                            </View>
                                            <CartValid
                                                changeSelect={(id, checked, itemIndex) => {
                                                    if (cartMode === "cart") {
                                                        if (!checkTranType(list, checked, index, itemIndex)) {
                                                            return;
                                                        }
                                                        changeSelectMethod(id, checked);
                                                    } else {
                                                        list[index].cartItems[itemIndex].checked = checked;
                                                        setList([...list]);
                                                        let allChecked = true;
                                                        for (let i = 0; i < list.length; i++) {
                                                            // richCarts[i].allChecked = false;
                                                            let checked = true;
                                                            for (let j = 0; j < list[i].cartItems.length; j++) {
                                                                // const goodsDisabled = list[i].cartItems[j].itemStatus !== 1;
                                                                if (!list[i].cartItems[j].checked) {
                                                                    checked = false;
                                                                }
                                                            }
                                                            list[i].allChecked = checked;
                                                            if (!checked) {
                                                                allChecked = false;
                                                            }
                                                        }
                                                        setAllChecked(allChecked);
                                                    }
                                                }}
                                                cartMode={cartMode}
                                                // getCartList={getCartList}
                                                deleteGoods={deleteGoods}
                                                items={item}
                                                index={index}
                                                changeNumber={changeNumber}
                                                showSkuModal={(goodsCode, skuId, num, note, cartItemId) => {
                                                    setCurrentGoodsCode(goodsCode);
                                                    setCurrentSkuId(skuId);
                                                    setSkuModal(true);
                                                    setCurrentSkuNum(num);
                                                    setCurrentSkuNote(note);
                                                    setCurrentCartItemId(cartItemId);
                                                }}
                                            />
                                        </View>
                                    </>
                                );
                            })}
                        </View>
                        {/* {list.length > 0 ? ( */}
                        <SubmitBar
                            // price={totalCount}
                            buttonText={cartMode === "cart" ? `${unget ? "领券结算" : "去结算"}(${num})` : "批量删除"}
                            decimalLength={2}
                            // onTap={}
                            onSubmit={() => {
                                if (cartMode === "delete") {
                                    batchDelete();
                                    return;
                                }
                                setPopshow(false);
                                if (unget) {
                                    getCouponsSend();
                                } else {
                                    toPayOrder();
                                }
                            }}
                            safeAreaInsetBottom={false}
                        >
                            <View className="right-container">
                                <View className="right-left">
                                    <Checkbox
                                        value={allChecked}
                                        onChange={e => {
                                            if (cartMode === "cart" && !checkTranType(list, e.detail, null, null)) {
                                                return;
                                            }
                                            setAllCheckedChange(e.detail);
                                        }}
                                    >
                                        全选
                                    </Checkbox>
                                </View>

                                {cartMode === "cart" ? (
                                    <View className="total">
                                        <View className="total-num">
                                            合计:<Text className="total-num-red">¥{totalCount.toFixed(2)}</Text>
                                            <Text className="total-note">(不含运费税费)</Text>
                                        </View>
                                        <View
                                            className="discount-num"
                                            style={{ textAlign: "center" }}
                                            onClick={() => {
                                                setPopshow(!popshow);
                                            }}
                                        >
                                            共减¥{totalDiscount} | 查看明细
                                        </View>
                                    </View>
                                ) : null}
                            </View>
                        </SubmitBar>
                        {/* ) : null} */}

                        {list.length === 0 && <Empty />}
                    </>
                )}
                <ActionSheet show={popshow} title="优惠明细" onClose={() => setPopshow(false)}>
                    <View className="dis-pupop-rect">
                        <View className="dis-pupop-head">实际扣减金额请以下单页为准</View>
                        <View className="dis-pupop-title">商品选择</View>
                        <ScrollView scrollX={true} style={{ padding: "12px", boxSizing: "border-box" }}>
                            <View style={{ display: "flex", flexDirection: "row", flexWrap: "nowrap" }}>
                                {list.map((item, index) => {
                                    return (
                                        <>
                                            {item?.cartItems.map((goodsItem, goodIndex) => {
                                                const goodsDisabled = goodsItem.itemStatus !== 1;
                                                return (
                                                    <View
                                                        style={{
                                                            position: "relative",
                                                            marginRight: " 24px",
                                                        }}
                                                    >
                                                        <Image
                                                            src={goodsItem.itemImage}
                                                            mode="aspectFill"
                                                            style={{
                                                                width: "80px",
                                                                height: "80px",
                                                                borderRadius: "8px",
                                                            }}
                                                        />
                                                        <Checkbox
                                                            disabled={goodsDisabled}
                                                            value={goodsItem.checked}
                                                            style={{ position: "absolute", right: "-20px", top: "2px" }}
                                                            onChange={e => {
                                                                if (!checkTranType(list, e.detail, index, goodIndex)) {
                                                                    return;
                                                                }
                                                                //@ts-ignore
                                                                changeSelectMethod(goodsItem.cartItemId, e.detail);
                                                            }}
                                                        />
                                                    </View>
                                                );
                                            })}
                                        </>
                                    );
                                })}
                            </View>
                        </ScrollView>
                        <View className="dis-pupop-title">价格明细</View>
                        <View className="dis-pupop-item">
                            <Text className="dis-pupop-item-title">商品总价</Text>
                            <Text className="dis-pupop-item-text1">¥{totalOriginalPrice}</Text>
                        </View>
                        <View className="dis-pupop-item">
                            <Text className="dis-pupop-item-title">共减</Text>
                            <Text className="dis-pupop-item-text2">-¥{totalDiscount}</Text>
                        </View>
                        <View className="dis-pupop-item">
                            <Text className="dis-pupop-item-title1">优惠券:</Text>
                            <Text className="dis-pupop-item-text3">-¥{totalDiscountPrice}</Text>
                        </View>
                        <View className="dis-pupop-item">
                            <Text className="dis-pupop-item-title1">福豆:</Text>
                            <Text className="dis-pupop-item-text3">
                                -¥{totalCashGift}({totalCashGiftCount}个)
                            </Text>
                        </View>
                    </View>
                </ActionSheet>
                <SkuAddPopup
                    show={skuModal}
                    closeFn={load => {
                        setSkuModal(false);
                        if (load) {
                            getCartList();
                            // getTabCartNum();
                        }
                    }}
                    goodsId={currentGoodsCode}
                    skuId={currentSkuId}
                    skuNum={currentSkuNum}
                    skuNote={currentSkuNote}
                    cartItemId={currentCartItemId}
                    openType={0}
                    hasGbActivity={false}
                />
            </View>
        </View>
    );
};

export default Index;
