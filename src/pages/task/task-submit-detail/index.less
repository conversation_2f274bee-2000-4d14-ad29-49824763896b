@import "../../../styles/result-iconfont.less";

.task-submit-detail {
    height: 100%;

    .page-title {
        z-index: 999;
        --nav-bar-title-text-color: #333;
        --nav-bar-icon-color: #333;
        background: transparent;
    }

    .white {
        background-color: #fff;
    }

    .transparent {
        background-color: transparent;
    }

    .task-submit-head {
        position: relative;

        &-bg {
            width: 750px;
            height: 275px;
            background: #f4dcdc;
        }

        .matters-wrapper {
            padding-left: 32px;
            padding-right: 32px;
            z-index: 99;
            position: absolute;
            top: 188px;
            display: flex;
        }

        .matters-title-wrapper {
            margin-left: 20px;
            display: flex;
            flex-direction: column;
        }

        .matters-title {
            color: #333;
            font-weight: 400;
            font-size: 36px;
        }

        .matters-desc {
            margin-top: 10px;
            color: #999999;
            font-weight: 400;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 2;
            overflow: hidden;
            font-size: 30px;
        }
    }

    .task-submit-card {
        top: 345px;
        margin: 0 24px;
        position: absolute;
        height: 100%;
        background-color: #fff;
        border-radius: 16px;

        .card-title {
            padding: 32px 32px 0;
            display: flex;
            align-items: center;
        }

        .card-title-text {
            display: flex;
            color: #333;
            font-size: 32px;
            margin-left: 20px;
            font-weight: 600;
        }
    }

    .upload-title {
        display: flex;
        padding: 32px 32px 0;

        &-desc {
            color: #999999;
        }
    }

    .link-input {
        width: 100%;
        height: 145px;
        padding: 0 24px;
        text-align: left;
        border-radius: 13px;
        border: solid 1px rgba(170, 170, 170, 1);
    }
}
