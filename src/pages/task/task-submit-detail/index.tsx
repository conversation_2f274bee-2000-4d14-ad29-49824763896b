import { Button, Cell, Col, Form, FormItem, Icon, Image, NavBar, pxTransform, Row, Uploader } from "@antmjs/vantui";
import "./index.less";
import { Input, ITouchEvent, View } from "@tarojs/components";
import Taro, { usePageScroll, useRouter } from "@tarojs/taro";
import { isAlipay } from "@/utils/common";
import { useEffect, useState } from "react";
import newApi from "@/utils/newApi";
import { DDYBack } from "@/utils/route";
import { timestampToTime } from "@/utils/DateTimeUtils";

export default () => {
    const route = useRouter();
    const [detail, setDetail] = useState({});
    const form = Form.useForm();
    const [top, setTop] = useState(false);
    usePageScroll(payload => {
        if (payload.scrollTop > 30) {
            setTop(true);
        } else {
            setTop(false);
        }
    });
    useEffect(() => {
        getDetail();
    }, []);
    const { id } = route.params;
    function getDetail() {
        newApi
            .activityUserDetail({
                method: "POST",
                data: { id },
            })
            .then(res => {
                setDetail(res);
                let obj = {
                    postsUrl: res.postsUrl,
                    materialUrlList: res.materialUrlList.map(item => {
                        return [
                            {
                                url: item,
                            },
                        ];
                    }),
                };
                form.setFields(obj);
            });
    }

    return (
        <View className="task-submit-detail">
            <NavBar
                title="提交核销资料"
                leftText=""
                onClickLeft={() => {
                    DDYBack();
                }}
                className={`page-title ${top ? "white" : "transparent"}`}
                fixed
                leftArrow={!isAlipay()}
                border={false}
            />
            <View className={"task-submit-head"}>
                <View className={"task-submit-head-wrap"}>
                    <View className={"task-submit-head-bg"} />
                    <Image
                        width={"100%"}
                        height={211}
                        src={"https://dante-img.oss-cn-hangzhou.aliyuncs.com/97354462137.png"}
                    />
                </View>
                <View className={"matters-wrapper"}>
                    <Icon name={"success"} size={98} classPrefix="dt-icon-result" />
                    <View className={"matters-title-wrapper"}>
                        <View className={"matters-title"}>
                            {detail.auditStatus === "ACTIVITY_USER_WAIT" ? <>提交成功，待审核</> : null}
                            {detail.auditStatus === "ACTIVITY_USER_PASS" ? <>审核通过</> : null}
                            {detail.auditStatus === "ACTIVITY_USER_REJECT" ? <>审核驳回</> : null}
                        </View>

                        <View className={"matters-desc"}>
                            {detail.rejectReasons ? <> {detail.rejectReasons} </> : null}
                            {detail.auditStatus === "ACTIVITY_USER_WAIT" ? (
                                <>
                                    我们会在{timestampToTime(detail.auditEndTime, "MM-DD HH:mm")}
                                    前完成审核，请在此期间保持内容的有效性以避免驳回
                                </>
                            ) : null}
                        </View>
                    </View>
                </View>
            </View>
            <Form form={form}>
                <View className={"task-submit-card"}>
                    <Cell title={"提交时间"} value={timestampToTime(detail.submitTime, "MM-DD HH:mm")} />
                    <Cell title={"新媒体平台"} value={detail.promotionChannelName} />
                    <View className={"card-title"}></View>
                    <View className={"upload-title"}>上传活动要求的截图</View>
                    <Row>
                        {["截图一", "截图二", "截图三", "截图四"].map((item, index) => (
                            <Col span="12">
                                <FormItem
                                    name={["materialUrlList", index]}
                                    layout="vertical"
                                    mutiLevel
                                    label=""
                                    valueKey="fileList"
                                    trigger="onAfterRead"
                                    validateTrigger="onAfterRead"
                                    className="image-uploader-form-item-not-padding"
                                >
                                    <Uploader
                                        maxCount={1}
                                        previewSize={200}
                                        className="uploader-img"
                                        disabled
                                        uploadText={item}
                                    ></Uploader>
                                </FormItem>
                            </Col>
                        ))}
                    </Row>
                    {detail?.promotionChannel !== "PYQ_CHANNEL" ? (
                        <FormItem
                            name={"postsUrl"}
                            layout="vertical"
                            label="作品链接"
                            trigger="onInput"
                            valueFormat={e => e.detail.value}
                        >
                            <Input disabled={true} className={"link-input"} placeholderStyle={"#BBBBBB"} />
                        </FormItem>
                    ) : null}
                    <View style={{ textAlign: "center" }}>
                        <Button className={"submit-btn"} onClick={() => DDYBack({ delta: 2 })}>
                            返回活动主页
                        </Button>
                    </View>
                </View>
            </Form>
        </View>
    );
};
