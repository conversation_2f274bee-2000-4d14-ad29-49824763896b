import { View, Text } from "@tarojs/components";
import { Button, Image } from "@antmjs/vantui";
import "./index.less";
import Taro, { useRouter } from "@tarojs/taro";
import { useEffect, useState } from "react";
import newApi from "@/utils/newApi";
import DDYToast from "@/utils/toast";
import { DDYBack, DDYNavigateTo } from "@/utils/route";

export default () => {
    const route = useRouter();
    const [detail, setDetail] = useState({});
    useEffect(() => {
        getDetail();
    }, []);

    function getDetail() {
        newApi
            .activityDetail({
                method: "POST",
                data: { id: route.params.id },
            })
            .then(res => {
                setDetail(res);
            });
    }
    function signUp() {
        newApi
            .activitySignUp({
                method: "POST",
                data: { activityId: route.params.id },
            })
            .then(res => {
                DDYToast.success("报名成功");
                const pages = Taro.getCurrentPages();
                const current = pages[pages.length - 1];
                const eventChannel = current.getOpenerEventChannel();
                if (route.params.pageJump === "taskCenter") {
                    eventChannel.emit("refreshTaskList", {});
                } else {
                    eventChannel.emit("refreshDetail", {});
                }
                DDYBack();
            });
    }

    return (
        <View className="task-sign-up">
            {/* 广告banner*/}
            <View className="banner">
                <Image className="banner-img" src={detail.bannerUrl} />
            </View>

            {/*   活动卡片 */}
            <View className="activity-card">
                <View className="activity-header">
                    <Image
                        className="activity-header-bg"
                        src={"https://dante-img.oss-cn-hangzhou.aliyuncs.com/53921281728.png"}
                    />
                    <Text className="activity-title">{detail.activityName}</Text>
                </View>

                <View className="reward-section">
                    <View className="reward-title">{detail.activityTopic}</View>
                    <View className="reward-amount">¥ {detail.rewardsNum}</View>
                    <View className="reward-label">活动福豆</View>
                </View>

                <View className="activity-desc">
                    <Text className="desc-Text">说明：</Text>
                    <Text className="desc-content">
                        作品需按照活动规则参与，否则会核销不通过 请【点击报名】后仔细阅读活动规则哦~
                    </Text>
                </View>

                <Button className="submit-btn" onClick={signUp}>
                    点击报名
                </Button>
            </View>
        </View>
    );
};
