import {
    Button,
    Col,
    Form,
    FormItem,
    Icon,
    Image,
    ImageViewer,
    NavBar,
    pxTransform,
    Row,
    Uploader,
} from "@antmjs/vantui";
import "./index.less";
import { Input, ITouchEvent, View } from "@tarojs/components";
import DDYToast from "@/utils/toast";
import { PROJECT_CONFIG } from "@/utils/env";
import Taro, { usePageScroll, useRouter } from "@tarojs/taro";
import { isAlipay } from "@/utils/common";
import { Fragment, useEffect, useState } from "react";
import newApi from "@/utils/newApi";
import { DDYBack, DDYNavigateTo } from "@/utils/route";

export default () => {
    const route = useRouter();
    const [detail, setDetail] = useState({});

    const [top, setTop] = useState(false);
    usePageScroll(payload => {
        if (payload.scrollTop > 30) {
            setTop(true);
        } else {
            setTop(false);
        }
    });
    useEffect(() => {
        getDetail();
    }, []);

    function getDetail() {
        newApi
            .activityDetail({
                method: "POST",
                data: { id: route.params.id },
            })
            .then(res => {
                setDetail(res);
            });
    }

    const form = Form.useForm();
    const deleteFile = (event: ITouchEvent, name: string | Array<string | number>) => {
        const { index, fileList } = event.detail;
        fileList.splice(index, 1);
        form?.setFieldsValue(name, fileList);
    };
    const valueFormatUpload = event => {
        DDYToast.showLoading("上传中...");

        // 异步更新
        return new Promise(resolve => {
            const { file } = event.detail;
            Taro.uploadFile({
                url: `${PROJECT_CONFIG.API_MALL}/api/user/files/upload`, //仅为示例，非真实的接口地址
                name: "file",
                filePath: file.url,
                success(res) {
                    DDYToast.hideLoading();
                    file.url = JSON.parse(res.data).image;
                    resolve([file]);
                },
                fail() {
                    DDYToast.hideLoading();
                    DDYToast.error("上传失败");
                },
            });
        });
    };

    function submit() {
        form.validateFields((_, fieldValues) => {
            const materialUrlList = (fieldValues.materialUrl || []).filter(item => item);
            if (materialUrlList.length == 0) {
                DDYToast.info("至少上传1张截图");
                return;
            }
            const materialUrl = materialUrlList.map(item => item[0].url);

            const activityUserId = detail.activityUserId;
            newApi
                .activitySubmitMaterial({
                    method: "POST",
                    data: {
                        activityId: route.params.id,
                        id: activityUserId,
                        materialUrl: JSON.stringify(materialUrl),
                        postsUrl: fieldValues.postsUrl,
                    },
                })
                .then(res => {
                    DDYToast.success("提交成功");
                    const pages = Taro.getCurrentPages();
                    const current = pages[pages.length - 1];
                    const eventChannel = current.getOpenerEventChannel();
                    eventChannel.emit("refreshDetail", {});
                    let url = `/pages/task/task-submit-detail/index?id=${activityUserId}`;
                    DDYNavigateTo({ url: url });
                });
        });
    }

    return (
        <View className="task-submit">
            <NavBar
                title="提交核销资料"
                leftText=""
                onClickLeft={() => {
                    DDYBack();
                }}
                className={`page-title ${top ? "white" : "transparent"}`}
                fixed
                leftArrow={!isAlipay()}
                border={false}
            />
            <View className={"task-submit-head"}>
                <View className={"task-submit-head-wrap"}>
                    <View className={"task-submit-head-bg"} />
                    <Image
                        width={"100%"}
                        height={211}
                        src={"https://dante-img.oss-cn-hangzhou.aliyuncs.com/97354462137.png"}
                    />
                </View>
                <View className={"matters-wrapper"}>
                    <View className={"matters-title"}>注意事项：</View>
                    <View className={"matters-desc"}>
                        上传前您须准备好下方所示1张截图，请确保提交资料的准确性，禁止上传不相关的、人为造假的截图
                    </View>
                </View>
            </View>
            <Form form={form}>
                <View className={"task-submit-card"}>
                    <View className={"card-title"}>
                        <View style={{ display: "flex", flex: 1 }}>
                            <View>
                                <Image
                                    round
                                    width={pxTransform(48)}
                                    height={pxTransform(48)}
                                    src={detail?.promotionChannelUrl}
                                />
                            </View>
                            <View className={"card-title-text"}>提交核销资料</View>
                        </View>
                        <View
                            className={"card-title-example"}
                            onClick={() =>
                                ImageViewer.show({
                                    list: [detail?.exampleUrl],
                                })
                            }
                        >
                            <Icon name="photo" size="28" style={{ marginRight: Taro.pxTransform(8) }} />
                            查看示例
                        </View>
                    </View>
                    <View className={"upload-title"}>上传活动要求的截图</View>
                    <Row>
                        {["截图一", "截图二", "截图三", "截图四"].map((item, index) => (
                            <Col span="12">
                                <FormItem
                                    name={["materialUrl", index]}
                                    layout="vertical"
                                    label=""
                                    valueKey="fileList"
                                    valueFormat={valueFormatUpload}
                                    trigger="onAfterRead"
                                    validateTrigger="onAfterRead"
                                    className="image-uploader-form-item-not-padding"
                                >
                                    <Uploader
                                        maxCount={1}
                                        previewSize={200}
                                        className="uploader-img"
                                        onDelete={event => deleteFile(event, ["materialUrl", index])}
                                        uploadText={item}
                                    ></Uploader>
                                </FormItem>
                            </Col>
                        ))}
                    </Row>
                    {detail?.promotionChannel !== "PYQ_CHANNEL" ? (
                        <Fragment>
                            <FormItem
                                name={"postsUrl"}
                                layout="vertical"
                                label="作品链接"
                                trigger="onInput"
                                valueFormat={e => e.detail.value}
                            >
                                <Input
                                    className={"link-input"}
                                    placeholder={"亲,可以上传活动相关的作品链接"}
                                    placeholderStyle={"#BBBBBB"}
                                />
                            </FormItem>
                            <View className="bottom-ettra-info">请确保链接可访问</View>
                        </Fragment>
                    ) : null}
                    <Button className={"submit-btn"} onClick={() => submit()}>
                        确认提交
                    </Button>
                </View>
            </Form>
        </View>
    );
};
