.task-submit {
    height: 100%;

    .page-title {
        z-index: 999;
        --nav-bar-title-text-color: #333;
        --nav-bar-icon-color: #333;
        background: transparent;
    }

    .white {
        background-color: #fff;
    }

    .transparent {
        background-color: transparent;
    }

    .task-submit-head {
        position: relative;

        &-bg {
            width: 750px;
            height: 230px;
            background: #f4dcdc;
        }

        .matters-wrapper {
            padding-left: 32px;
            padding-right: 32px;
            z-index: 99;
            position: absolute;
            top: 168px;
        }

        .matters-title {
            color: #333;
            font-weight: 400;
            font-size: 24px;
        }

        .matters-desc {
            color: #333;
            font-weight: 400;
            font-size: 24px;
        }
    }

    .task-submit-card {
        top: 285px;
        margin: 0 24px;
        position: absolute;
        height: 100%;
        background-color: #fff;
        border-radius: 16px;
        text-align: center;

        .card-title {
            padding: 32px 32px 0;
            display: flex;
            align-items: center;
        }
        .bottom-ettra-info {
            font-size: 24px;
            color: #999;
            margin-bottom: 30px;
            line-height: 30px;
            text-align: left;
            margin-left: 54px;
        }
        .card-title-text {
            display: flex;
            color: #333;
            font-size: 32px;
            margin-left: 20px;
            font-weight: 600;
        }
        .card-title-example {
            color: #1677ff;
            display: flex;
            align-items: center;
        }
        .example-icon {
            margin-right: 8px;
            font-size: 22px;
            height: 32px;
        }
    }

    .upload-title {
        display: flex;
        padding: 32px 32px 0;

        &-desc {
            color: #999999;
        }
    }

    .link-input {
        width: 100%;
        height: 80px;
        padding: 0 24px;
        text-align: left;
        border-radius: 13px;
        border: solid 1px rgba(170, 170, 170, 1);
    }
}
