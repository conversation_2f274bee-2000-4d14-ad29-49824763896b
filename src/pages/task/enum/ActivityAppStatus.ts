/**
 * 活动应用状态枚举
 * key 和 value 都使用相同的字符串值
 */
enum ActivityAppStatus {
    /** 待报名 */
    ACTIVITY_APP_CREATE = "ACTIVITY_APP_CREATE",

    /** 待上传 */
    ACTIVITY_APP_WAIT = "ACTIVITY_APP_WAIT",

    /** 待核销 */
    ACTIVITY_APP_PASS = "ACTIVITY_APP_PASS",

    /** 已结束 */
    ACTIVITY_APP_END = "ACTIVITY_APP_END",
}

/**
 * 活动应用状态的中文描述映射
 * 用于获取枚举对应的描述信息
 */
const ActivityAppStatusLabels: Record<ActivityAppStatus, string> = {
    [ActivityAppStatus.ACTIVITY_APP_CREATE]: "待报名",
    [ActivityAppStatus.ACTIVITY_APP_WAIT]: "待上传",
    [ActivityAppStatus.ACTIVITY_APP_PASS]: "待核销",
    [ActivityAppStatus.ACTIVITY_APP_END]: "已结束",
};
const ActivityAppStatusButton: Record<ActivityAppStatus, string> = {
    [ActivityAppStatus.ACTIVITY_APP_CREATE]: "待报名",
    [ActivityAppStatus.ACTIVITY_APP_WAIT]: "上传资料",
    [ActivityAppStatus.ACTIVITY_APP_PASS]: "待核销",
    [ActivityAppStatus.ACTIVITY_APP_END]: "已结束",
};

export { ActivityAppStatus, ActivityAppStatusLabels, ActivityAppStatusButton };
