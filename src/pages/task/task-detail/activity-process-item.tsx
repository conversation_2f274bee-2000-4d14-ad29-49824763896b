import { View, Text, Image } from "@tarojs/components";
import "./activity-process-item.less";

export default function ActivityProcessItem({ steps = [] }: { steps: any[] }) {
    return (
        <View className="process-container">
            {steps.map((step, index) => (
                <View key={index} className="step">
                    <Image className="icon" src={step.icon} />
                    <Text className="title">{step.title}</Text>
                    <Text className="desc">{step.desc}</Text>
                    {index < steps.length - 1 && <View className="divider"></View>}
                </View>
            ))}
        </View>
    );
}
