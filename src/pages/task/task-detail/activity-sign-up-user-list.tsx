import { useRouter } from "@tarojs/taro";
import { Icon, Image, InfiniteScroll, InfiniteScrollInstance, InfiniteScrollProps } from "@antmjs/vantui";
import { View, Text } from "@tarojs/components";
import { useRef, useState } from "react";
import newApi from "@/utils/newApi";
import "./activity-sign-up-user-list.less";
import Taro from "@tarojs/taro";

export default function ActivitySignUpUserList() {
    const [users, setUsers] = useState([]);
    const route = useRouter();
    const InfiniteScrollInstance = useRef<InfiniteScrollInstance>();
    const pageNo = useRef(0);
    const getDetail = () => {
        return newApi.activityUserList({
            method: "POST",
            data: { activityId: route.params.id, currentPage: pageNo.current, pageSize: 10 },
        });
    };
    const loadMore: InfiniteScrollProps["loadMore"] = async () => {
        return new Promise(async resolve => {
            try {
                const result = await getDetail();
                const { page, dataList } = result;
                const newData = users.concat(dataList);
                setUsers(newData);
                const hasMore = page.currentPage + 1 < page.totalPage;
                pageNo.current = page.currentPage + 1;
                resolve(hasMore ? "loading" : "complete");
            } catch (e) {
                resolve("error");
            }
        });
    };
    return (
        <View className="user-list">
            <View>
                <Icon name={"https://dante-img.oss-cn-hangzhou.aliyuncs.com/17334191994.svg"} />
                <Text className="process-title">参与用户</Text>
            </View>
            {users.map(user => (
                <View key={user.userName} className="user-item">
                    <View className={"avatar-box"}>
                        <Image src={user.userUrl} round width={Taro.pxTransform(72)} height={Taro.pxTransform(72)} />
                        <Text className="name">{user.userName}</Text>
                    </View>
                    <View className="info">
                        <Text className="time">{user.registrationTimeDesc}</Text>
                        <View className="join-btn">已参加</View>
                    </View>
                </View>
            ))}
            <InfiniteScroll loadMore={loadMore} ref={InfiniteScrollInstance} />
        </View>
    );
}
