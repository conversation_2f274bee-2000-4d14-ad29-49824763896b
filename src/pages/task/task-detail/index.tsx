import { View, Text, RichText } from "@tarojs/components";
import { Button, Dialog, Icon, Image, NavBar } from "@antmjs/vantui";
import { useMemo, useState, useCallback } from "react";
import newApi from "@/utils/newApi";
import Taro, { useDidShow, usePageScroll, useRouter, useShareAppMessage } from "@tarojs/taro";
import { isAlipay } from "@/utils/common";
import ActivityProcessItem from "./activity-process-item";
import ActivitySignUpUserList from "./activity-sign-up-user-list";
import { DDYBack, DDYNavigateTo, DDYRedirectTo, DDYSwitchTab } from "@/utils/route";
import { timestampToTime } from "@/utils/DateTimeUtils";
import DDYToast from "@/utils/toast";
import ActivityRuleModal from "./rule-modal";
import "./rule-modal.less";
import { jumpLogin } from "@/utils/PageUtils";
import "./index.less";

const StatusEnum = {
    PENDING_REGISTRATION: 1, // 待报名
    REGISTERED_PENDING_START: 2, // 报名了待开始
    PENDING_SUBMISSION: 3, // 待提交资料
    PENDING_REVIEW: 4, // 待审核
    APPROVED: 5, // 审核通过
    REJECTED: 6, // 驳回
    ENDED: 7, // 已结束
};
const Dialog_ = Dialog.createOnlyDialog();
export default () => {
    const route = useRouter();
    const [detail, setDetail] = useState({});
    const [open, setRuleOpen] = useState(false);
    const { id, shareId } = route.params;
    useDidShow(() => {
        getDetail();
    });
    const alert = function () {
        Dialog_.alert({
            title: "提示",
            message: "您还没有登录或登录已经过期，请先登录再进行操作",
            confirmButtonText: "立即登录",
        }).then(value => {
            jumpLogin();
        });
    };
    const cancel = function () {
        Dialog_.alert({
            title: "取消确认",
            message: "确定要取消本次活动报名吗",
            showCancelButton: true,
            cancelButtonText: "再想想",
            confirmButtonText: "确定",
        }).then(value => {
            if ("confirm" === value) {
                newApi
                    .activityCancelBatch({
                        method: "POST",
                        data: { id: detail?.activityUserId },
                    })
                    .then(res => {
                        DDYToast.success("取消报名成功");
                        DDYRedirectTo({
                            url: `/pages/task/task-center/index`,
                        });
                    });
            }
        });
    };
    function getDetail() {
        newApi
            .activityDetail({
                method: "POST",
                data: { id: id },
            })
            .then(res => {
                if (res.loginStatus === -1) {
                    alert();
                } else if (res.detailStatus === StatusEnum.PENDING_REGISTRATION) {
                    DDYNavigateTo({
                        url: `/pages/task/task-sign-up/index?id=${res.id}`,
                        events: {
                            refreshDetail: () => {
                                getDetail();
                            },
                        },
                    });
                }
                setDetail(res);
            });
    }

    const [top, setTop] = useState(false);
    usePageScroll(payload => {
        if (payload.scrollTop > 30) {
            setTop(true);
        } else {
            setTop(false);
        }
    });
    const steps = useMemo(() => {
        let step1Desc = `从${timestampToTime(detail.activityStartTime, "MM-DD HH:mm")}\n至${timestampToTime(
            detail.activityEndTime,
            "MM-DD HH:mm",
        )}`;
        let step2Desc = step1Desc;
        let step3Desc = `${timestampToTime(detail.auditEndTime, "MM-DD HH:mm")}\n前审核完成`;
        let step4Desc = `${timestampToTime(detail.rewardsTime, "MM-DD HH:mm")}\n开始发券到账户`;
        return [
            {
                icon: "https://dante-img.oss-cn-hangzhou.aliyuncs.com/18174542742.svg",
                title: "报名",
                desc: step1Desc,
            },
            {
                icon: "https://dante-img.oss-cn-hangzhou.aliyuncs.com/19167907457.svg",
                title: "上传核销资料",
                desc: step2Desc,
            },
            {
                icon: "https://dante-img.oss-cn-hangzhou.aliyuncs.com/19163683274.svg",
                title: "核销",
                desc: step3Desc,
            },
            {
                icon: "https://dante-img.oss-cn-hangzhou.aliyuncs.com/19165854762.svg",
                title: "发券",
                desc: step4Desc,
            },
        ];
    }, [detail]);
    const submitView = useMemo(() => {
        let button;
        switch (detail.detailStatus) {
            case StatusEnum.REGISTERED_PENDING_START: // 报名了待开始
                button = <View>活动开始时间：{timestampToTime(detail.activityStartTime, "MM-DD HH:mm")}</View>;
                break;

            case StatusEnum.PENDING_SUBMISSION: // 待提交资料
                button = (
                    <Button
                        className="submit-verification-btn"
                        onClick={() => {
                            DDYNavigateTo({
                                url: `/pages/task/task-submit/index?id=${detail.id}`,
                                events: {
                                    refreshDetail: () => {
                                        getDetail();
                                    },
                                },
                            });
                        }}
                    >
                        提交材料
                    </Button>
                );
                break;
            case StatusEnum.PENDING_REVIEW: // 待审核
                button = (
                    <Button
                        className="submit-verification-btn"
                        onClick={() => {
                            DDYNavigateTo({
                                url: `/pages/task/task-submit-detail/index?id=${detail.activityUserId}`,
                                events: {
                                    refreshDetail: () => {
                                        getDetail();
                                    },
                                },
                            });
                        }}
                    >
                        待审核
                    </Button>
                );
                break;
            case StatusEnum.APPROVED: // 审核通过
                button = (
                    <Button
                        className="submit-verification-btn"
                        onClick={() => {
                            DDYNavigateTo({
                                url: `/pages/task/task-submit-detail/index?id=${detail.activityUserId}`,
                                events: {
                                    refreshDetail: () => {
                                        getDetail();
                                    },
                                },
                            });
                        }}
                    >
                        审核通过
                    </Button>
                );
                break;
            case StatusEnum.REJECTED: // 驳回
                button = (
                    <Button
                        className="submit-verification-btn"
                        onClick={() => {
                            DDYNavigateTo({ url: `/pages/task/task-submit-detail/index?id=${detail.activityUserId}` });
                        }}
                    >
                        驳回
                    </Button>
                );
                break;
            case StatusEnum.ENDED: // 已结束
                button = (
                    <Button
                        className="submit-verification-btn"
                        onClick={() => {
                            DDYToast.info("活动已结束，无法上传");
                        }}
                    >
                        已结束
                    </Button>
                );
                break;
            default:
                <></>;
                break;
        }
        return button;
    }, [detail]);

    useShareAppMessage(callback => {
        return {
            title: detail.activityName,
            path: `/pages/task/task-detail/index?id=${detail.id}`,
            imageUrl: detail.bannerUrl,
        };
    });
    return (
        <View className="task-detail">
            <Dialog_ />
            <NavBar
                title={detail.activityName}
                className={`page-title ${top ? "white" : "transparent"}`}
                fixed
                leftArrow={shareId ? false : !isAlipay()}
                renderLeft={shareId ? <Icon name="wap-home-o" size="28px" /> : null}
                onClickLeft={() => {
                    if (shareId) {
                        DDYSwitchTab({
                            url: "/pages/index/index",
                        });
                    } else {
                        DDYBack();
                    }
                }}
                border={false}
            />
            <View className="header">
                <Image className="header-image" width={"100%"} height={409} src={detail.bannerUrl} />
            </View>

            <View className="content-uui">
                <View className="activity-header">
                    <View className="activity-info">
                        <View className="activity-title">
                            <Icon name={"https://dante-img.oss-cn-hangzhou.aliyuncs.com/32771189702.webp"} />
                            <Text className="title-text">{detail.activityDes}</Text>
                        </View>

                        <Button className="share-btn" openType="share">
                            <View className={"share-text-wrapper"}>
                                <Icon name={"https://dante-img.oss-cn-hangzhou.aliyuncs.com/91998262757.svg"} />
                                <View className={"share-btn-text"}>分享</View>
                            </View>
                        </Button>
                    </View>
                    <View className="activity-meta">
                        <View className="participants">
                            <Icon name="friends" color={"#FF9DB3"} />
                            {detail.signUpNum || 0} 人已参与
                        </View>
                        <Text className="activity-time">
                            活动时间：{timestampToTime(detail.activityStartTime, "MM-DD HH:mm")} -{" "}
                            {timestampToTime(detail.activityEndTime, "MM-DD HH:mm")}
                        </Text>
                    </View>
                    <View className="upload-section">
                        <View className="upload-content">
                            <Text className={"upload-content-text"}>上传核销资料</Text>
                        </View>
                    </View>

                    <View className="verification-info">
                        <View className={"brand-wrapper"}>
                            <View
                                className={`brand-status-logo ${
                                    detail.detailStatus === StatusEnum.PENDING_REVIEW ||
                                    detail.status === StatusEnum.APPROVED
                                        ? "success"
                                        : "normal"
                                }`}
                            />
                            <Image className="brand-logo" width={60} height={60} src={detail.promotionChannelUrl} />
                            <Text className="brand-name">{detail.promotionChannelName}核销资料</Text>
                        </View>
                        <View className={"operate-wrapper"}>
                            {submitView}
                            {/*【未开始】【去提交资料】【待审核】*/}
                            {detail.detailStatus === StatusEnum.REGISTERED_PENDING_START ||
                            detail.detailStatus === StatusEnum.PENDING_SUBMISSION ||
                            detail.detailStatus === StatusEnum.PENDING_REVIEW ? (
                                <Button
                                    className="cancel-sing-up"
                                    onClick={() => {
                                        cancel();
                                    }}
                                >
                                    取消报名
                                </Button>
                            ) : null}
                        </View>
                    </View>
                </View>

                <View className="activity-process">
                    <View>
                        <Icon name={"https://dante-img.oss-cn-hangzhou.aliyuncs.com/17334191994.svg"} />
                        <Text className="process-title">活动流程</Text>
                    </View>

                    <View className="process-steps">
                        <ActivityProcessItem steps={steps} />
                    </View>
                </View>
                <View className="activity-rules">
                    <View>
                        <Icon name={"https://dante-img.oss-cn-hangzhou.aliyuncs.com/19344470765.svg"} />
                        <Text className="rules-title">活动规则</Text>
                    </View>

                    <View className="activity-rule-content">
                        <View>
                            规则介绍：
                            <RichText className={"rule-content-ellipsis"} nodes={detail?.activityRule} />
                        </View>
                    </View>
                    <View className="rewards-rule-content">
                        <View>
                            奖励规则：
                            <View className={"rule-content-ellipsis"}>{detail?.rewardsRule}</View>
                        </View>
                    </View>
                    <View className="check-rule-content">
                        <View>
                            核销规则：
                            <View className={"rule-content-ellipsis"}>{detail?.checkRule}</View>
                        </View>
                    </View>
                    <View className="reward-rule-more">
                        <View className="reward-rule-more-button" onClick={() => setRuleOpen(true)}>
                            查看完整规则
                            <Icon
                                name={"https://dante-img.oss-cn-hangzhou.aliyuncs.com/38590800316.svg"}
                                style={{ marginLeft: Taro.pxTransform(10) }}
                            />
                        </View>
                    </View>
                </View>
                <ActivitySignUpUserList />
                <ActivityRuleModal detail={detail} open={open} onClose={() => setRuleOpen(false)} />
            </View>
        </View>
    );
};
