import { ActionSheet } from "@antmjs/vantui";
import { RichText, View } from "@tarojs/components";
import "./rule-modal.less";

interface RuleModalProps {
    detail: any;
    open: boolean;
    onClose: () => void;
}

export default function ActivityRuleModal({ detail, open, onClose }: RuleModalProps) {
    return (
        <ActionSheet show={open} title="使用规则" onClose={onClose}>
            <View className="activity-rule-modal">
                <View className="activity-rule-content">
                    <View>
                        规则介绍：
                        <View>
                            <RichText nodes={detail?.activityRule} />
                        </View>
                    </View>
                </View>
                <View className="rewards-rule-content">
                    <View>
                        奖励规则：
                        <View>
                            <View>{detail?.rewardsRule}</View>
                        </View>
                    </View>
                </View>
                <View className="check-rule-content">
                    <View>
                        核销规则：
                        <View>{detail?.checkRule}</View>
                    </View>
                </View>
            </View>
        </ActionSheet>
    );
}
