.task-detail {
    min-height: 100%;
    background: #f2f2f2;

    .page-title {
        background: transparent;
    }

    .white {
        --nav-bar-title-text-color: #333;
        --nav-bar-icon-color: #333;
        background-color: #fff;
    }

    .transparent {
        --nav-bar-title-text-color: #333;
        --nav-bar-icon-color: #333;
        background-color: transparent;
    }

    .container {
        display: flex;
        flex-direction: column;
        height: 100%;
    }

    .header {
        position: relative;
        width: 750px;
        height: 400px;
    }

    .header-image {
        width: 100%;
        height: 100%;
    }

    .header-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        display: flex;
        justify-content: space-between;
        padding: 20px;
    }

    .content-uui {
        flex: 1;
    }

    .activity-header {
        background: #fff;
        border-radius: 24rpx;
        transform: translateY(-30rpx);
        padding: 38px;
    }

    .activity-info {
        display: flex;
        justify-content: space-between;
    }

    .activity-title {
        display: flex;
        align-items: center;
    }

    .title-text {
        font-size: 28px;
        font-weight: 300;
        margin-left: 18px;
        width: 450rpx;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
    }

    .activity-meta {
        display: flex;
        justify-content: space-between;
        font-size: 24px;
        margin-top: 46px;
        color: #999999;
    }

    .participants {
        font-weight: 300;
        font-size: 24px;
        color: #ff0940;
        margin-left: 25px;
    }

    .share-btn {
        margin: 0;
        padding: 0;
        width: 135px;
        height: 51px;
        border-radius: 8px;
        border: 1px solid rgba(0, 0, 0, 0.25);

        .share-text-wrapper {
            display: flex;
            align-items: center;
        }

        &-text {
            margin-left: 11rpx;
            font-size: 24px;
        }
    }

    .upload-section {
        margin: 30px auto;
        height: 98px;
        width: 671px;
        background-size: 100% 100%;
        background-image: url("https://dante-img.oss-cn-hangzhou.aliyuncs.com/98634645567.png");
        border-radius: 20px;
    }

    .upload-content {
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 28px;
        color: #ff6b6b;

        &-text {
            margin-top: 25px;
            font-size: 36px;
            color: #ffffff;
            line-height: 43px;
            text-align: left;
            font-style: normal;
            text-shadow: -2px -2px 0 #575879, 2px -2px 0 #575879, -2px 2px 0 #575879, 2px 2px 0 #575879;
        }
    }

    .verification-info {
        display: flex;
        align-items: baseline;
        justify-content: space-between;

        .brand-wrapper {
            flex: 1;
            align-items: center;
            display: flex;

            .brand-status-logo {
                width: 27px;
                height: 27px;
                border-radius: 13.5px;
                background: #f2f2f2;
                &.success {
                    background: #23b85d;
                }
            }

            .brand-logo {
                margin-left: 30px;
                margin-right: 18px;
            }

            .brand-name {
                flex: 1;
                font-size: 28px;
            }
        }
        .operate-wrapper {
            display: flex;
            align-items: flex-end;
            flex-direction: column;
            gap: 20px;
        }

        .submit-verification-btn {
            padding: 0;
            width: 169px;
            height: 54px;
            font-weight: 300;
            font-size: 26px;
            color: #ff0940;
            border-radius: 32px;
            border: 1px solid #ff0940;
        }
        .cancel-sing-up {
            margin: 0;
            padding: 0;
            width: 169px;
            height: 54px;
            font-weight: 300;
            font-size: 26px;
            color: 1px solid rgba(0, 0, 0, 0.25);
            border-radius: 32px;
            border: 1px solid rgba(0, 0, 0, 0.25);
        }
    }

    .activity-process {
    }

    .process-title,
    .rules-title {
        margin-left: 24px;
        font-size: 26px;
        color: #333333;
        line-height: 44px;
    }

    .activity-rules {
        .activity-rule-content,
        .rewards-rule-content,
        .check-rule-content {
            height: 138px;
            margin-top: 21px;
            background: rgba(122, 122, 122, 0.04);
            border-radius: 22px;
            font-weight: 300;
            font-size: 20px;
            color: #999999;
            line-height: 30px;
            padding: 20px 24px;
            word-break: break-all;
            .rule-content-ellipsis {
                height: 125px;
                overflow: hidden;
                text-overflow: ellipsis;
                display: -webkit-box;
                -webkit-line-clamp: 4;
                -webkit-box-orient: vertical;
            }
        }

        .reward-rule-more {
            margin-top: 23px;
            width: 100%;
            text-align: center;

            &-button {
                display: flex;
                justify-content: center;
                align-items: center;
                margin: 0 auto;
                width: 411px;
                height: 54px;
                font-size: 26px;
                background: #ffffff;
                border-radius: 32px;
                line-height: 54px;
                color: #ff0940;
                border: 1px solid #ff0940;
            }
        }
    }

    .activity-rules,
    .activity-process,
    .user-list {
        margin-top: 21px;
        padding: 24px 36px;
        background: #ffffff;
        border-radius: 16px;
    }

    .activity-process {
        margin-top: 0;
    }

    .rules-content {
        font-size: 26px;
        color: #666666;
        line-height: 1.6;
    }
}
