.process-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%; // 宽度填充整个容器
    padding: 20px;
    position: relative;
}

.step {
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    flex: 1; // 让每个步骤均分宽度
    text-align: center;
    margin: 0 5px; // 适当调整间距，防止挤压

    .icon {
        width: 87px;
        height: 87px;
        margin-bottom: 8px;
    }

    .title {
        font-weight: 300;
        font-size: 24px;
        color: #333333;
        line-height: 44px;
        margin-bottom: 4px;
    }

    .desc {
        font-weight: 300;
        font-size: 20px;
        color: #b9b9b9;
        white-space: pre-wrap;
    }

    // 中间连线
    .divider {
        position: absolute;
        top: 43rpx;
        right: 0;
        width: 50rpx;
        height: 2rpx;
        background-color: #ddd;
        transform: translateX(50%);
    }

    &:last-child {
        .divider {
            display: none; // 隐藏最后一个步骤的连线
        }
    }
}
