.task-center {
    min-height: 100%;
    background: linear-gradient(180deg, rgba(255, 207, 207, 0.42) 0%, rgba(253, 135, 135, 0.16) 100%);
    .rule-button {
        display: flex;
        flex-direction: column;
        align-items: center;
        width: 61px;
        text-align: center;
        position: absolute;
        top: 230px;
        z-index: 10;
        left: 0;
        height: 195px;
        background: rgba(255, 255, 255, 0.9);
        box-shadow: 0 2px 4px 0 rgba(91, 91, 91, 0.22);
        border-top-right-radius: 14px;
        border-bottom-right-radius: 14px;

        .text {
            margin-top: 21px;
            writing-mode: vertical-rl; /* 竖直排列，从右到左 */
            text-align: center;
            white-space: nowrap;
            height: 132px;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            font-size: 24px;
            letter-spacing: 6px;
            color: #5e5e5e;
        }
    }

    .banner {
        width: 750px;
        height: 400px;
        background: linear-gradient(180deg, #e8f4ff 0%, #ffffff 100%);
    }

    .banner-img {
        width: 100%;
        height: 100%;
    }

    .activity-header {
        margin-top: 24px;
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
        height: 76px;
        border-radius: 13px;

        &-bg {
            position: absolute;
            width: 713rpx;
            top: -15rpx;
            height: 101rpx;
        }
    }

    .activity-title {
        z-index: 9;
        font-size: 40px;
        color: #fff;
        line-height: 47px;
        text-align: left;
        font-style: normal;
        text-shadow: -2px -2px 0 #575879, 2px -2px 0 #575879, -2px 2px 0 #575879, 2px 2px 0 #575879;
    }

    .activity-card {
        margin: 5px 24px 0;
        border-radius: 16px;
        min-height: 600px;
        background: #fff;

        .activity-chanel {
            display: flex;
            padding: 0 56px;
            justify-content: space-between;
            align-items: center;

            .left-text {
                font-weight: 400;
                font-size: 30px;
                color: #363636;
                line-height: 32px;
                text-align: left;
            }
        }

        .activity-list {
            &-item {
                border-bottom: 1px dashed rgba(0, 0, 0, 0.13);
                margin: 18px 54px 0;
                display: flex;

                .left-view {
                    flex: 1;

                    .title {
                        font-size: 24px;
                        color: #333333;
                        line-height: 40px;
                        display: inline-flex;

                        &-left {
                            max-width: 285px;
                            overflow: hidden;
                            white-space: nowrap;
                            text-overflow: ellipsis;
                        }

                        .amount {
                            color: #ff0940;
                        }
                    }

                    .desc {
                        font-size: 20px;
                        color: #999;
                        line-height: 40px;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        display: -webkit-box;
                        -webkit-line-clamp: 2;
                        -webkit-box-orient: vertical;
                    }
                }

                .right-view {
                    .sign-up-button {
                        &.disabled {
                            opacity: 0.5;
                        }
                        width: 146px;
                        height: 43px;
                        text-align: center;
                        background: linear-gradient(90deg, #ff3b61 0%, #ff0940 100%);
                        border-radius: 100px;

                        .sign-up-button-text {
                            height: 28px;
                            margin: auto 0;
                            font-weight: 400;
                            font-size: 22px;
                            color: #ffffff;
                            line-height: 28px;
                            text-align: center;
                        }
                    }
                }
            }
        }
    }

    .activity-card-fixed {
        margin: 5px 0 0;
    }
}
