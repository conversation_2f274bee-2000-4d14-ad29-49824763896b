import { ActionSheet } from "@antmjs/vantui";
import { RichText, View } from "@tarojs/components";
import "./rule-modal.less";
if (process.env.TARO_ENV !== "h5") {
    require("@tarojs/taro/html.css");
}
interface RuleModalProps {
    detail: any;
    open: boolean;
    onClose: () => void;
}

export default function ActivityRuleModal({ detail, open, onClose }: RuleModalProps) {
    const createMarkup = htmlContent => {
        return { __html: htmlContent };
    };
    return (
        <ActionSheet show={open} title="使用规则" onClose={onClose}>
            <View className="activity-rule-modal">
                <View className="activity-task-rule-content">
                    <View className={"taro_html"} dangerouslySetInnerHTML={createMarkup(detail)}></View>
                </View>
            </View>
        </ActionSheet>
    );
}
