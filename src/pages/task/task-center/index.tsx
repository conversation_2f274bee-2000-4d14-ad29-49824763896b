import { View, Text } from "@tarojs/components";
import { useEffect, useMemo, useState } from "react";
import { Image, Tabs, Tab, Icon } from "@antmjs/vantui";
import { DropdownMenu, DropdownItem } from "@antmjs/vantui";
import { ActivityAppStatus } from "../enum/ActivityAppStatus";
import newApi from "@/utils/newApi";
import "./index.less";
import { DropdownMenuOption } from "@antmjs/vantui/types/dropdown-menu";
import ActivityList from "./activity-list";
import DesignPage from "@/components/desgin";
import Taro from "@tarojs/taro";
import ActivityRuleModal from "./rule-modal";

export default function SubsidyTask() {
    const [status, setStatus] = useState(ActivityAppStatus.ACTIVITY_APP_CREATE);
    const [currentChannel, setCurrentChannel] = useState<string | number>("");
    const [isFixed, setIsFixed] = useState(false);
    const [pageSet, setPageSet] = useState({});
    const [open, setRuleOpen] = useState(false);

    const [typeList, setTypeList] = useState<DropdownMenuOption[]>([]);
    const tabs = [
        {
            name: ActivityAppStatus.ACTIVITY_APP_CREATE,
            title: "待报名",
        },
        {
            name: ActivityAppStatus.ACTIVITY_APP_WAIT,
            title: "待上传",
        },
        {
            name: ActivityAppStatus.ACTIVITY_APP_PASS,
            title: "待核销",
        },
        {
            name: ActivityAppStatus.ACTIVITY_APP_END,
            title: "已结束",
        },
    ];
    useEffect(() => {
        newApi
            .activityChannel({
                method: "POST",
                data: {},
                showError: false,
            })
            .then(res => {
                let list: DropdownMenuOption[] = [{ value: "", text: "全部类型" }];
                list = list.concat(
                    res.map(item => {
                        return {
                            text: item.name,
                            value: item.id,
                        };
                    }),
                );
                setTypeList(list);
            });
    }, []);

    const fixedCardClass = useMemo(() => {
        return isFixed ? "activity-card-fixed" : "";
    }, [isFixed]);
    return (
        <View className="task-center">
            {/* 标题部分 */}
            <View className={"rule-button"} onClick={() => setRuleOpen(true)}>
                <Text className={"text"}>活动规则</Text>
                <Icon name={"https://dante-img.oss-cn-hangzhou.aliyuncs.com/28325258598.svg"} />
            </View>
            <View className="banner">
                <DesignPage
                    pageType={50}
                    onGetPageConfig={(data: any) => {
                        setPageSet(data.pageSet);
                        Taro.setNavigationBarTitle({ title: data?.pageSet?.title || "补贴任务页" });
                    }}
                />
            </View>
            <View className="activity-header">
                <Image
                    className="activity-header-bg"
                    src={"https://dante-img.oss-cn-hangzhou.aliyuncs.com/53921281728.png"}
                />
                <Text className="activity-title">{pageSet.subTitle || "晒单或点赞评论 送福豆福卡"}</Text>
            </View>

            {/* 筛选标签 */}
            <View className={`activity-card ${fixedCardClass}`}>
                <View className={"activity-chanel"}>
                    <View className={"left-text"}>活动类型</View>
                    <DropdownMenu>
                        <DropdownItem
                            options={typeList}
                            title={"全部类型"}
                            onChange={e => {
                                setCurrentChannel(e);
                            }}
                        />
                    </DropdownMenu>
                </View>
                <Tabs
                    ellipsis={false}
                    sticky={true}
                    onChange={e => {
                        setStatus(e.detail.name);
                    }}
                    onScroll={(data: {
                        detail: {
                            scrollTop?: number | null;
                            isFixed?: boolean;
                        };
                    }) => {
                        setIsFixed(data.detail.isFixed || false);
                    }}
                >
                    {tabs.map((item, index) => {
                        return (
                            <Tab title={item.title} key={index} name={item.name}>
                                <ActivityList status={status} tabName={item.name} currentChannel={currentChannel} />
                            </Tab>
                        );
                    })}
                </Tabs>
            </View>
            <ActivityRuleModal detail={pageSet.taskRule} open={open} onClose={() => setRuleOpen(false)} />
        </View>
    );
}
