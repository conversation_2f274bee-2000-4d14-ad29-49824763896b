import { Text, View } from "@tarojs/components";
import { useEffect, useRef, useState } from "react";
import { ActivityAppStatus, ActivityAppStatusButton } from "../enum/ActivityAppStatus";
import { DDYNavigateTo } from "@/utils/route";
import newApi from "@/utils/newApi";
import { InfiniteScroll, InfiniteScrollInstance, InfiniteScrollProps } from "@antmjs/vantui";
import DDYToast from "@/utils/toast";

export default function ActivityList({ tabName, currentChannel }) {
    const [dataSource, setDataSource] = useState([]);
    const infiniteScrollInstance = useRef<InfiniteScrollInstance>();
    const pageNo = useRef(1);

    useEffect(() => {
        infiniteScrollInstance.current?.reset(true);
        return () => {
            pageNo.current = 1;
            setDataSource([]);
        };
    }, [currentChannel]);

    const getList = () => {
        return newApi.activityList({
            method: "POST",
            data: {
                appStatus: tabName,
                promotionChannel: !currentChannel ? undefined : currentChannel,
                currentPage: pageNo.current,
                pageSize: 10,
            },
        });
    };

    const loadMore: InfiniteScrollProps["loadMore"] = async () => {
        return new Promise(async resolve => {
            try {
                const result = await getList();
                const { page, dataList } = result;
                const newData = dataSource.concat(dataList);
                setDataSource(newData);
                const hasMore = page.currentPage < page.totalPage;
                if (hasMore) {
                    pageNo.current = page.currentPage + 1;
                }
                resolve(hasMore ? "loading" : "complete");
            } catch (e) {
                resolve("error");
            }
        });
    };

    function itemOnClick(item) {
        switch (tabName) {
            case ActivityAppStatus.ACTIVITY_APP_CREATE:
                DDYNavigateTo({
                    url: "/pages/task/task-sign-up/index?pageJump=taskCenter&id=" + item.id,
                    events: {
                        refreshTaskList: () => {
                            pageNo.current = 1;
                            setDataSource([]);
                            infiniteScrollInstance.current?.reset(true);
                        },
                    },
                });
                break;
            case ActivityAppStatus.ACTIVITY_APP_WAIT:
                DDYNavigateTo({ url: "/pages/task/task-detail/index?id=" + item.id });
                break;
            case ActivityAppStatus.ACTIVITY_APP_PASS:
                DDYNavigateTo({ url: "/pages/task/task-detail/index?id=" + item.id });
                break;
            case ActivityAppStatus.ACTIVITY_APP_END:
                DDYNavigateTo({ url: "/pages/task/task-detail/index?id=" + item.id });
                break;
            default:
                break;
        }
    }

    return (
        <View className={"activity-list"}>
            {dataSource.map(item => {
                const signUpNumFull = tabName === ActivityAppStatus.ACTIVITY_APP_CREATE && item.signUpNumFull;
                return (
                    <View className="activity-list-item" key={item.id}>
                        <View className="left-view">
                            <View className="title-wrapper">
                                <View className="title">
                                    <View className={"title-left"}>{item.activityName}</View>
                                    <View className="amount">
                                        {item.rewardsType === "GIFT_TYPE" ? `送${item.rewardsNum}福豆` : ""}
                                        {item.rewardsType === "CASH_TYPE" ? `送${item.rewardsNum}元` : ""}
                                        {item.rewardsType === "GIFT_AND_CASH_TYPE"
                                            ? `送${item.rewardsNum}+${item.fortuneCardRewardsNum}元`
                                            : ""}
                                    </View>
                                </View>
                            </View>
                            <Text className="desc">{item.activityTopic}</Text>
                        </View>
                        <View className="right-view">
                            <View className={`sign-up-button ${signUpNumFull ? "disabled" : ""}`}>
                                <Text
                                    className="sign-up-button-text"
                                    onClick={() => {
                                        if (signUpNumFull) {
                                            DDYToast.info("报名已满");
                                            return;
                                        }
                                        itemOnClick(item);
                                    }}
                                >
                                    {signUpNumFull ? "报名已满" : ActivityAppStatusButton[tabName]}
                                </Text>
                            </View>
                        </View>
                    </View>
                );
            })}
            <InfiniteScroll loadMore={loadMore} ref={infiniteScrollInstance} />
        </View>
    );
}
