page,
.page {
    height: 100%;
    background-color: #f1f2f3;
}
.noLogin {
    margin: 200px auto;
    text-align: center;
    .log-text {
        color: #333;
    }
    .log-btn {
        margin-top: 30px;
        width: 300px;
        height: 88px;
        line-height: 88px;
        font-size: 32px;
        color: #fff;
        border-radius: 100px;
        background: linear-gradient(90deg, #ff3b61 0%, #ff0940 100%);
    }
    .log-btn::after {
        border: none;
    }
}
.line {
    width: 8px;
    height: 50px;
    position: absolute;
    // top: 8px;
    left: -10px;
    border-radius: 0 10px 10px 0;
    background: #f1f2f3;
    z-index: 10;
}
.line2 {
    width: 8px;
    height: 50px;
    position: absolute;
    // top: 8px;
    left: 0;
    border-radius: 0 10px 10px 0;
    background: linear-gradient(to right, #f50050, #f5001d);
    z-index: 10;
}
.text-cut {
    font-size: 34px;
    margin-right: 10px;
}
.original-price {
    font-size: 18px;
    color: #888888;
    line-height: 26px;
    margin-left: 10rpx;
    font-family: "D-DIN";
    text-decoration: line-through;
}
.cu-item-left {
    display: flex;
    flex-direction: row;
    margin: 32px 0px 32px 16px;
    position: relative;
    .sell-out {
        width: 154px;
        height: 154px;
        background: rgba(0, 0, 0, 0.4);
        position: absolute;
        top: 0;
        left: 0;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    .sell-out image {
        display: block;
        width: 154px;
        height: 154px;
    }
}
.title {
}

.title2 {
    position: relative;
    color: #f50050;
    // font-size: 32px;
    // font-weight: 500;
    font-size: 31px;
    font-weight: 500;
    height: 40px;
}
// .cu-list.menu-avatar>.cu-item {
//   margin: 10px 0px;
// }
.text-name {
    color: #333;
    font-size: 26px !important;
    font-weight: 400;
    overflow: hidden;
    text-overflow: ellipsis;
    width: 100%;
    display: -webkit-box;
    -webkit-line-clamp: 2; /*要显示的行数*/
    -webkit-box-orient: vertical;
}
.yuan {
    position: relative;
    top: 4px;
    left: 0px;
    width: 14px;
    height: 14px;
    border-radius: 14px;
    background: linear-gradient(to right, #f5001d, #ffeaef);
}
.cu-avatar.lg {
    width: 154px;
    height: 154px;
}
.cu-list.menu-avatar > .cu-item .content {
    left: 195px;
}
.yuan2 {
    position: relative;
    top: 4px;
    left: 0px;
    width: 14px;
    height: 14px;
    border-radius: 14px;
    // background: linear-gradient(to right, #f7f7f7, #f7f7f7);
    border: none;
}
.VerticalNav,
.nav {
    width: 200px;
    white-space: initial;
}

.VerticalNav.nav .cu-item {
    width: 100%;
    text-align: center;
    // background-color: #fff;
    margin: 0;
    border: none;
    height: 80px;
    overflow: hidden;
    padding: 0 10px 0 20px;
    box-sizing: border-box;
    // position: relative;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    position: relative;
}

.VerticalNav.nav .cu-item.cur {
    background-color: #fff;
}
.VerticalNav.nav .cu-item.cur::after {
    content: "";
    width: 8px;
    height: 40px;
    border-radius: 10px 0 0 10px;
    position: absolute;
    background-color: currentColor;
    top: 0;
    right: 0px;
    bottom: 0;
    margin: auto;
}
.verticalBox {
    display: flex;
    height: 100%;
}
.VerticalMain {
    background-color: #f1f1f1;
    width: 550rpx;
    box-sizing: border-box;
}

.container {
    background-color: #f7f7f7;
    position: fixed;
    z-index: 9999;
    width: 100%;
}

.swiper {
    height: 348px;
}

.header {
    position: relative;
}

.avatar {
    position: absolute;
    display: flex;
    bottom: 50px;
    left: 50px;
    height: 150px;
    .avatar_info {
        margin-left: 30px;
        color: white;
        .title {
            margin-top: 20px;
            font-size: 50px;
            color: white;
        }
        .desc {
            margin-top: 10px;
            font-size: 30px;
            color: white;
        }
    }
}
.text-sm {
    font-size: 16px;
    color: #f5001d;
    font-weight: 500;
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    align-items: center;
    // justify-content: space-between;
}
.coupon-box {
    // height: 36px;
    font-size: 24px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #de1600;
    display: inline-block;
    margin-left: 5px;
}
.coupon-box .cash-rebate {
    border-radius: 4px;
    border: 1px solid #ffeff2;
    padding: 0 6px;
    background-color: #ffeff2;
}
.avatar_img {
    height: 150px;
    width: 150px;
}

.store_bgimg {
    width: 100%;
}

.footer_img {
    width: 100%;
}

.share {
    position: fixed;
    bottom: 181px;
    right: 44px;
    width: 75px;
    height: 75px;
    line-height: 100px;
    text-align: center;
    color: #fff;
    border-radius: 50%;
}
.contentBox {
    // width: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding: 0px 32px 0px 24px;
    width: 380rpx;
    box-sizing: border-box;
}
.search_bottom {
    display: flex;
    flex-direction: row;
    overflow: hidden;
    background-color: white;
    height: 90px;
    align-items: center;
    .serch_content {
        display: flex;
        align-items: center;
        border-radius: 10px;
        border: 1px solid #959595;
        color: #333;
        width: 640px;
        margin-left: 24px;
        height: 60px;
        box-sizing: border-box;
        .search_input {
            font-size: 24px;
            width: 100%;
            margin-left: 5px;
            color: #7d7d7d;
        }
    }
    /*.icon-search {
            font-size: 40px;
          }*/
    .search_img {
        width: 40px;
        height: 40px;
        margin-left: 14px;
    }
    .list_grid_btn {
        width: 40px;
        height: 40px;
        margin-left: 20px;
    }
}
.slide-image {
    width: 100%;
}
/*数字下标*/
.broadcast {
    background: #fff;
}

page .wx-swiper-dots.wx-swiper-dots-horizontal {
    margin-bottom: -4px;
}

page .wx-swiper-dot {
    width: 12px;
    display: inline-flex;
    height: 12px;
    vertical-align: sub;
}

page .wx-swiper-dot::before {
    content: "";
    background: white;
    border-radius: 6px;
    flex-grow: 1;
}

page .wx-swiper-dot-active::before {
    content: "";
    background: #f51214;
    flex-grow: 1;
    border-radius: 6px;
}

.bottom_scroll {
    position: relative;
    margin-top: 0px;
    height: 96px;
    width: 100%;
    white-space: nowrap;
    background-color: white;
}
.single_cat {
    display: inline-block;
    height: 100%;
    margin-left: 14px;
}
.inner_single {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}
.word_txt {
    font-size: 24px;
    width: 100%;
    padding-left: 10px;
    padding-right: 10px;
    height: 50px;
    border-radius: 13px;

    display: flex;
    align-items: center;
    justify-content: center;
}
.cat_chk {
    color: #333333;
    background-color: white;
    border: 1px solid #333333;
}
.cat_default {
    color: #7d7d7d;
    background-color: #ececec;
}
.top {
    position: fixed;
    bottom: 74px;
    right: 44px;
    image {
        width: 75px;
        height: 75px;
    }
}
.topnav {
    position: fixed;
    top: 90px;
    z-index: 99;
    background: #fff;
    width: 100%;
}
/*--授权开始--*/
.white_content {
    position: absolute;
    top: 25%;
    left: 15%;
    width: 70%;
    height: 200px;
    padding: 6px 6px;
    background-color: white;
    border-radius: 2px;
    z-index: 1002;
    overflow: auto;
}
.black_overlay {
    position: absolute;
    top: 0%;
    left: 0%;
    width: 100%;
    height: 100%;
    background-color: #808080;
    z-index: 1001;
    -moz-opacity: 0.8;
    opacity: 0.8;
    filter: alpha(opacity=80);
}
.agree {
    font-size: 36px;
    color: #1aad19;
}
.modelBody {
    height: 175px;
    padding-left: 4px;
}
.modelTitle {
    height: 50px;
    text-align: center;
    vertical-align: middle;
    line-height: 50px;
    font-size: 36px;
    font-weight: bold;
}
.label-box {
    margin-right: 10px;
    display: inline-block;
    .label-item {
        border: 1rpx solid #f50e0c;
        border-radius: 6px;
        padding: 0 4px;
        // height: 36px;
        font-size: 24px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #f50e0c;
        display: inline-block;
    }
    .label-item-limit {
        border: 1rpx solid #f50e0c;
        background: #f50e0c;
        color: #fff;
        padding: 0 4px;
        border-radius: 6px;
        // height: 36px;
        font-size: 24px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        display: inline-block;
    }
}

.tree-select-content {
    width: 580px;
    box-sizing: border-box;
}
