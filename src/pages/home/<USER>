import React, { useState, useEffect, useRef } from "react";
import { ScrollView, View, Button } from "@tarojs/components";
import "./index.less";
import api from "@/utils/api";
import { getStorage } from "@/utils/storage";
import { IS_LOGIN, STORE_ID, SUB_STORE_ID } from "@/utils/constant";
import { DDYNavigateTo } from "@/utils/route";
import { useDidShow } from "@tarojs/taro";
import GoodClassItem from "@/components/good-class-item";

export default () => {
    const [typeList, setTypeList] = useState<{ id: number; name: string }[]>([]);
    const [tabcurrent, setTabcurrent] = useState<number | string>("");
    const [prodList, setProdList] = useState<any[]>([]);
    const [navTop, setNavTop] = useState(0);
    const navigation = useRef({ pageNo: 1, pageSize: 20, totalPage: 0 });

    const onReachBottom = () => {
        // 大坑只能查两页的数据if (this.showLoading) {return;}//上一个网络请求还没回来，不允许加载
        if (navigation.current.pageNo > navigation.current.totalPage) {
            return;
        } //表示最后一页了，也不允许加载
        // 如果总页数只有1页时，则不查第二页的数据，避免请求两次查询
        if (navigation.current.totalPage != 1) {
            getHomeData(false, tabcurrent);
        }
    };

    const selectType = (item: any) => {
        setNavTop((item.id - 1) * 50);
        setTabcurrent(item.id);
        getHomeData(true, item.id);
    };

    const getHomeData = (refresh: boolean, shopCatId: number | string) => {
        const storeId = getStorage(STORE_ID);
        if (refresh) {
            navigation.current.pageNo = 1;
        } else {
            navigation.current.pageNo++;
        }
        const params = {
            shopId: storeId,
            // q: keyword|| '',
            pageNo: navigation.current.pageNo,
            pageSize: navigation.current.pageSize,
            shopCatId: shopCatId === 0 ? "" : shopCatId,
        };
        if (getStorage(SUB_STORE_ID) > 0) {
            //@ts-ignore
            params.ssid = getStorage(SUB_STORE_ID);
        }

        api.getSubStoreGoodsList({
            data: params,
            showLoad: false,
            showError: false,
        })
            .then(res => {
                // 搜索时清空商品列表，要不会重复显示，但搜不到商品时要显示全部商品不清空
                if (refresh) {
                    setProdList(res.list);
                    navigation.current.totalPage = Math.ceil(res.total / navigation.current.pageSize);
                } else {
                    const result = prodList.concat(res.list);
                    setProdList(result);
                }
            })
            .catch(err => {});
    };

    const getcategory = async () => {
        return api
            .getcategory({
                data: {
                    shopId: getStorage(STORE_ID),
                    subStoreId: getStorage(SUB_STORE_ID),
                },
                showError: false,
            })
            .then(res => {
                setTypeList(res);
            });
    };

    useDidShow(async () => {
        await getcategory();
        await getHomeData(true, tabcurrent);
    });

    return (
        <View className="page">
            <View className="verticalBox">
                <ScrollView
                    className="VerticalNav nav"
                    scroll-y
                    scroll-with-animation
                    scroll-top={navTop}
                    style={{ height: "100%", background: "#F1F2F3" }}
                >
                    {typeList.map((item, index) => {
                        return (
                            <View
                                style={{ background: item.id == tabcurrent ? "#fff" : "" }}
                                className="cu-item"
                                key={index}
                                onClick={() => {
                                    selectType(item);
                                }}
                            >
                                <View className={item.id == tabcurrent ? "line2" : "line"}></View>
                                <View className={item.id == tabcurrent ? "title2" : "title"}>{item.name}</View>
                            </View>
                        );
                    })}
                </ScrollView>
                <ScrollView
                    className="VerticalMain"
                    scroll-y
                    scroll-with-animation
                    style="height:100vh;background:#fff"
                    scroll-into-View={`main-${navTop}`}
                    onScrollToLower={onReachBottom}
                >
                    {prodList.map((item, index) => {
                        return <GoodClassItem {...item} />;
                    })}
                    <View style={{ height: "1rpx" }} />
                </ScrollView>
            </View>
        </View>
    );
};
