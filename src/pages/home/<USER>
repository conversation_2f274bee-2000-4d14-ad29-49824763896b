/**
 * @description 商品分类
 */
import GoodClassItem from "@/components/good-class-item";
import api from "@/utils/api";
import { STORE_ID, SUB_STORE_ID } from "@/utils/constant";
import newAPi from "@/utils/newApi";
import { getStorage } from "@/utils/storage";
import { Search, Toast } from "@antmjs/vantui";
import { ScrollView, View } from "@tarojs/components";
import { useDidShow } from "@tarojs/taro";
import { useEffect, useRef, useState } from "react";
import "./index.less";
import Taro from "@tarojs/taro";

export default () => {
    const [typeList, setTypeList] = useState<{ id: number; name: string }[]>([]);
    const [tabcurrent, setTabcurrent] = useState<number | string>("");
    const [prodList, setProdList] = useState<any[]>([]);
    const [navTop, setNavTop] = useState(0);
    const navigation = useRef({ pageNo: 1, pageSize: 20, totalPage: 0 });
    const [value, setValue] = useState("");
    const [scrollHeight, setScrollHeight] = useState("100%");
    const onSearch = e => {
        setValue(e.detail);
        getHomeData(true, tabcurrent, e.detail);
    };

    const searchAction = () => {
        getHomeData(true, tabcurrent, value);
    };

    const onReachBottom = () => {
        console.log("onReachBottom:");
        // 大坑只能查两页的数据if (this.showLoading) {return;}//上一个网络请求还没回来，不允许加载
        if (navigation.current.pageNo > navigation.current.totalPage) {
            return;
        } //表示最后一页了，也不允许加载
        // 如果总页数只有1页时，则不查第二页的数据，避免请求两次查询
        if (navigation.current.totalPage != 1) {
            getHomeData(false, tabcurrent, value);
        }
    };

    const selectType = (item: any) => {
        setNavTop((item.id - 1) * 50);
        setTabcurrent(item.id);
        getHomeData(true, item.id, value);
    };

    const getHomeData = (refresh: boolean, shopCatId: number | string, value: string | undefined) => {
        const storeId = getStorage(STORE_ID);
        if (refresh) {
            navigation.current.pageNo = 1;
        } else {
            navigation.current.pageNo++;
        }
        const params = {
            shopId: storeId,
            // q: keyword|| '',
            pageNo: navigation.current.pageNo,
            pageSize: navigation.current.pageSize,
            shopCatId: shopCatId === 0 ? "" : shopCatId,
            calculateDiscount: true,
            keyword: value,
        };
        if (getStorage(SUB_STORE_ID) > 0) {
            //@ts-ignore
            params.ssid = getStorage(SUB_STORE_ID);
        }

        newAPi
            .getCartGoods({
                data: params,
                method: "POST",
                showLoad: false,
                showError: false,
            })
            .then(res => {
                // 搜索时清空商品列表，要不会重复显示，但搜不到商品时要显示全部商品不清空
                if (refresh) {
                    setProdList(res.list);
                    navigation.current.totalPage = Math.ceil(res.total / navigation.current.pageSize);
                } else {
                    const result = prodList.concat(res.list);
                    setProdList(result);
                }
            })
            .catch(err => {});
    };

    const getcategory = async () => {
        return api
            .getcategory({
                data: {
                    shopId: getStorage(STORE_ID),
                    subStoreId: getStorage(SUB_STORE_ID),
                },
                showError: false,
            })
            .then(res => {
                setTypeList(res);
            });
    };

    useDidShow(async () => {
        await getcategory();
        await getHomeData(true, tabcurrent, value);
    });

    const seachHandle = (e: string) => {
        setValue(e);
        if (e === "") {
            getHomeData(true, tabcurrent, value);
        }
    };

    useEffect(() => {
        const query = Taro.createSelectorQuery();
        query
            .select("#scroll-rect")
            .boundingClientRect(rect => {
                if (rect) {
                    console.log("rect:", rect);
                    // setIsOverflow(rect.height > maxHeight);
                    // setShow(rect.height < maxHeight);
                    setScrollHeight(rect.height + "px");
                }
            })
            .exec();
    }, []);

    return (
        <View className="home">
            <Search
                onChange={e => {
                    seachHandle(e.detail);
                }}
                value={value}
                placeholder="请输入搜索关键词"
                onSearch={onSearch}
                renderAction={<View onClick={searchAction}>搜索</View>}
                className="home-search"
                background="#F8F8F8"
                clearable
            />
            {/* <Toast /> */}
            <View className="verticalBox">
                <View className="left">
                    <ScrollView className="scroll-container-left" scroll-y scroll-with-animation scroll-top={navTop}>
                        {typeList.map((item, index) => {
                            return (
                                <View
                                    style={{ background: item.id == tabcurrent ? "#fff" : "" }}
                                    className="cu-item"
                                    key={index}
                                    onClick={() => {
                                        selectType(item);
                                        setValue("");
                                    }}
                                >
                                    {/* <View className={item.id == tabcurrent ? "line2" : "line"}></View> */}
                                    <View className={item.id == tabcurrent ? "title2 line2" : "title line"}>
                                        {item.name}
                                    </View>
                                </View>
                            );
                        })}
                    </ScrollView>
                </View>
                <View className="right" id="scroll-rect">
                    <ScrollView
                        className="scroll-container-right"
                        scroll-y
                        scroll-with-animation
                        scroll-into-View={`main-${navTop}`}
                        style={{ height: scrollHeight }}
                        onScrollToLower={onReachBottom}
                        onScrollEnd={() => {
                            console.log("onScrollEnd");
                        }}
                    >
                        {prodList.map((item, index) => {
                            return <GoodClassItem {...item} />;
                        })}
                        <View style={{ height: "1rpx" }} />
                    </ScrollView>
                </View>
            </View>
        </View>
    );
};
