/**
 * @description: 优惠券 demo
 * @author: wangchunting
 */
import { DDYNavigateTo } from "@/utils/route";
import { Button, View } from "@tarojs/components";
import { useState } from "react";
import Coupon from "./coupon";
import GiftVoucher from "./giftVoucher";
import "./index.less";

export default function Index() {
    const [showPopup, setShowPopup] = useState(false);
    const [showGiftVoucher, setShowGiftVoucher] = useState(false);
    return (
        <View className="page">
            <Button onClick={() => setShowGiftVoucher(true)}>显示福豆弹窗</Button>
            {showGiftVoucher && <GiftVoucher visible={showGiftVoucher} onClose={() => setShowGiftVoucher(false)} />}

            <Button
                onClick={() => {
                    setShowPopup(true);
                    DDYNavigateTo({ url: "/pages/index/index" });
                }}
            >
                显示优惠券弹窗
            </Button>
            {showPopup && <Coupon visible={showPopup} onClose={() => setShowPopup(false)} />}
        </View>
    );
}
