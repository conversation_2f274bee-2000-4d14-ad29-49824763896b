/**
 * @description: 福豆券
 * @author: wangchunting
 */
import { DDYNavigateTo } from "@/utils/route";
import { Popup } from "@antmjs/vantui";
import { ScrollView, Text, View } from "@tarojs/components";
import { formatAmountUtil } from "../account-balance/fn/formatAmountUtil";

import "./index.less";

interface VouchersProps {
    dataList?: any;
    totalAmount?: number;
    visible: boolean;
    onClose: () => void;
}

function Vouchers({ dataList, totalAmount, visible, onClose }: VouchersProps) {
    return (
        <Popup show={visible} closeable onClose={() => onClose()} className="wallet-card-container">
            <View>
                {/* 头部 */}
                <View className="header">
                    <Text>恭喜余额入账</Text>
                    <View className="amount">
                        {totalAmount?.toFixed(2)}
                        <Text className="unit">元</Text>
                    </View>
                </View>

                {/* 可滚动的优惠券区域 */}
                <ScrollView className="coupon-list" scrollY>
                    {dataList.map((coupon, index) => (
                        <View key={index} className="coupon-item">
                            <View className="coupon-info">
                                <Text className="coupon-title">{coupon?.activityName}</Text>
                                <View className="coupon-time">{coupon?.activityStartTimeDesc}</View>
                            </View>
                            <Text className="coupon-amount">
                                {/* +{coupon?.amount}元 */}+{formatAmountUtil(coupon?.amountType, coupon?.amount)}
                            </Text>
                        </View>
                    ))}
                </ScrollView>

                {/* 按钮 */}
                <View className="button" onClick={onClose}>
                    开心收下
                </View>

                {/* 底部文字 */}
                <View className="footer">
                    <Text onClick={() => DDYNavigateTo({ url: "/pages/account-balance/index" })}>
                        已存入余额账户，立即查看
                    </Text>
                    <Text className="arrow">＞</Text>
                </View>
            </View>
        </Popup>
    );
}

export default Vouchers;
