/**
 *
 * @param 优惠券领取规则展示
 * @returns
 */
/**
 *
 * @param coupons 券数据
 * @param popup 弹窗
 * @returns
 */
export function optimizeCoupons(coupons, popup?: boolean) {
    return coupons.map(item => {
        let amountDesc = "";
        let methodDesc = "";

        switch (item.method) {
            case "DISCOUNT":
                amountDesc = `${item.discount}折`;
                methodDesc = "折扣";
                break;
            case "DIRECT_REDUCTION":
                amountDesc = `￥${item.deductionAmount}`;
                methodDesc = `立减${popup ? item.deductionAmount : ""}`;
                break;
            case "FULL_REDUCTION":
                amountDesc = `￥${item.thresholdDeductionAmount}`;
                methodDesc = `满${item.thresholdAmount}减${popup ? item.thresholdDeductionAmount : ""}`;
                break;
            default:
                break;
        }

        return {
            ...item,
            amountDesc,
            methodDesc,
        };
    });
}
