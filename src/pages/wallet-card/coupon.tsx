/**
 * @description: 优惠券
 * @author: wangchunting
 */
import { DDYNavigateTo } from "@/utils/route";
import { Popup } from "@antmjs/vantui";
import { ScrollView, Text, View } from "@tarojs/components";
import "./index.less";

interface VouchersProps {
    dataList?: any;
    visible: boolean;
    onClose: () => void;
}
function Vouchers({ dataList, visible, onClose }: VouchersProps) {
    return (
        <Popup show={visible} closeable onClose={() => onClose()} className="wallet-card-container">
            <View>
                {/* 头部 */}
                <View className="header">
                    <Text>{dataList.length > 1 ? "恭喜多重优惠券入账" : "恭喜优惠券入账"}</Text>
                </View>

                {/* 可滚动的优惠券区域 */}
                <ScrollView className="coupon-list" scrollY>
                    {dataList.map((coupon, index) => (
                        <View key={index} className="coupon-item">
                            <View className="coupon-info">
                                <Text className="coupon-title">{coupon?.activityTypeDesc}</Text>
                                <View className="coupon-time">{coupon?.methodDesc}</View>
                            </View>
                            <Text className="coupon-amount">{coupon?.amountDesc}</Text>
                        </View>
                    ))}
                </ScrollView>

                {/* 按钮 */}
                <View className="button" onClick={onClose}>
                    开心收下
                </View>

                {/* 底部文字 */}
                <View className="footer">
                    <Text onClick={() => DDYNavigateTo({ url: "/pages/coupon/index" })}>
                        已存入我的优惠券中，立即查看
                    </Text>
                    <Text className="arrow">＞</Text>
                </View>
            </View>
        </Popup>
    );
}

export default Vouchers;
