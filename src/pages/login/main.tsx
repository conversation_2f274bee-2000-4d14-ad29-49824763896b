/**
 * @description 登录
 */
import { loginSuccessRediction, useSafeRouter } from "@/utils/common";
import { LOGIN_BACK_URL, USER_INFO } from "@/utils/constant";
import { PROJECT_CONFIG } from "@/utils/env";
import newAPi from "@/utils/newApi";
import { DDYNavigateTo } from "@/utils/route";
import { getStorage, setStorage } from "@/utils/storage";
import DDYToast from "@/utils/toast";
import { Button, Checkbox, Image, Popup } from "@antmjs/vantui";
import { View } from "@tarojs/components";
import Taro, { useRouter } from "@tarojs/taro";
import React, { useState } from "react";
import "./main.less";
import RegisterSelectModal from "./register-select-modal";
import { setSession } from "@/utils/server/session";
import useCheckedAgreement from "./useCheckedAgreement";

const App: React.FC = () => {
    const { activityId, marketingToolId, groupId } = useSafeRouter().params;
    // const [isChecked, setIsChecked] = useState(false);
    const [show, setShow] = useState(false);
    const { isChecked, node } = useCheckedAgreement();
    const loginPhone = e => {
        const { detail } = e;
        console.log("e:", e);
        if (!isChecked) {
            DDYToast.info("请阅读用户注册协议和隐私保护");
            return;
        }
        Taro.login().then(res => {
            if (res.code) {
                newAPi
                    .loginPhone({
                        method: "POST",
                        data: {
                            wxCode: res.code,
                            phone: detail.code,
                            encryptedData: detail.encryptedData,
                            cloudID: detail.cloudID,
                            iv: detail.iv,
                            activityId: activityId || "",
                            marketingToolId: marketingToolId || "",
                            groupId: groupId || "",
                        },
                    })
                    .then(data => {
                        console.log(data);
                        DDYToast.info("登录成功");
                        setSession(data);
                        setStorage(USER_INFO, data);
                        const url = getStorage(LOGIN_BACK_URL);
                        if (!url && !data.inGroup && data.isJumpAllowed) {
                            setStorage(LOGIN_BACK_URL, `pages/wool-herd/index?join=false`);
                        }
                        setTimeout(() => {
                            loginSuccessRediction();
                        }, 1000);
                    });
            }
        });
    };
    return (
        <View className="main-container">
            <View className="main-content">
                {/* Logo区域 */}
                <View className="mascot-box">
                    <Image radius={80} width={160} height={160} src={PROJECT_CONFIG.LOGIN_LOGO} />
                </View>

                {/* 登录选项 */}
                <View className="login-options">
                    <Button className="button-phone" openType="getPhoneNumber" onGetPhoneNumber={loginPhone}>
                        手机号码登录
                    </Button>
                    <Button
                        className="button-code"
                        onClick={() => {
                            DDYNavigateTo({
                                url: `/pages/login/login?activityId=${activityId}&marketingToolId=${marketingToolId}&groupId=${groupId}`,
                            });
                        }}
                    >
                        验证码登录
                    </Button>
                </View>

                {/* 用户协议 */}
                {node}

                {/* 底部链接 */}
                <View className="bottom-links">
                    <View
                        className="link-item"
                        onClick={() => {
                            DDYNavigateTo({
                                url: `/pages/login/login-psw?activityId=${activityId}&marketingToolId=${marketingToolId}&groupId=${groupId}`,
                            });
                        }}
                    >
                        密码登录
                    </View>
                    <View
                        className="link-item"
                        onClick={() => {
                            setShow(true);
                        }}
                    >
                        立即注册
                    </View>
                </View>
            </View>
            <RegisterSelectModal
                activityId={activityId}
                marketingToolId={marketingToolId}
                groupId={groupId}
                show={show}
                closeFn={() => {
                    setShow(false);
                }}
            />
        </View>
    );
};

export default App;
