import { IS_LOGIN, USER_INFO, USER_SPECICAL_INFO } from "@/utils/constant";
import newAPi from "@/utils/newApi";
import { DDYRedirectTo, DDYReLaunch } from "@/utils/route";
import { setStorage } from "@/utils/storage";
import DDYToast from "@/utils/toast";
import { Field, Icon } from "@antmjs/vantui";
import { Button, Text, View } from "@tarojs/components";
import Taro, { useRouter } from "@tarojs/taro";
import { useEffect, useState } from "react";
import "./index.less";

definePageConfig({
    navigationBarTitleText: "重置密码",
});
export default () => {
    const router = useRouter();
    const { title, phone } = router.params;
    const [phoneNumber, setPhoneNumber] = useState("");
    const [verificationCode, setVerificationCode] = useState("");
    const [countdown, setCountdown] = useState(0);
    const [password, setPassword] = useState("");
    const [newPassword, setNewPassword] = useState("");
    const [showPassword, setShowPassword] = useState(false);
    const [showNewPassword, setShowNewPassword] = useState(false);
    useEffect(() => {
        if (title) {
            Taro.setNavigationBarTitle({ title: title });
        } else {
            Taro.setNavigationBarTitle({ title: "忘记密码" });
        }
        if (phone) {
            const encryptedNumber = phone.slice(0, 3) + "****" + phone.slice(7);
            setPhoneNumber(encryptedNumber);
        }
    }, []);
    const handleGetVerificationCode = () => {
        if (!/^1[3-9]\d{9}$/.test(phone || phoneNumber)) {
            DDYToast.info("请输入正确的手机号");
            return;
        }
        newAPi
            .sendUpdateMsg({
                method: "POST",
                data: {
                    phone: phone || phoneNumber,
                },
            })
            .then(res => {
                if (countdown > 0) return;
                setCountdown(60);
                const timer = setInterval(() => {
                    setCountdown(prev => {
                        if (prev <= 1) {
                            clearInterval(timer);
                            return 0;
                        }
                        return prev - 1;
                    });
                }, 1000);
            });
    };

    const update = () => {
        if (!/^1[3-9]\d{9}$/.test(phone || phoneNumber)) {
            DDYToast.info("请输入正确的手机号");
            return;
        }
        if (!password) {
            DDYToast.info("请输入新密码");
            return;
        }
        if (!newPassword) {
            DDYToast.info("请输入确认密码");
            return;
        }
        if (verificationCode.length !== 6) {
            DDYToast.info("请输入6位验证码");
            return;
        }
        if (password !== newPassword) {
            DDYToast.info("2次密码输入不相同");
            return;
        }
        newAPi
            .updatePassword({
                method: "POST",
                data: {
                    phone: phone || phoneNumber,
                    smsCode: verificationCode,
                    oldPassword: password,
                    newPassword: newPassword,
                },
            })
            .then(res => {
                DDYToast.info("修改成功，请重新登录");
                setStorage(IS_LOGIN, 0);
                setStorage(USER_SPECICAL_INFO, "");
                setStorage(USER_INFO, "");
                setTimeout(() => {
                    DDYReLaunch({
                        url: "/pages/login/login-psw",
                    });
                }, 1000);
            });
    };

    const togglePassword = () => {
        setShowPassword(!showPassword);
    };
    const toggleNewPassword = () => {
        setShowNewPassword(!showNewPassword);
    };

    return (
        <View className="login-warp">
            <View className="login-content">
                <View className="form-container">
                    <View className="form-item">
                        <View className="label">手机号</View>
                        <View>
                            <Field
                                maxlength={11}
                                placeholder="请输入手机号"
                                disabled={!!phone}
                                value={phoneNumber}
                                onChange={e => setPhoneNumber(e.detail)}
                                className="input"
                            />
                        </View>
                    </View>

                    <View className="form-item">
                        <View className="label">验证码</View>
                        <View className="verify-container">
                            <Field
                                maxlength={6}
                                placeholder="请输入验证码"
                                value={verificationCode}
                                className="input"
                                onChange={e => setVerificationCode(e.detail)}
                                renderButton={
                                    <Button
                                        onClick={handleGetVerificationCode}
                                        disabled={countdown > 0}
                                        className="verify-button"
                                    >
                                        {countdown > 0 ? `${countdown}s后重试` : "获取验证码"}
                                    </Button>
                                }
                            />
                        </View>
                    </View>

                    <View className="form-item">
                        <View className="label">新密码</View>
                        <View className="password-input">
                            <Field
                                placeholder="请输入新密码"
                                value={password}
                                onChange={e => {
                                    console.log("e.detail:", e.detail);
                                    setPassword(e.detail);
                                }}
                                className="input"
                                type={showPassword ? "text" : "password"}
                                renderButton={
                                    <View onClick={togglePassword} className="password-icon">
                                        <Icon name={showPassword ? "eye-o" : "closed-eye"} size={42} />
                                    </View>
                                }
                            />
                        </View>
                    </View>

                    <View className="form-item">
                        <View className="label">确认密码</View>
                        <View className="password-input">
                            <Field
                                placeholder="请再次输入新密码"
                                value={newPassword}
                                onChange={e => setNewPassword(e.detail)}
                                className="input"
                                type={showNewPassword ? "text" : "password"}
                                renderButton={
                                    <View onClick={toggleNewPassword} className="password-icon">
                                        <Icon name={showNewPassword ? "eye-o" : "closed-eye"} size={42} />
                                    </View>
                                }
                            />
                        </View>
                    </View>

                    <Button
                        className="submit-button"
                        onClick={() => {
                            update();
                        }}
                    >
                        确认重置
                    </Button>
                </View>

                {/* Bottom Links */}
                <View className="bottom-links">
                    <Text></Text>
                </View>
            </View>
        </View>
    );
};
