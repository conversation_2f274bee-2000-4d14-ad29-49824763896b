.main-container {
    width: 100%;
    min-height: 100vh;
    background-color: #ffffff;
    padding: 32px;
    box-sizing: border-box;

    .main-content {
        display: flex;
        flex-direction: column;
        align-items: center;
        height: 100%;
    }

    .mascot-box {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 24px;
        margin-top: 60px;
    }

    .login-options {
        width: 100%;
        display: flex;
        flex-direction: column;
        gap: 32px;
        margin-top: 100px;
    }

    .button-phone {
        display: flex;
        align-items: center;
        justify-content: center;
        background: linear-gradient(90deg, #ff3b61 0%, #ff0940 100%);
        color: #ffffff;
        font-weight: 500;
        border-radius: 48px;
        width: 100%;
        margin-bottom: 12px;
    }

    .button-code {
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #f5f5f5;
        color: #888;
        border-radius: 48px;
        width: 100%;
    }

    .button-text {
        margin-left: 16px;
    }

    .agreement {
        margin-top: 48px;
        display: flex;
        align-items: flex-start;
        flex-direction: row;
        color: #888;
    }

    .checkbox {
        width: 32px;
        height: 32px;
        border: 1px solid #dddddd;
        border-radius: 4px;
        margin-right: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .checkbox-inner {
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .agreement-text {
        flex: 1;
        display: flex;
        flex-direction: row;
    }

    .agreement-desc {
        font-size: 24px;
        color: #888;
    }

    .link {
        font-size: 24px;
        color: #ff4d4f;
    }

    .bottom-links {
        color: #888;
        position: fixed;
        bottom: 64px;
        left: 0;
        width: 100%;
        padding: 0 48px;
        display: flex;
        align-items: center;
        justify-content: center;
        box-sizing: border-box;
    }

    .link-item {
        display: flex;
        align-items: center;
        flex-direction: row;
        padding: 0 60px;

        &:last-child {
            border-left: 1px solid #dddddd;
        }
    }

    .link-text {
        font-size: 28px;
        color: #888;
        margin-left: 8px;
    }

    .highlight .link-text {
        color: #ff4d4f;
    }

    .pop-rect {
        width: 88%;
        box-sizing: border-box;
        padding: 48px 32px;

        .pop-title {
            font-weight: 500;
            font-size: 38px;
            color: #333;
            text-align: center;
        }

        .pop-btn {
            text-align: center;
            padding: 16px;
            color: #ffffff;
            border-radius: 48px;
            margin: 36px 0;

            &.red {
                // background: linear-gradient(90deg, #ff3b61 0%, #ff0940 100%);
                border: 1px solid #ff0940;
                color: #ff0940;
            }

            &.pink {
                color: #ff0940;
                background: rgba(255, 0, 0, 0.12);
            }
        }

        .pop-slogo {
            font-size: 24px;
            color: #ff0940;
            padding-top: 24px;
            text-align: center;
        }
    }
}
