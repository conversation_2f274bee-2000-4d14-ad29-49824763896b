/**
 * @description 验证码登录
 */
import { loginSuccessRediction, useSafeRouter } from "@/utils/common";
import { LOGIN_BACK_URL, USER_INFO, USER_SPECICAL_INFO } from "@/utils/constant";
import newAPi from "@/utils/newApi";
import { DDYNavigateTo } from "@/utils/route";
import { getStorage, setStorage } from "@/utils/storage";
import DDYToast from "@/utils/toast";
import { Checkbox, Field } from "@antmjs/vantui";
import { Button, View } from "@tarojs/components";
import Taro, { useRouter } from "@tarojs/taro";
import { useState } from "react";
import "./index.less";
import RegisterSelectModal from "./register-select-modal";
import useCheckedAgreement from "./useCheckedAgreement";

definePageConfig({
    navigationBarTitleText: "验证码登录",
});

export default () => {
    const { activityId, marketingToolId, groupId } = useSafeRouter().params;
    const [phoneNumber, setPhoneNumber] = useState("");
    const [verificationCode, setVerificationCode] = useState("");
    const [countdown, setCountdown] = useState(0);
    const [show, setShow] = useState(false);
    // const [isChecked, setIsChecked] = useState(false);
    const { isChecked, node } = useCheckedAgreement();
    const handleGetVerificationCode = () => {
        if (!/^1[3-9]\d{9}$/.test(phoneNumber)) {
            DDYToast.info("请输入正确的手机号");
            return;
        }
        newAPi
            .sendMsg({
                method: "POST",
                data: {
                    phone: phoneNumber,
                },
            })
            .then(res => {
                if (countdown > 0) return;
                setCountdown(60);
                const timer = setInterval(() => {
                    setCountdown(prev => {
                        if (prev <= 1) {
                            clearInterval(timer);
                            return 0;
                        }
                        return prev - 1;
                    });
                }, 1000);
            });
    };

    const loginBtn = () => {
        if (!isChecked) {
            DDYToast.info("请阅读用户注册协议和隐私保护");
            return;
        }
        Taro.login().then(res => {
            newAPi
                .loginPswOrVerify({
                    method: "POST",
                    data: {
                        wxCode: res.code,
                        phone: phoneNumber,
                        authCode: verificationCode,
                        loginType: "sms",
                        activityId: activityId || "",
                        marketingToolId: marketingToolId || "",
                        groupId: groupId || "",
                    },
                })
                .then(data => {
                    DDYToast.success("登录成功");
                    setStorage(USER_INFO, data);
                    const url = getStorage(LOGIN_BACK_URL);
                    if (!url && !data.inGroup && data.isJumpAllowed) {
                        setStorage(LOGIN_BACK_URL, `pages/wool-herd/index?join=false`);
                    }
                    // DDYRedirectTo()
                    setTimeout(() => {
                        loginSuccessRediction();
                    }, 500);
                });
        });
    };

    return (
        <View className="login-warp">
            <View className="login-content">
                <View className="form-item">
                    <View className="label">手机号</View>
                    <View>
                        <Field
                            maxlength={11}
                            placeholder="请输入手机号"
                            value={phoneNumber}
                            onChange={e => setPhoneNumber(e.detail)}
                            className="input"
                        />
                    </View>
                </View>

                <View className="form-item">
                    <View className="label">验证码</View>
                    <View className="verify-containerr">
                        <Field
                            value={verificationCode}
                            onChange={e => setVerificationCode(e.detail)}
                            className="input"
                            placeholder="请输入验证码"
                            renderButton={
                                <Button
                                    onClick={handleGetVerificationCode}
                                    disabled={countdown > 0}
                                    className="verify-button"
                                >
                                    {countdown > 0 ? `${countdown}s` : "获取验证码"}
                                </Button>
                            }
                        />
                    </View>
                </View>

                {/* 用户协议 */}
                {node}
                <Button
                    className="submit-button"
                    onClick={() => {
                        loginBtn();
                    }}
                >
                    登 录
                </Button>
            </View>

            <View className="bottom-links">
                <View
                    className="link-item"
                    onClick={() => {
                        DDYNavigateTo({
                            url: `/pages/login/main?activityId=${activityId}&marketingToolId=${marketingToolId}&groupId=${groupId}`,
                        });
                    }}
                >
                    手机号快捷登录
                </View>
                <View
                    className="link-item"
                    onClick={() => {
                        setShow(true);
                    }}
                >
                    立即注册
                </View>
            </View>
            <RegisterSelectModal
                activityId={activityId}
                marketingToolId={marketingToolId}
                groupId={groupId}
                show={show}
                closeFn={() => {
                    setShow(false);
                }}
            />
        </View>
    );
};
