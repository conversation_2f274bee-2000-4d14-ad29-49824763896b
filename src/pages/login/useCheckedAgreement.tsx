import { DDYNavigateTo } from "@/utils/route";
import { Checkbox } from "@antmjs/vantui";
import { View } from "@tarojs/components";
import { useState } from "react";
import "./useCheckedAgreement.less";
export default () => {
    // console.log()
    const [isChecked, setIsChecked] = useState(false);
    return {
        isChecked,
        node: (
            <View className="use-agreement">
                <Checkbox
                    value={isChecked}
                    // iconSize={30}
                    onChange={e => setIsChecked(e.detail)}
                    checkedColor={process.env.TARO_ENV === "weapp" ? "#07c160" : "#1778FF"}
                />
                <View className="agreement-text">
                    我已阅读并同意
                    <View className="link" onClick={() => DDYNavigateTo({ url: "/pages/h5/privacy-policy?type=2" })}>
                        《用户注册条款》
                    </View>
                    和
                    <View className="link" onClick={() => DDYNavigateTo({ url: "/pages/h5/privacy-policy?type=1" })}>
                        《隐私保护》
                    </View>
                </View>
            </View>
        ),
    };
};
