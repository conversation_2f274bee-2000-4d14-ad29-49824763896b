import newAPi from "@/utils/newApi";
import { DDYNavigateTo } from "@/utils/route";
import { Popup } from "@antmjs/vantui";
import { View } from "@tarojs/components";
import { useEffect, useState } from "react";
import "./register-select-modal.less";

export default ({ show, closeFn, activityId = "", marketingToolId = "", groupId = "" }) => {
    const [registerPrice, setRegisterPrice] = useState();
    const getPrice = () => {
        newAPi
            .getWxappPages({
                method: "POST",
                data: {
                    pageType: 30,
                },
            })
            .then(res => {
                console.log("res:", res);
                const data = res[0].publishData;
                if (data) {
                    const parseData = JSON.parse(data);
                    const page = parseData.pages[0];
                    setRegisterPrice(page?.pageSet?.registerPrice);
                }
            });
    };
    useEffect(() => {
        getPrice();
    }, []);
    // const
    return (
        <Popup show={show} round onClose={() => closeFn(false)} className="pop-rect">
            <View className="pop-title">选择注册方式</View>
            <View
                className="pop-btn pink "
                onClick={() => {
                    DDYNavigateTo({
                        url: `/pages/register/index?activityId=${activityId}&marketingToolId=${marketingToolId}&groupId=${groupId}`,
                    });
                }}
            >
                使用邀请码注册
            </View>
            <View
                className="pop-btn red"
                onClick={() => {
                    DDYNavigateTo({
                        url: `/pages/register-price/main?activityId=${activityId}&marketingToolId=${marketingToolId}&groupId=${groupId}`,
                    });
                }}
            >
                {registerPrice} 立即注册
            </View>
            <View className="pop-slogo">可享受会员专属优惠及跨境购物特权</View>
        </Popup>
    );
};
