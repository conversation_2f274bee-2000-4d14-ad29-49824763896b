page {
    height: 100vh;
    .login-psw,
    .login {
        min-height: 100vh;
        background-color: #ffffff;
        position: relative;
        .content {
            padding: 0 48px;
            padding-top: 168px;
            height: 100vh;
            box-sizing: border-box;
        }

        .logo-section {
            display: flex;
            flex-direction: column;
            align-items: center;
            margin-bottom: 96px;
        }

        .logo {
            width: 120px;
            height: 120px;
            margin-bottom: 24px;
        }

        .slogan {
            font-size: 28px;
            color: #666666;
            font-weight: 300;
        }

        .form-section {
            margin-top: 48px;
            .forget-psw {
                color: #666666;
            }
        }

        .form-item {
            margin-bottom: 48px;
        }

        .label {
            display: block;
            font-size: 28px;
            color: #333333;
            margin-bottom: 16px;
        }

        .input-container {
            position: relative;
            display: flex;
            align-items: center;
            background-color: #f5f5f5;
            border: 2px solid #e5e5e5;
            border-radius: 16px;
            height: 96px;
            padding-left: 32px;
        }

        .input {
            flex: 1;
            height: 96px;
            padding-left: 64px;
            font-size: 32px;
        }

        .verification-container {
            display: flex;
            gap: 24px;
        }

        .verification-button {
            width: 250px;
            height: 96px;
            line-height: 96px;
            background-color: #ff565e;
            color: #ffffff;
            font-size: 32px;
            font-weight: 500;
            border-radius: 16px;
        }

        .login-button {
            width: 100%;
            height: 96px;
            line-height: 96px;
            background-color: #ff565e;
            color: #ffffff;
            font-size: 32px;
            font-weight: 500;
            border-radius: 16px;
            margin-top: 64px;
        }

        .bottom-links {
            display: flex;
            justify-content: space-between;
            margin-top: 48px;
            position: fixed;
            padding: 0 48px;
            width: 100%;
            box-sizing: border-box;
            left: 0;
            bottom: calc(20px+ env(safe-area-inset-bottom));
        }

        .quick-login {
            font-size: 28px;
            color: #999;
        }

        .register {
            font-size: 28px;
            color: #ff565e;
        }

        .placeholder {
            color: #999999;
            font-size: 32px;
        }
    }
}

.login-warp {
    .agreement {
        margin-top: 48px;
        display: flex;
        align-items: flex-start;
        flex-direction: row;
        color: #888;
    }

    .checkbox {
        width: 32px;
        height: 32px;
        border: 1px solid #dddddd;
        border-radius: 4px;
        margin-right: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .checkbox-inner {
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .agreement-text {
        flex: 1;
        display: flex;
        flex-direction: row;
    }

    .agreement-desc {
        font-size: 24px;
        color: #888;
    }

    .link {
        font-size: 24px;
        color: #ff4d4f;
    }
}
