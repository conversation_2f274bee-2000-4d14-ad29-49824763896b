import { DDYBack, DDYNavigateTo } from "@/utils/route";
import { View } from "@tarojs/components";
import { Button, NavBar } from "@antmjs/vantui";
import "./home.less";
import DesignPage from "@/components/desgin";
import { useState } from "react";
import RegisterSelectModal from "./register-select-modal";
import Taro, { useRouter, usePageScroll } from "@tarojs/taro";
definePageConfig({
    transparentTitle: "always",
    navigationStyle: "custom",
});
export default () => {
    const { activityId, marketingToolId, groupId } = useRouter().params;

    const [pageSet, setPageSet] = useState({});
    const [top, setTop] = useState(false);
    const [show, setShow] = useState(false);
    usePageScroll(payload => {
        if (payload.scrollTop > 30) {
            setTop(true);
        } else {
            setTop(false);
        }
    });
    return (
        <View className="login-home-page">
            <NavBar
                fixed
                leftArrow={true}
                title={pageSet?.title || "登录"}
                onClickLeft={() => {
                    DDYBack();
                }}
                border={false}
                className={`page-title ${top ? "white" : "transparent"}`}
            />
            {/* <Button></Button> */}
            <DesignPage
                pageType={40}
                onGetPageConfig={data => {
                    setPageSet(data.pageSet);
                }}
            />
            <View className="btn-rect">
                <Button
                    className="register-btn"
                    onClick={() => {
                        setShow(true);
                    }}
                >
                    立即注册
                </Button>
                <Button
                    className="login-btn"
                    onClick={() => {
                        DDYNavigateTo({
                            url: `/pages/login/main?activityId=${activityId}&marketingToolId=${marketingToolId}&groupId=${groupId}`,
                        });
                    }}
                >
                    登录
                </Button>
            </View>
            <RegisterSelectModal
                show={show}
                activityId={activityId}
                marketingToolId={marketingToolId}
                groupId={groupId}
                closeFn={() => {
                    setShow(false);
                }}
            />
        </View>
    );
};
