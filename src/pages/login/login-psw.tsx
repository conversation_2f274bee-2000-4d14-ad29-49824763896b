/**
 * @description 登录
 */
import { loginSuccessRediction, useSafeRouter } from "@/utils/common";
import { LOGIN_BACK_URL, USER_SPECICAL_INFO } from "@/utils/constant";
import newAPi from "@/utils/newApi";
import { DDYNavigateTo } from "@/utils/route";
import { getStorage, setStorage } from "@/utils/storage";
import DDYToast from "@/utils/toast";
import { Icon, Field, Checkbox } from "@antmjs/vantui";
import { Button, Input, View } from "@tarojs/components";
import Taro, { useRouter } from "@tarojs/taro";
import { useState } from "react";
import "./index.less";
import "../register/index.less";
import RegisterSelectModal from "./register-select-modal";
import useCheckedAgreement from "./useCheckedAgreement";

definePageConfig({
    navigationBarTitleText: "登录",
});
export default () => {
    const { activityId, marketingToolId, groupId } = useSafeRouter().params;
    const [phoneNumber, setPhoneNumber] = useState("");
    const [showPassword, setShowPassword] = useState(false);
    const [password, setPassword] = useState("");
    const [show, setShow] = useState(false);
    // const [isChecked, setIsChecked] = useState(false);
    const togglePassword = () => {
        setShowPassword(!showPassword);
    };
    const { isChecked, node } = useCheckedAgreement();

    const login = () => {
        if (!isChecked) {
            DDYToast.info("请阅读用户注册协议和隐私保护");
            return;
        }
        Taro.login().then(res => {
            newAPi
                .loginPswOrVerify({
                    method: "POST",
                    data: {
                        wxCode: res.code,
                        phone: phoneNumber,
                        authCode: password,
                        loginType: "password",
                        activityId: activityId || "",
                        marketingToolId: marketingToolId || "",
                        groupId: groupId || "",
                    },
                })
                .then(data => {
                    setStorage(USER_SPECICAL_INFO, data);
                    DDYToast.success("登录成功");
                    const url = getStorage(LOGIN_BACK_URL);
                    if (!url && !data.inGroup && data.isJumpAllowed) {
                        setStorage(LOGIN_BACK_URL, `pages/wool-herd/index?join=false`);
                    }
                    setTimeout(() => {
                        loginSuccessRediction();
                    }, 1000);
                });
        });
    };
    return (
        <View className="login-warp">
            <View className="login-content">
                <View className="form-container">
                    <View className="form-item">
                        <View className="label">手机号</View>
                        <View>
                            <Field
                                maxlength={11}
                                placeholder="请输入手机号"
                                value={phoneNumber}
                                onChange={e => setPhoneNumber(e.detail)}
                                className="input"
                            />
                        </View>
                    </View>

                    <View className="form-item">
                        <View className="label">密码</View>
                        <View className="password-input">
                            <Field
                                placeholder="请输入密码"
                                value={password}
                                onChange={e => setPassword(e.detail)}
                                className="input"
                                type={showPassword ? "text" : "password"}
                                renderButton={
                                    <View onClick={togglePassword} className="password-icon">
                                        <Icon name={showPassword ? "eye-o" : "closed-eye"} size={42} />
                                    </View>
                                }
                            />
                        </View>
                    </View>
                    {/* 用户协议 */}
                    {node}

                    <Button
                        className="submit-button"
                        onClick={() => {
                            login();
                        }}
                    >
                        登 录
                    </Button>

                    {/*  */}
                </View>

                {/* 跳转 */}
                <View className="bottom-links">
                    <View
                        className="link-item"
                        onClick={() => {
                            DDYNavigateTo({
                                url: `/pages/login/main?activityId=${activityId}&marketingToolId=${marketingToolId}&groupId=${groupId}`,
                            });
                        }}
                    >
                        手机号快捷登录
                    </View>
                    <View
                        className="link-item"
                        onClick={() => {
                            DDYNavigateTo({
                                url: `/pages/login/reset-psw?activityId=${activityId}&marketingToolId=${marketingToolId}&groupId=${groupId}`,
                            });
                        }}
                    >
                        忘记密码
                    </View>
                    <View
                        className="link-item"
                        onClick={() => {
                            setShow(true);
                        }}
                    >
                        立即注册
                    </View>
                </View>
            </View>
            <RegisterSelectModal
                show={show}
                activityId={activityId}
                marketingToolId={marketingToolId}
                groupId={groupId}
                closeFn={() => {
                    setShow(false);
                }}
            />
        </View>
    );
};
