import React from "react";
import { View } from "@tarojs/components";
import { Cell, CellGroup } from "@antmjs/vantui";
import { DDYNavigateTo } from "@/utils/route";

const PrivacyPolicy: React.FC = () => {
    return (
        <View>
            <CellGroup inset>
                <Cell
                    title="隐私条款"
                    isLink
                    onClick={() => DDYNavigateTo({ url: "/pages/h5/privacy-policy?type=1" })}
                />
                <Cell
                    title="服务协议"
                    isLink
                    onClick={() => DDYNavigateTo({ url: "/pages/h5/privacy-policy?type=2" })}
                />
                <Cell
                    title="消费者服务条款"
                    isLink
                    onClick={() => DDYNavigateTo({ url: "/pages/h5/privacy-policy?type=3" })}
                />
                <Cell
                    title="门店推广服务协议"
                    isLink
                    onClick={() => DDYNavigateTo({ url: "/pages/h5/privacy-policy?type=15" })}
                />
            </CellGroup>
        </View>
    );
};
export default PrivacyPolicy;
