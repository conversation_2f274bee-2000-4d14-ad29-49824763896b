.collect-list {
    .header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 24px;
        background-color: #fff;
        font-size: 28px;
        background: #f4dcdc;

        .manage {
            color: #ff0940;
        }
    }

    .delete-btn {
        background: #f4dcdc;
        height: 228px;
        color: #ff0940;
    }

    .product-item {
        margin-bottom: 24px;

        .content {
            display: flex;
            align-items: center;
            background-color: #fff;
            padding: 24px;
            margin: 24px;
            border-radius: 16px;

            .checkbox {
                margin-right: 8px;
            }

            .image-container {
                position: relative;
                margin-right: 24px;
            }

            .product-image {
                width: 180px;
                height: 180px;
                border-radius: 16px;
            }

            .off-shelf-overlay {
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                display: flex;
                justify-content: center;
                align-items: center;
                background-color: rgba(0, 0, 0, 0.5);
                color: #fff;
                font-size: 32px;
                border-radius: 16px;
            }

            .product-info {
                flex: 1;
                display: flex;
                flex-direction: column;
                justify-content: space-between;
                height: 180px;

                .product-title {
                    font-size: 28px;
                    color: #333;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    display: -webkit-box;
                    -webkit-line-clamp: 2;
                    -webkit-box-orient: vertical;
                }

                .product-spec {
                    font-size: 20px;
                    color: #999;
                }

                .product-price {
                    font-size: 36px;
                    color: #ff0940;
                    .unit {
                        font-size: 24px;
                        margin-left: 2px;
                    }
                }
            }
        }
    }

    .footer {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 24px;
        background-color: #fff;
        position: fixed;
        bottom: 0;
        left: 0;
        width: 100%;
        box-sizing: border-box;

        .select-all {
            display: flex;
            align-items: center;
            font-size: 28px;
        }

        .remove-favorites {
            background-color: #f4dcdc;
            color: #e64340;
            font-size: 28px;
            border-radius: 16px;
            padding: 16px 24px;
        }
    }
}
