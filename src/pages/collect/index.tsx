/**
 * @description: 收藏列表
 * @author: wangchunting
 */
import newApi from "@/utils/newApi";
import { DDYSwitchTab } from "@/utils/route";
import DDYToast from "@/utils/toast";
import {
    Button,
    Checkbox,
    Empty,
    IPullToRefreshProps,
    InfiniteScroll,
    InfiniteScrollInstance,
    InfiniteScrollProps,
    PullToRefresh,
} from "@antmjs/vantui";
import { Text, View } from "@tarojs/components";
import { useDidShow } from "@tarojs/taro";
import React, { useState, useRef, useEffect } from "react";
import ProductItem from "./components/productItem";
import "./index.less";

const ProductList: React.FC = () => {
    const pageNo = useRef(1);
    const infiniteScrollInstance = useRef<InfiniteScrollInstance>();

    const [isManageMode, setIsManageMode] = useState(false);
    const [products, setProducts] = useState<any[]>([]);
    const [allSelected, setAllSelected] = useState(false); // 新增：全选状态
    const [page, setPage] = useState({});

    useEffect(() => {
        pageNo.current = 1;
        infiniteScrollInstance.current?.reset(true);
    }, []);

    // 下拉刷新
    const onRefresh: IPullToRefreshProps["onRefresh"] = () => {
        return new Promise(resolve => {
            pageNo.current = 1;
            infiniteScrollInstance.current?.reset(true);
            resolve(undefined);
        });
    };

    // 获取列表数据
    function getList() {
        return newApi.getCollectList({
            method: "POST",
            data: {
                currentPage: pageNo.current,
                pageSize: 10,
            },
        });
    }

    const loadMore: InfiniteScrollProps["loadMore"] = async () => {
        return new Promise(async resolve => {
            try {
                const result = await getList();
                const { page, dataList } = result;
                setPage(page);

                let newData: any = [];
                if (pageNo.current === 1) {
                    newData = dataList;
                } else {
                    newData = products.concat(dataList);
                }
                pageNo.current++;

                setProducts(newData);
                resolve(dataList.length < 10 ? "complete" : "loading");
            } catch (e) {
                resolve("error");
            }
        });
    };

    // 切换管理模式
    const toggleManageMode = () => {
        setIsManageMode(!isManageMode);
        setAllSelected(false);
    };

    // 单选商品
    const handleProductSelect = (itemId: number) => {
        const newList = [...products];
        const index = newList.findIndex(item => item.itemId === itemId);

        newList[index].checked = !newList[index].checked;
        setProducts(newList);

        const allChecked = newList.every(item => item.checked);
        setAllSelected(allChecked);
    };

    // 全选商品
    const handleSelectAll = () => {
        const newList = products.map(item => ({ ...item, checked: !allSelected }));
        setProducts(newList);
        setAllSelected(!allSelected);
    };

    // 批量取消收藏
    const handleRemoveFavorites = () => {
        let ids = products.filter(item => item.checked).map(item => item.itemId);
        toastRemoveFavorites(ids);
    };

    // 取消收藏api
    function getRemoveFavorites(ids) {
        newApi
            .getCancelCollectBatch({
                method: "POST",
                data: {
                    itemIdList: ids,
                },
            })
            .then(() => {
                DDYToast.info("取消收藏成功");
                setAllSelected(false);
                pageNo.current = 1;
                infiniteScrollInstance.current?.reset(true);
            });
    }

    // 删除二次确认
    const toastRemoveFavorites = ids => {
        DDYToast.showModal({
            title: "取消确认",
            content: "确定要取消收藏商品吗？",
            cancelText: "再想想",
            success(res) {
                if (res.confirm) {
                    getRemoveFavorites(ids);
                } else if (res.cancel) {
                    console.log("用户点击取消");
                }
            },
        });
    };

    return (
        <View className="collect-list">
            <View className="header">
                <Text>共 {page?.totalCount || 0} 件商品</Text>
                <Text className="manage" onClick={toggleManageMode}>
                    {isManageMode ? "完成" : "管理"}
                </Text>
            </View>
            <PullToRefresh onRefresh={onRefresh}>
                {products.length
                    ? products?.map(item => (
                          <ProductItem
                              key={item.itemId}
                              detail={item}
                              isManageMode={isManageMode}
                              onSelect={handleProductSelect}
                              onRemoveFavorites={id => {
                                  toastRemoveFavorites([id]);
                              }}
                          />
                      ))
                    : ""}
                <InfiniteScroll
                    loadMore={loadMore}
                    ref={infiniteScrollInstance}
                    completeText={
                        <>
                            {products.length == 0 ? (
                                <Empty description="您还没有收藏商品哦~">
                                    <Button
                                        round
                                        type="danger"
                                        onClick={() => {
                                            DDYSwitchTab({ url: "/pages/index/index" });
                                        }}
                                    >
                                        去首页逛逛
                                    </Button>
                                </Empty>
                            ) : (
                                "没有更多了"
                            )}
                        </>
                    }
                ></InfiniteScroll>
            </PullToRefresh>

            {isManageMode && (
                <View className="footer">
                    <View className="select-all">
                        <Checkbox value={allSelected} onChange={handleSelectAll}>
                            全选
                        </Checkbox>
                    </View>
                    <View className="remove-favorites" onClick={handleRemoveFavorites}>
                        取消收藏
                    </View>
                </View>
            )}
        </View>
    );
};

export default ProductList;
