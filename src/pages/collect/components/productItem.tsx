/**
 * @description: 收藏列表item
 * @author: wangchunting
 */
import { DDYNavigateTo } from "@/utils/route";
import { Button, Checkbox, SwipeCell } from "@antmjs/vantui";
import { Image, Text, View } from "@tarojs/components";
import React from "react";
import { ITEM_STATUS_ENUM } from "../enum";

interface ProductItemProps {
    detail?: {
        checked: boolean | undefined;
        itemId: number;
        itemName: string;
        mainImage: string;
        specification: string;
        lowPrice: number;
    };
    isManageMode?: boolean;
    onSelect?: (itemId?: number | string) => void;
    onRemoveFavorites?: (itemId?: number | string) => void;
}

const ProductItem: React.FC<ProductItemProps> = ({ detail, isManageMode, onSelect, onRemoveFavorites }) => {
    return (
        <SwipeCell
            rightWidth={75}
            renderRight={
                <Button className="delete-btn" onClick={() => onRemoveFavorites(detail?.itemId)}>
                    删除
                </Button>
            }
        >
            <View
                className="product-item"
                onLongPress={() => onRemoveFavorites(detail?.itemId)}
                onClick={() => {
                    DDYNavigateTo({ url: `/pages/goods_detail?id=${detail?.itemId}` });
                }}
            >
                <View className="content">
                    {isManageMode && (
                        <Checkbox
                            className="checkbox"
                            value={detail?.checked}
                            onChange={() => {
                                onSelect(detail?.itemId);
                            }}
                        />
                    )}
                    <View className="image-container">
                        <Image className="product-image" src={detail?.mainImage} mode="aspectFill" />
                        {detail?.itemStatus != ITEM_STATUS_ENUM.ON_SHELF && (
                            <View className="off-shelf-overlay">已下架</View>
                        )}
                    </View>
                    <View className="product-info">
                        <View>
                            <Text className="product-title">{detail?.itemName}</Text>
                            <Text className="product-spec">{detail?.specification}</Text>
                        </View>
                        <Text className="product-price">
                            ¥ {detail?.lowPrice?.toFixed(2)}
                            <Text className="unit">起</Text>
                        </Text>
                    </View>
                </View>
            </View>
        </SwipeCell>
    );
};
export default ProductItem;
