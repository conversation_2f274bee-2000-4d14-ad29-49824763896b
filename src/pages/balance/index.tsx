import { Text, View } from "@tarojs/components";
import { Button, Cell, CellGroup } from "@antmjs/vantui";
import { useLoad } from "@tarojs/taro";
import infoStore from "@/store/info-store";
import { observer } from "mobx-react-lite";
import api from "@/utils/api";
import { getStorage } from "@/utils/storage";
import { STORE_ID } from "@/utils/constant";
import { useState } from "react";
import identityStore from "@/store/identity-store";
import "./index.less";
import { DDYNavigateTo } from "@/utils/route";

definePageConfig({
    navigationBarTitleText: "余额",
});

export interface IWithdrawPeriod {
    userRole: number;
    dayOfMonthFrom: number | undefined;
    dayOfMonthTo: number | undefined;
}

export default observer(() => {
    const [withdrawDate, setWithdrawDate] = useState<IWithdrawPeriod>({
        userRole: 0,
        dayOfMonthFrom: undefined,
        dayOfMonthTo: undefined,
    });
    useLoad(() => {
        getWithdrawDate();
        infoStore.getBalances();
    });
    const toWithdraw = () => {
        api.getIsWithdraw({
            data: {
                shopId: getStorage("storeId"),
            },
        }).then(() => {
            DDYNavigateTo({
                url: "/pages/withdrawal/index?totalWithdrawalMoney=" + Number(infoStore.balance).toFixed(2),
            });
        });
    };
    const getWithdrawDate = () => {
        api.getWithdrawDate({
            data: { shopId: getStorage(STORE_ID) },
        }).then((res: IWithdrawPeriod[]) => {
            res.forEach(item => {
                if (identityStore.isServiceProvider() && item.userRole === 3) {
                    setWithdrawDate(item);
                } else if (identityStore.isGuider() && item.userRole === 1) {
                    setWithdrawDate(item);
                }
            });
        });
    };

    return (
        <View className={"balance"}>
            <View className="top-box">
                <View className="title">可提现金额</View>
                <View className="price">
                    ￥<Text className="price-din">{Number(infoStore.balance).toFixed(2)}</Text>
                </View>
                <Button className="button" onTap={toWithdraw}>
                    去提现
                </Button>
                <View className={"bottom-title"}>
                    请在每月{withdrawDate.dayOfMonthFrom}号-{withdrawDate.dayOfMonthTo}号期间进行提现
                </View>
            </View>
            <CellGroup inset>
                <Cell
                    title={"提现历史"}
                    isLink
                    onTap={() => DDYNavigateTo({ url: "/pages/withdrawal/history/index" })}
                />
                <Cell title={"结算账单"} isLink onTap={() => DDYNavigateTo({ url: "/pages/withdrawal/order/index" })} />
            </CellGroup>
        </View>
    );
});
