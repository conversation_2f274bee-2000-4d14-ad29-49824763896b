.balance {
    .top-box {
        background: #ffffff;
        border-radius: 16px;
        margin: 20px 32px;
        text-align: center;
        overflow: hidden;

        .title {
            font-size: 24px;
            font-weight: 400;
            line-height: 24px;
            margin-top: 72px;
            margin-bottom: 24px;
        }
        .price {
            font-family: "D-DIN";
            font-size: 48px;
            font-weight: bold;
            line-height: 72px;
            .price-din {
                font-size: 72px;
            }
        }
        .button {
            width: 300px;
            height: 88px;
            background: linear-gradient(90deg, #ff3b61 0%, #ff0940 100%);
            border-radius: 100px;
            color: #ffffff;
            line-height: 88px;
            margin-top: 72px;
            margin-bottom: 32px;
        }
        .bottom-title {
            font-size: 24px;
            font-weight: 400;
            color: #999999;
            line-height: 24px;
            margin-bottom: 40px;
        }
    }
}
