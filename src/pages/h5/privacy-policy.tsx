import Taro, { getStorageSync, useLoad, useRouter } from "@tarojs/taro";
import { WebView } from "@tarojs/components";
import api from "@/utils/api";
import { IS_TEST, PROJECT_CONFIG } from "@/utils/env";

export default function () {
    useLoad((param: Record<string, any>) => {
        api.getPrivacyPolicy({
            data: {
                shopId: Taro.getStorageSync("storeId"),
                status: param.type,
            },
            filterCheck: true,
        }).then(json => {
            if (json.title) {
                Taro.setNavigationBarTitle({
                    title: json.title,
                });
            }
        });
    });
    const { type } = useRouter().params;
    const shopId = getStorageSync("storeId");
    let h5Path = "";
    switch (PROJECT_CONFIG.ENV) {
        case "test":
            h5Path = "-test";
            break;
        case "pre":
            h5Path = "-pre";
            break;
        case "stag":
            h5Path = "-stag";
            break;
        default:
            break;
    }

    return (
        <WebView
            src={`https://m.mall.yang800.com/mini-program/h5${h5Path}.htm?shopId=${shopId}&status=${type}`}
        ></WebView>
    );
}
