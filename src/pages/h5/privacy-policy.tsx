import Taro, { getStorageSync, useLoad, useRouter } from "@tarojs/taro";
import { WebView } from "@tarojs/components";
import api from "@/utils/api";
import { IS_TEST } from "@/utils/env";

export default function () {
    useLoad((param: Record<string, any>) => {
        api.getPrivacyPolicy({
            data: {
                shopId: Taro.getStorageSync("storeId"),
                status: param.type,
            },
            filterCheck: true,
        }).then(json => {
            if (json.title) {
                Taro.setNavigationBarTitle({
                    title: json.title,
                });
            }
        });
    });
    const { type } = useRouter().params;
    const shopId = getStorageSync("storeId");

    return (
        <WebView
            src={`https://m.mall.yang800.com/mini-program/h5${
                IS_TEST ? "-test" : ""
            }.htm?shopId=${shopId}&status=${type}`}
        ></WebView>
    );
}
