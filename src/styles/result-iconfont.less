@font-face {
    font-family: dt-icon-result;
    src: url("data:font/truetype;charset=utf-8;base64,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")
        format("truetype");
    font-weight: normal;
    font-style: normal;
}

.dt-icon-result {
    /* stylelint-disable-next-line font-family-no-missing-generic-family-keyword */
    font-family: dt-icon-result !important;
    font-size: 32px;
    font-style: normal;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.dt-icon-result-warning::before {
    content: "\ec72";
}

.dt-icon-result-info::before {
    content: "\e6ce";
}

.dt-icon-result-wait::before {
    content: "\e743";
}

.dt-icon-result-success::before {
    content: "\e68b";
}

.dt-icon-result-error::before {
    content: "\e6cb";
}
.dt-icon-result-success {
    color: #07c160;
}

.dt-icon-result-wait {
    color: #4dd3b5;
}

.dt-icon-result-info {
    color: #1677ff;
}

.dt-icon-result-warning {
    color: #ff8f1f;
}

.dt-icon-result-error {
    color: #fc3e39;
}
