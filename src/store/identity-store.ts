import { observable, runInAction, toJS } from "mobx";
import api from "@/utils/api";
import { GUIDER_INFO, IDENTITY, IDENTITYINFO, POSTER_NAME, STORE_ID, SUB_STORE_ID } from "@/utils/constant";
import { getStorage, removeStorage, setStorage } from "@/utils/storage";

export interface IdentityResponse {
    serviceProvider?: ServiceProvider;
    serviceProviderInfo?: IServiceProviderInfo;
    subStoreOwner?: SubStoreOwner;
    guider?: Guider;
    shopOwner: boolean;
}

export interface ServiceProvider {
    status: number;
    pending: boolean;
    authed: boolean;
    reject: boolean;
}
export interface IServiceProviderInfo {
    id: string;
    name: string;
    userId: number;
    shopId: number;
    mobile: string;
    address: string;
    authAddress: string;
    province: string;
    city: string;
    county: string;
    frontImg: string;
    backImg: string;
    businessImg: string;
    foodSellAllowImg: string;
    relationId: number;
    createdAt: number;
    bankName: string;
    bankAccountNo: string;
    bankAccountName: string;
}

export interface SubStoreOwner {
    id: number;
    createdAt: number;
    updatedAt: number;
    status: number;
    shopId: number;
    userId: number;
    name: string;
    mobile: string;
    province: string;
    city: string;
    county: string;
    address: string;
    bankName: string;
    bankNo: string;
    nameInLaw: string;
    foodSellAllowImg: string;
    frontImg: string;
    backImg: string;
    businessImg: string;
    fullMobile: string;
    authed: boolean;
    reject: boolean;
    pending: boolean;
    extra: any;
}

export interface Extra {
    shopName: string;
}

export interface Guider {
    id: number;
    createdAt: number;
    updatedAt: number;
    status: number;
    shopId: number;
    subStoreId: number;
    storeGuiderId: number;
    storeGuiderNickname: string;
    storeGuiderMobile: string;
    authed: boolean;
    reject: boolean;
    extra: Extra;
}

const identityStore = observable({
    identityInfo: {},
    identity: "buyer",
    status: 2,
    /**
     * 角色是服务商
     */
    isServiceProvider() {
        return this.identity === "serviceProvider";
    },
    /**
     * 角色是门店
     */
    isSubStore() {
        return this.identity === "subStore";
    },
    /**
     * 角色是消费者
     */
    isBuyer() {
        return this.identity === "buyer";
    },
    /**
     * 角色是导购
     */
    isGuider() {
        return this.identity === "guider";
    },
    isAuthed() {
        if (this.isSubStore()) {
            return (this.identityInfo as IdentityResponse)?.subStoreOwner?.authed ?? false;
        }
        return false;
    },
    isReject() {
        if (this.isSubStore()) {
            return (this.identityInfo as IdentityResponse)?.subStoreOwner?.reject ?? false;
        }
        return false;
    },
    isPending() {
        if (this.isSubStore()) {
            return (this.identityInfo as IdentityResponse)?.subStoreOwner?.pending ?? false;
        }
        return false;
    },
    getIdentityInfo(isDefaultGoLogin?: boolean) {
        //获取角色
        return api
            .getIdentity({
                data: { shopId: getStorage(STORE_ID) },
                filterCheck: true,
                showError: false,
                isDefaultGoLogin,
            })
            .then((data: IdentityResponse) => {
                runInAction(() => {
                    if (data.subStoreOwner) {
                        this.identity = "subStore";
                        this.status = data.subStoreOwner.status;
                    } else if (data.serviceProvider) {
                        this.identity = "serviceProvider";
                        this.status = data.serviceProvider.status;
                    } else if (data.guider) {
                        this.identity = "guider";
                        this.status = data.guider.status;
                    } else {
                        this.identity = "buyer";
                    }

                    this.identityInfo = data;
                });
                setStorage(IDENTITY, toJS(this.identity));
                // 若获取为空，则是消费者，不是代言人
                setStorage(IDENTITYINFO, data);
                // 若是代理，则赋值门店ID
                if (data.subStoreOwner) {
                    setStorage(SUB_STORE_ID, data.subStoreOwner.id);
                    // 清空导购信息
                    removeStorage(GUIDER_INFO);
                    setStorage(POSTER_NAME, data.subStoreOwner.name);
                }

                if (data.guider) {
                    // 导购
                    setStorage(SUB_STORE_ID, data.guider.subStoreId);
                    setStorage(GUIDER_INFO, data.guider.storeGuiderId);
                    setStorage(POSTER_NAME, data.guider.storeGuiderNickname);
                }
                return data;
            })
            .catch(res => {
                removeStorage(IDENTITYINFO);
            });
    },
});

export default identityStore;
