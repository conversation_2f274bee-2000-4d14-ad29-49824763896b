import { observable, runInAction } from "mobx";
import api from "@/utils/api";
import { SHOP_INFO, STORE_ID, SUB_STORE_ID, SUB_STORE_INFO } from "@/utils/constant";
import { getStorage, setStorage } from "@/utils/storage";
import identityStore from "@/store/identity-store";
import newApi from "@/utils/newApi";

const infoStore = observable({
    counter: 0,
    balance: 0,
    profitData: {
        todayOrderCount: 0,
        todayOrderTotalAmount: 0,
        currentMonthProfitAmount: 0,
        todayTotalGuiderProfitAmount: 0,
    },
    newIdentity: "",
    newUserProfile: {},
    userProfile: {
        id: -1,
        username: "",
        mobile: "",
        avatar: "",
        realName: "微信用户",
        gender: 0,
        province: "",
        city: "",
    },
    nickName: "微信用户",
    avatarUrl: "",
    subStore: null,
    isAuthed() {
        return !!this.subStore && this.subStore.authed;
    },
    getUserProfile() {
        return api
            .getUserInfo({
                data: {},
                filterCheck: true,
            })
            .then(res => {
                runInAction(() => {
                    this.userProfile = res;
                    this.avatarUrl = res.avatar;
                });
                return res;
            });
    },
    getNewUserProfile(isDefaultGoLogin?: boolean) {
        return newApi
            .getPersonalCenterInfo({
                method: "POST",
                data: {},
                isDefaultGoLogin,
            })
            .then(res => {
                runInAction(() => {
                    this.newUserProfile = res;
                });
                return res;
            });
    },
    getPersonalCenterActivity() {
        return newApi
            .getPersonalCenterActivity({
                method: "POST",
                data: {},
            })
            .then(res => {
                runInAction(() => {
                    this.personalCenterActivity = res;
                });
                return res;
            });
    },
    getBalances() {
        if (identityStore.isSubStore()) {
            api.getStoreProfitSum({
                data: { sourceId: getStorage(STORE_ID) },
                filterCheck: true,
            }).then(data => {
                runInAction(() => {
                    this.balance = (data.avalidProfit / 100).toFixed(2);
                });
            });
        } else {
            api.getBalance({
                data: { shopId: getStorage(STORE_ID) },
            }).then(data => {
                runInAction(() => {
                    this.balance = (data.remainAmount / 100).toFixed(2);
                });
            });
        }
    },
    /**
     * 收益统计
     */
    getProfitStatistics() {
        api.getProfitStatistics({
            data: { shopId: getStorage(STORE_ID) },
        }).then(data => {
            runInAction(() => {
                this.profitData = {
                    ...data,
                    todayProfitAmount: (data.todayProfitAmount / 100).toFixed(2),
                    todayTotalGuiderProfitAmount: (data.todayTotalGuiderProfitAmount / 100).toFixed(2),
                    currentMonthProfitAmount: (data.currentMonthProfitAmount / 100).toFixed(2),
                    todayOrderTotalAmount: (data.todayOrderTotalAmount / 100).toFixed(2),
                };
            });
        });
    },
    getShopInfo() {
        api.getShopInfo({
            data: { shopId: getStorage(STORE_ID) },
            filterCheck: true,
        }).then(data => {
            setStorage(SHOP_INFO, data);
        });
    },

    getStoreInfo(isDefaultGoLogin?: boolean) {
        const subStoreId = getStorage(SUB_STORE_ID);
        return api
            .getOrSetStoreInfo({
                data:
                    subStoreId != 0
                        ? {
                              id: subStoreId,
                          }
                        : {
                              sourceId: getStorage(STORE_ID),
                          },
                filterCheck: true,
                showError: false,
                isDefaultGoLogin,
            })
            .then(data => {
                runInAction(() => {
                    this.subStore = data;
                });
                if (data.id) {
                    setStorage(SUB_STORE_ID, data.id);
                }
                setStorage(SUB_STORE_INFO, data);
            });
    },
});

export default infoStore;
