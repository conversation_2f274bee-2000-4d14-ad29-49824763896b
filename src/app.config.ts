import routes from "./router";

export default defineAppConfig({
    pages: routes,
    window: {
        backgroundTextStyle: "dark",
        navigationBarBackgroundColor: "#f1f2f3",
        /*navigationBarTitleText: '商城',*/
        navigationBarTextStyle: "black",
        //@ts-ignore
        navigationBarFrontColor: "black",
        enablePullDownRefresh: false,
        backgroundColor: "#f1f2f3",
        // navigationStyle:"custom"
    },
    tabBar: {
        color: "#7e7e7e",
        selectedColor: "#ec3a4a",
        backgroundColor: "#ffffff",
        borderStyle: "black",
        list: [
            {
                pagePath: "pages/index/index",
                text: "首页",
                iconPath: "images/home.png",
                selectedIconPath: "images/home_chk.png",
            },
            {
                pagePath: "pages/home/<USER>",
                text: "分类",
                iconPath: "images/thumb.png",
                selectedIconPath: "images/thumb_chk.png",
            },
            {
                pagePath: "pages/shop_cart/index",
                text: "购物袋",
                iconPath: "images/bag.png",
                selectedIconPath: "images/bag_chk.png",
            },
            {
                pagePath: "pages/info",
                text: "我的",
                iconPath: "images/mine.png",
                selectedIconPath: "images/mine_chk.png",
            },
        ],
    },
    requiredPrivateInfos: ["chooseAddress", "chooseLocation"],
    // "transparentTitle": "always",
    // "titlePenetrate": "YES", // 允许点击穿透后，才能触发导航栏上的 onTap 事件
    permission: {
        "scope.userLocation": {
            desc: "获取您的收货地址用于发货",
        },
    },
    plugins: {
        // tencentvideo: {
        //     version: "2.3.10",
        //     provider: "wxa75efa648b60994b",
        // },
    },
    enableShareAppMessage: true,
});
