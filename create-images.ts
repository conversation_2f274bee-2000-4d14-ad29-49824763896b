import glob from "glob";
import path from "path";
import fs from "fs-extra";

glob("./images/**/*.png", { cwd: path.resolve(__dirname, "./src") }, (err, files) => {
    if (err) return console.error(err);
    const images = files.map(filePath => {
        let importPath = path.basename(filePath);
        let name = path.basename(filePath, path.extname(filePath)).replace(/-/g, "_");
        return {
            import: `import icon_${name} from "./${importPath}";\n`,
            export: `icon_${name}`,
        };
    });
    fs.writeFile(
        path.resolve(__dirname, "./src/images/index.ts"),
        `// auto generated code
${images
    .map(img => {
        return img.import;
    })
    .join("")}
export {\n${images
            .map(img => {
                return `    ${img.export}`;
            })
            .join(",\n")},\n};\n`,
        "utf8",
    );
});
