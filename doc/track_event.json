[{"event_id": "mall_event_goods_share", "event_name": "mall_event_goods_share", "event_comment": "", "event_key_list": ["mall_key_userid", "mall_key_openid", "mall_key_identity", "mall_key_env_version", "mall_key_item_name", "mall_key_item_id", "mall_key_source_from", "mall_key_path"]}, {"event_id": "mall_event_app_update", "event_name": "mall_event_app_update", "event_comment": "", "event_key_list": ["mall_key_userid", "mall_key_openid", "mall_key_identity", "mall_key_env_version", "mall_key_app_update_result"]}, {"event_id": "mall_event_scan_service_provider_mini_qr", "event_name": "mall_event_scan_service_provider_mini_qr", "event_comment": "", "event_key_list": ["mall_key_userid", "mall_key_openid", "mall_key_identity", "mall_key_env_version", "mall_key_invite_code", "mall_key_login_call_back"]}, {"event_id": "mall_event_scan_service_provider_mini_qr_submit", "event_name": "mall_event_scan_service_provider_mini_qr_submit", "event_comment": "", "event_key_list": ["mall_key_userid", "mall_key_openid", "mall_key_identity", "mall_key_env_version", "mall_key_invite_code", "mall_key_login_call_back", "mall_key_submit", "mall_key_submit_info"]}, {"event_id": "mall_event_scan_guider_mini_qr", "event_name": "mall_event_scan_guider_mini_qr", "event_comment": "", "event_key_list": ["mall_key_userid", "mall_key_openid", "mall_key_identity", "mall_key_env_version", "mall_key_invite_scene", "mall_key_submit_info"]}, {"event_id": "mall_event_scan_guider_mini_qr_submit", "event_name": "mall_event_scan_guider_mini_qr_submit", "event_comment": "", "event_key_list": ["mall_key_userid", "mall_key_openid", "mall_key_identity", "mall_key_env_version", "mall_key_invite_add_guider_name", "mall_key_invite_add_guider_mobile", "mall_key_invite_add_from", "mall_key_submit_info"]}, {"event_id": "mall_event_share_invite_guider_mini_qr", "event_name": "mall_event_share_invite_guider_mini_qr", "event_comment": "", "event_key_list": ["mall_key_userid", "mall_key_openid", "mall_key_identity", "mall_key_env_version", "mall_key_submit_info", "mall_key_invite_add_from", "mall_key_invite_add_guider_result"]}, {"event_id": "mall_event_share_invite_store_mini_qr", "event_name": "mall_event_share_invite_store_mini_qr", "event_comment": "", "event_key_list": ["mall_key_userid", "mall_key_openid", "mall_key_identity", "mall_key_env_version"]}, {"event_id": "mall_event_look_good", "event_name": "mall_event_look_good", "event_comment": "", "event_key_list": ["mall_key_userid", "mall_key_openid", "mall_key_identity", "mall_key_env_version"]}, {"event_id": "mall_event_add_cart", "event_name": "mall_event_add_cart", "event_comment": "", "event_key_list": ["mall_key_userid", "mall_key_openid", "mall_key_identity", "mall_key_env_version", "mall_key_good_id", "mall_key_good_num"]}, {"event_id": "mall_event_good_immediately_bug", "event_name": "mall_event_good_immediately_bug", "event_comment": "", "event_key_list": ["mall_key_userid", "mall_key_openid", "mall_key_identity", "mall_key_env_version", "mall_key_good_id", "mall_key_good_num", "mall_key_good_spec_id"]}, {"event_id": "mall_event_cart_settlement", "event_name": "mall_event_cart_settlement", "event_comment": "", "event_key_list": ["mall_key_userid", "mall_key_openid", "mall_key_identity", "mall_key_env_version", "mall_key_goods_id", "mall_key_goods_num"]}, {"event_id": "mall_event_submit_order", "event_name": "mall_event_submit_order", "event_comment": "", "event_key_list": ["mall_key_userid", "mall_key_openid", "mall_key_identity", "mall_key_env_version"]}, {"event_id": "mall_event_pay_success", "event_name": "mall_event_pay_success", "event_comment": "", "event_key_list": ["mall_key_userid", "mall_key_openid", "mall_key_identity", "mall_key_env_version", "mall_key_order_id", "mall_key_order_pay_channel"]}, {"event_id": "mall_event_pay_fail", "event_name": "mall_event_pay_fail", "event_comment": "", "event_key_list": ["mall_key_userid", "mall_key_openid", "mall_key_identity", "mall_key_env_version", "mall_key_order_id", "mall_key_order_pay_channel", "mall_key_order_pay_error_message"]}, {"event_id": "mall_event_order_apply_refund", "event_name": "mall_event_order_apply_refund", "event_comment": "", "event_key_list": ["mall_key_userid", "mall_key_openid", "mall_key_identity", "mall_key_env_version", "mall_key_order_id", "mall_key_order_ids", "mall_key_order_refund_reason", "mall_key_order_refund_introduce"]}, {"event_id": "mall_event_order_cancel_order", "event_name": "mall_event_order_cancel_order", "event_comment": "", "event_key_list": ["mall_key_userid", "mall_key_openid", "mall_key_identity", "mall_key_env_version"]}, {"event_id": "mall_event_order_apply_pay", "event_name": "mall_event_order_apply_pay", "event_comment": "", "event_key_list": ["mall_key_userid", "mall_key_openid", "mall_key_identity", "mall_key_env_version", "mall_key_order_id"]}, {"event_id": "mall_event_order_confirm_receipt", "event_name": "mall_event_order_confirm_receipt", "event_comment": "", "event_key_list": ["mall_key_userid", "mall_key_openid", "mall_key_identity", "mall_key_env_version", "mall_key_order_id"]}, {"event_id": "mall_event_order_confirm_result", "event_name": "mall_event_order_confirm_result", "event_comment": "", "event_key_list": ["mall_key_userid", "mall_key_openid", "mall_key_identity", "mall_key_env_version", "mall_key_order_id", "mall_key_order_confirm_response"]}, {"event_id": "mall_event_perf_module_monitor", "event_name": "mall_event_perf_module_monitor", "event_comment": "", "event_key_list": ["mall_key_userid", "mall_key_openid", "mall_key_identity", "mall_key_env_version", "mall_key_perf_module_id", "mall_key_perf_monitor_id", "mall_key_perf_monitor_level", "mall_key_perf_error_code", "mall_key_perf_error_msg", "mall_key_perf_cost_time"]}, {"event_id": "mall_event_order_paging_start", "event_name": "mall_event_order_paging_start", "event_comment": "订单分页加载开始", "event_key_list": ["mall_key_userid", "mall_key_openid", "mall_key_identity", "mall_key_env_version", "mall_key_order_paging_page", "mall_key_order_paging_statusstr"]}, {"event_id": "mall_event_order_paging_end", "event_name": "mall_event_order_paging_end", "event_comment": "订单分页加载结束", "event_key_list": ["mall_key_userid", "mall_key_openid", "mall_key_identity", "mall_key_env_version", "mall_key_order_paging_page", "mall_key_order_paging_statusstr", "mall_key_order_paging_count", "mall_key_order_paging_total", "mall_key_order_paging_result", "mall_key_order_paging_error", "mall_key_order_paging_refund_status"]}]