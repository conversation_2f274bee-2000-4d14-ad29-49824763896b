[{"key": "mall_key_userid", "desc": "mall_key_userid", "comment": "", "key_type": "KEY_TYPE_INT64", "dict_name": ""}, {"key": "mall_key_openid", "desc": "mall_key_openid", "comment": "", "key_type": "KEY_TYPE_STRING", "dict_name": ""}, {"key": "mall_key_identity", "desc": "mall_key_identity", "comment": "", "key_type": "KEY_TYPE_STRING", "dict_name": ""}, {"key": "mall_key_env_version", "desc": "mall_key_env_version", "comment": "", "key_type": "KEY_TYPE_STRING", "dict_name": ""}, {"key": "mall_key_app_update_result", "desc": "mall_key_app_update_result", "comment": "", "key_type": "KEY_TYPE_STRING", "dict_name": ""}, {"key": "mall_key_invite_code", "desc": "mall_key_invite_code", "comment": "", "key_type": "KEY_TYPE_STRING", "dict_name": ""}, {"key": "mall_key_login_call_back", "desc": "mall_key_login_call_back", "comment": "", "key_type": "KEY_TYPE_STRING", "dict_name": ""}, {"key": "mall_key_submit", "desc": "mall_key_submit", "comment": "", "key_type": "KEY_TYPE_STRING", "dict_name": ""}, {"key": "mall_key_submit_info", "desc": "mall_key_submit_info", "comment": "", "key_type": "KEY_TYPE_STRING", "dict_name": ""}, {"key": "mall_key_good_id", "desc": "mall_key_good_id", "comment": "", "key_type": "KEY_TYPE_INT64", "dict_name": ""}, {"key": "mall_key_goods_id", "desc": "mall_key_goods_id", "comment": "", "key_type": "KEY_TYPE_INT64", "dict_name": ""}, {"key": "mall_key_good_num", "desc": "mall_key_good_num", "comment": "", "key_type": "KEY_TYPE_STRING", "dict_name": ""}, {"key": "mall_key_goods_num", "desc": "mall_key_goods_num", "comment": "", "key_type": "KEY_TYPE_STRING", "dict_name": ""}, {"key": "mall_key_good_spec_id", "desc": "mall_key_good_spec_id", "comment": "", "key_type": "KEY_TYPE_INT64", "dict_name": ""}, {"key": "mall_key_order_id", "desc": "mall_key_order_id", "comment": "", "key_type": "KEY_TYPE_INT64", "dict_name": ""}, {"key": "mall_key_order_ids", "desc": "mall_key_order_ids", "comment": "", "key_type": "KEY_TYPE_STRING", "dict_name": ""}, {"key": "mall_key_order_pay_channel", "desc": "mall_key_order_pay_channel", "comment": "", "key_type": "KEY_TYPE_STRING", "dict_name": ""}, {"key": "mall_key_order_pay_error_message", "desc": "mall_key_order_pay_error_message", "comment": "", "key_type": "KEY_TYPE_STRING", "dict_name": ""}, {"key": "mall_key_order_confirm_response", "desc": "mall_key_order_confirm_response", "comment": "", "key_type": "KEY_TYPE_STRING", "dict_name": ""}, {"key": "mall_key_order_refund_reason", "desc": "mall_key_order_refund_reason", "comment": "", "key_type": "KEY_TYPE_STRING", "dict_name": ""}, {"key": "mall_key_order_refund_introduce", "desc": "mall_key_order_refund_introduce", "comment": "", "key_type": "KEY_TYPE_STRING", "dict_name": ""}, {"key": "mall_key_item_name", "desc": "mall_key_item_name", "comment": "", "key_type": "KEY_TYPE_STRING", "dict_name": ""}, {"key": "mall_key_item_id", "desc": "mall_key_item_id", "comment": "", "key_type": "KEY_TYPE_INT64", "dict_name": ""}, {"key": "mall_key_source_from", "desc": "mall_key_source_from", "comment": "", "key_type": "KEY_TYPE_STRING", "dict_name": ""}, {"key": "mall_key_path", "desc": "mall_key_path", "comment": "", "key_type": "KEY_TYPE_STRING", "dict_name": ""}, {"key": "mall_key_invite_add_from", "desc": "mall_key_invite_add_from", "comment": "", "key_type": "KEY_TYPE_STRING", "dict_name": ""}, {"key": "mall_key_invite_substore_id", "desc": "mall_key_invite_substore_id", "comment": "", "key_type": "KEY_TYPE_INT64", "dict_name": ""}, {"key": "mall_key_invite_scene", "desc": "mall_key_invite_scene", "comment": "", "key_type": "KEY_TYPE_STRING", "dict_name": ""}, {"key": "mall_key_invite_add_guider_name", "desc": "mall_key_invite_add_guider_name", "comment": "", "key_type": "KEY_TYPE_STRING", "dict_name": ""}, {"key": "mall_key_invite_add_guider_mobile", "desc": "mall_key_invite_add_guider_mobile", "comment": "", "key_type": "KEY_TYPE_STRING", "dict_name": ""}, {"key": "mall_key_invite_add_guider_result", "desc": "mall_key_invite_add_guider_result", "comment": "", "key_type": "KEY_TYPE_STRING", "dict_name": ""}, {"key": "mall_key_invite_add_store_service_provider_name", "desc": "mall_key_invite_add_store_service_provider_name", "comment": "", "key_type": "KEY_TYPE_STRING", "dict_name": ""}, {"key": "mall_key_invite_add_store_service_provider_avatar", "desc": "mall_key_invite_add_store_service_provider_avatar", "comment": "", "key_type": "KEY_TYPE_STRING", "dict_name": ""}, {"key": "mall_key_invite_add_store_service_provider_name", "desc": "mall_key_invite_add_store_service_provider_name", "comment": "", "key_type": "KEY_TYPE_STRING", "dict_name": ""}, {"key": "mall_key_global_catch_error_page", "desc": "mall_key_global_catch_error_page", "comment": "", "key_type": "KEY_TYPE_STRING", "dict_name": ""}, {"key": "mall_key_global_catch_error_info", "desc": "mall_key_global_catch_error_info", "comment": "", "key_type": "KEY_TYPE_STRING", "dict_name": ""}, {"key": "mall_key_perf_module_id", "desc": "mall_key_perf_module_id", "comment": "模块ID，例如搜索模块的内部ID编码", "key_type": "KEY_TYPE_STRING", "dict_name": ""}, {"key": "mall_key_perf_monitor_id", "desc": "mall_key_perf_monitor_id", "comment": "接口ID，即服务调用接口ID或者接口英文名称", "key_type": "KEY_TYPE_STRING", "dict_name": ""}, {"key": "mall_key_perf_monitor_level", "desc": "mall_key_perf_monitor_level", "comment": "重要等级，0为普通，非0为重要，数字越大越重要", "key_type": "KEY_TYPE_INT64", "dict_name": ""}, {"key": "mall_key_perf_error_code", "desc": "mall_key_perf_error_code", "comment": "接口调用情况，0代表正常，其他均为失败", "key_type": "KEY_TYPE_INT64", "dict_name": ""}, {"key": "mall_key_perf_error_msg", "desc": "mall_key_perf_error_msg", "comment": "调用信息，用来辅助判断接口调用失败的内容", "key_type": "KEY_TYPE_STRING", "dict_name": ""}, {"key": "mall_key_perf_cost_time", "desc": "mall_key_perf_cost_time", "comment": "", "key_type": "KEY_TYPE_INT64", "dict_name": ""}, {"key": "mall_key_order_paging_page", "desc": "mall_key_order_paging_page", "comment": "订单分页第几页", "key_type": "KEY_TYPE_INT64", "dict_name": ""}, {"key": "mall_key_order_paging_statusstr", "desc": "mall_key_order_paging_statusstr", "comment": "订单状态", "key_type": "KEY_TYPE_STRING", "dict_name": ""}, {"key": "mall_key_order_paging_count", "desc": "mall_key_order_paging_count", "comment": "当前已加载的订单总数量", "key_type": "KEY_TYPE_INT64", "dict_name": ""}, {"key": "mall_key_order_paging_total", "desc": "mall_key_order_paging_total", "comment": "当前状态的订单总数量", "key_type": "KEY_TYPE_INT64", "dict_name": ""}, {"key": "mall_key_order_paging_result", "desc": "mall_key_order_paging_result", "comment": "当前状态加载的订单返回结果", "key_type": "KEY_TYPE_STRING", "dict_name": ""}, {"key": "mall_key_order_paging_error", "desc": "mall_key_order_paging_error", "comment": "当前状态加载的订单错误信息", "key_type": "KEY_TYPE_STRING", "dict_name": ""}, {"key": "mall_key_order_paging_refund_status", "desc": "mall_key_order_paging_refund_status", "comment": "订单退款状态", "key_type": "KEY_TYPE_STRING", "dict_name": ""}]