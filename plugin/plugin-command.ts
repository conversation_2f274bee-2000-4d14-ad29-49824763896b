import { IPluginContext } from "@tarojs/service";
import path from "path";
import * as https from "https";
import * as inquirer from "inquirer";
import fs from "fs-extra";

function loadMariaConfig(mariaPath: string) {
    return new Promise((resolve, reject) => {
        if (!mariaPath) {
            reject("mariaPath is empty");
        }
        console.log("mariaPath", mariaPath);
        let data = "";
        https.get(`https://maria.yang800.com/${mariaPath}`, res => {
            // 获取数据的过程
            res.on("data", chunk => {
                data += chunk;
            });
            // end是获取的最终数据
            res.on("end", () => {
                // 将数据返回给前端
                fs.writeFile(
                    path.resolve(__dirname, "../src/config.js"),
                    `module.exports= ${JSON.stringify(JSON.parse(data).data)}`,
                );
                resolve("end");
            });
        });
    });
}
export default (ctx: IPluginContext, _pluginOpts) => {
    const miniChoices = [
        {
            name: "显辉电商",
            value: "api/data/v2/802/617",
        },
        {
            name: "显辉欧兔欧",
            value: "api/data/v2/802/618",
        },
        {
            name: "显辉欧兔欧2",
            value: "api/data/v2/802/619",
        },
        {
            name: "显辉欧兔欧3",
            value: "api/data/v2/802/640",
        },
        {
            name: "显辉欧兔欧5",
            value: "api/data/v2/802/691",
        },

        {
            name: "显辉欧兔欧5（支付宝）",
            value: "api/data/v2/802/692",
        },

        {
            name: "显辉电商（支付宝）",
            value: "api/data/v2/802/669",
        },
        {
            name: "显辉欧兔欧（支付宝）",
            value: "api/data/v2/802/673",
        },
        {
            name: "显辉欧兔欧2（支付宝）",
            value: "api/data/v2/802/674",
        },
        {
            name: "显辉欧兔欧2（联调）",
            value: "api/data/v2/802/825",
        },
        {
            name: "但丁分销商城（支付宝）",
            value: "api/data/v2/802/675",
        },
        {
            name: "显辉欧兔欧3（支付宝）",
            value: "api/data/v2/802/676",
        },
        {
            name: "显辉电商（大贸）",
            value: "api/data/v2/802/678",
        },
        {
            name: "ORDESA欧迪赛全球购（测试）",
            value: "api/data/v2/802/710",
        },
        {
            name: "ORDESA欧迪赛全球购",
            value: "api/data/v2/802/711",
        },
        {
            name: "微信显辉stag",
            value: "api/data/v2/1000/837",
        },
        {
            name: "支付宝显辉stag",
            value: "api/data/v2/1000/841",
        },
    ];
    ctx.onBuildStart(() => {
        console.log("编译开始！");
    });

    ctx.registerCommand({
        // 命令名
        name: "weappName",
        // 执行 taro weappName --help 时输出的 options 信息
        optionsMap: {
            "--mariaPath": "maria 配置路径",
            "--miniChoice": "选择打包小程序",
        },
        // 执行 taro weappName --help 时输出的使用例子的信息
        synopsisList: ["taro weappName --mariaPath api/data/v2/802/617 --"],
        async fn() {
            let { mariaPath, miniChoice } = ctx.runOpts.options;
            if (!mariaPath) {
                if (miniChoice) {
                    miniChoices.forEach(value => {
                        if (value.name === miniChoice) {
                            mariaPath = value.value;
                        }
                    });
                } else {
                    let answers = await inquirer.prompt([
                        {
                            type: "list",
                            message: "请输入要读取小程序配置(显辉电商/显辉欧兔欧/显辉欧兔欧2/显辉欧兔欧3):",
                            name: "mini",
                            choices: miniChoices,
                        },
                    ]);
                    mariaPath = answers?.mini ?? "";
                }
            }
            await loadMariaConfig(mariaPath);
        },
    });
};
