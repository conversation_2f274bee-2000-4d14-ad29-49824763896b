import { IPluginContext } from "@tarojs/service";
import * as fs from "fs";
import axios from "axios";
const execSync = require("child_process").execSync;
const execGitCmd = cmd => execSync(cmd).toString().replace(/\n$/, "");
const crypto = require("crypto");

export default (ctx: IPluginContext, _pluginOpts) => {
    function pic2Base64(filePath) {
        // 读取文件数据
        let data = fs.readFileSync(filePath);
        return Buffer.from(data).toString("base64");
    }

    function getPicMd5(path: string) {
        const buffer = fs.readFileSync(path);
        const md5 = crypto.createHash("md5");
        md5.update(buffer);
        return md5.digest("hex").toLowerCase();
    }
    async function sendTextMessage(data) {
        const gitBranch = execGitCmd(`git symbolic-ref --short -q HEAD`); // git 分支
        const commit = execGitCmd(`git log -n 1 --pretty=format:"%H"`); // 最近commit
        const commitInfo = execGitCmd('git log -n 1 --pretty=format:"%s"'); // 最近1次 git 提交信

        const compileConfig = require("../src/config");
        return axios
            .post("https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=0292a348-fbfd-4bb8-a2dc-9306beac69d7&debug=1", {
                msgtype: "markdown",
                markdown: {
                    content: `<font color=\"warning\">打包结果如下：</font>

           >编译时间:<font color=\"comment\">${new Date().toLocaleString()}</font>
           >小程序:<font color=\"comment\">${compileConfig.miniAppName}</font>
           >appid:<font color=\"comment\">${compileConfig.appid}</font>
           >环境:<font color=\\"comment\\">${compileConfig.apiMall}</font>
           >版本号:<font color=\"comment\">${data.version}</font>
           >版本号描述:<font color=\"comment\">${data.desc}</font>
           >分支:<font color=\\"comment\\">${gitBranch}</font>
           >commitId:<font color=\"comment\">${commit}</font>
           >commit message:<font color=\"comment\">${commitInfo}</font>
           ><font color="info">体验二维码如下图</font>`,
                },
            })
            .then(response => {
                console.log(response.data);
            })
            .catch(error => {
                console.log(error);
            });
    }
    function sendQrMessage(data) {
        const base64 = pic2Base64(data.qrCodeLocalPath);
        const md5 = getPicMd5(data.qrCodeLocalPath);
        return axios
            .post("https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=0292a348-fbfd-4bb8-a2dc-9306beac69d7&1", {
                msgtype: "image",
                image: { base64, md5 },
            })
            .then(response => {
                console.log("sendTextMessage", response.data);
            })
            .catch(error => {
                console.log("sendTextMessage", error);
            });
    }
    ctx.register({
        name: "onPreviewComplete",
        fn: ({ success, data, error }) => {
            console.log("接收预览后数据", success, data, error);
        },
    });
    ctx.register({
        name: "onUploadComplete",
        fn: ({ success, data, error }) => {
            console.log("接收上传后数据", success, data, error);
            sendTextMessage(data).then(() => sendQrMessage(data));
        },
    });
};
