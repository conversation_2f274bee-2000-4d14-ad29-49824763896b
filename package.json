{"name": "new-mall-app", "version": "1.0.0", "private": true, "description": "显辉小程序", "templateInfo": {"name": "mobx", "typescript": true, "css": "less"}, "scripts": {"weappName": "taro weappName", "upload": "taro upload --type weapp", "build": "npm run create:images && taro build --type weapp", "dev:weapp": "npm run build -- --watch", "build:alipay": "taro build --type alipay", "dev:alipay": "npm run build:alipay -- --watch", "prettier": "prettier -c --write **/*", "prepare": "husky install", "create:images": "ts-node create-images"}, "browserslist": ["last 3 versions", "Android >= 4.1", "ios >= 8"], "author": "", "license": "MIT", "dependencies": {"@antmjs/vantui": "^3.6.4", "@babel/runtime": "^7.7.7", "@tarojs/components": "3.6.15", "@tarojs/helper": "3.6.15", "@tarojs/plugin-framework-react": "3.6.15", "@tarojs/plugin-platform-alipay": "3.6.15", "@tarojs/plugin-platform-h5": "3.6.15", "@tarojs/plugin-platform-jd": "3.6.15", "@tarojs/plugin-platform-qq": "3.6.15", "@tarojs/plugin-platform-swan": "3.6.15", "@tarojs/plugin-platform-tt": "3.6.15", "@tarojs/plugin-platform-weapp": "3.6.15", "@tarojs/react": "3.6.15", "@tarojs/runtime": "3.6.15", "@tarojs/shared": "3.6.15", "@tarojs/taro": "3.6.15", "babel-plugin-import": "^1.13.8", "dayjs": "^1.11.9", "mobx": "^6.10.2", "mobx-react-lite": "^4.0.4", "react": "^18.0.0", "react-dom": "^18.0.0", "taro-code": "^4.0.1"}, "devDependencies": {"@babel/core": "^7.8.0", "@pmmmwh/react-refresh-webpack-plugin": "^0.5.5", "@tarojs/cli": "3.6.15", "@tarojs/plugin-mini-ci": "^3.6.18", "@tarojs/taro-loader": "3.6.15", "@tarojs/webpack5-runner": "3.6.15", "@types/node": "^18.15.11", "@types/react": "^18.0.0", "@types/webpack-env": "^1.13.6", "@typescript-eslint/eslint-plugin": "^5.20.0", "@typescript-eslint/parser": "^5.20.0", "axios": "^1.5.1", "babel-preset-taro": "3.6.15", "eslint": "^8.12.0", "eslint-config-taro": "3.6.15", "eslint-plugin-import": "^2.12.0", "eslint-plugin-react": "^7.8.2", "eslint-plugin-react-hooks": "^4.2.0", "husky": "^8.0.3", "postcss": "^8.4.18", "prettier": "2.8.8", "pretty-quick": "^3.1.3", "react-refresh": "^0.11.0", "stylelint": "9.3.0", "ts-node": "^10.9.1", "typescript": "^4.1.0", "webpack": "^5.78.0"}, "lint-staged": {"**/*": "prettier --write --ignore-unknown"}}