import path from "path";
import { CIPluginFn } from "./CIPluginFn";

const config = {
    projectName: "newMallApp",
    date: "2023-9-6",
    designWidth: 750,
    deviceRatio: {
        640: 2.34 / 2,
        750: 1,
        828: 1.81 / 2,
    },
    sourceRoot: "src",
    outputRoot: "dist",
    plugins: [
        ["@tarojs/plugin-mini-ci", CIPluginFn],
        path.resolve(__dirname, "../plugin/plugin-upload.ts"),
        path.resolve(__dirname, "../plugin/plugin-command.ts"),
    ],
    defineConstants: {},
    copy: {
        patterns: [],
        options: {},
    },
    framework: "react",
    compiler: "webpack5",
    cache: {
        enable: false, // Webpack 持久化缓存配置，建议开启。默认配置请参考：https://docs.taro.zone/docs/config-detail#cache
    },
    mini: {
        miniCssExtractPluginOption: {
            ignoreOrder: true,
        },
        lessLoaderOption: {
            lessOptions: {
                modifyVars: {
                    hack: `true; @import "${path.join(process.cwd(), "src/styles/index.less")}";`,
                },
            },
            // 适用于全局引入样式
            // additionalData: "@import '~/src/styles/index.less';",
        },
        postcss: {
            pxtransform: {
                enable: true,
                config: {},
            },
            url: {
                enable: true,
                config: {
                    limit: 1024, // 设定转换尺寸上限
                },
            },
            cssModules: {
                enable: false, // 默认为 false，如需使用 css modules 功能，则设为 true
                config: {
                    namingPattern: "module", // 转换模式，取值为 global/module
                    generateScopedName: "[name]__[local]___[hash:base64:5]",
                },
            },
        },
    },
    h5: {
        publicPath: "/",
        staticDirectory: "static",
        postcss: {
            autoprefixer: {
                enable: true,
                config: {},
            },
            cssModules: {
                enable: false, // 默认为 false，如需使用 css modules 功能，则设为 true
                config: {
                    namingPattern: "module", // 转换模式，取值为 global/module
                    generateScopedName: "[name]__[local]___[hash:base64:5]",
                },
            },
        },
    },
    alias: {
        "@": path.resolve(__dirname, "..", "src"),
        // "@/components/*": path.resolve(__dirname, '..', 'src/components/*'),
    },
};

module.exports = function (merge) {
    if (process.env.NODE_ENV === "development") {
        return merge({}, config, require("./dev"));
    }
    return merge({}, config, require("./prod"));
};
