export const CIPluginFn = async () => {
    const compileConfig = require("../src/config");
    /**
     * @typedef { import("@tarojs/plugin-mini-ci").CIOptions } CIOptions
     * @type {CIOptions}
     */
    return {
        weapp: {
            appid: compileConfig.appid || "",
            privateKeyPath: `key/private.${compileConfig.appid || ""}.key`,
        },
        // 版本号
        version: "1.0.3",
        // 版本发布描述
        desc: "测试阶段",
    };
};
