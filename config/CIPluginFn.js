export const CIPluginFn = async () => {
    const compileConfig = require("../src/config");
    /**
     * @typedef { import("@tarojs/plugin-mini-ci").CIOptions } CIOptions
     * @type {CIOptions}
     */
    return {
        weapp: {
            appid: compileConfig.appid || "wx7b32eee85988e4a1",
            privateKeyPath: `key/private.${compileConfig.appid || "wx7b32eee85988e4a1"}.key`,
        },
        // 版本号
        version: "1.3.2",
        // 版本发布描述
        desc: "fix支付成功页面请求报错",
    };
};
