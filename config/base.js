export const devAppConfig = {
    is_test: true,
    apiMall: '"https://mtest.mall.yang800.cn/backend"',
    appid: '"wx7b32eee85988e4a1"', //  wxe01dd24b3a1d85c1 但丁商城
    storeId: 179,
    subStoreId: 179,
    projectId: '"26"',
    miniAppName: '"显辉电商"',
    loginLogo: '"https://dante-img.oss-cn-hangzhou.aliyuncs.com/online/aff40d917695a3667483431ad299ae8c.png"',
    inviteBg: '"https://dante-img.oss-cn-hangzhou.aliyuncs.com/38524941351.jpg"',
    attorneyList: [
        {
            name: '"FY24年a2 IMF授权书"',
            url: '"https://dante-img.oss-cn-hangzhou.aliyuncs.com/45569568969.pdf"',
        },
        {
            name: '"FY24年a2 SN授权书"',
            url: '"https://dante-img.oss-cn-hangzhou.aliyuncs.com/45572616615.pdf"',
        },
    ],
};

export const productAppConfig = {
    xianhui: {
        appid: '"wxe01dd24b3a1d85c1"',
        storeId: 274,
        projectId: 37,
        miniAppName: '"显辉欧兔欧"',
        loginLogo: '"https://dante-img.oss-cn-hangzhou.aliyuncs.com/online/aff40d917695a3667483431ad299ae8c.png"',
        inviteBg: '"https://dante-img.oss-cn-hangzhou.aliyuncs.com/38524941351.jpg"',
        attorneyList: [
            {
                name: '"FY24年a2 IMF授权书"',
                url: '"https://dante-img.oss-cn-hangzhou.aliyuncs.com/45569568969.pdf"',
            },
            {
                name: '"FY24年a2 SN授权书"',
                url: '"https://dante-img.oss-cn-hangzhou.aliyuncs.com/45572616615.pdf"',
            },
        ],
    },
    xianhui_2: {
        appid: '"wx0593dc81863bd448"',
        storeId: 293,
        projectId: 40,
        miniAppName: '"显辉欧兔欧2"',
        loginLogo: '"https://dante-img.oss-cn-hangzhou.aliyuncs.com/93314882495.jpg"',
        inviteBg: '"https://dante-img.oss-cn-hangzhou.aliyuncs.com/38524941351.jpg"',
        attorneyList: [
            {
                name: '"2023年美赞臣亲舒授权书"',
                url: '"https://dante-img.oss-cn-hangzhou.aliyuncs.com/45632205639.pdf"',
            },
            {
                name: '"2023年惠氏授权书"',
                url: '"https://dante-img.oss-cn-hangzhou.aliyuncs.com/25369440816.pdf"',
            },
            {
                name: '"2023年雀巢授权书"',
                url: '"https://dante-img.oss-cn-hangzhou.aliyuncs.com/34771708621.pdf"',
            },
            {
                name: '"FY24年a2 IMF授权书"',
                url: '"https://dante-img.oss-cn-hangzhou.aliyuncs.com/45569568969.pdf"',
            },
            {
                name: '"FY24年a2 SN授权书"',
                url: '"https://dante-img.oss-cn-hangzhou.aliyuncs.com/45572616615.pdf"',
            },
        ],
    },
    youshunbuy: {
        appid: '"wx9c8c5942069d93b8"',
        storeId: 296,
        projectId: 41,
        miniAppName: '"邮瞬购"',
        loginLogo: '"https://dante-img.oss-cn-hangzhou.aliyuncs.com/8614273168.png"',
        inviteBg: '"https://dante-img.oss-cn-hangzhou.aliyuncs.com/38524941351.jpg"',
    },
};

export const productBaseConfig = {
    apiMall: '"https://m.mall.yang800.com/backend"',
    appid: '"wxe01dd24b3a1d85c1"',
    storeId: 274, //274  显辉欧兔欧  293 显辉欧兔欧2  296 邮瞬通
    subStoreId: -1,
    projectId: 37, //37显辉欧兔欧  40 显辉欧兔欧2    41 邮瞬通
    miniAppName: '"显辉欧兔欧"', //显辉欧兔欧  显辉欧兔欧2 //邮瞬购
    is_test: false,
    loginLogo: '"https://dante-img.oss-cn-hangzhou.aliyuncs.com/online/aff40d917695a3667483431ad299ae8c.png"',
};
