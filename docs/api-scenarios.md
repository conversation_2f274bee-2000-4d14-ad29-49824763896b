# API 请求场景文档

## 首页/主页 (pages/index/index)
- `${host}/api/adv/img/wxaShopImg` - 获取首页广告图，页面加载时触发
- `${host}/xhr/html/home/<USER>
- `${host}/mall-app/api/wxappPages/getPage` - 获取小程序页面配置，页面初始化时触发
- `${host}/mall-app/api/items/hotSaleRankList` - 获取热销排行榜，页面滚动到排行榜区域时触发
- `${host}/api/home/<USER>
- `${host}/api/mall/discoverList` - 获取首页发现商品，页面加载时触发
- `${host}/api/msg/current` - 获取当前消息，页面加载时触发
- `${host}/api/msg/unread-main` - 判断是否有未读消息，页面加载时触发

## 分类页面 (pages/category/index)
- `${host}/api/seller/shopCategories/fullTree` - 获取商品完整类目树，页面加载时触发
- `${host}/api/v2/search/category` - 获取分类数据，页面加载时触发
- `${host}/api/mall/rootCtegoryList` - 获取一级分类，页面加载时触发
- `${host}/api/mall/childGoodsCatetoryList` - 获取二级三级分类，选择一级分类时触发

## 商品列表页 (pages/goods_list/index)
- `${host}/api/search-in-shop` - 获取店内商品列表，页面加载和筛选条件变化时触发
- `${host}/api/mall/searchGoodsList` - 查询商品列表，搜索或分类筛选时触发
- `${host}/mall-app/api/news/search-in-shop` - 获取门店商品列表，页面加载时触发

## 商品详情页 (pages/goods_detail/index)
- `${host}/api/goods/detail` - 获取商品详情，页面加载时触发
- `${host}/mall-app/api/items/detail` - 获取商品详情(新版)，页面加载时触发
- `${host}/api/goods/spec` - 获取商品规格，页面加载和选择规格时触发
- `${host}/api/goodsFavorite/favoriteInfo` - 获取商品收藏信息，页面加载时触发
- `${host}/api/goodsFavorite/favorite` - 收藏商品，点击收藏按钮时触发
- `${host}/api/goodsFavorite/unfavorite` - 取消收藏，点击取消收藏按钮时触发
- `${host}/api/wxa/share/url` - 获取商品分享二维码，点击分享按钮时触发
- `${host}/mall-app/api/user/coupon/queryWaitGetCoupons` - 查询可领取优惠券，页面加载时触发
- `${host}/mall-app/api/user/coupon/autoGetCoupons` - 自动领取优惠券，用户点击领取按钮时触发
- `${host}/mall-app/api/item/evaluation/detailList` - 获取商品评价信息，页面加载时触发
- `${host}/mall-app/api/item/evaluation/count` - 获取商品评价汇总，页面加载时触发
- `${host}/mall-app/api/item/evaluation/list` - 获取商品评价列表，查看更多评价时触发
- `${host}/mall-app/api/gb_activity/info` - 查询拼团活动状态，页面加载时触发
- `${host}/mall-app/api/gb_group/getGroupDetail` - 获取拼团活动信息，页面加载时触发

## 购物车 (pages/shop_cart/index)
- `${host}/api/cart/list` - 获取购物车列表，页面加载和刷新时触发
- `${host}/mall-app/api/cart/list` - 获取购物车列表(新版)，页面加载和刷新时触发
- `${host}/api/cart/add` - 添加商品到购物车，用户点击添加按钮时触发
- `${host}/api/cart/update` - 更新购物车商品数量，用户调整数量时触发
- `${host}/api/cart/delete` - 删除购物车商品，用户点击删除按钮时触发
- `${host}/api/cart/check` - 选中/取消选中购物车商品，用户点击选择框时触发
- `${host}/mall-app/api/cart/count` - 获取购物车商品数量，页面加载时触发
- `${host}/mall-app/api/cart/update` - 更新购物车(新版)，用户调整数量时触发
- `${host}/mall-app/api/cart/batchDelete` - 批量删除购物车商品，用户点击批量删除按钮时触发

## 订单相关页面
### 订单确认页 (pages/order/comfire-order/index)
- `${host}/api/order/preview` - 获取订单预览信息，页面加载时触发
- `${host}/api/order/preview/check` - 订单预览检查，页面加载时触发
- `${host}/api/buyer/receiver-infos` - 获取收货地址列表，页面加载时触发
- `${host}/api/delivery-fee-charge/order-preview` - 获取运费信息，页面加载和地址变更时触发
- `${host}/api/user/hasUserAgreeTerms` - 检查用户是否同意协议，页面加载时触发
- `${host}/api/user/agreeConsumerTerms` - 用户同意协议，用户点击同意按钮时触发
- `${host}/mall-app/api/order/preview` - 订单预览(新版)，页面加载时触发

### 订单提交
- `${host}/api/order` - 提交订单，用户点击提交订单按钮时触发
- `${host}/mall-app/api/order/submit` - 提交订单(新版)，用户点击提交订单按钮时触发
- `${host}/api/order/pay` - 支付订单，用户点击支付按钮时触发

### 订单列表页 (pages/order/my-order/index)
- `${host}/api/order/list` - 获取订单列表，页面加载和切换状态标签时触发
- `${host}/api/order/count-by-status-all` - 批量获取各状态订单数量，页面加载时触发
- `${host}/api/order/countNotPaid` - 获取未支付订单数量，页面加载时触发
- `${host}/api/order/cancel` - 取消订单，用户点击取消订单按钮时触发
- `${host}/api/order/delete` - 删除订单，用户点击删除订单按钮时触发
- `${host}/api/order/confirm` - 确认收货，用户点击确认收货按钮时触发

### 订单详情页 (pages/order/order-detail/index)
- `${host}/api/order/{id}/detail` - 获取订单详情，页面加载时触发
- `${host}/api/order/{orderNo}/detail` - 根据订单号获取订单详情，页面加载时触发
- `${host}/mall-app/api/order/findActivityByOrderId` - 查询订单相关活动，页面加载时触发

### 物流信息页 (pages/order/logistics/index)
- `${host}/api/order/express/{id}` - 获取物流信息，页面加载时触发
- `${host}/api/order/express/detail` - 获取物流详情，页面加载时触发

### 退款/售后页面
- `${host}/api/order/substore/findRefundDetail` - 获取退款详情，页面加载时触发
- `${host}/api/mall/refund/saveRefundApply` - 申请退款，用户提交退款申请时触发
- `${host}/api/thirdPartySku/check/stockInfo` - 查询京东云库存状态，页面加载时触发
- `${host}/api/jd/return/apply` - 京东商品申请退货，用户提交退货申请时触发
- `${host}/api/jd/return/reason/list` - 获取京东退货原因列表，页面加载时触发
- `${host}/api/order/return/express` - 提交退货物流信息，用户填写物流信息时触发

## 用户中心 (pages/home/<USER>
- `${host}/api/user/my-profile` - 获取用户信息，页面加载时触发
- `${host}/mall-app/api/personal/center/user/info` - 获取个人中心用户信息，页面加载时触发
- `${host}/mall-app/api/personal/center/activity/content` - 获取个人中心活动内容，页面加载时触发
- `${host}/mall-app/api/family/user/syncWeChatUserInfo` - 同步微信用户信息，授权后触发
- `${host}/api/userPoint/pointInfo` - 获取用户积分信息，页面加载时触发
- `${host}/api/userSign/signInfo` - 获取用户签到信息，页面加载时触发
- `${host}/api/userSign/doSign` - 用户签到，用户点击签到按钮时触发
- `${host}/api/userSign/getSignDate` - 获取最近七天签到情况，页面加载时触发

## 登录/注册相关页面
### 登录页 (pages/login/home)
- `${host}/mall-app/api/family/login/sms/send` - 发送登录短信验证码，用户点击获取验证码按钮时触发
- `${host}/mall-app/api/family/login` - 密码/验证码登录，用户点击登录按钮时触发
- `${host}/mall-app/api/family/loginByUnionId` - 静默登录，微信授权后自动触发
- `${host}/mall-app/api/family/loginByUnionIdAndPhone` - 手机号快捷登录，微信授权后自动触发
- `${host}/api/wechat/jscode2session` - 微信code换取session，微信授权后自动触发
- `${host}/api/user/wxaProjectLogin` - 微信小程序登录，微信授权后自动触发
- `${host}/gateway/getWxOpenIdByProjectId` - 获取微信OpenID，微信授权后自动触发
- `${host}/mall-app/api/family/login/check` - 登录检查，页面加载时触发

### 注册页
- `${host}/mall-app/api/family/register/sms/send` - 发送注册短信验证码，用户点击获取验证码按钮时触发
- `${host}/mall-app/api/family/register/source` - 获取注册来源，页面加载时触发
- `${host}/mall-app/api/family/registerByInviteCode` - 邀请码注册，用户提交注册表单时触发
- `${host}/mall-app/api/family/register/info` - 获取注册信息，页面加载时触发
- `${host}/mall-app/api/family/registerByPay` - 支付注册，用户点击支付注册按钮时触发
- `${host}/gateway/registerByMobile` - 手机号一键注册，用户点击一键注册按钮时触发

### 手机号绑定/修改
- `${host}/api/user/change-mobile` - 修改手机号，用户提交新手机号时触发
- `${host}/api/user/wxa/change-mobile/send-sms` - 发送修改手机号验证码，用户点击获取验证码按钮时触发
- `${host}/api/user/login-by-mobile/send-sms` - 发送手机号登录验证码，用户点击获取验证码按钮时触发
- `${host}/api/user/register-by-mobile/without-captcha/send-sms` - 发送手机号注册验证码，用户点击获取验证码按钮时触发
- `${host}/gateway/verifyMobile` - 验证手机号，用户提交验证码时触发
- `${host}/gateway/login` - 手机号登录，用户提交登录表单时触发
- `${host}/api/user/change-mobile-by-old-code-check` - 旧手机号验证，用户提交验证码时触发
- `${host}/api/user/change-mobile-wallet-password-check` - 提现密码验证，用户提交提现密码时触发
- `${host}/api/user/change-mobile-by-old-after-check` - 验证后修改手机号，验证通过后触发

## 地址管理页面
- `${host}/api/buyer/receiver-infos` - 获取收货地址列表，页面加载时触发
- `${host}/api/buyer/receiver-infos/{id}` - 获取/编辑收货地址，页面加载和编辑地址时触发
- `${host}/api/buyer/receiver-infos/default` - 获取默认收货地址，页面加载时触发
- `${host}/api/buyer/receiver-infos/{id}/set-default` - 设置默认收货地址，用户点击设为默认按钮时触发
- `${host}/api/buyer/receiver-infos/{id}/set-paper-no` - 修改身份证信息，用户修改身份证信息时触发
- `${host}/api/address/{parentId}/children` - 获取地址子级，选择省市区时触发
- `${host}/api/baseAddress/all/list` - 获取地址库，页面加载时触发
- `${host}/api/baseAddress/children` - 获取地址子级，选择省市区时触发
- `${host}/api/baseAddress/street` - 获取街道列表，选择区域后触发

## 优惠券相关页面
- `${host}/xhr/coupon/unuseList` - 获取未使用优惠券，页面加载时触发
- `${host}/xhr/coupon/expireList` - 获取已过期优惠券，页面加载时触发
- `${host}/xhr/coupon/countInfo` - 获取优惠券数量，页面加载时触发
- `${host}/mall-app/api/user/coupon/list` - 获取优惠券列表，页面加载时触发
- `${host}/mall-app/api/user/coupon/queryWaitGetCoupons` - 查询可领取优惠券，页面加载时触发
- `${host}/mall-app/api/couponActivity/clickFissionActivity` - 助力领券，用户点击助力按钮时触发
- `${host}/mall-app/api/couponActivity/queryFissionDetail` - 获取助力详情，助力页面加载时触发
- `${host}/mall-app/api/user/coupon/autoGetCoupons` - 自动领取优惠券，用户点击领取按钮时触发

## 活动相关页面
- `${host}/mall-app/api/app-promotion/activity/list` - 获取活动列表，活动页面加载时触发
- `${host}/mall-app/api/app-promotion/activity/detail` - 获取活动详情，活动详情页加载时触发
- `${host}/mall-app/api/app-activity/user/detail` - 获取用户报名活动详情，页面加载时触发
- `${host}/mall-app/api/app-activity/user/list` - 获取参与用户列表，页面加载时触发
- `${host}/mall-app/api/app-activity/user/signUp` - 活动报名，用户点击报名按钮时触发
- `${host}/mall-app/api/app-activity/user/material` - 提交活动资料，用户提交资料时触发
- `${host}/mall-app/api/app-promotion/activity/channel` - 获取活动渠道，页面加载时触发
- `${host}/mall-app/api/app-activity/user/cancelSignUp` - 取消活动报名，用户点击取消报名按钮时触发

## 拼团活动页面
- `${host}/mall-app/api/gb_group/getGroupDetail` - 获取拼团详情，页面加载时触发
- `${host}/mall-app/api/gb_group/member_join` - 用户参团，用户点击参团按钮时触发
- `${host}/mall-app/api/gb_group/before_pay_create` - 用户开团，用户点击开团按钮时触发
- `${host}/mall-app/api/gb_group/myGroup` - 查询我的团信息，页面加载时触发
- `${host}/mall-app/api/gb_activity/info` - 查询活动状态，页面加载时触发
- `${host}/mall-app/api/gb_group/page` - 查询活动日志信息，页面加载时触发

## 我的收藏 (pages/collect/index)
- `${host}/mall-app/api/user/favorite/list` - 获取收藏列表，页面加载时触发
- `${host}/api/goodsFavorite/favorite` - 添加收藏，用户点击收藏按钮时触发
- `${host}/api/goodsFavorite/unfavorite` - 取消收藏，用户点击取消收藏按钮时触发

## 消息中心 (pages/notification/index)
- `${host}/api/msg/list` - 获取消息列表，页面加载时触发
- `${host}/api/msg/unread-main` - 判断是否有未读消息，页面加载时触发
- `${host}/api/systemMessage/messageInfo` - 获取系统消息，页面加载时触发

## 余额/福豆相关页面
- `${host}/mall-app/api/userAmount/detail` - 获取用户余额详情，页面加载时触发
- `${host}/mall-app/api/userAmountSend/list` - 获取福豆券列表，页面加载时触发
- `${host}/mall-app/api/userAmountFlow/list` - 获取余额变更记录，页面加载时触发
- `${host}/mall-app/api/rule/balance/getDefaultRemark` - 获取福豆规则，页面加载时触发
- `${host}/mall-app/api/userAmountSend/getUnReadAmountSend` - 获取未读余额券，页面加载时触发

## 积分相关页面
- `${host}/api/integral/gift/get-useGrade` - 获取用户可用积分，页面加载时触发
- `${host}/api/integral/genAccept` - 扫码加积分，用户扫描积分码时触发
- `${host}/api/search-in-shop-gift` - 查询积分商品，页面加载时触发
- `${host}/api/integral/gift/get-recordGrade` - 查询积分记录，页面加载时触发

## 店铺相关页面
- `${host}/api/shop/{shopId}` - 获取店铺信息，页面加载时触发
- `${host}/api/shop/inviteCodeWithShopId` - 获取商家邀请码，页面加载时触发
- `${host}/api/wxa/share/url` - 获取店铺分享二维码，用户点击分享按钮时触发
- `${host}/api/shop/reach` - 提交用户上下级关系，页面加载时触发
- `${host}/api/shop/authorization/pages` - 获取授权书列表，页面加载时触发

## 门店相关页面
- `${host}/api/v2/subStore/current` - 获取门店审核信息，页面加载时触发
- `${host}/api/v2/subStore/auth/current` - 获取门店审核状态，页面加载时触发
- `${host}/api/subStore/joinSubStore` - 扫码加入门店成为导购，用户扫描门店码时触发
- `${host}/api/open/v1/user/createQrCode` - 生成邀请导购二维码，页面加载时触发
- `${host}/api/subStore/profit` - 获取门店总收益，页面加载时触发
- `${host}/api/storeMonthlyPunch` - 提交门店打卡，用户点击打卡按钮时触发
- `${host}/api/storeMonthlyPunch/latest/record` - 获取最近打卡时间，页面加载时触发

## 提现相关页面
- `${host}/api/withdrawProfitApply/findWithdrawPeriod` - 判断当前提现时间，页面加载时触发
- `${host}/api/withdrawProfitApply/withdrawAll` - 发起提现申请，用户点击提现按钮时触发
- `${host}/api/balance/profit-balance` - 检查提现密码设置状态，页面加载时触发
- `${host}/api/balance/withdraw-password` - 设置/修改提现密码，用户提交密码时触发
- `${host}/api/balance/withdraw-password/check` - 验证提现密码，用户输入密码时触发
- `${host}/api/balance/withdraw-password/reset` - 重置提现密码，用户点击重置按钮时触发
- `${host}/api/balance/withdraw-password/reset/send-sms` - 发送重置密码验证码，用户点击获取验证码按钮时触发
- `${host}/api/balance/withdraw-apply` - 提交提现申请，用户点击提现按钮时触发
- `${host}/api/balance/withdraw-apply/list` - 获取提现记录，页面加载时触发
- `${host}/api/balance/withdraw-apply/detail` - 获取提现详情，页面加载时触发
- `${host}/api/balance/withdraw-apply/cancel` - 取消提现申请，用户点击取消按钮时触发
- `${host}/api/balance/withdraw-apply/fee` - 获取提现手续费，页面加载时触发
- `${host}/api/balance/withdraw-apply/check` - 提现前检查，用户点击提现按钮时触发
- `${host}/mall-app/api/withdrawal/account-statement/list` - 获取账单列表，页面加载时触发
- `${host}/mall-app/api/withdrawal/account-statement/detail` - 获取账单详情，页面加载时触发
- `${host}/mall-app/api/withdrawal/apply` - 申请提现，用户点击提现按钮时触发
- `${host}/mall-app/api/withdrawal/record/list` - 获取提现记录，页面加载时触发
- `${host}/mall-app/api/withdrawal/record/detail` - 获取提现记录详情，页面加载时触发

## 银行卡相关页面
- `${host}/api/bank-card/list` - 获取银行卡列表，页面加载时触发
- `${host}/api/bank-card/add` - 添加银行卡，用户提交银行卡信息时触发
- `${host}/api/bank-card/delete` - 删除银行卡，用户点击删除按钮时触发
- `${host}/api/bank-card/set-default` - 设置默认银行卡，用户点击设为默认按钮时触发
- `${host}/api/bank-card/bank-list` - 获取银行列表，页面加载时触发
- `${host}/api/bank-card/verify-code/send` - 发送银行卡验证码，用户点击获取验证码按钮时触发
- `${host}/api/bank-card/verify-code/check` - 验证银行卡验证码，用户提交验证码时触发
- `${host}/api/bank-card/bind-mobile/send-sms` - 发送绑定手机验证码，用户点击获取验证码按钮时触发
- `${host}/api/bank-card/bind-mobile/check` - 验证绑定手机验证码，用户提交验证码时触发
- `${host}/mall-app/api/bank/list` - 获取银行列表(新版)，页面加载时触发
- `${host}/mall-app/api/bank/add` - 添加银行卡(新版)，用户提交银行卡信息时触发
- `${host}/mall-app/api/bank/delete` - 删除银行卡(新版)，用户点击删除按钮时触发

## 实名认证相关页面
- `${host}/api/user/real-name-auth/status` - 获取实名认证状态，页面加载时触发
- `${host}/api/user/real-name-auth/submit` - 提交实名认证，用户提交实名信息时触发
- `${host}/api/user/real-name-auth/detail` - 获取实名认证详情，页面加载时触发
- `${host}/api/user/real-name-auth/update` - 更新实名认证信息，用户修改实名信息时触发
- `${host}/api/user/real-name-auth/check` - 检查实名认证状态，页面加载时触发
- `${host}/api/user/real-name-auth/send-sms` - 发送实名认证验证码，用户点击获取验证码按钮时触发
- `${host}/api/user/real-name-auth/verify-sms` - 验证实名认证验证码，用户提交验证码时触发
- `${host}/mall-app/api/user/real-name/status` - 获取实名认证状态(新版)，页面加载时触发
- `${host}/mall-app/api/user/real-name/submit` - 提交实名认证(新版)，用户提交实名信息时触发

## 账户余额页面 (pages/account-balance/index)
- `${host}/mall-app/api/userAmount/detail` - 获取用户余额详情，页面加载时触发
- `${host}/mall-app/api/userAmountFlow/list` - 获取余额变更记录，页面加载时触发
- `${host}/mall-app/api/userAmountSend/list` - 获取福豆券列表，页面加载时触发
- `${host}/mall-app/api/rule/balance/getDefaultRemark` - 获取福豆规则，页面加载时触发
- `${host}/mall-app/api/userAmountSend/getUnReadAmountSend` - 获取未读余额券，页面加载时触发
- `${host}/mall-app/api/userAmount/cash/list` - 获取福卡列表，页面加载时触发
- `${host}/mall-app/api/userAmount/cash/detail` - 获取福卡详情，页面加载时触发

## 福豆券/优惠券弹窗 (pages/wallet-card)
- `${host}/mall-app/api/userAmountSend/getUnReadAmountSend` - 获取未读余额券，页面加载时触发
- `${host}/mall-app/api/user/coupon/queryWaitGetCoupons` - 查询可领取优惠券，页面加载时触发
- `${host}/mall-app/api/user/coupon/autoGetCoupons` - 自动领取优惠券，用户点击领取按钮时触发

## 账单明细页面 (pages/withdrawal/account-statement)
- `${host}/mall-app/api/withdrawal/account-statement/list` - 获取账单列表，页面加载和筛选条件变化时触发
- `${host}/mall-app/api/withdrawal/account-statement/detail` - 获取账单详情，点击账单项时触发
- `${host}/mall-app/api/withdrawal/account-statement/summary` - 获取账单汇总信息，页面加载时触发
- `${host}/mall-app/api/withdrawal/account-statement/export` - 导出账单，用户点击导出按钮时触发

## 授权书页面 (pages/attorney)
- `${host}/api/shop/authorization/pages` - 获取授权书列表，页面加载时触发
- `${host}/api/shop/authorization/detail` - 获取授权书详情，点击授权书项时触发
- `${host}/api/shop/authorization/download` - 下载授权书，用户点击下载按钮时触发
- `${host}/api/getAuthLetter` - 获取授权书列表(旧版)，页面加载时触发

## 通用接口
- `${host}/api/common/upload` - 上传文件，用户上传图片或文件时触发
- `${host}/api/common/config` - 获取系统配置，页面加载时触发
- `${host}/api/common/version` - 获取系统版本，页面加载时触发
- `${host}/api/common/area` - 获取地区列表，页面加载时触发
- `${host}/api/common/dict` - 获取字典数据，页面加载时触发
- `${host}/api/common/qrcode` - 生成二维码，页面加载时触发
- `${host}/api/common/captcha` - 获取图形验证码，页面加载时触发
- `${host}/api/common/sms/send` - 发送短信验证码，用户点击获取验证码按钮时触发
- `${host}/api/common/sms/verify` - 验证短信验证码，用户提交验证码时触发